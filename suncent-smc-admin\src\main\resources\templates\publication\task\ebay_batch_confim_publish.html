<!DOCTYPE html>
<html lang="zh">
<head>
    <th:block th:include="include :: header('批量确认Ebay刊登')"/>
    <th:block th:include="include :: jquery-smartwizard-css"/>
    <th:block th:include="include :: select2-css"/>
    <link th:href="@{/css/fancybox.css}" rel="stylesheet"/>
    <style type="text/css">
        /* 如果要让工具栏固定在页面底部,使用下面的样式,不需要的可以注释 */
        .sw > .toolbar-bottom {
            z-index: 100;
            bottom: 0px;
            left: 0;
            width: 100%;
            position: fixed;
            text-align: right;
            background: #fff;
            box-shadow: 0 -2px 6px 1px hsla(223, 8%, 83%, .5);
            border-top: 1px solid #e3e4e8;
        }

        /* 如果设置了是否自动调节高度为false,需要加滚动条 */
        /*.sw > .tab-content {*/
        /*    overflow-x: auto;*/
        /*    overflow-y: auto;*/
        /*    height: 800px !important;*/
        /*}*/

        /* 解决工具栏无法固定底部的问题（如果页面没有animated类可以不写这部分代码） */
        .animated {
            animation-fill-mode: none;
            -webkit-animation-fill-mode: none;
            -moz-animation-fill-mode: none;
            -o-animation-fill-mode: none;
        }

        #showimg {
            /*display: flex;*/
            justify-content: flex-start;
        }

        #showimg li {
            width: 150px;
            height: 150px;
            position: relative;
            overflow: hidden;
            display: inline-block;
            margin-right: 5px;
        }

        #showimg li img.showimg {
            position: absolute;
            text-align: center;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 6;
        }

        #showimg li:first-child img.left {
            opacity: .6;
            cursor: no-drop;
        }

        #showimg li:last-child img.right {
            opacity: .6;
            cursor: no-drop;
        }

        .showdiv {
            position: absolute;
            z-index: 7;
            bottom: 0;
            width: calc(100% - 20px);
            padding: 10px;
            display: flex;
            justify-content: space-around;
            background: rgba(0, 0, 0, .6);
        }

        .showdiv img {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight" style="height: 100%;">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox">
                <div class="ibox-content">
                    <div class="row select-list" style="padding-left: 15px; margin-bottom: 10px;">
                        <ul style="float: right">
                            <li>
                                <div class="btn-group-sm" role="group">
                                    <a class="btn btn-info" id="prev-btn"> 上一步 </a>
                                    <a class="btn btn-success" id="next-btn"> 下一步 </a>
                                    <a class="btn btn-danger" id="reset-btn"> 重置 </a>
                                </div>
                            </li>
                        </ul>

                    </div>
                    <div id="smartwizard">
                        <ul class="nav">
                            <li class="nav-item">
                                <a class="nav-link" href="#step-1"> 基本信息 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-2"> 标题 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-3"> 价格&库存 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-4"> 属性 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-5"> 包裹信息</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-6"> 长描述 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-7"> 图片 </a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div id="step-1" class="tab-pane" role="tabpanel" aria-labelledby="step-1">

                                <div>
                                    <form class="form form-horizontal m-t" th:object="${listingEditVO}">
                                        <div class="form-group">
                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label is-required">站点：</label>
                                                <div class="col-sm-9">
                                                    <input type="text" class="form-control" name="siteCode" id="siteCode" th:field="*{siteCode}" readonly>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label font-noraml is-required">店铺：</label>
                                                <div class="col-sm-9">
                                                    <input type="text" class="form-control" name="shopCode" id="shopCode" th:field="*{shopCode}" readonly>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">

                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label">刊登类型：</label>
                                                <div class="col-sm-9">
                                                    <input type="text" id="publishTypeName" class="form-control"
                                                           name="publishTypeName" th:field="*{publishTypeName}" readonly>
                                                    <input type="hidden" id="publishType" class="form-control"
                                                           name="publishType" th:field="*{publishType}" readonly>
                                                </div>
                                            </div>

                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label is-required">币种：</label>
                                                <div class="col-sm-9">
                                                    <input type="text" class="form-control"
                                                           name="currency" value="USD" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label is-required">品牌：</label>
                                                <div class="col-sm-9">
                                                    <select id="brandCode" name="brandCode" th:field="*{brandCode}" class="form-control m-b"
                                                            data-none-selected-text="请选择品牌"  required>
                                                        <option value="">请选择</option>
                                                        <option
                                                                th:each="keyValueEntity:${@cdpBaseConfig.getBrandAllKVList()}"
                                                                th:value="${keyValueEntity.value}"
                                                                th:text="${keyValueEntity.value}"></option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label is-required">物品所在地：</label>
                                                <div class="col-sm-8">
                                                    <select id="location" class="form-control m-b" th:field="*{location}" disabled
                                                            name="location" required>
                                                        <option
                                                                th:each='keyValueEntity:${@omsUsaZipCode.selectOmsUsaZipCodeList()} '
                                                                th:value="${keyValueEntity.state}"
                                                                th:text="${keyValueEntity.state}"></option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-12">
                                                <!--                                    平台第一品类-->
                                                <div class="form-group">
                                                    <div class="col-sm-6">
                                                        <label class="col-sm-3 control-label is-required">平台第一品类：</label>
                                                        <div class="col-sm-9">
                                                            <select name="firstCategoryid" class="form-control"
                                                                    id="firstCategoryid" th:field="*{firstCategoryid}" disabled
                                                                    data-none-selected-text="请选择" required>
                                                                <option value="">--请选择平台第一品类--</option>
<!--                                                                <option th:each="keyValueEntity:${@smcBaseConfig.getPlatformCategoryByPlatformCode('EB')}"-->
<!--                                                                        th:value="${keyValueEntity.key}"-->
<!--                                                                        th:text="${keyValueEntity.value}"></option>-->
                                                            </select>
                                                        </div>
                                                        <label class="control-label" id="categoryOneLabel"></label>
                                                        <label class="control-label" id="categoryOneLabelEn"></label>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <label class="col-sm-3 control-label">平台第二品类(收费)：</label>
                                                        <div class="col-sm-9">
                                                            <select name="secondCategoryid" class="form-control"
                                                                    id="secondCategoryid" th:field="*{secondCategoryid}" disabled
                                                                    data-none-selected-text="请选择">
                                                                <option value="">--请选择平台第二品类--</option>
<!--                                                                <option th:each="keyValueEntity:${@smcBaseConfig.getPlatformCategoryByPlatformCode('EB')}"-->
<!--                                                                        th:value="${keyValueEntity.key}"-->
<!--                                                                        th:text="${keyValueEntity.value}"></option>-->
                                                            </select>
                                                        </div>
                                                        <label class="control-label" id="categoryTwoLabel"></label>
                                                        <label class="control-label" id="categoryTwoLabelEn"></label>
                                                    </div>
                                                </div>

                                                <!--                                    商铺第一品类-->
                                                <div class="form-group">
                                                    <div class="col-sm-6">
                                                        <label class="col-sm-3 control-label">店铺第一品类：</label>
                                                        <div class="col-sm-9">
                                                            <input type="text" class="form-control"
                                                                   placeholder="请填写品类名称"
                                                                   name="firstShopCategory" th:field="*{firstShopCategory}" disabled>
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <label class="col-sm-3 control-label">店铺第二品类：</label>
                                                        <div class="col-sm-9">
                                                            <input type="text" class="form-control"
                                                                   placeholder="请填写品类名称"
                                                                   name="secondShopCategory" th:field="*{secondShopCategory}" disabled>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <div class="col-sm-6">
                                                        <label class="col-sm-3 control-label is-required">商品状况：</label>
                                                        <div class="col-sm-9">
                                                            <select name="condition" class="form-control m-b"
                                                                    th:with="type=${@dict.getType('amazon_publication_goods_condition')}">
                                                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                                        th:value="${dict.dictValue}"></option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>

                            <div id="step-2" class="tab-pane" role="tabpanel" aria-labelledby="step-2">
                                <div>
                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-3" align="center">
                                                    <a class="btn btn-info" id="fillingTitle"> 一键填充 </a>
                                                </div>
                                                <div class="col-sm-5">
                                                    <input type="text" maxlength="200" class="form-control round-shop-title-input fillingTitle">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-3" align="center">
                                                    <a class="btn btn-info" id="replaceTitle"> 一键替换 </a>
                                                </div>
                                                <div class="col-sm-2">
                                                    <input type="text" class="form-control" name="itemTitleOld" placeholder="查找词">
                                                </div>
                                                <div class="col-sm-2">
                                                    <input type="text" class="form-control" name="itemTitleNew" placeholder="替换词">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-3" align="center">
                                                    <a class="btn btn-info" id="analysisTitle"> 一键解析 </a>
                                                </div>
                                                <div class="col-sm-5">
                                                    <textarea type="text" class="form-control analysisTitle"  style="margin-bottom: 3px"  placeholder="excel将平台商品编码按升序排列,再将对应标题列复制过来，即可以一键解析."></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <form class="form form-horizontal m-t">
                                        <div class="ibox-content">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div id="title-data">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div id="step-3" class="tab-pane" role="tabpanel" aria-labelledby="step-3">
                                <div>
                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-2" align="center">
                                                    <a class="btn btn-info" id="analysisPrice"> 价格解析 </a>
                                                </div>
                                                <div class="col-sm-5">
                                                    <textarea type="text" class="form-control analysisPrice"  style="margin-bottom: 3px"  placeholder="excel将平台商品编码按升序排列,再将对应价格列复制过来，即可以一键解析."></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <form class="form form-horizontal m-t">
                                        <div class="ibox-content">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div id="stock-price-data">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div id="step-4" class="tab-pane" role="tabpanel" aria-labelledby="step-4">
                                <!--属性信息-->
                                <form class="form form-horizontal m-t">
                                    <div class="ibox-title">
                                        <h6>属性信息</h6>
                                    </div>
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="btn-group-sm" id="toolbar2" role="group">
                                                <a class="btn btn-success" onclick="addColumn('bootstrap-table')">
                                                    <i class="fa fa-plus"></i> 新增
                                                </a>
<!--                                                <a class="btn btn-danger" onclick="removeEmptyItem('bootstrap-table')">-->
<!--                                                    <i class="fa fa-remove"></i> 删除空项-->
<!--                                                </a>-->
                                            </div>

                                            <div class="col-xs-12 select-table table-striped">
<!--                                                <table id="goods-info-table"></table>-->
                                                <form class="form form-horizontal m-t">
                                                    <table id="bootstrap-table" data-height="400" data-virtual-scroll="true"></table>
                                                </form>
                                                <div style="height: 30px"></div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </form>
                            <div id="step-5" class="tab-pane" role="tabpanel" aria-labelledby="step-5">
                                <div>
                                    <form class="form form-horizontal m-t">
                                        <div class="ibox-content">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div id="logistics-data">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>


                            <div id="step-6" class="tab-pane" role="tabpanel" aria-labelledby="step-6">
                                <div>
                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-2" align="center">
                                                    <a class="btn btn-info" id="fillingDetailDescription"> 一键填充 </a>
                                                    <a class="btn btn-info" id="analysisDetailDescription"> 一键解析 </a>
                                                </div>
                                                <div class="col-sm-10">
                                                    <textarea type="text" class="form-control is-required fillingDetailDescription"  style="margin-bottom: 3px"  placeholder="文本描述"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-2" align="center">
                                                    <a class="btn btn-info" id="replaceDetailDescription"> 一键替换 </a>
                                                </div>
                                                <div class="col-sm-2">
                                                    <input type="text" class="form-control" name="detailDescriptionOld" placeholder="查找词">
                                                </div>
                                                <div class="col-sm-2">
                                                    <input type="text" class="form-control" name="detailDescriptionNew" placeholder="替换词">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div>
                                    <form class="form form-horizontal m-t">
                                        <div class="ibox-content">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div id="long-desc-data">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div id="step-7" class="tab-pane" role="tabpanel" aria-labelledby="step-7">
                                <div>
                                    <form class="form form-horizontal m-t">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <h4 style="color: rgba(246,2,24,0.84)">默认第一张图为主图</h4>
                                            </div>
                                        </div>
                                        <div class="ibox-content">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div id="image-data">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: jquery-smartwizard-js"/>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-table-fixed-columns-js" />
<script th:src="@{/js/jquery-ui-1.10.4.min.js}"></script>
<script th:src="@{/extend/js/batch/ebayAddInfo.js?version=20250616}"></script>
<script th:src="@{/extend/js/batch/ebayAttributeV2.js?version=20250730}"></script>
<script th:src="@{/extend/js/batch/batchCommon.js?version=2024110701}"></script>
<script th:src="@{/extend/js/ebaySite/ebaySiteCommon.js?version=20231128}"></script>
<script th:src="@{/js/fancybox.umd.js}"></script>
<script th:inline="javascript">
    let prefixRequiredField = ctx + "publication/goods/property";
    let prefix = ctx + "publication/goods";
    let listingEditVO = [[${listingEditVO}]];
    let saleGoodsDTOList = [[${listingEditVO.listingEditDTOS}]];
    let siteCode = [[${listingEditVO.siteCode}]];
    let platform = 'EB';
    var goodsInfoList;
    var goodsDetails;
    var safetyCompliance;
    var fd; //FormData方式发送请求
    const checkTypeValue='confim';
    var transmitAttributeArr = new Array();
    var sourceType = [[${source}]];
 
    $(function () {
        let siteCode = $("#siteCode").val();
        initCategoryInfo(siteCode,listingEditVO.firstCategoryid,listingEditVO.secondCategoryid);

        //渲染标题、价格库存、物流、五点、描述、图片
        rendering();

        getTemplateHtml();
        // 品牌可以填入自定义值
        $('#brandCode').select2({
            tags: true
        });
        //渲染类目
        // getCategoryOneLabel();
    });


    /**
     * 保存描述内容回调函数 - 由编辑页面调用
     * @param index 行索引
     * @param content 富文本内容
     */
    function saveDetailDescription(index, content) {
        try {
            // 检查参数
            if (typeof index === 'undefined' || index === null) {
                $.modal.alertError("保存失败：缺少索引参数");
                return;
            }

            if (typeof content === 'undefined' || content === null) {
                content = "";
            }

            // 检查文本域是否存在
            var textArea = $("#detailDescription_"+index);
            if (textArea.length === 0) {
                $.modal.alertError("保存失败：找不到对应的描述文本域");
                return;
            }

            // 更新文本区域
            textArea.val(content);
        } catch (error) {
            $.modal.alertError("保存描述内容失败，请重试");
        }
    }
    function setDataInfo(data) {
        $('.form').each(function (index, form) {
            // 这里可以使用$.common.formToJSON(formId);  需要在form上加id
            $.each($(form).serializeArray(), function (i, field) {
                if (data[field.name]) {
                    data[field.name] += ("," + field.value);
                } else {
                    data[field.name] = field.value;
                }
            });
        });
        data["platform"] = "EB";
        data['tag'] = "batchSave"
        data["shopCode"] = listingEditVO.shopCode;
        data["condition"] = listingEditVO.condition;
        data["location"] = listingEditVO.location;
        data["country"] = listingEditVO.siteCode;
        data["firstCategoryid"] = listingEditVO.firstCategoryid;
        data["firstCategoryid"] = listingEditVO.firstCategoryid;
        $.each(saleGoodsDTOList, function (index, item) {
            //取出每个商品的图片
            let imageSrcArr = [];
            let currentImage = $("ul#showui" + index + " li");
            for (let i = 0; i < currentImage.length; i++) {
                let usid = currentImage[i].getElementsByTagName('img')[1];
                let imageSrc = $("#" + usid.id + "").attr("src");
                imageSrcArr.push(imageSrc);
                data["listings[" + index + "].imgArrs[" + i + "]"] = imageSrc;
            }
            //取出每个商品的其他信息
            data["listings[" + index + "].goodsCode"] = item.goodsCode;
            data["listings[" + index + "].id"] = item.goodsHeadId;
            data["listings[" + index + "].taskName"] = item.taskName;
        });

        //设置属性
        refreshTransmitAttributeData();
        for (let i = 0; i < transmitAttributeArr.length; i++) {
            data["listingAttributeLine[" + i + "]"] = JSON.stringify(transmitAttributeArr[i]);
        }
    }

    function previewTemplate(index, element){
        //如果是新模板,则调用新模板的预览方法
        var templateHtml = "";
        // 通过name=listings[index].templateSelect获取模板id
        const templateId = $('[name="listings['+index+'].descriptionId"]').val();
        const desc =  $('[name="listings['+index+'].detailDescription"]').val();
        const title = $('[name="listings['+index+'].itemTitle"]').val();
        templateHtml=  renderTemplateHtmlNew(index,templateId,desc,title) ;

        var win = window.open();
        win.document.write(templateHtml);
        win.document.close();
    }

    function renderTemplateHtmlNew(index, templateId, desc, title) {
        console.log(index, templateId, desc);
        //获取图片信息
        let imageSrcArr = [];
        for (let i = 0; i < $("ul#showui li").length; i++) {
            imageSrcArr.push( $("#" + $("ul#showui li")[i].getElementsByTagName('img')[1].id + "").attr("src") );
        }
        //获取属性信息
        sub.editRow();
        const data = $("#bootstrap-table").bootstrapTable('getData');
        let attributeJsonArr = [];
        for (let j = 0; j < data.length; j++) {
            attributeJsonArr.push({
                "name": data[j].attributeCode,
                "value": data[j].attributeValue
            });
        }

        //将数据封装到renderTemplateHtmlData对象中
        var renderTemplateHtmlData = {
            "onlineDesc": desc,
            "imageSrcArr": imageSrcArr,
            "attributeJsonArr": attributeJsonArr,
            "templateId": templateId,
            "title": title
        }

        var html = "";
        $.ajax({
            cache : true,
            type : "POST",
            datatype : "json",
            contentType : "application/json",
            url : ctx + "template/description/renderTemplateToHtml",
            data : JSON.stringify(renderTemplateHtmlData),
            async : false,
            error : function(request) {
                $.modal.alertError("系统错误");
            },
            success : function(data) {
                if (data.code == web_status.SUCCESS) {
                    html= data.msg;
                }
            }
        });
        return html;
    }


    /**
     * 处理模板内容
     * @returns {string}
     */
    function renderTemplateHtml() {
        const templateId = $('#templateSelect').val();
        const warpDiv = document.createElement('div');
        if(!templateId){
            warpDiv.innerHTML = getGrapesHtml();
        }

        return warpDiv.outerHTML;
    }
    function submit(){
        var data = {};
        setDataInfo(data);

        let config = {
            url: prefix + "/batchAddPublish",
            type: "post",
            dataType: "json",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
            },
            success: function (e) {
                if (e.code === 0) {
                    $.modal.close();
                    $.modal.closeLoading();
                    top.layer.confirm("生成草稿成功，请移至listing管理草稿页面！", {
                        icon: 3,
                        title: "系统提示",
                        btn: ['确认']
                    });
                } else {
                    if (e.msg == null || e.msg == '') {
                        $.modal.alertError("批量生成草稿失败,请重新检查数据或联系管理员.");
                    } else {
                        $.modal.alertError(e.msg);
                    }
                    $.modal.closeLoading();
                }
            }
        };
        $.ajax(config);

    }

    function submitSave(){
        var data = {};
        setDataInfo(data);

        let config = {
            url: prefix + "/batchTempSaveListing",
            type: "post",
            dataType: "json",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
            },
            success: function (e) {
                if (e.code === 0) {
                    $.modal.close();
                    $.modal.closeLoading();
                    top.layer.confirm("临时保存成功！", {
                        icon: 3,
                        title: "系统提示",
                        btn: ['确认']
                    });
                } else {
                    if (e.msg == null || e.msg == '') {
                        $.modal.alertError("临时保存失败,请重新检查数据或联系管理员.");
                    } else {
                        $.modal.alertError(e.msg);
                    }
                    $.modal.closeLoading();
                }
            }
        };
        $.ajax(config);

    }

</script>
</body>
<script th:src="@{/js/jquery.tmpl.js}"></script>
<th:block th:include="include :: select2-js"/>
<script id="goodsInfoTpl" type="text/x-jquery-tmpl">
<div>
<select name="listingAmazonAttributeLinesInfo[${index}].tableName" id="goodsInfoAttribute" class="form-control" style="width: 100%;">
    <option value="" selected>请选择</option>
     {{each(rowIndex,data) list}}
         <option value="${data.attributeName}" data-type="${data.attributeMemo}" {{if type===data.attributeName}}selected{{/if}} >${data.attributeName}</option>
     {{/each}}
</select>
</div>

</script>
<script id="goodsDetailsTpl" type="text/x-jquery-tmpl">
<div>
<select name="listingAmazonAttributeLinesDetail[${index}].tableName" id="goodsDetailsAttribute" class="form-control" style="width: 100%;">
    <option value="" selected>请选择</option>
     {{each(rowIndex,data) list}}
         <option value="${data.attributeName}" data-type="${data.attributeMemo}" {{if type===data.attributeName}}selected{{/if}}>${data.attributeName}</option>
     {{/each}}
</select>
</div>

</script>
<script id="safetyComplianceTpl" type="text/x-jquery-tmpl">
<div>
<select name="listingAmazonAttributeLinesSafety[${index}].tableName" id="goodsSafetyAttribute" class="form-control" style="width: 100%;">
    <option value="" selected>请选择</option>
     {{each(rowIndex,data) list}}
         <option value="${data.attributeName}" data-type="${data.attributeMemo}" {{if type===data.attributeName}}selected{{/if}}>${data.attributeName}</option>
     {{/each}}
</select>
</div>

</script>
<script id="attributeImport" type="text/x-jquery-tmpl">
<div align='center'>
<select name="listingAmazonAttributeLinesInfo[${index}].tableValue" id="tableValue" class="form-control" style="width: 100%;">
    <option value="">请选择</option>
     {{each(rowIndex,data) list}}
         <option class='form-control' value="${data}"  {{if type===data}}selected{{/if}} >${data}</option>
     {{/each}}
</select>
</div>

</script>
<script id="attributeDetails" type="text/x-jquery-tmpl">
<div align='center'>
<select name="listingAmazonAttributeLinesDetail[${index}].tableValue" id="tableValue" class="form-control" style="width: 100%;">
    <option value="">请选择</option>
     {{each(rowIndex,data) list}}
         <option class='form-control'  value="${data}"  {{if type===data}}selected{{/if}} >${data}</option>
     {{/each}}
</select>
</div>

</script>
<script id="attributeSafety" type="text/x-jquery-tmpl">
<div align='center'>
<select name="listingAmazonAttributeLinesSafety[${index}].tableValue" id="tableValue" class="form-control" style="width: 100%;">
    <option value="">请选择</option>
     {{each(rowIndex,data) list}}
         <option class='form-control'  value="${data}"  {{if type===data}}selected{{/if}} >${data}</option>
     {{/each}}
</select>
</div>

</script>
</html>
