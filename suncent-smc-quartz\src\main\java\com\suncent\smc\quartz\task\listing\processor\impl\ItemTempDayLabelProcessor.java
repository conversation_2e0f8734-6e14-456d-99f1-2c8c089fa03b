package com.suncent.smc.quartz.task.listing.processor.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.persistence.bi.domain.entity.DwiPromotionsDetail;
import com.suncent.smc.persistence.bi.entity.CrlRpaAdvertisingRestrictions;
import com.suncent.smc.persistence.bi.service.IBi2DataService;
import com.suncent.smc.persistence.bi.service.IOdsCrlCrlVcCatalogDataService;
import com.suncent.smc.persistence.competitiveProducts.domain.ItemTempDay;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.quartz.task.listing.processor.LabelProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ItemTempDay标签处理器
 * 处理BD异常、Coupon异常和广告受限标签
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Component
@Slf4j
public class ItemTempDayLabelProcessor implements LabelProcessor {

    @Autowired
    private IGoodsHeadService goodsHeadService;

    @Autowired
    private IBi2DataService bi2DataService;

    @Autowired
    private IOdsCrlCrlVcCatalogDataService odsCrlCrlVcCatalogDataService;

    @Autowired
    @Qualifier("mongoTemplateAWS")
    private MongoTemplate awsMongoTemplate;

    @Override
    public String getLabelType() {
        return "ADS-itemTempDay";
    }
    
    @Override
    public List<GoodsHead> querySourceData(String shopCode, int offset, int limit) {
        // 只处理VC1店铺
        if (!"VC1".equalsIgnoreCase(shopCode)) {
            return Collections.emptyList();
        }

        // 分批查询需要更新发布状态的商品列表
        return goodsHeadService.listNeedUpdatePublishStatusPaged(offset, limit);
    }

    @Override
    public List<ListingLabel> processToLabels(List<? extends Object> sourceData, String shopCode) {
        List<GoodsHead> goodsHeadList = (List<GoodsHead>) sourceData;

        if (CollUtil.isEmpty(goodsHeadList)) {
            return Collections.emptyList();
        }

        String today = DateUtils.getDate();

        // 提取所有的ASIN
        List<String> asinList = goodsHeadList.stream()
                .map(GoodsHead::getPlatformGoodsId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(asinList)) {
            log.warn("当前批次没有有效的ASIN数据");
            return Collections.emptyList();
        }

        // 查询促销数据
        Map<String, List<DwiPromotionsDetail>> asinPromotionMap = queryPromotionData(asinList);
        List<String> promotionAsinList = new ArrayList<>(asinPromotionMap.keySet());

        // 查询ItemTempDay数据
        Map<String, ItemTempDay> asinToItemTempDayMap = queryItemTempDayData(promotionAsinList, today);

        // 查询广告受限数据
        Set<String> restrictedAsinSet = queryAdvertisingRestrictionsData(asinList);

        // 生成标签
        return generateLabelsFromItemTempDay(goodsHeadList, asinToItemTempDayMap, restrictedAsinSet, asinPromotionMap);
    }

    @Override
    public int getBatchSize() {
        return 1000;
    }

    @Override
    public int getPriority() {
        return 3;
    }


    @Override
    public String getDescription() {
        return "ItemTempDay标签处理器（BD异常、Coupon异常、广告受限）";
    }

    /**
     * 查询促销数据
     */
    private Map<String, List<DwiPromotionsDetail>> queryPromotionData(List<String> asinList) {
        try {
            List<DwiPromotionsDetail> promotionDetails = odsCrlCrlVcCatalogDataService.listPromotionDetailsByAsins(asinList);
            if (CollUtil.isNotEmpty(promotionDetails)) {
                Map<String, List<DwiPromotionsDetail>> result = promotionDetails.stream()
                        .collect(Collectors.groupingBy(DwiPromotionsDetail::getPlatformSaleCode));
                log.debug("查询到{}个ASIN当前处于促销期间，促销明细记录数：{}", result.size(), promotionDetails.size());
                return result;
            }
        } catch (Exception e) {
            log.error("查询促销期间ASIN数据时发生错误", e);
        }
        return Collections.emptyMap();
    }

    /**
     * 查询ItemTempDay数据
     */
    private Map<String, ItemTempDay> queryItemTempDayData(List<String> promotionAsinList, String today) {
        try {
            if (CollUtil.isNotEmpty(promotionAsinList)) {
                Query query = new Query();
                query.addCriteria(Criteria.where("date").is(today)
                        .and("asin").in(promotionAsinList));
                List<ItemTempDay> itemTempDayList = awsMongoTemplate.find(query, ItemTempDay.class);

                if (CollUtil.isNotEmpty(itemTempDayList)) {
                    Map<String, ItemTempDay> result = itemTempDayList.stream()
                            .collect(Collectors.toMap(ItemTempDay::getAsin, item -> item, (existing, replacement) -> existing));
                    log.debug("成功查询到{}条ItemTempDay数据", itemTempDayList.size());
                    return result;
                } else {
                    log.warn("MongoDB中没有找到对应的ItemTempDay数据，日期：{}, 促销ASIN数量：{}", today, promotionAsinList.size());
                }
            } else {
                log.debug("没有ASIN处于促销期间，跳过ItemTempDay数据查询");
            }
        } catch (Exception e) {
            log.error("查询ItemTempDay数据时发生错误", e);
        }
        return Collections.emptyMap();
    }

    /**
     * 查询广告受限数据
     */
    private Set<String> queryAdvertisingRestrictionsData(List<String> asinList) {
        try {
            List<CrlRpaAdvertisingRestrictions> advertisingRestrictions = bi2DataService.selectAdvertisingRestrictionsByAsinList(asinList);
            if (CollUtil.isNotEmpty(advertisingRestrictions)) {
                Set<String> result = advertisingRestrictions.stream()
                        .map(CrlRpaAdvertisingRestrictions::getAsin)
                        .collect(Collectors.toSet());
                log.debug("成功查询到{}条广告受限数据", advertisingRestrictions.size());
                return result;
            } else {
                log.debug("没有找到广告受限数据，ASIN数量：{}", asinList.size());
            }
        } catch (Exception e) {
            log.error("查询广告受限数据时发生错误", e);
        }
        return Collections.emptySet();
    }

    /**
     * 根据ItemTempDay数据和广告受限数据生成标签
     */
    private List<ListingLabel> generateLabelsFromItemTempDay(List<GoodsHead> goodsHeadList,
                                                             Map<String, ItemTempDay> asinToItemTempDayMap,
                                                             Set<String> restrictedAsinSet,
                                                             Map<String, List<DwiPromotionsDetail>> asinPromotionMap) {
        List<ListingLabel> labels = new ArrayList<>();

        for (GoodsHead goodsHead : goodsHeadList) {
            String asin = goodsHead.getPlatformGoodsId();
            if (ObjUtil.isEmpty(asin)) {
                continue;
            }

            // 处理ItemTempDay相关标签（BD异常和Coupon异常）
            if (asinToItemTempDayMap != null && !asinToItemTempDayMap.isEmpty()) {
                ItemTempDay itemTempDay = asinToItemTempDayMap.get(asin);
                if (itemTempDay != null) {
                    List<DwiPromotionsDetail> promotionDetails = asinPromotionMap.getOrDefault(asin, Collections.emptyList());

                    // 检查BD异常
                    if (shouldGenerateBdExceptionLabel(itemTempDay, promotionDetails)) {
                        labels.add(createLabel(goodsHead, "BD异常"));
                    }

                    // 检查Coupon异常
                    if (shouldGenerateCouponExceptionLabel(itemTempDay, promotionDetails)) {
                        labels.add(createLabel(goodsHead, "Coupon异常"));
                    }
                }
            }

            // 处理广告受限标签
            if (restrictedAsinSet != null && !restrictedAsinSet.isEmpty() && restrictedAsinSet.contains(asin)) {
                labels.add(createLabel(goodsHead, "广告受限"));
            }
        }

        return labels;
    }

    /**
     * 判断是否应该生成BD异常标签
     */
    private boolean shouldGenerateBdExceptionLabel(ItemTempDay itemTempDay, List<DwiPromotionsDetail> promotionDetails) {
        // 检查是否有BEST_DEAL类型的促销
        boolean hasBestDealPromotion = promotionDetails.stream()
                .anyMatch(detail -> "BEST_DEAL".equals(detail.getPromotionType()));

        if (hasBestDealPromotion) {
            // 有BEST_DEAL促销，检查ItemTempDay是否有BD数据
            String deal = itemTempDay.getDeal();
            ItemTempDay.Flag flag = itemTempDay.getFlag();
            String flagDeal = (flag != null) ? flag.getDeal() : null;

            // 如果ItemTempDay中有BD数据，则不生成BD异常标签
            if ((ObjUtil.isNotEmpty(deal) && !"0".equals(deal)) ||
                    (ObjUtil.isNotEmpty(flagDeal) && !"0".equals(flagDeal))) {
                return false;
            }
        }

        // 检查deal字段，如果为空或者为0，生成"BD异常"标签
        String deal = itemTempDay.getDeal();
        if (ObjUtil.isEmpty(deal) || "0".equals(deal)) {
            return true;
        }

        // 检查flag对象和flag.deal字段
        ItemTempDay.Flag flag = itemTempDay.getFlag();
        if (flag == null) {
            return true;
        }

        String flagDeal = flag.getDeal();
        return ObjUtil.isEmpty(flagDeal) || "0".equals(flagDeal);
    }

    /**
     * 判断是否应该生成Coupon异常标签
     */
    private boolean shouldGenerateCouponExceptionLabel(ItemTempDay itemTempDay, List<DwiPromotionsDetail> promotionDetails) {
        // 检查是否有CODED_COUPON类型的促销
        boolean hasCodedCouponPromotion = promotionDetails.stream()
                .anyMatch(detail -> "CODED_COUPON".equals(detail.getPromotionType()));

        if (hasCodedCouponPromotion) {
            // 有CODED_COUPON促销，检查ItemTempDay是否有Coupon数据
            ItemTempDay.Flag flag = itemTempDay.getFlag();
            String coupon = (flag != null) ? flag.getCoupon() : null;

            // 如果ItemTempDay中有Coupon数据，则不生成Coupon异常标签
            if (ObjUtil.isNotEmpty(coupon) && !"0".equals(coupon)) {
                return false;
            }
        }

        // 检查flag的coupon字段
        ItemTempDay.Flag flag = itemTempDay.getFlag();
        if (flag == null) {
            return true;
        }

        String coupon = flag.getCoupon();
        return ObjUtil.isEmpty(coupon) || "0".equals(coupon);
    }

    /**
     * 创建标签对象
     */
    private ListingLabel createLabel(GoodsHead goodsHead, String labelValue) {
        ListingLabel label = new ListingLabel();
        label.setHeadId(goodsHead.getId());
        label.setPlatform(goodsHead.getPlatform());
        label.setSiteCode(goodsHead.getSiteCode());
        label.setShopCode(goodsHead.getShopCode());
        label.setLabelType(getLabelType());
        label.setLabel(labelValue);
        label.setCreateTime(DateUtils.getNowDate());
        return label;
    }
}
