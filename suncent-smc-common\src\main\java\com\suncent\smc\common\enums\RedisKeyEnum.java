package com.suncent.smc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * RedisKey枚举
 */
@Getter
@AllArgsConstructor
public enum RedisKeyEnum {
    EBAY_SYNC_SHOP_Listing("ebay-sync-shop-listing:", "ebay同步店铺刊登数据"),
    AM_SYNC_SHOP_Listing("am-sync-shop-listing:", "am同步店铺刊登数据"),
    EBAY_PUSH_Listing("ebay-push-listing:", "ebay刊登商品"),
    EBAY_AUTO_PUSH_Listing("ebay-auto-push-listing", "ebay自动刊登商品"),
    AM_AUTO_PUSH_Listing("am-auto-push-listing", "ebay自动刊登商品"),
    AM_AUTO_STOCK_UPDATE("am_auto-stock-update", "am库存自动更新"),
    EBAY_AUTO_STOCK_UPDATE("ebay_auto-stock-update", "ebay库存自动更新"),
    INVENTORY_CONSUMER_SWITCH("inventory-consumer-switch", "库存消费者开关"),
    TEMU_SYNC_SHOP_Listing("temu-sync-shop-listing:", "temu同步店铺刊登数据"),
    TEMU_PUSH_Listing("temu-push-listing:", "temu店铺刊登数据"),

    SKU_BLOCK_KEY("skus_block_key:", "红线价sku黑名单"),


    ;

    private final String key;
    private final String value;

}
