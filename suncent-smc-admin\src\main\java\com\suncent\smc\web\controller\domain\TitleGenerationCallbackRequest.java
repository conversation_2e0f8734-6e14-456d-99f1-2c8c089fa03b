package com.suncent.smc.web.controller.domain;

import com.alibaba.fastjson.JSONObject;
import com.suncent.smc.common.api.domain.ApiCloudBasicRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.suncent.smc.provider.biz.publication.dto.EbayTitleGenerationResponseDTO;


/**
 * AI标题生成回调请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TitleGenerationCallbackRequest extends ApiCloudBasicRequest {

    /**
     * 任务关联标识符
     */
    private String taskCorrelationId;

    /**
     * 生成状态 (success/failed)
     */
    private String status;

    /**
     * 生成结果数据，格式与EbayTitleGenerationResponseDTO.data一致
     * Key: 商品编码
     * Value: 商品标题信息
     */
    private JSONObject result;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

    /**
     * 回调时间戳
     */
    private Long timestamp;
}
