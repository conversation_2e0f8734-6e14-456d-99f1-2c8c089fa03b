package com.suncent.smc.persistence.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.suncent.smc.common.dto.AmazonFrontMappingDTO;
import com.suncent.smc.common.enums.AmazonFrontMappingEnum;
import com.suncent.smc.persistence.amazon.domain.AmazonFrontDetail;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Amazon前台数据映射工具类
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public class AmazonFrontMappingUtil {

    /**
     * 处理前台数据映射
     *
     * @param frontDetails 前台数据列表
     * @return 映射后的数据Map，key为后台字段名，value为映射DTO列表
     */
    public static Map<String, List<AmazonFrontMappingDTO>> processFrontDataMapping(List<AmazonFrontDetail> frontDetails) {
        if (CollUtil.isEmpty(frontDetails)) {
            return new HashMap<>();
        }

        Map<String, List<AmazonFrontMappingDTO>> resultMap = new HashMap<>();

        // 遍历所有映射配置
        for (AmazonFrontMappingEnum mapping : AmazonFrontMappingEnum.getAllMappings()) {
            // 查找匹配的前台数据
            List<AmazonFrontDetail> matchedDetails = findMatchedFrontDetails(mapping, frontDetails);

            if (CollUtil.isNotEmpty(matchedDetails)) {
                String backendFieldName = mapping.getBackendFieldName();

                // 为每个匹配的前台数据创建DTO
                AmazonFrontMappingDTO mappingDTOs = matchedDetails.stream()
                        .map(detail -> createMappingDTO(mapping, detail))
                        .filter(Objects::nonNull).findFirst().get();


                if (ObjectUtil.isNotEmpty(mappingDTOs)) {
                    resultMap.computeIfAbsent(backendFieldName, k -> new ArrayList<>()).add(mappingDTOs);
                }
            }
        }

        // 对每个字段的映射数据按显示顺序排序
        resultMap.forEach((key, value) ->
                value.sort(Comparator.comparing(AmazonFrontMappingDTO::getDisplayOrder, Comparator.nullsLast(Integer::compareTo)))
        );

        return resultMap;
    }

    /**
     * 根据后台字段名称获取前台数据
     *
     * @param backendFieldName 后台字段名称
     * @param frontDetails     前台数据列表
     * @return 映射DTO列表
     */
    public static List<AmazonFrontMappingDTO> getFrontDataByBackendField(String backendFieldName, List<AmazonFrontDetail> frontDetails) {
        if (StrUtil.isBlank(backendFieldName) || CollUtil.isEmpty(frontDetails)) {
            return new ArrayList<>();
        }

        List<AmazonFrontMappingEnum> mappings = AmazonFrontMappingEnum.getByBackendField(backendFieldName);
        if (CollUtil.isEmpty(mappings)) {
            return new ArrayList<>();
        }

        List<AmazonFrontMappingDTO> result = new ArrayList<>();

        for (AmazonFrontMappingEnum mapping : mappings) {
            List<AmazonFrontDetail> matchedDetails = findMatchedFrontDetails(mapping, frontDetails);

            for (AmazonFrontDetail detail : matchedDetails) {
                AmazonFrontMappingDTO dto = createMappingDTO(mapping, detail);
                if (dto != null) {
                    result.add(dto);
                }
            }
        }

        // 按显示顺序排序
        result.sort(Comparator.comparing(AmazonFrontMappingDTO::getDisplayOrder, Comparator.nullsLast(Integer::compareTo)));

        return result;
    }

    /**
     * 获取特定字段的第一个前台数据值
     *
     * @param backendFieldName 后台字段名称
     * @param frontDetails     前台数据列表
     * @return 前台数据值，如果没有则返回null
     */
    public static String getFirstFrontValue(String backendFieldName, List<AmazonFrontDetail> frontDetails) {
        List<AmazonFrontMappingDTO> mappings = getFrontDataByBackendField(backendFieldName, frontDetails);
        return mappings.stream()
                .map(AmazonFrontMappingDTO::getFrontValue)
                .filter(StrUtil::isNotBlank)
                .findFirst()
                .orElse(null);
    }

    /**
     * 查找匹配的前台数据
     * 使用精确的path+key组合匹配
     *
     * @param mapping      映射配置
     * @param frontDetails 前台数据列表
     * @return 匹配的前台数据列表
     */
    private static List<AmazonFrontDetail> findMatchedFrontDetails(AmazonFrontMappingEnum mapping, List<AmazonFrontDetail> frontDetails) {
        List<AmazonFrontDetail> matchedDetails = frontDetails.stream()
                .filter(detail -> {
                    boolean matches = mapping.matches(detail.getKey(), detail.getPath());

                    return matches;
                })
                .collect(Collectors.toList());

        // 如果没有匹配到数据，输出调试信息
        if (matchedDetails.isEmpty()) {
            System.out.println(String.format("未匹配到数据: 枚举[%s] 在 %d 条前台数据中未找到匹配项",
                    mapping.getPathKeyIdentifier(), frontDetails.size()));
        }

        return matchedDetails;
    }

    /**
     * 创建映射DTO
     *
     * @param mapping 映射配置
     * @param detail  前台数据
     * @return 映射DTO
     */
    private static AmazonFrontMappingDTO createMappingDTO(AmazonFrontMappingEnum mapping, AmazonFrontDetail detail) {
        if (StrUtil.isBlank(detail.getValue())) {
            return null;
        }

        AmazonFrontMappingDTO dto = new AmazonFrontMappingDTO();
        dto.setBackendFieldName(mapping.getBackendFieldName());
        dto.setTargetFieldName(mapping.getTargetFieldName());
        dto.setFrontValue(detail.getValue());
        dto.setDisplayLabel(mapping.getDisplayLabel());
        dto.setDisplayPosition(mapping.getDisplayPosition());
        dto.setDisplayOrder(mapping.getDisplayOrder());
        dto.setFieldType(mapping.getFieldType());
        dto.setCssClass(mapping.getCssClass());
        dto.setFrontFieldKey(detail.getKey());
        dto.setFrontFieldPath(detail.getPath());

        return dto;
    }

    /**
     * 获取所有支持的后台字段名称
     *
     * @return 后台字段名称列表
     */
    public static List<String> getAllBackendFieldNames() {
        return Arrays.stream(AmazonFrontMappingEnum.values())
                .map(AmazonFrontMappingEnum::getBackendFieldName)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 检查是否支持指定的后台字段
     *
     * @param backendFieldName 后台字段名称
     * @return 是否支持
     */
    public static boolean isSupportedBackendField(String backendFieldName) {
        return Arrays.stream(AmazonFrontMappingEnum.values())
                .anyMatch(mapping -> mapping.getBackendFieldName().equals(backendFieldName));
    }
}
