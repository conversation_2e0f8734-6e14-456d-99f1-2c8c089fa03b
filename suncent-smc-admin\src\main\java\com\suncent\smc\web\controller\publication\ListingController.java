package com.suncent.smc.web.controller.publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.networknt.schema.*;
import com.networknt.schema.resource.InputStreamSource;
import com.networknt.schema.resource.SchemaLoader;
import com.networknt.schema.resource.UriSchemaLoader;
import com.suncent.smc.common.annotation.Log;
import com.suncent.smc.common.annotation.RecordLog;
import com.suncent.smc.common.constant.Constants;
import com.suncent.smc.common.core.controller.BaseController;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysRole;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.core.page.TableDataInfo;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.dto.AmazonFrontMappingDTO;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.poi.ExcelUtil;
import com.suncent.smc.common.utils.poi.MultiSheetExcelUtil;
import com.suncent.smc.persistence.ads.domain.AdsListingLabel;
import com.suncent.smc.persistence.ads.domain.AdsRecordData;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.amazon.domain.AmazonFrontDetail;
import com.suncent.smc.persistence.amazon.domain.AmazonFrontDetailV2;
import com.suncent.smc.persistence.amazon.domain.AmazonWarehouseMapping;
import com.suncent.smc.persistence.amazon.domain.VcListingInventory;
import com.suncent.smc.persistence.amazon.service.IAmazonFrontDetailService;
import com.suncent.smc.persistence.amazon.service.IAmazonFrontDetailV2Service;
import com.suncent.smc.persistence.amazon.service.IAmazonWarehouseMappingService;
import com.suncent.smc.persistence.amazon.service.IVcListingInventoryService;
import com.suncent.smc.persistence.api.domain.AmazonVcNetPPM;
import com.suncent.smc.persistence.api.service.IApiService;
import com.suncent.smc.persistence.bi.entity.BiSalesAnalysis;
import com.suncent.smc.persistence.bi.service.IBiDataService;
import com.suncent.smc.persistence.cdp.domain.entity.Shop;
import com.suncent.smc.persistence.cdp.service.ICateProductService;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.common.AmazonFrontMappingUtil;
import com.suncent.smc.persistence.configuration.category.domain.entity.CategoryInfo;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigRequiredField;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsImage;
import com.suncent.smc.persistence.pdm.domain.entity.MappingGoods;
import com.suncent.smc.persistence.pdm.service.IGoodsImageService;
import com.suncent.smc.persistence.pdm.service.IMappingGoodsService;
import com.suncent.smc.persistence.publication.domain.dto.*;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.domain.vo.AmazonErrorFieldVO;
import com.suncent.smc.persistence.publication.domain.vo.DynamicListingDetailVO;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO;
import com.suncent.smc.persistence.publication.domain.vo.TemuGoodsHeadVO;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.temu.domain.entity.TemuGoodsHead;
import com.suncent.smc.persistence.temu.domain.entity.TemuListingSpecs;
import com.suncent.smc.persistence.temu.service.ITemuGoodsHeadService;
import com.suncent.smc.provider.base.service.ICdpBaseConfigService;
import com.suncent.smc.provider.base.service.ISmcBaseConfigService;
import com.suncent.smc.provider.biz.configuration.ViolateWordBiz;
import com.suncent.smc.provider.biz.configuration.domain.DefinitionsDTO;
import com.suncent.smc.provider.biz.consumer.AmazonListingResultHandler;
import com.suncent.smc.provider.biz.file.FileBiz;
import com.suncent.smc.provider.biz.publication.*;
import com.suncent.smc.provider.biz.publication.domain.FindKeyWordVO;
import com.suncent.smc.provider.biz.publication.domain.SyncListingVO;
import com.suncent.smc.provider.biz.publication.dto.AmazonEditPlatSkuDTO;
import com.suncent.smc.provider.biz.publication.service.IBaseListingService;
import com.suncent.smc.provider.biz.publication.service.ListingInfoService;
import com.suncent.smc.provider.biz.publication.service.impl.AmazonPlatformListingServiceImpl;
import com.suncent.smc.provider.biz.publication.vo.AmazonEditPlatSkuVO;
import com.suncent.smc.provider.biz.temu.TemuBiz;
import com.suncent.smc.system.service.ISysBaseConfigService;
import com.suncent.smc.system.service.ISysRoleService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Listing管理Controller
 *
 * <AUTHOR>
 * @since 2023-01-12 19:18:00
 */
@Controller
@RequestMapping("/publication/listing")
public class ListingController extends BaseController {
    @Autowired
    ListingInfoBiz listingInfoBiz;
    @Autowired
    IListingService listingService;
    @Autowired
    IListingLogService listingLogService;
    @Autowired
    IListingAdapterLogService listingAdapterLogService;
    @Autowired
    IGoodsHeadService goodsHeadService;
    @Autowired
    AmazonListingResultHandler amazonListingResultHandler;
    @Autowired
    IGoodsImageService goodsImageService;
    @Autowired
    private ListingInfoService listingInfoService;
    @Autowired
    private PlatformListingFactory platformListingFactory;
    @Resource
    private ViolateWordBiz violateWordBiz;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private IGoodsResourceService goodsResourceService;
    @Autowired
    private IGoodsTaskService goodsTaskService;
    @Autowired
    public IGoodsTaskInfoService goodsTaskInfoService;
    @Resource
    FileBiz fileBiz;
    @Resource
    @Lazy
    ListingUpdateBuilder listingUpdateBuilder;
    @Autowired
    AmazonPlatformListingServiceImpl amazonPlatformListingService;
    @Autowired
    IAdsService adsService;
    @Autowired
    IBiDataService biDataService;

    @Autowired
    IApiService apiService;
    @Autowired
    protected ICdpBaseConfigService cdpBaseConfigService;
    @Autowired
    private IMappingGoodsService mappingGoodsService;
    @Autowired
    IShopService shopService;
    @Autowired
    ICateProductService cateProductService;
    @Autowired
    protected IListingAmazonAttributeLineService listingAmazonAttributeLineService;
    @Autowired
    private IPlatformCategoryService platformCategoryService;
    @Autowired
    private AmazonProductBiz amazonProductBiz;
    @Autowired
    private AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;
    @Autowired
    private GoodsInfoBiz goodsInfoBiz;
    @Autowired
    ITemuGoodsHeadService temuGoodsHeadService;
    @Autowired
    private IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;

    @Autowired
    private IAmazonWarehouseMappingService amazonWarehouseMappingService;
    @Autowired
    IVcListingInventoryService vcListingInventoryService;
    @Autowired
    private IAmazonFrontDetailService amazonFrontDetailService;

    @Autowired
    private IAmazonFrontDetailV2Service amazonFrontDetailV2Service;

    @Autowired
    private ISysBaseConfigService sysBaseConfigService;

    @Autowired
    private ISmcBaseConfigService smcBaseConfigService;

    private final String prefix = "publication/listing";
    private final String temu_prefix = "temu/listing";
    static final String schemaId = "https://schemas.amazon.com/selling-partners/definitions/product-types/meta-schema/v1";

    private final String prefixCompetingProducts = "products/competitiveProducts";
    @Autowired
    private TemuBiz temuBiz;
    @Autowired
    private IAmCategoryTemplateSmcMappingService amCategoryTemplateSmcMappingService;

    @InitBinder  //类初始化是调用的方法注解
    public void initBinder(WebDataBinder binder) {
        //给这个controller配置接收list的长度100000，仅在这个controller有效
        binder.setAutoGrowCollectionLimit(100000);
        super.initBinder(binder);
    }

    @RequiresPermissions("publication:goods:listing")
    @GetMapping()
    public String list(ModelMap mmap) {
        List<SysRole> roles = sysRoleService.selectRoleListByUserId(ShiroUtils.getUserId());
        if (CollUtil.isNotEmpty(roles)) {
            //roles只包含了100EB运营则赋值给EB 只101AM运营则赋值AM
            if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "101"))) {
                mmap.put("currentUserId", ShiroUtils.getUserId());
            }
        }
        // 临时恢复同步加载，确保页面可用
        mmap.put("operationClassificationList", cateProductService.getOperationClassification());
        return prefix + "/listing";
    }

    /**
     * 异步获取店铺数据
     * 添加缓存支持，缓存30分钟
     */
    @GetMapping("/getShopData")
    @ResponseBody
    @Cacheable(value = "listingBaseData", key = "'shopData'", cacheManager = "cacheManager")
    public AjaxResult getShopData() {
        try {
            List<KeyValueEntity> shopData = cdpBaseConfigService.getShopAllKVList();
            return AjaxResult.success(shopData);
        } catch (Exception e) {
            logger.error("获取店铺数据失败", e);
            return AjaxResult.error("获取店铺数据失败");
        }
    }

    /**
     * 异步获取类目数据
     * 添加缓存支持，缓存30分钟
     */
    @GetMapping("/getCategoryData")
    @ResponseBody
    @Cacheable(value = "listingBaseData", key = "'categoryData'", cacheManager = "cacheManager")
    public AjaxResult getCategoryData() {
        try {
            List<KeyValueEntity> categoryData = smcBaseConfigService.getPlatformCategoryByPlatformCode("null");
            return AjaxResult.success(categoryData);
        } catch (Exception e) {
            logger.error("获取类目数据失败", e);
            return AjaxResult.error("获取类目数据失败");
        }
    }

    /**
     * 异步获取业务分类数据
     * 添加缓存支持，缓存30分钟
     */
    @GetMapping("/getOperationClassificationData")
    @ResponseBody
    @Cacheable(value = "listingBaseData", key = "'operationClassificationData'", cacheManager = "cacheManager")
    public AjaxResult getOperationClassificationData() {
        try {
            List<KeyValueEntity> operationClassificationData = cateProductService.getOperationClassification();
            return AjaxResult.success(operationClassificationData);
        } catch (Exception e) {
            logger.error("获取业务分类数据失败", e);
            return AjaxResult.error("获取业务分类数据失败");
        }
    }

    /**
     * 异步获取用户数据
     * 添加缓存支持，缓存10分钟（用户数据变化较频繁）
     */
    @GetMapping("/getUserData")
    @ResponseBody
    @Cacheable(value = "listingBaseData", key = "'userData'", cacheManager = "cacheManager")
    public AjaxResult getUserData() {
        try {
            List<KeyValueEntity> userData = sysBaseConfigService.getSysUserNameAllKVList();
            return AjaxResult.success(userData);
        } catch (Exception e) {
            logger.error("获取用户数据失败", e);
            return AjaxResult.error("获取用户数据失败");
        }
    }

    /**
     * 批量获取基础数据
     * 一次请求获取所有基础数据，减少请求次数
     */
    @GetMapping("/getBaseDataBatch")
    @ResponseBody
    @Cacheable(value = "listingBaseData", key = "'batchData'", cacheManager = "cacheManager")
    public AjaxResult getBaseDataBatch() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 设置超时时间为10秒
            long timeoutMillis = 10000;

            // 并行获取所有数据，添加超时控制
            CompletableFuture<List<KeyValueEntity>> shopDataFuture = CompletableFuture
                    .supplyAsync(() -> cdpBaseConfigService.getShopAllKVList())
                    .exceptionally(ex -> {
                        logger.error("获取店铺数据超时或失败");
                        return new ArrayList<>();
                    });

            CompletableFuture<List<KeyValueEntity>> categoryDataFuture = CompletableFuture
                    .supplyAsync(() -> smcBaseConfigService.getPlatformCategoryByPlatformCode("null"))
                    .exceptionally(ex -> {
                        logger.error("获取类目数据超时或失败");
                        return new ArrayList<>();
                    });

            CompletableFuture<List<KeyValueEntity>> operationClassificationDataFuture = CompletableFuture
                    .supplyAsync(() -> cateProductService.getOperationClassification())
                    .exceptionally(ex -> {
                        return new ArrayList<>();
                    });

            CompletableFuture<List<KeyValueEntity>> userDataFuture = CompletableFuture
                    .supplyAsync(() -> sysBaseConfigService.getSysUserNameAllKVList())
                    .exceptionally(ex -> {
                        return new ArrayList<>();
                    });

            // 等待所有任务完成，但不超过总超时时间
            try {
                CompletableFuture.allOf(shopDataFuture, categoryDataFuture, operationClassificationDataFuture, userDataFuture)
                        .get(timeoutMillis * 2, TimeUnit.MILLISECONDS);
            } catch (TimeoutException e) {
                logger.warn("批量获取数据部分超时，将返回已获取的数据");
            }

            // 即使部分失败，也返回已成功获取的数据
            result.put("shopData", safeGetFutureResult(shopDataFuture));
            result.put("categoryData", safeGetFutureResult(categoryDataFuture));
            result.put("operationClassificationData", safeGetFutureResult(operationClassificationDataFuture));
            result.put("userData", safeGetFutureResult(userDataFuture));

            return AjaxResult.success(result);
        } catch (ExecutionException | InterruptedException e) {
            logger.error("批量获取基础数据执行失败", e);
            return AjaxResult.error("批量获取基础数据失败");
        } catch (Exception e) {
            logger.error("批量获取基础数据失败", e);
            return AjaxResult.error("批量获取基础数据失败");
        }
    }

    /**
     * 安全获取CompletableFuture的结果
     */
    private List<KeyValueEntity> safeGetFutureResult(CompletableFuture<List<KeyValueEntity>> future) {
        try {
            if (future.isDone() && !future.isCompletedExceptionally()) {
                return future.get();
            }
        } catch (Exception e) {
            logger.warn("获取Future结果失败", e);
        }
        return new ArrayList<>();
    }

    @RequiresPermissions("publication:goods:temuListing")
    @GetMapping("/temuListing")
    public String temuListing(ModelMap mmap) {
        if (!ShiroUtils.getSysUser().isAdmin()){
            mmap.put("currentUserId", ShiroUtils.getUserId());
        }
        return prefix + "/temu_listing";
    }

    /**
     * 带任务编号
     *
     * @param taskId
     * @param mmap
     * @return
     */
    @RequiresPermissions("publication:goods:listing")
    @GetMapping("/{platform}/{type}/{taskId}")
    public String list(@PathVariable("platform") String platform,@PathVariable("type") String type,@PathVariable("taskId") String taskId, ModelMap mmap) {
        String jumpTab="";
        if (Objects.equals(platform,PlatformTypeEnum.AM.name())|| Objects.equals(platform,PlatformTypeEnum.EB.name())){
            jumpTab= prefix + "/listing";
        }
        if (Objects.equals(platform,PlatformTypeEnum.EB_V2.name())){
            jumpTab= "ebay/listing/ebay_listing";
        }
        if (Objects.equals(platform,PlatformTypeEnum.TEMU.name())){
            jumpTab= prefix + "/temu_listing";
        }
        List<SysRole> roles = sysRoleService.selectRoleListByUserId(ShiroUtils.getUserId());
        if (CollUtil.isNotEmpty(roles)) {
            //roles只包含了100EB运营则赋值给EB 只101AM运营则赋值AM
            if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "101"))) {
                mmap.put("currentUserId", ShiroUtils.getUserId());
            }
        }
        // 添加业务分类数据到页面
        mmap.put("operationClassificationList", cateProductService.getOperationClassification());

        if (StringUtils.isNotBlank(taskId)) {
            GoodsTask goodsTask = goodsTaskService.selectGoodsTaskById(taskId);
            if (ObjUtil.equals(goodsTask.getTaskType(), GoodsTaskTypeEnum.STORE_SYNC.getInfo())) {
                String shopCode = goodsTask.getTaskName().split("_")[0];
                mmap.put("jumpShopCode", shopCode);
            }

            GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
            goodsTaskInfo.setTaskId(taskId);
            goodsTaskInfo.setStatus(getTaskStatus(type));
            List<GoodsTaskInfo> infoList = goodsTaskInfoService.selectGoodsTaskInfoList(goodsTaskInfo);
            if (CollUtil.isNotEmpty(infoList)) {
                String headIds = infoList.stream().map(GoodsTaskInfo::getListingHeadId).filter(f -> StrUtil.isNotEmpty(f)).collect(Collectors.joining(" "));
                mmap.put("jumpHeadIds", headIds);
            }
        }
        return jumpTab;
    }

    private String getTaskStatus(String type) {
        if (Objects.equals(type,"sum")){
            return null;
        }
        if (Objects.equals(type,"success")){
            return "0";
        }
        if (Objects.equals(type,"fail")){
            return "1";
        }
        return null;
    }
    /**
     * 查询刊登商品数据集合 Temu 查询入口
     *
     * @param listingDTO 刊登商品数据传输实体类
     * @return 刊登商品数据
     */
    @PostMapping("/listTemu")
    @ResponseBody
    public TableDataInfo selectTemuListingVoList(ListingDTO listingDTO) {
        listingDTO.setOperator(String.valueOf(ShiroUtils.getUserId()));
        return selectTemuListingVoListV2(listingDTO);
    }

    @GetMapping("/listTemuDetail/{id}")
    @ResponseBody
    public List<TemuListingSpecs> listTemuDetail(@PathVariable("id") Long id) {
        return temuBiz.selectTemuListingSpecs(id);
    }

    /**
     * 新版接口去除先查头表id，采用sql联合查询
     * @param listingDTO
     * @return
     */
    private TableDataInfo selectTemuListingVoListV2(ListingDTO listingDTO) {
        startPage();
        if (listingDTO.getPublishStatusFlag().equals("0")) {
            listingDTO.setPublishStatus("2");
        }
        List<Integer> ids = temuBiz.selectTemuListingVoList(listingDTO, false);
        if (ids == null) {
            return getDataTable(new ArrayList<GoodsHeadVO>());
        }

        PageHelper.orderBy("id desc");
        List<TemuGoodsHeadVO> listingVoList = temuBiz.selectTemuListByIds(ids, listingDTO, false);
        PageInfo pageInfo = new PageInfo(ids);
        pageInfo.setList(listingVoList);
        return dataTable(pageInfo);
    }
    /**
     * 查询刊登商品数据集合 Amazon、Ebay 查询入口
     *
     * @param listingDTO 刊登商品数据传输实体类
     * @return 刊登商品数据
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo selectListingVoList(ListingDTO listingDTO) {
        listingDTO.setOperator(String.valueOf(ShiroUtils.getUserId()));
        listingInfoBiz.handleQueryNew(listingDTO);
        return selectListingVoListV2(listingDTO);
    }

    /**
     * 新版接口去除先查头表id，采用sql联合查询
     * @param listingDTO
     * @return
     */
    private TableDataInfo selectListingVoListV2(ListingDTO listingDTO) {
        startPage();
        if (listingDTO.getPublishStatusFlag().equals("0")) {
            listingDTO.setPublishStatus("2");
        }
        List<Integer> ids = listingInfoBiz.selectListingVoListV2(listingDTO, false);
        if (ids == null) {
            return getDataTable(new ArrayList<GoodsHeadVO>());
        }

        PageHelper.orderBy("id desc");
        List<GoodsHeadVO> listingVoList = listingInfoBiz.selectListByIds(ids, listingDTO, false);
        //处理listing销量订单量
        if (CollUtil.isNotEmpty(listingVoList)) {
            listingInfoBiz.handleListingData(listingVoList);
        }
        PageInfo pageInfo = new PageInfo(ids);
        pageInfo.setList(listingVoList);
        return dataTable(pageInfo);
    }

    protected TableDataInfo dataTable(PageInfo pageInfo)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(pageInfo.getList());
        rspData.setTotal(pageInfo.getTotal());
        return rspData;
    }

    @GetMapping("/historyList/{pdmGoodsCode}/{platform}")
    public String historyList(@PathVariable("pdmGoodsCode") String pdmGoodsCode, @PathVariable("platform") String platform, ModelMap mmap) {
        mmap.put("pdmGoodsCode", pdmGoodsCode);
        mmap.put("platform", platform);
        return prefix + "/historyList";
    }


    @PostMapping("/historyList")
    @ResponseBody
    public TableDataInfo historyList(ListingDTO listingDTO) {
        startPage();
        List<Integer> ids = listingInfoBiz.selectListingVoListV2(listingDTO, false);
        if (ObjUtil.isEmpty(ids)) {
            return getDataTable(new ArrayList<GoodsHeadVO>());
        }
        List<GoodsHeadVO> listingVoList = listingInfoBiz.selectListByIds(ids, listingDTO, false);
        return getDataTable(listingVoList);
    }


    @PostMapping("/historyList/{id}")
    @ResponseBody
    public TableDataInfo historyById(@PathVariable("id") Integer id) {
        ListingEditDTO listingEditDTO = listingInfoBiz.queryEditListing(id);
        List<ListingEditDTO> listingEditList = new ArrayList<>();
        listingEditList.add(listingEditDTO);
        return getDataTable(listingEditList);
    }

    /**
     * 批量删除Listing
     *
     * @param ids Listing ID集合
     * @return 删除结果
     */
    @Log(title = "Listing管理-批量删除", businessType = BusinessType.DELETE)
    @RequiresPermissions("publication:listing:remove")
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        try {
            Long userId = ShiroUtils.getUserId();
            return toAjax(listingInfoService.deleteListingByIds(ids, userId));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
    /**
     * 批量删除Temu Listing
     *
     * @param ids Listing ID集合
     * @return 删除结果
     */
    @Log(title = "Listing管理", businessType = BusinessType.DELETE)
    @PostMapping("/removeTemu")
    @ResponseBody
    public AjaxResult removeTemu(String ids) {
        try {
            Long userId = ShiroUtils.getUserId();
            return toAjax(listingInfoService.deleteTemuListingByIds(ids, userId));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @GetMapping("/exportColumns")
    public String exportColumns(ModelMap mmap, String platform) {
        mmap.put("platform", platform);
        mmap.put("existEB", true);
        List<SysRole> roles = sysRoleService.selectRoleListByUserId(ShiroUtils.getUserId());
        if (CollUtil.isEmpty(roles)) {
            return prefix + "/export_column";
        }
        //roles只包含了100EB运营则赋值给EB 只101AM运营则赋值AM
        if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "101"))) {
            mmap.put("existEB", false);
        }
        return prefix + "/export_column";
    }


    /**
     * 批量导出Listing
     *
     * @param listingDTO 刊登商品数据传输实体类
     * @return 导出结果
     */
    @RequiresPermissions("publication:listing:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ListingDTO listingDTO) {
        try {
            listingDTO.setOperator(String.valueOf(ShiroUtils.getUserId()));
            logger.info("开始准备进行导出,userName:{}", ShiroUtils.getSysUser().getUserName());
            //判断是选中
            String ids = listingDTO.getIds();
            if (PlatformTypeEnum.TEMU.name().equalsIgnoreCase(listingDTO.getActivePlatform())) {
                List<TemuGoodsHeadVO> listingVoList = new ArrayList<>();
                if (StringUtils.isNotBlank(ids)) {
                    List<Integer> idsList = Arrays.stream(ids.split(",")).map(Integer::valueOf).collect(Collectors.toList());
                    listingVoList = temuBiz.selectTemuListByIds(idsList, listingDTO, true);
                }else {
                    List<Integer> idsList = temuBiz.selectTemuListingVoList(listingDTO, true);
                    if (idsList != null) {
                        listingVoList = temuBiz.selectTemuListByIds(idsList, listingDTO, true);
                    }
                }
                ExcelUtil<TemuGoodsHeadVO> util = new ExcelUtil<>(TemuGoodsHeadVO.class);
                util.hideColumn(listingInfoBiz.excludeFields(listingDTO.getNoSelectColumn(), listingDTO.getActivePlatform()));
                return util.exportExcel(listingVoList, "Listing数据");
            }else {
                List<GoodsHeadVO> listingVoList = new ArrayList<>();
                if (StringUtils.isNotBlank(ids)) {
                    Integer[] idArray = Arrays.stream(ids.split(",")).map(Integer::valueOf).toArray(Integer[]::new);
                    List<GoodsHeadVO> goodsHeadList = goodsHeadService.listingGoodsHeadVOList(idArray);
                    listingVoList = listingInfoBiz.getGoodsHeadVOS(true, goodsHeadList, listingDTO,false);
                } else {
                    listingInfoBiz.handleQueryNew(listingDTO);
                    if (listingDTO.getPublishStatusFlag().equals("0")) {
                        listingDTO.setPublishStatus("2");
                    }
                    List<Integer> resultIds = listingInfoBiz.selectListingVoListV2(listingDTO, true);
                    if (resultIds != null) {
                        listingVoList = listingInfoBiz.selectListByIds(resultIds, listingDTO, true);
                        //处理listing销量订单量
                        if (CollUtil.isNotEmpty(listingVoList)) {
                            listingInfoBiz.handleListingData(listingVoList);
                        }
                    }
                }

                // 处理主要标签概览
                listingInfoBiz.processMainTags(listingVoList);

                // 转换为动态标签和表现详情数据
                List<DynamicListingDetailVO> tagDetailList = listingInfoBiz.convertToDynamicTagDetailVOList(listingVoList);
                List<DynamicListingDetailVO> performanceDetailList = listingInfoBiz.convertToDynamicPerformanceDetailVOList(listingVoList);

                // 获取动态表头
                LinkedHashMap<String, String> tagHeaders = listingInfoBiz.getDynamicTagHeaders();
                LinkedHashMap<String, String> performanceHeaders = listingInfoBiz.getDynamicPerformanceHeaders();

                // 获取需要隐藏的列
                String[] hideMainColumns = listingInfoBiz.excludeFields(listingDTO.getNoSelectColumn(), listingDTO.getActivePlatform());

                // 使用动态多Sheet导出
                return MultiSheetExcelUtil.exportDynamicMultiSheetExcel(
                        listingVoList, tagDetailList, performanceDetailList,
                        GoodsHeadVO.class,
                        "主要数据", "标签详情", "表现详情",
                        hideMainColumns, tagHeaders, performanceHeaders, "Listing数据"
                );
            }
        } catch (Exception e) {
            logger.error("导出失败", e);
            if (e instanceof BusinessException) {
                return AjaxResult.error(e.getMessage());
            }
            return AjaxResult.error("导出失败,请联系管理员.");
        }
    }


    /**
     * 下载模板
     *
     * @return
     */
    @RequiresPermissions("publication:listing:view")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        ExcelUtil<GoodsHeadVO> util = new ExcelUtil<GoodsHeadVO>(GoodsHeadVO.class);
        return util.importTemplateExcel("Listing数据");
    }

    /**
     * 导入数据修改
     */
    @PostMapping("/importUpdateListing")
    @RequiresPermissions("publication:listing:importUpdateListing")
    @ResponseBody
    public AjaxResult importUpdateListing(MultipartFile importFile, boolean updateSupport) throws Exception {
        if (ObjUtil.isEmpty(importFile)) {
            throw new BusinessException("文件不能为空.");
        }
        //MultipartFile 转 InputStream
        String md5Hex = DigestUtils.md5Hex(importFile.getInputStream());
        if (fileBiz.checkMd5(md5Hex)) {
            throw new BusinessException("文件重复导入,请确认数据是否有修改!");
        }
        String ossFileUrl = fileBiz.uploadToOSS(importFile);

        ExcelUtil<GoodsHeadVO> util = new ExcelUtil<GoodsHeadVO>(GoodsHeadVO.class);
        List<GoodsHeadVO> listingVoList = util.importExcel(importFile.getInputStream());
        Long userId = ShiroUtils.getUserId();
        String message = fileBiz.importListting(listingVoList, userId,ossFileUrl);
        return AjaxResult.success(message);
    }

    /**
     * 批量下架Listing
     *
     * @param ids Listing ID集合
     * @return 删除结果
     */
    @PostMapping("/stopPublish")
    @ResponseBody
    @RequiresPermissions("publication:listing:stopPublish")
    @Log(title = "Listing管理-批量下架", businessType = BusinessType.UPDATE)
    public AjaxResult stopPublish(String ids) {
        String[] idsArr = Convert.toStrArray(ids);
        String[] platformArr = {"AM"};
        for (String id : idsArr) {
            ListingLog listingLog = new ListingLog();
            listingLog.setStatus(0);
            listingLog.setListingId(Integer.valueOf(id));
            listingLog.setOperName(String.valueOf(ShiroUtils.getUserId()));
            listingLog.setOperTime(new Date());

            String publishStatusName = listingService.stopPublishListingByIds(id, platformArr);

            listingLog.setDetails("批量下架Listing,数据由["+publishStatusName+"]移至[下架中]");
            listingLogService.insertListingLog(listingLog);
        }
        List<Integer> list = Arrays.stream(idsArr).map(f -> Integer.valueOf(f)).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(platformArr[0], GoodsTaskTypeEnum.BATCH_DELISTING, list, String.valueOf(ShiroUtils.getUserId()));
        return toAjax(Convert.toStrArray(ids).length);
    }

    /**
     * 批量刊登Listing
     *
     * @param ids Listing ID集合
     * @return 删除结果
     */
    @Log(title = "Listing管理-批量刊登", businessType = BusinessType.UPDATE)
    @PostMapping("/publish")
    @ResponseBody
    @RequiresPermissions("publication:listing:publish")
    public AjaxResult publish(String ids) {
        Long userId = ShiroUtils.getSysUser().getUserId();
        return listingInfoBiz.publishListingByIds(ids, userId);
    }

    /**
     * 批量刊登Listing temu
     *
     * @param ids Listing ID集合
     * @return 删除结果
     */
    @Log(title = "Listing管理-批量刊登", businessType = BusinessType.UPDATE)
    @PostMapping("/publishTemu")
    @ResponseBody
    public AjaxResult publishTemu(String ids) {
        Long userId = ShiroUtils.getSysUser().getUserId();
        return listingInfoBiz.publishTemuListingByIds(ids, userId);
    }

    /**
     * listing复制到店铺
     *
     * @param listingCopyDTO
     * @return
     */
    @Log(title = "Listing管理-listing复制", businessType = BusinessType.INSERT)
    @PostMapping("/copyToShop")
    @ResponseBody
    public AjaxResult listingCopyToShop(ListingCopyDTO listingCopyDTO) {
        listingInfoBiz.listingCopyToShop(listingCopyDTO);
        return AjaxResult.success();
    }

    @Log(title = "Listing管理-listing复制", businessType = BusinessType.INSERT)
    @PostMapping("/copyTemuShop")
    @ResponseBody
    public AjaxResult copyTemuShop(ListingCopyDTO listingCopyDTO) {
        try {
            listingCopyDTO.setCreateBy(String.valueOf(ShiroUtils.getUserId()));

            String platform = listingCopyDTO.getPlatform();
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            listingServiceByPlatformType.listingCopy(listingCopyDTO);
        } catch (Exception e) {
            logger.error("复制到店铺失败", e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }
    /**
     * 修改Listing
     *
     * @param id   ListingID
     * @param mmap ModelMap
     * @return Listing数据
     */
    @GetMapping("/edit/{id}")
    @RequiresPermissions("publication:listing:edit")
    public String edit(@PathVariable("id") Integer id, ModelMap mmap) {
        if (id == null) {
            return "error/404";
        }
        try {
            ListingEditDTO listingEditDTO = listingInfoBiz.queryEditListing(id);
            if (listingEditDTO.getListingEbayShippingHead() == null) {
                ListingEbayShippingHead listingEbayShippingHead = new ListingEbayShippingHead();
                listingEbayShippingHead.setShippingTypeLineList(new ArrayList<>());
                listingEditDTO.setListingEbayShippingHead(listingEbayShippingHead);
            }
            List<GoodsImage> goodsImages = goodsImageService.selectGoodsImageByGoodsCode(listingEditDTO.getGoodsCode(), null, listingEditDTO.getBrandCode());
            mmap.put("goodsImages", goodsImages);
            mmap.put("listingEditDTO", listingEditDTO);
            if (PlatformTypeEnum.EB.name().equals(listingEditDTO.getPlatform())) {
                return prefix + "/ebay_listing_edit";
            } else {
//            重定向到新的编辑页面
                return redirect("/publication/listing/v2/edit/" + id);
            }
        }catch (BusinessException e){
            mmap.put("errorMessage", e.getMessage());
            return "error/service";
        }catch (Exception e){
            logger.error("编辑失败", e);
            mmap.put("errorMessage", "编辑失败");
            return "error/service";
        }
    }

    /**
     * 修改Listing
     *
     * @param id   ListingID
     * @param mmap ModelMap
     * @return Listing数据
     */
    @GetMapping("/v2/edit/{id}")
    @RequiresPermissions("publication:listing:edit")
    public String editV2(@PathVariable("id") Integer id, ModelMap mmap) {
        if (id == null) {
            return "error/404";
        }
        try {
            ListingEditDTO listingEditDTO = listingInfoBiz.queryEditListing(id);
            if (listingEditDTO.getListingEbayShippingHead() == null) {
                ListingEbayShippingHead listingEbayShippingHead = new ListingEbayShippingHead();
                listingEbayShippingHead.setShippingTypeLineList(new ArrayList<>());
                listingEditDTO.setListingEbayShippingHead(listingEbayShippingHead);
            }
            List<GoodsImage> goodsImages = goodsImageService.selectGoodsImageByGoodsCode(listingEditDTO.getGoodsCode(), null, listingEditDTO.getBrandCode());
            mmap.put("goodsImages", goodsImages);
            mmap.put("listingEditDTO", listingEditDTO);
            mmap.put("noEditFlag", !StrUtil.equals(listingEditDTO.getPublishStatus(), "0") && !StrUtil.equals(listingEditDTO.getPublishStatus(), "8"));

            // 获取Amazon前台数据
            if (PlatformTypeEnum.AM.name().equals(listingEditDTO.getPlatform())) {
                amFrontDetails(mmap, listingEditDTO);
            }

            if (PlatformTypeEnum.EB.name().equals(listingEditDTO.getPlatform())) {
                return prefix + "/ebay_listing_edit";
            } else {
                return prefix + "/v2/am_listing_edit";
            }
        } catch (BusinessException e) {
            mmap.put("errorMessage", e.getMessage());
            return "error/service";
        } catch (Exception e) {
            logger.error("编辑失败", e);
            mmap.put("errorMessage", "编辑失败");
            return "error/service";
        }
    }

    /**
     * 处理AM前台数据
     *
     * @param mmap
     * @param listingEditDTO
     */
    private void amFrontDetails(ModelMap mmap, ListingEditDTO listingEditDTO) {
        try {
            // ASIN通常存储在platformGoodsId字段
            String asin = listingEditDTO.getPlatformGoodsId();
            String shopCode = listingEditDTO.getShopCode();
            if (StrUtil.isNotBlank(asin) && StrUtil.isNotBlank(shopCode)) {
                // 使用V2表查询前台数据（新的path处理逻辑）
                List<AmazonFrontDetailV2> frontDetailsV2 = amazonFrontDetailV2Service.selectAmazonFrontDetailV2ByAsinAndShopCode(asin, shopCode);
                if (CollUtil.isNotEmpty(frontDetailsV2)) {
                    // 转换为老版本格式以兼容现有的映射逻辑
                    List<AmazonFrontDetail> frontDetails = convertV2ToV1(frontDetailsV2);

                    // 使用枚举映射处理前台数据
                    Map<String, List<AmazonFrontMappingDTO>> frontMappingData = AmazonFrontMappingUtil.processFrontDataMapping(frontDetails);

                    mmap.put("frontMappingData", frontMappingData);
                    mmap.put("frontDetails", frontDetails);

                    logger.debug("Amazon前台数据映射完成，共映射{}个字段", frontMappingData.size());
                }
            }
        } catch (Exception e) {
            logger.error("获取Amazon前台数据失败: {}", e.getMessage());
        }
    }

    /**
     * 将V2版本的前台数据转换为V1版本格式
     *
     * @param frontDetailsV2 V2版本前台数据
     * @return V1版本前台数据
     */
    private List<AmazonFrontDetail> convertV2ToV1(List<AmazonFrontDetailV2> frontDetailsV2) {
        if (CollUtil.isEmpty(frontDetailsV2)) {
            return new ArrayList<>();
        }

        List<AmazonFrontDetail> result = new ArrayList<>(frontDetailsV2.size());
        for (AmazonFrontDetailV2 v2 : frontDetailsV2) {
            AmazonFrontDetail v1 = new AmazonFrontDetail();
            v1.setId(v2.getId());
            v1.setHeadId(v2.getHeadId());
            v1.setShopCode(v2.getShopCode());
            v1.setAsin(v2.getAsin());
            v1.setPath(v2.getPath());
            v1.setKey(v2.getKey());
            v1.setValue(v2.getValue());
            v1.setValueType(v2.getValueType());
            v1.setCreateTime(v2.getCreateTime());
            v1.setUpdateTime(v2.getUpdateTime());
            v1.setDelFlag(v2.getDelFlag());
            result.add(v1);
        }
        return result;
    }

    /**
     * 保存修改的Listing
     *
     * @param listingEditDTO 商品数据
     * @return 保存结果
     */
    @PostMapping("/edit")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#listingEditDTO.goodsHeadId", operType = OperTypeEnum.SINGLE_EDIT, operDesc = "单个修改Listing信息:", ebayV2 = "#listingEditDTO.ebayV2")
    public AjaxResult listingSave(ListingEditDTO listingEditDTO) {
        try {
            listingEditDTO.setUpdateBy(String.valueOf(ShiroUtils.getUserId()));
            String platform = listingEditDTO.getPlatform();
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform,listingEditDTO.isEbayV2());
            listingServiceByPlatformType.listingEdit(listingEditDTO);

        } catch (Exception e) {
            logger.error("修改失败", e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }
    /**
     * 保存修改的Listing
     *
     * @param listingEditDTO 商品数据
     * @return 保存结果
     */
    @PostMapping("/v2/edit")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#listingEditDTO.goodsHeadId", operType = OperTypeEnum.SINGLE_EDIT, operDesc = "单个修改Listing信息:")
    public AjaxResult listingEditV2(ListingEditDTO listingEditDTO) {
        try {
            listingEditDTO.setUpdateBy(String.valueOf(ShiroUtils.getUserId()));
            String platform = listingEditDTO.getPlatform();
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            listingServiceByPlatformType.listingEditV2(listingEditDTO);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 保存修改的Listing属性
     *
     * @param listingEditDTO 商品数据
     * @return 保存结果
     */
    @PostMapping("/editAttribute")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#listingEditDTO.goodsHeadId", operType = OperTypeEnum.EDIT_ATTRIBUTE, operDesc = "同步更新属性信息:")
    public AjaxResult editAttribute(ListingEditDTO listingEditDTO) {
        try {
            listingEditDTO.setUpdateBy(String.valueOf(ShiroUtils.getUserId()));
            String platform = listingEditDTO.getPlatform();
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            listingServiceByPlatformType.editAttribute(listingEditDTO);

        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    @GetMapping("/batchEdit")
    @RequiresPermissions("publication:listing:edit")
    public String batchEdit(String ids, ModelMap mmap) {
        Integer[] idArray = Arrays.stream(ids.split(",")).map(Integer::valueOf).toArray(Integer[]::new);
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(idArray);
        boolean existEB = goodsHeads.stream().anyMatch(o -> PlatformTypeEnum.EB.name().equals(o.getPlatform()));
        List<String> shopList = goodsHeads.stream().map(GoodsHead::getShopCode).distinct().collect(Collectors.toList());
        mmap.put("ids", ids);
        mmap.put("existEB", existEB);
        mmap.put("shopList", shopList);
        return prefix + "/batch_edit";
    }

    @GetMapping("/batchEditByExcel")
    @RequiresPermissions("publication:listing:edit")
    public String batchEditByExcel(String ids, ModelMap mmap) {
        mmap.put("goodsIds", ids);
        return prefix + "/batch_edit_excel";
    }

    @PostMapping("/batchEditAttrByExcel")
    @ResponseBody
    @RequiresPermissions("publication:listing:edit")
    @RecordLog(isBackup = true, businessId = "#dto.listingAttributeLine", operType = OperTypeEnum.BATCH_UPDATE_ATTRIBUTE, operDesc = "批量更新Lisitng属性信息", taskRecord = true)
    public AjaxResult batchEditByExcelSave(BatchListingDTO dto) {
        List<ItemDTO> itemDTOList = listingInfoBiz.batchEditAttrByExcel(dto, ShiroUtils.getUserId());
        if (CollUtil.isNotEmpty(itemDTOList)) {
            listingUpdateBuilder.updateApi(itemDTOList);
        }
        return AjaxResult.success();
    }

    @PostMapping("/batchEditByExcel/listBatchEditData")
    @ResponseBody
    public AjaxResult listBatchEditData(String goodsIds, String source) {
        if (StringUtils.isBlank(goodsIds)) {
            return AjaxResult.error("请选择需要批量编辑的商品");
        }

        return AjaxResult.success(listingInfoBiz.listBatchEditData(Convert.toIntArray(goodsIds), source));
    }

    /**
     * 批量修改 Listing
     * @return 保存结果
     */
    @PostMapping("/batchEdit")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#batchEditDTO.ids", operType = OperTypeEnum.BATCH_QUICK_EDIT, operDesc = "批量自定义修改Lisitng信息:", taskRecord = true)
    public AjaxResult batchEdit(@RequestBody ListingBatchEditDTO batchEditDTO) {
        if (StringUtils.isEmpty(batchEditDTO.getIds()) || StringUtils.isNull(batchEditDTO.getIds())) {
            return AjaxResult.error("Listing编号不能为空！");
        }
        boolean isPriceRequired = !ListingBatchOptions.COVER_RED_LINE_PRICE.name().equals(batchEditDTO.getPriceType()) && null == batchEditDTO.getPrice();
        if (batchEditDTO.getItemList().isEmpty()
                && null == batchEditDTO.getStockOnSalesQty()
                && isPriceRequired
                && StringUtils.isBlank(batchEditDTO.getTitle())
                && StringUtils.isBlank(batchEditDTO.getLocation())
                && StringUtils.isBlank(batchEditDTO.getCategory())
                && StringUtils.isBlank(batchEditDTO.getDescTemplate())
                && StringUtils.isBlank(batchEditDTO.getPlatformGoodsCode())
                && StringUtils.isBlank(batchEditDTO.getShopCategory())) {
            return AjaxResult.error("请至少选中一个字段进行修改！");
        }

        if ( StringUtils.isNotBlank(batchEditDTO.getShopCategory()) && batchEditDTO.getShopList().split(",").length > 1) {
            return AjaxResult.error("店铺分类只能选择一个店铺进行修改！");
        }

        List<ItemDTO> itemDTOList =  listingInfoBiz.batchListing(batchEditDTO);
        listingUpdateBuilder.updateApi(itemDTOList);
        return AjaxResult.success();
    }


    @PostMapping("/v2/batchEdit")
    @ResponseBody
    @RequiresPermissions("publication:listing:edit")
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_EDIT, operDesc = "批量listing编辑", taskRecord = true)
    public AjaxResult batchEditV2(BatchListingDTO dto) {
        try {
            convertV2ToListing(dto);

            String platform = PlatformTypeEnum.AM.name();
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            listingServiceByPlatformType.batchEditV2(dto);
        return AjaxResult.success();
        } catch (Exception e) {
            logger.error("批量编辑失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }


    private void convertV2ToListing(BatchListingDTO dto) {
        // 将V2结构传递的参数转换到listings中
        List<String> titleList = dto.getTitleList();
        List<String> longDescList = dto.getLongDescList();
        List<String> fiveDescList = dto.getFiveDescList();
        List<String> stockPriceList = dto.getStockPriceList();
        List<String> logisticsList = dto.getLogisticsList();

        Map<Integer, JSONObject> titleMap = convertToMap(titleList);
        Map<Integer, JSONObject> longDescMap = convertToMap(longDescList);
        Map<Integer, JSONObject> fiveDescMap = convertToMap(fiveDescList);
        Map<Integer, JSONObject> stockPriceMap = convertToMap(stockPriceList);
        Map<Integer, JSONObject> logisticsMap = convertToMap(logisticsList);



        dto.getListings().forEach(listing -> {
            Integer headId = listing.getId();
            JSONObject title = titleMap.get(headId);
            JSONObject longDesc = longDescMap.get(headId);
            JSONObject fiveDesc = fiveDescMap.get(headId);
            JSONObject stockPrice = stockPriceMap.get(headId);
            JSONObject logistics = logisticsMap.get(headId);

            if (ObjUtil.isNotEmpty(title)) {
                listing.setTitle(title.getString("title"));
            }
            if (ObjUtil.isNotEmpty(longDesc)) {
                listing.setDetailDescription(longDesc.getString("detailDescription"));
            }
            if (ObjUtil.isNotEmpty(fiveDesc)) {
                listing.setItemDescription1(fiveDesc.getString("itemDescription1"));
                listing.setItemDescription2(fiveDesc.getString("itemDescription2"));
                listing.setItemDescription3(fiveDesc.getString("itemDescription3"));
                listing.setItemDescription4(fiveDesc.getString("itemDescription4"));
                listing.setItemDescription5(fiveDesc.getString("itemDescription5"));
            }
            if (ObjUtil.isNotEmpty(stockPrice)) {
                listing.setStandardPrice(stockPrice.getString("standardPrice"));
                if (stockPrice.containsKey("stockOnSalesQty")) {
                    listing.setStockOnSalesQty(stockPrice.getBigDecimal("stockOnSalesQty"));
                }
                if (stockPrice.containsKey("listPrice")) {
                    listing.setListPrice(stockPrice.getString("listPrice"));
                }
            }
            if (ObjUtil.isNotEmpty(logistics)) {
                if (logistics.containsKey("itemLength")) {
                    listing.setItemLength(logistics.getBigDecimal("itemLength"));
                }
                if (logistics.containsKey("itemWidth")) {
                    listing.setItemWidth(logistics.getBigDecimal("itemWidth"));
                }
                if (logistics.containsKey("itemHeight")) {
                    listing.setItemHeight(logistics.getBigDecimal("itemHeight"));
                }
                if (logistics.containsKey("itemLengthUnit")) {
                    String itemLengthUnit = logistics.getString("itemLengthUnit");
                    if (StrUtil.isNotBlank(itemLengthUnit)) {
                        if (!StringUtils.isUnit(itemLengthUnit)) {
                            throw new BusinessException("商品长度单位不支持");
                        }
                        listing.setItemLengthUnit(itemLengthUnit);
                    }
                }

                if (logistics.containsKey("packageWeight")) {
                    listing.setPackageWeight(logistics.getBigDecimal("packageWeight"));
                }
                if (logistics.containsKey("packageWeightUnit")) {
                    String packageWeightUnit = logistics.getString("packageWeightUnit");
                    if (StrUtil.isNotBlank(packageWeightUnit)) {
                        if (!StringUtils.isUnit(packageWeightUnit)) {
                            throw new BusinessException("商品重量单位不支持");
                        }
                        listing.setPackageWeightUnit(packageWeightUnit);
                    }
                }

                if (logistics.containsKey("packageLength")) {
                    listing.setPackageLength(logistics.getBigDecimal("packageLength"));
                }
                if (logistics.containsKey("packageWidth")) {
                    listing.setPackageWidth(logistics.getBigDecimal("packageWidth"));
                }
                if (logistics.containsKey("packageHeight")) {
                    listing.setPackageHeight(logistics.getBigDecimal("packageHeight"));
                }
                if (logistics.containsKey("packageLengthUnit")) {
                    String packageLengthUnit = logistics.getString("packageLengthUnit");
                    if (StrUtil.isNotBlank(packageLengthUnit)) {
                        if (!StringUtils.isUnit(packageLengthUnit)) {
                            throw new BusinessException("包裹长度单位不支持");
                        }
                        listing.setPackageLengthUnit(packageLengthUnit);
                    }
                }

            }
        });
    }

    private Map<Integer, JSONObject> convertToMap(List<String> list) {
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, JSONObject> map = new HashMap<>();
        list.forEach(item -> {
            JSONObject jsonObject = JSON.parseObject(item);
            map.put(jsonObject.getInteger("headId"), jsonObject);
        });
        return map;
    }
    /**
     * 批量更新标题
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdateTitle")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_UPDATE_TITLE, operDesc = "批量更新Lisitng标题信息:", taskRecord = true)
    public AjaxResult batchUpdateTitle(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdateTitle(dto);
        } catch (Exception e) {
            logger.error("批量更新刊登商品信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }
    /**
     * 批量更新价格库存
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdatePriceAndStock")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_UPDATE_PRICE_STOCK, operDesc = "批量更新Lisitng价格库存信息:", taskRecord = true)
    public AjaxResult batchUpdatePriceAndStock(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdatePriceAndStock(dto);
        } catch (Exception e) {
            logger.error("批量更新刊登商品信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量更新价格库存
     *
     * @return 保存结果
     */
    @PostMapping("/v2/batchUpdatePriceAndStock")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_UPDATE_PRICE_STOCK, operDesc = "批量更新Lisitng价格库存信息:", taskRecord = true)
    public AjaxResult batchUpdatePriceAndStockV2(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            convertV2ToListing(dto);

            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdatePriceAndStockV2(dto);
        } catch (Exception e) {
            logger.error("批量更新刊登商品信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }
    /**
     * 批量更新价格
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdatePrice")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_UPDATE_PRICE, operDesc = "批量编辑Listing价格:", taskRecord = true)
    public AjaxResult batchUpdatePrice(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdatePrice(dto);
        } catch (Exception e) {
            logger.error("批量更新刊登商品信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }



    /**
     * 批量更新适配信息
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdateEbayAdaptive")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.ids", operType = OperTypeEnum.BATCH_EDIT_ADAPTIVE, operDesc = "批量更新Listing适配信息:", taskRecord = true)
    public AjaxResult batchUpdateEbayAdaptive(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        String ids = dto.getIds();
        if (StringUtils.isBlank(ids)) {
            return AjaxResult.error("请选择Listing后进行保存");
        }
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdateAdaptive(dto);
        } catch (Exception e) {
            logger.error("批量更新Listing适配信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }


    /**
     * 批量监控适配信息
     *
     * @return 保存结果
     */
    @PostMapping("/batchMonitorAmazonAdaptive")
    @ResponseBody
    public AjaxResult batchMonitorAmazonAdaptive(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        String ids = dto.getIds();
        if (StringUtils.isBlank(ids)) {
            return AjaxResult.error("请选择Listing后进行保存");
        }
        if (!ObjUtil.equals(platformType, PlatformTypeEnum.AM.name())) {
            return AjaxResult.error("暂时只能手动监控amazon链接的适配");
        }
        try {
            dto.setMonitor(true);
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdateAdaptive(dto);
        } catch (Exception e) {
            logger.error("批量更新Listing适配信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }


    /**
     * @description: TODO 竞品采集页面
     * @param: [id, mmap]
     * @return: java.lang.String
     * <AUTHOR>
     * @date: 2023/1/29 10:44
     */
    @GetMapping("/getCompetingProducts/{platform}/{goodsCode}")
    public String getCompetingProducts(@PathVariable("platform") String platform, @PathVariable("goodsCode") String goodsCode, ModelMap mmap) {
        mmap.put("platform", platform);
        mmap.put("goodsCode", goodsCode);
        if ("amazon".equals(platform)) {
            return prefixCompetingProducts + "/getAmazonCompetingProductsListing";
        }
        return prefixCompetingProducts + "/getCompetingProductsListing";
    }

    @GetMapping("/getCompetingProducts/{platform}")
    public String getCompetingProducts(@PathVariable("platform") String platform, ModelMap mmap) {
        mmap.put("platform", platform);
        if ("amazon".equals(platform)) {
            return prefixCompetingProducts + "/getAmazonCompetingProductsListing";
        }
        return prefixCompetingProducts + "/getCompetingProductsListing";
    }


    /**
     * 采集页面
     *
     * @param platform
     * @param goodsCode
     * @param mmap
     * @return
     */
    @GetMapping("/getCompetingProductsV2/{platform}/{goodsCode}")
    public String getCompetingProductsV2(@PathVariable("platform") String platform, @PathVariable("goodsCode") String goodsCode, ModelMap mmap) {
        mmap.put("platform", platform);
        mmap.put("goodsCode", goodsCode);
        if ("amazon".equals(platform)) {
            return prefixCompetingProducts + "/getAmazonCompetingProductsListing";
        }
        return prefixCompetingProducts + "/getCompetingProductsListingV2";
    }

    @GetMapping("/getCompetingProductsV2/{platform}")
    public String getCompetingProductsV2(@PathVariable("platform") String platform, ModelMap mmap) {
        mmap.put("platform", platform);
        if ("amazon".equals(platform)) {
            return prefixCompetingProducts + "/getAmazonCompetingProductsListing";
        }
        return prefixCompetingProducts + "/getCompetingProductsListingV2";
    }
    /**
     * @description: TODO 查询日志信息展示跳转页面
     * @param: [id, mmap]
     * @return: java.lang.String
     * <AUTHOR>
     * @date: 2023/1/29 10:44
     */
    @GetMapping("/detail/{id}")
    public String detail(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("listingId", id);
        return prefix + "/logInfo";
    }

    /**
     * 查询listing操作日志列表
     */
    @PostMapping("/log/list")
    @ResponseBody
    public TableDataInfo list(ListingLog listingLog) {
        startPage();
        listingLog.setExcludeDetails("ListingId:"+listingLog.getListingId()+" 在库存更新黑名单，不做更新库存处理");
        List<ListingLog> list = listingLogService.selectListingLogList(listingLog);
        return getDataTable(list);
    }

    /**
     * 查询listing适配日志列表
     */
    @GetMapping("/adapterDetail/{id}")
    public String adapterDetail(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("listingId", id);
        return prefix + "/logInfoAdapter";
    }


    /**
     * 查询listing适配日志列表
     */
    @PostMapping("/adapterLog/list")
    @ResponseBody
    public TableDataInfo adapterLogList(ListingAdapterLog listingLog) {
        startPage();
        List<ListingAdapterLog> list = listingAdapterLogService.selectListingAdapterLogList(listingLog);
        return getDataTable(list);
    }

    /**
     * 查询listing表现
     * @param listingId
     * @param mmap
     * @return
     */
    @GetMapping("/getListingPerformanceDetails/{listingId}")
    public String getListingPerformanceDetails(@PathVariable("listingId") Integer listingId, ModelMap mmap) {
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(listingId);
        if (ObjUtil.isEmpty(goodsHead)){
            throw new BusinessException("未查询到listing信息");
        }
        //获取ads信息详情
        AdsListingLabel  adsListingLabel = adsService.getListingLabelByAsin(goodsHead.getShopCode(),goodsHead.getPlatformGoodsId());
        BiSalesAnalysis  analysis= biDataService.getTodaySalesAnalysisByAsin(goodsHead.getShopCode(),goodsHead.getPlatformGoodsId());
        List<AmazonVcNetPPM> listingNetPPMLabelByAsin = apiService.getListingNetPPMLabelByAsin(goodsHead.getShopCode(), goodsHead.getPlatformGoodsId());

        List<AdsRecordData> todayLPErrorList = adsService.selectTodayCartList(DateUtils.getDate(), goodsHead.getShopCode(), Lists.newArrayList(goodsHead.getPlatformGoodsId()));
        List<AdsRecordData> yesterdayLPErrorList = adsService.selectTodayCartList(DateUtils.getYesterday(), goodsHead.getShopCode(), Lists.newArrayList(goodsHead.getPlatformGoodsId()));
        List<AdsRecordData> lPErrorList = new ArrayList<>();
        lPErrorList.addAll(todayLPErrorList);
        lPErrorList.addAll(yesterdayLPErrorList);

        mmap.put("lPError", lPErrorList);
        mmap.put("netPPM", listingNetPPMLabelByAsin);
        mmap.put("listingId", listingId);
        mmap.put("adsListingLabel", adsListingLabel);
        mmap.put("goodsHead", goodsHead);
        mmap.put("analysis", analysis);
        return prefix + "/listingPerformanceDetails";
    }


    /**
     * 跳转批量定时刊登Listing
     *
     * @param ids Listing ID集合
     * @return 删除结果
     */
    @GetMapping("/scheduledPublish")
    public String scheduledPublish(String ids, ModelMap mmap) {
        int num = Convert.toIntArray(ids).length;
        mmap.put("listingNum", num);
        mmap.put("ids", ids);
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(Convert.toIntArray(ids));
        //如果goodsHeadList包含有amazon平台则跳转到amazon定时刊登页面
        boolean existAmazon = goodsHeadList.stream().anyMatch(o -> PlatformTypeEnum.AM.name().equals(o.getPlatform()));
        if (existAmazon) {
            return prefix + "/publish";
        }
        mmap.put("platform", PlatformTypeEnum.EB.name());
        return prefix + "/publish";
    }


    /**
     * 获取刊登各节点状态数量 适用于temu
     *
     * @return
     */
    @PostMapping("/selectTemuPublishStatusCount")
    @ResponseBody
    public AjaxResult selectTemuPublishStatusCount(ListingDTO listingDTO) {
        listingDTO.setPublishStatus("");
        listingDTO.setPublishStatusFlag("");
        listingDTO.setOperator(String.valueOf(ShiroUtils.getUserId()));
        TemuGoodsHead temuGoodsHeadParam = temuGoodsHeadService.getTemuGoodsHeadParam(listingDTO);
        if (!temuBiz.getTemuGoodHeadParamByActulStockScoop(listingDTO, temuGoodsHeadParam)) return AjaxResult.success();

        List<TemuGoodsHeadVO> goodsHeadVOList = temuGoodsHeadService.countTemuGoodsHeadVOList(temuGoodsHeadParam);
        ListingPublishStatusCountDTO listingPublishStatusCount = temuGoodsHeadService.selectTemuPublishStatusCount(goodsHeadVOList);
        return AjaxResult.success(listingPublishStatusCount);
    }

    /**
     * 获取刊登各节点状态数量 适用于amazon ebay
     *
     * @return
     */
    @PostMapping("/selectPublishStatusCount")
    @ResponseBody
    public AjaxResult selectPublishStatusCount(ListingDTO listingDTO) {
        listingDTO.setPublishStatus("");
        listingDTO.setPublishStatusFlag("");
        listingDTO.setOperator(String.valueOf(ShiroUtils.getUserId()));
        listingInfoBiz.handleQueryNew(listingDTO);
        GoodsHead goodsHeadParam = goodsHeadService.getGoodsHeadParam(listingDTO);
        if (!listingInfoBiz.getGoodHeadParamByActulStockScoop(listingDTO, goodsHeadParam)) return AjaxResult.success();

        return selectPublishStatusCountV2(goodsHeadParam);
    }

    private AjaxResult selectPublishStatusCountV2(GoodsHead goodsHeadParam) {
        List<GoodsHeadVO> goodsHeadVOList = goodsHeadService.countGoodsHeadVOList(goodsHeadParam);
        ListingPublishStatusCountDTO listingPublishStatusCount = listingInfoBiz.selectPublishStatusCount(goodsHeadVOList);
        return AjaxResult.success(listingPublishStatusCount);
    }



    @GetMapping("/copyListing/{goodsIds}")
    @RequiresPermissions("publication:listing:copy")
    public String copyListing(@PathVariable("goodsIds") String goodsIds, String platform, ModelMap mmap) {
        mmap.put("goodsIds", goodsIds);
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(Convert.toIntArray(goodsIds));
        ListingEditDTO editDTO = new ListingEditDTO();
        listingInfoBiz.handlePublicationDay(editDTO);

        if(PlatformTypeEnum.TEMU.name().equalsIgnoreCase(platform)) {
            mmap.put("platform", PlatformTypeEnum.TEMU.name());
            return temu_prefix + "/copyListing";
        } else {
            //如果goodsHeadList包含有amazon平台则跳转到复制页面
            boolean existAmazon = goodsHeadList.stream().anyMatch(o -> PlatformTypeEnum.AM.name().equals(o.getPlatform()));
            if (existAmazon) {
                mmap.put("platform", PlatformTypeEnum.AM.name());
                mmap.put("listingEditDTO", editDTO);
                return prefix + "/copyListing";
            }
            mmap.put("platform", PlatformTypeEnum.EB.name());
            int num = Convert.toIntArray(goodsIds).length;
            mmap.put("num", num);
            if (num == 1) {
                //处理刊登类型对应数据回显
                listingInfoBiz.handlePublishType(editDTO, goodsHeadList.get(0));
            }
            mmap.put("listingEditDTO", editDTO);
            return prefix + "/copyListing";
            }
    }


    @GetMapping("/todo/{goodsIds}")
    @RequiresPermissions("publication:listing:todo")
    public String todo(@PathVariable("goodsIds") String goodsIds, ModelMap mmap) {
        mmap.put("goodsIds", goodsIds);
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(Convert.toIntArray(goodsIds));
        mmap.put("num", goodsHeadList.size());
        return prefix + "/todo";
    }


    /**
     * Asin直接回写到pdm和适配数据, 暴露一个接口
     *
     * @param platformGoodsIds
     * @return
     */
    @PostMapping("/writeback/asin")
    @ResponseBody
    public AjaxResult writebackAsin(@RequestBody List<String> platformGoodsIds) {
        for (String platformGoodsId : platformGoodsIds) {
            GoodsHead head = new GoodsHead();
            head.setPublishingHandler("已处理");
            head.setPlatformGoodsId(platformGoodsId);
            head.setPlatform(PlatformTypeEnum.AM.name());
            List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadList(head);
            if (CollectionUtils.isEmpty(goodsHeadList)) {
                continue;
            }
            amazonListingResultHandler.listingReportConsumerHandle(JSON.toJSONString(goodsHeadList),null);
        }
        return AjaxResult.success();
    }

    /**
     * 定时刊登
     * @param dto
     * @return
     */
    @Log(title = "Listing管理-定时刊登", businessType = BusinessType.UPDATE)
    @PostMapping("/scheduledPublishToShop")
    @ResponseBody
    public AjaxResult scheduledPublishToShop(ScheduledPublishDTO dto) {
        //根据当前人判断是什么平台
        List<SysRole> roles = sysRoleService.selectRoleListByUserId(ShiroUtils.getUserId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(roles)) {
            //roles只包含了100EB运营则赋值给EB 只101AM运营则赋值AM
            if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "100"))) {
                dto.setPlatform(PlatformTypeEnum.EB.name());
            } else if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "101"))) {
                dto.setPlatform(PlatformTypeEnum.AM.name());
            }
        }
        String error = goodsHeadService.scheduledPublishToShop(dto);
        if (StringUtils.isNotEmpty(error)) {
            return AjaxResult.error(error);
        }
        return AjaxResult.success();
    }


    /**
     * 批量/取消定时刊登
     *
     * @param ids
     * @return
     */
    @Log(title = "Listing管理-取消定时刊登", businessType = BusinessType.UPDATE)
    @PostMapping("/cancelScheduledPublish")
    @ResponseBody
    public AjaxResult cancelScheduledPublish(String ids) {
        Long userId = ShiroUtils.getUserId();
        listingInfoBiz.cancelScheduledPublish(ids, userId);
        return AjaxResult.success();
    }

    @GetMapping("/syncListing")
    @RequiresPermissions("publication:listing:sync")
    public String syncListing(ModelMap mmap) {
        List<SysRole> roles = sysRoleService.selectRoleListByUserId(ShiroUtils.getUserId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(roles)) {
            //roles只包含了100EB运营则赋值给EB 只101AM运营则赋值AM
            if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "100"))) {
                mmap.put("platform", PlatformTypeEnum.EB.name());
            } else if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "101"))) {
                mmap.put("platform", PlatformTypeEnum.AM.name());
            }
        }
        return prefix + "/syncListing";
    }

    @GetMapping("/syncTemuListing")
    @RequiresPermissions("publication:listing:sync")
    public String syncTemuListing(ModelMap mmap) {
        mmap.put("platform", PlatformTypeEnum.TEMU.name());
        return prefix + "/syncListing";
    }

    @GetMapping("/syncLostListing")
    @RequiresPermissions("publication:listing:sync")
    public String syncLostListing(ModelMap mmap) {
        return prefix + "/syncLostListing";
    }

    /**
     * 同步listing平台商品信息
     *
     * @param vo
     */
    @Log(title = "Listing管理-同步选中商品", businessType = BusinessType.UPDATE)
    @PostMapping("/syncListing")
    @ResponseBody
    @RequiresPermissions("publication:listing:sync")
    public AjaxResult syncListing(SyncListingVO vo) {
        vo.setUserId(String.valueOf(ShiroUtils.getUserId()));
        if (ObjectUtils.isEmpty(vo) || vo.isNull()) {
            return AjaxResult.error("请选择要同步的商品");
        }
        if (ObjectUtils.isNotEmpty(vo.getGoodsIds())) {
            listingInfoService.syncListing(vo);
        }
        if (ObjectUtils.isNotEmpty(vo.getShopCode())) {
            listingInfoService.syncShopListing(vo);
        }
        return AjaxResult.success();
    }


    /**
     * 同步listing平台商品信息
     *
     * @param vo
     */
    @Log(title = "Listing管理-同步选中商品", businessType = BusinessType.UPDATE)
    @PostMapping("/syncTemuListing")
    @ResponseBody
    public AjaxResult syncTemuListing(SyncListingVO vo) {
        vo.setUserId(String.valueOf(ShiroUtils.getUserId()));
        if (ObjectUtils.isEmpty(vo) || vo.isNull()) {
            return AjaxResult.error("请选择要同步的商品");
        }
        if (ObjectUtils.isNotEmpty(vo.getGoodsIds())) {
            listingInfoService.syncTemuListing(vo);
        }
        if (ObjectUtils.isNotEmpty(vo.getShopCode())) {
            listingInfoService.syncShopListing(vo);
        }
        return AjaxResult.success();
    }

    /**
     * 同步listing平台商品信息
     * 仅支持一个店铺同步（ebay会有拿别的店铺token获取到listing的问题）
     *
     * @param vo
     */
    @Log(title = "Listing管理-同步缺失商品", businessType = BusinessType.UPDATE)
    @PostMapping("/syncLostListing")
    @ResponseBody
    @RequiresPermissions("publication:syncLostListing:sync")
    public AjaxResult syncLostListing(SyncListingVO vo) {
        if (ObjectUtils.isEmpty(vo) || vo.isNull()) {
            return AjaxResult.error("请选择要同步的商品");
        }
        if (ObjectUtils.isNotEmpty(vo.getPlatformGoodsId())) {
            vo.setUserId(String.valueOf(ShiroUtils.getUserId()));
            listingInfoService.syncLostListing(vo);
        }
        return AjaxResult.success();
    }

    /**
     * 重上架商品
     *
     * @param ids Listing ID集合
     * @return 删除结果
     */
    @GetMapping("/scheduledRelistItem")
    public String scheduledRelistItem(String ids, ModelMap mmap) {
        int num = Convert.toIntArray(ids).length;
        mmap.put("listingNum", num);
        mmap.put("headId", ids);
        return prefix + "/relistItem";
    }

    /**
     * 重上架商品
     *
     * @param
     */
    @Log(title = "Listing管理-重上架", businessType = BusinessType.UPDATE)
    @PostMapping("/saveRelistItem")
    @ResponseBody
    @RequiresPermissions("publication:listing:batchRelistItem")
    public AjaxResult relistItem(TaskConfiguration taskConfiguration) {
        Long userId = ShiroUtils.getUserId();
        taskConfiguration.setCreateBy(String.valueOf(userId));
        taskConfiguration.setUpdateBy(String.valueOf(userId));
        listingInfoBiz.handleRelistItem(taskConfiguration);
        return AjaxResult.success();
    }

    /**
     * 查找替换更新
     *
     * @return
     */
    @GetMapping("/findReplaceUpdate")
    public String toFindReplacePage() {
        return prefix + "/config/findReplaceUpdate";
    }

    /**
     * 查找替换更新
     *
     * @param dto
     * @return
     */
    @Log(title = "Listing管理-查找替换更新", businessType = BusinessType.UPDATE)
    @PostMapping("/findReplaceUpdate")
    @ResponseBody
    @RequiresPermissions("publication:listing:findReplaceUpdate")
    public AjaxResult findReplaceUpdate(FindReplaceUpdate dto) {
        try {
            Long userId = getUserId();
            violateWordBiz.findWordReplaceWord(userId, dto);
            return AjaxResult.success("SMC后台正在查找包含该词的商品中.");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
    /**
     * 批量编辑标题
     */
    @GetMapping("/batchUpdateTitle")
    public String batchUpdateTitle(@RequestParam List<Integer> listingIds, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            //按照平台分组,取出数量最多的平台
            Optional<List<GoodsHead>> optional = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform)).values().stream().sorted((o1, o2) -> o2.size() - o1.size()).findFirst();
            if (!optional.isPresent()) {
                throw new BusinessException("该选择有效Listing.");
            }
            platform = optional.get().get(0).getPlatform();
            listingIds = optional.get().stream().map(GoodsHead::getId).collect(Collectors.toList());
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            mmap.put("listingEditVO", listingServiceByPlatformType.getListingsEditDTOSByListingIds(listingIds));
        } catch (Exception e) {
            logger.error("查看Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
            return prefix + "/ebay_batch_edit_title";
        } else {
            return prefix + "/am_batch_edit_title";
        }
    }

    /**
     * 批量更新平台SKU
     */
    @GetMapping("/batchUpdatePlatformSku")
    public String batchUpdatePlatformSku(@RequestParam List<Integer> ids, ModelMap mmap) {
        try {
            if (Objects.isNull(ids)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(ids.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            // 过滤掉非AM平台的listing
            heads = heads.stream().filter(o -> PlatformTypeEnum.AM.name().equals(o.getPlatform())).collect(Collectors.toList());
            if (CollUtil.isEmpty(heads)) {
                throw new BusinessException("批量更新平台SKU只支持AM平台.");
            }
            // 过滤掉非草稿状态的listing
            heads = heads.stream().filter(o -> Objects.equals(o.getPublishStatus(), PublishStatus.DRAFT.getType())).collect(Collectors.toList());
            if (CollUtil.isEmpty(heads)) {
                throw new BusinessException("批量更新平台SKU只支持草稿状态的listing.");
            }
            List<AmazonEditPlatSkuVO> amazonEditPlatSkuVOList = new ArrayList<>();

            List<MappingGoods> query = new ArrayList<>();
            for (GoodsHead head : heads) {
                MappingGoods mappingGoods = new MappingGoods();
                mappingGoods.setGoodsCode(head.getPdmGoodsCode());
                mappingGoods.setShopCode(head.getShopCode());
                query.add(mappingGoods);

                AmazonEditPlatSkuVO amazonEditPlatSkuVO = new AmazonEditPlatSkuVO();
                amazonEditPlatSkuVO.setGoodsCode(head.getPdmGoodsCode());
                amazonEditPlatSkuVO.setShopCode(head.getShopCode());
                amazonEditPlatSkuVO.setId(head.getId());
                amazonEditPlatSkuVO.setGoodsHead(head);
                amazonEditPlatSkuVOList.add(amazonEditPlatSkuVO);

                String pn = listingAmazonAttributeLineV2Service.getPn(head.getId());

                if (StrUtil.isNotBlank(pn)) {
                    amazonEditPlatSkuVO.setPlatformPn(pn);
                    amazonEditPlatSkuVO.setPlatformSku(head.getPlatformGoodsCode());
                }else {
                   pn = listingAmazonAttributeLineService.getPn(head.getId());
                    if (StrUtil.isNotBlank(pn) && !"0".equals(pn)) {
                        amazonEditPlatSkuVO.setPlatformPn(pn);
                        amazonEditPlatSkuVO.setPlatformSku(head.getPlatformGoodsCode());
                    }
                }
            }

            List<MappingGoods> mappingGoodsList = mappingGoodsService.selectListByList(query);
            if (CollUtil.isNotEmpty(mappingGoodsList)) {
                // 分组
                Map<String, List<MappingGoods>> listMap = mappingGoodsList.stream().collect(Collectors.groupingBy(e -> e.getGoodsCode() + "_" + e.getShopCode()));
                amazonEditPlatSkuVOList.forEach(e -> {
                    List<MappingGoods> allMappingGoodsList = listMap.get(e.getGoodsCode() + "_" + e.getShopCode());
                    if (CollUtil.isEmpty(allMappingGoodsList)) {
                        return;
                    }
//                    List<MappingGoods> mappingGoods = allMappingGoodsList.stream().filter(f -> StringUtils.isEmpty(f.getAsin()) && StringUtils.isNotBlank(f.getPlatformSku())).collect(Collectors.toList());
                    List<MappingGoods> mappingGoods = allMappingGoodsList.stream().filter(f -> StringUtils.isEmpty(f.getAsin()) && StringUtils.isNotBlank(f.getPlatformSku()) && StringUtils.isNotBlank(f.getOperators()) && f.getOperators().equals(getUserId()+"")).collect(Collectors.toList());
                    String followVcPoAsin = goodsHeadService.getFollowVcPoAsin(e.getGoodsHead());
                    if (StringUtils.isNotBlank(followVcPoAsin)) {
                        List<MappingGoods> followVCPOMappingList = allMappingGoodsList.stream().filter(f -> StringUtils.isNotBlank(f.getPlatformSku())
                                && StringUtils.isNotBlank(f.getAsin()) && f.getAsin().equals(followVcPoAsin)).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(followVCPOMappingList)) {
                            mappingGoods.addAll(followVCPOMappingList);
                        }
                    }

                    if (CollUtil.isNotEmpty(mappingGoods)) {
                        mappingGoods.sort(Comparator.comparing(MappingGoods::getCreateTime));
                        MappingGoods mappingGoods1 = null;
                        if (StringUtils.isNotBlank(e.getPlatformPn()) && StringUtils.isNotBlank(e.getPlatformSku())) {
                            Optional<MappingGoods> first = mappingGoods.stream().filter(f -> StringUtils.equals(f.getPlatformPn(), e.getPlatformPn()) && StringUtils.equals(f.getPlatformSku(), e.getPlatformSku())).findFirst();
                            if (first.isPresent()) {
                                mappingGoods1= first.get();
                            }
                        }
                        if (Objects.isNull(mappingGoods1)) {
                            mappingGoods1 = mappingGoods.get(0);
                        }
                        e.setPlatformSku(mappingGoods1.getPlatformSku());
                        e.setPlatformPn(mappingGoods1.getPlatformPn());
                        e.setOperators(mappingGoods1.getOperators());
                        if (mappingGoods.size() > 1) {
                            e.setMultiSku(true);
                        }
                    }
                });
            }

            if (CollUtil.isNotEmpty(amazonEditPlatSkuVOList)) {
                amazonEditPlatSkuVOList = amazonEditPlatSkuVOList.stream().sorted(Comparator.comparing(AmazonEditPlatSkuVO::getId).reversed()).collect(Collectors.toList());
            }

            mmap.put("amazonEditVOList", amazonEditPlatSkuVOList);
        } catch (Exception e) {
            logger.error("Amazon批量更新平台SKU详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }
        return prefix + "/amazon_batch_edit_platform_sku";
    }




    /**
     * 批量更新平台节点
     */
    @GetMapping("/batchUpdatePlatformNode")
    public String batchUpdatePlatformNode(@RequestParam List<Integer> ids, ModelMap mmap) {
        try {
            if (Objects.isNull(ids)) {
                throw new BusinessException("该选择有效Listing.");
            }


            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(ids.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            // 过滤掉非AM平台的listing
            heads = heads.stream().filter(o -> PlatformTypeEnum.AM.name().equals(o.getPlatform())).collect(Collectors.toList());
            if (CollUtil.isEmpty(heads)) {
                throw new BusinessException("批量更新平台SKU只支持AM平台.");
            }
            // 过滤掉非草稿状态的listing
            heads = heads.stream().filter(o -> PublishStatus.getSaleStatus().contains(o.getPublishStatus()) || PublishStatus.OFF_SALE.getType().equals(o.getPublishStatus())).collect(Collectors.toList());
            if (CollUtil.isEmpty(heads)) {
                throw new BusinessException("批量更新平台SKU只支持在售状态的listing.");
            }
            List<PlatformCategory> platformCategories = platformCategoryService.selectPlatformCategoryListByIds(heads.stream().map(e -> String.valueOf(e.getCategoryId())).toArray(String[]::new));
            // to map
            Map<Long, PlatformCategory> platformCategoryMap = platformCategories.stream().collect(Collectors.toMap(PlatformCategory::getId, Function.identity()));

            heads.forEach(e -> {
                PlatformCategory platformCategory = platformCategoryMap.get(e.getCategoryId().longValue());
                if (Objects.nonNull(platformCategory)) {
                    e.setCategoryName(platformCategory.getCategoryEnName());
                }
            });

            mmap.put("heads", heads);
        } catch (Exception e) {
            logger.error("Amazon批量更新平台节点详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }
        return prefix + "/amazon_batch_edit_platform_node";
    }

    /**
     * 批量更新平台节点
     *
     * @param editPlatSkuDTOS
     * @return
     */
    @Log(title = "Listing管理-批量更新平台节点", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateNode")
    @ResponseBody
    public AjaxResult batchUpdateNode(@RequestBody List<AmazonEditPlatSkuDTO> editPlatSkuDTOS) {
        try {
            Long userId = ShiroUtils.getUserId();
            amazonPlatformListingService.batchUpdateNode(editPlatSkuDTOS, userId);
        } catch (Exception e) {
            logger.error("批量更新平台节点失败",  e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量更新平台SKU
     *
     * @return 保存结果
     */
    @Log(title = "Listing管理-批量更新平台SKU", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateSku")
    @ResponseBody
//    @RecordLog(isBackup = true, businessId = "#editPlatSkuDTOS.![id]", operType = OperTypeEnum.BATCH_EDIT, operDesc = "批量更新平台SKU信息:", taskRecord = true)
    public AjaxResult batchUpdateSku(@RequestBody List<AmazonEditPlatSkuDTO> editPlatSkuDTOS) {
        try {
            Long userId = ShiroUtils.getUserId();
            amazonPlatformListingService.batchUpdateSku(editPlatSkuDTOS, userId);
        } catch (Exception e) {
            logger.error("批量更新平台SKU失败",  e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量价格库存
     */
    @GetMapping("/batchUpdatePriceAndStock")
    public String batchUpdatePriceAndStock(@RequestParam List<Integer> listingIds,String type, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            //按照平台分组,取出数量最多的平台
            Optional<List<GoodsHead>> optional = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform)).values().stream().sorted((o1, o2) -> o2.size() - o1.size()).findFirst();
            if (!optional.isPresent()) {
                throw new BusinessException("该选择有效Listing.");
            }
            platform = optional.get().get(0).getPlatform();
            listingIds = optional.get().stream().map(GoodsHead::getId).collect(Collectors.toList());
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            mmap.put("listingEditVO", listingServiceByPlatformType.getListingsEditDTOSByListingIds(listingIds));
        } catch (Exception e) {
            logger.error("查看Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
            if("PRICE".equals(type)){
                return prefix + "/ebay_batch_edit_price";
            }
            if("STOCK".equals(type)){
                return  prefix +"/ebay_batch_edit_stock";
            }
            return prefix + "/ebay_batch_edit_priceAndStock";
        } else {
            if("PRICE".equals(type)){
                return prefix + "/am_batch_edit_price";
            }
            if("STOCK".equals(type)){
                return prefix + "/am_batch_edit_stock";
            }
            if("LOSTCART".equals(type)){
                mmap.put("tag", "lostCart");
                return prefix + "/am_batch_edit_price";
            }
            return prefix + "/v2/am_batch_edit_priceAndStock";
        }
    }

    /**
     * 批量价格库存
     */
    @GetMapping("/batchUpdateStock")
    public String batchUpdateStock(@RequestParam List<Integer> listingIds, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            platform = heads.get(0).getPlatform();
            if (!Objects.equals(platform, PlatformTypeEnum.AM.name())) {
                throw new BusinessException("EB暂不支持修改库存.");
            }

            mmap.put("listingEditVO", heads);
            mmap.put("goodsIds", listingIds);
            return prefix + "/v2/am_batch_edit_stock";
        } catch (Exception e) {
            logger.error("查看Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }
    }


    @PostMapping("listBatchEditStockData")
    @ResponseBody
    public AjaxResult listBatchEditStockData(String goodsIds, ModelMap mmap) {
        List<Integer> goodsIdList = JSON.parseArray(goodsIds, Integer.class);
        List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(goodsIdList.stream().toArray(Integer[]::new));
        if (Objects.isNull(heads)) {
            throw new BusinessException("该选择有效Listing.");
        }
        List<AmazonWarehouseMapping> amazonWarehouseMappings = amazonWarehouseMappingService.selectAmazonWarehouseMappingListByShopCode("VC1");
        Map<String, AmazonWarehouseMapping> warehouseMap = amazonWarehouseMappings.stream().collect(Collectors.toMap(AmazonWarehouseMapping::getAmWhCode, Function.identity()));
        List<String> amWhCodes = new ArrayList<>(warehouseMap.keySet());

        List<VcListingInventory> listingInventoryList = vcListingInventoryService.selectVcListingInventoryByGoodsIdList(goodsIdList);
        listingInventoryList = listingInventoryList.stream().filter(e -> amWhCodes.contains(e.getWarehouseCode())).collect(Collectors.toList());
        Map<Integer, List<VcListingInventory>> map = listingInventoryList.stream().collect(Collectors.groupingBy(VcListingInventory::getGoodsId));

        map.forEach((k, v) -> {
            List<String> subAmWhCodes = v.stream().map(VcListingInventory::getWarehouseCode).collect(Collectors.toList());
            for (VcListingInventory vcListingInventory : v) {
                AmazonWarehouseMapping amazonWarehouseMapping = warehouseMap.get(vcListingInventory.getWarehouseCode());
                vcListingInventory.setWhName(amazonWarehouseMapping.getWhName() + "[" + vcListingInventory.getWarehouseCode() + "(" + vcListingInventory.getWhCode() + ")]");
                if (vcListingInventory.getAvailableInventory() != null && vcListingInventory.getAvailableInventory() <= 0) {
                    vcListingInventory.setAvailableInventory(null);
                }
            }

             List<String> unExistWhCodes = amWhCodes.stream().filter(e -> !subAmWhCodes.contains(e)).collect(Collectors.toList());
             if (CollUtil.isNotEmpty(unExistWhCodes)) {
                 VcListingInventory first = v.get(0);
                 unExistWhCodes.forEach(e -> {
                     AmazonWarehouseMapping amazonWarehouseMapping = warehouseMap.get(e);
                     VcListingInventory vcListingInventory = new VcListingInventory();
                     vcListingInventory.setGoodsId(k);
                     vcListingInventory.setShopCode(first.getShopCode());
                     vcListingInventory.setWhCode(amazonWarehouseMapping.getWhCode());
                     vcListingInventory.setWhName(amazonWarehouseMapping.getWhName() + "[" + e + "(" + amazonWarehouseMapping.getWhCode() + ")]");
                     vcListingInventory.setSku(first.getSku());
                     vcListingInventory.setSellerSku(first.getSellerSku());
                     vcListingInventory.setAsin(first.getAsin());
                     vcListingInventory.setWarehouseCode(e);
                     vcListingInventory.setAvailableInventory(null);
                     v.add(vcListingInventory);
                 });
             }
        });
        heads.forEach(e -> {
            List<VcListingInventory> vcListingInventories = map.get(e.getId());
            if (CollUtil.isNotEmpty(vcListingInventories)) {
                e.setVcListingInventoryList(vcListingInventories);
            }else {
                e.setVcListingInventoryList(createVcListingInventory(e, amazonWarehouseMappings));
            }
            // 排序
            e.getVcListingInventoryList().sort(Comparator.comparing(VcListingInventory::getWhCode).reversed());
        });
        return AjaxResult.success(heads);
    }

    @PostMapping("batchEditStock")
    @ResponseBody
    @RequiresPermissions("publication:listing:batchEditVCStock")
    public AjaxResult batchEditStock(BatchListingDTO dto) {
        try {
            String platform = PlatformTypeEnum.AM.name();
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            listingServiceByPlatformType.batchEditStock(dto);
            return AjaxResult.success();
        }catch (Exception e) {
            logger.error("批量更新库存失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    private List<VcListingInventory> createVcListingInventory(GoodsHead e, List<AmazonWarehouseMapping> amazonWarehouseMappings) {
        List<VcListingInventory> vcListingInventories = new ArrayList<>();
        amazonWarehouseMappings.forEach(amazonWarehouseMapping -> {
            VcListingInventory vcListingInventory = new VcListingInventory();
            vcListingInventory.setGoodsId(e.getId());
            vcListingInventory.setShopCode(e.getShopCode());
            vcListingInventory.setWhCode(amazonWarehouseMapping.getWhCode());
            vcListingInventory.setWhName(amazonWarehouseMapping.getWhName() + "[" + amazonWarehouseMapping.getAmWhCode() + "(" + amazonWarehouseMapping.getWhCode() + ")]");
            vcListingInventory.setSku(e.getPdmGoodsCode());
            vcListingInventory.setSellerSku(e.getPlatformGoodsCode());
            vcListingInventory.setAsin(e.getPlatformGoodsId());
            vcListingInventory.setWarehouseCode(amazonWarehouseMapping.getAmWhCode());
            vcListingInventory.setAvailableInventory(null);
            vcListingInventories.add(vcListingInventory);
        });
        return vcListingInventories;
    }

    /**
     * 批量编辑属性
     */
    @GetMapping("/batchUpdateAttribute")
    public String batchUpdateAttribute(@RequestParam List<Integer> listingIds, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            //按照平台分组,取出数量最多的平台
            Optional<List<GoodsHead>> optional = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform)).values().stream().sorted((o1, o2) -> o2.size() - o1.size()).findFirst();
            if (!optional.isPresent()) {
                throw new BusinessException("该选择有效Listing.");
            }
            platform = optional.get().get(0).getPlatform();
            if (!Objects.equals(platform, PlatformTypeEnum.EB.name())) {
                throw new BusinessException("请选择EB平台的Listing.");
            }
            listingIds = optional.get().stream().map(GoodsHead::getId).collect(Collectors.toList());
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            mmap.put("listingEditVO", listingServiceByPlatformType.getListingsEditDTOSByListingIds(listingIds));
        } catch (Exception e) {
            logger.error("查看Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
            return prefix + "/ebay_batch_edit_attribute";
        } else {
            return prefix + "/am_batch_edit_attribute";
        }
    }


    /**
     * 批量编辑图片
     */
    @GetMapping("/batchUpdatePictures")
    public String batchUpdatePictures(@RequestParam List<Integer> listingIds, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            //按照平台分组,取出数量最多的平台
            Optional<List<GoodsHead>> optional = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform)).values().stream().sorted((o1, o2) -> o2.size() - o1.size()).findFirst();
            if (!optional.isPresent()) {
                throw new BusinessException("该选择有效Listing.");
            }
            platform = optional.get().get(0).getPlatform();
            listingIds = optional.get().stream().map(GoodsHead::getId).collect(Collectors.toList());
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            mmap.put("listingEditVO", listingServiceByPlatformType.getListingsEditDTOSByListingIds(listingIds));
        } catch (Exception e) {
            logger.error("查看Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
            return prefix + "/ebay_batch_edit_pictures";
        } else {
            return prefix + "/am_batch_edit_pictures";
        }
    }


    /**
     * 批量编辑视频
     */
    @GetMapping("/batchUpdateVideo")
    public String batchUpdateVideo(@RequestParam List<Integer> listingIds, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            //按照平台分组,取出数量最多的平台
            Optional<List<GoodsHead>> optional = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform)).values().stream().sorted((o1, o2) -> o2.size() - o1.size()).findFirst();
            if (!optional.isPresent()) {
                throw new BusinessException("该选择有效Listing.");
            }
            platform = optional.get().get(0).getPlatform();
            listingIds = optional.get().stream().map(GoodsHead::getId).collect(Collectors.toList());
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            mmap.put("listingEditVO", listingServiceByPlatformType.getListingsEditDTOSByListingIds(listingIds));
        } catch (Exception e) {
            logger.error("查看Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
            return prefix + "/ebay_batch_edit_video";
        } else {
            return "error/service";
        }
    }

    /**
     * 批量更新视频
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdateVideo")
    @Log(title = "Listing管理-批量更新视频", businessType = BusinessType.UPDATE)
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_UPDATE_VIDEO, operDesc = "批量更新视频信息:", taskRecord = true)
    public AjaxResult batchUpdateVideo(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdateVideo(dto);
        } catch (Exception e) {
            logger.error("批量更新刊登商品信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }


    /**
     * 批量更新物流信息
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdateShipping")
    @Log(title = "Listing管理-批量更新物流信息", businessType = BusinessType.UPDATE)
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.ids", operType = OperTypeEnum.SYNC_SHIPPING_LISTING, operDesc = "批量更新物流信息:", taskRecord = true)
    public AjaxResult batchUpdateShipping(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdateShipping(dto);
        } catch (Exception e) {
            logger.error("批量更新物流信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量更新付款退货信息
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdatePolicy")
    @Log(title = "Listing管理-批量更新付款退货信息", businessType = BusinessType.UPDATE)
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.ids", operType = OperTypeEnum.SYNC_POLILY_LISTING, operDesc = "批量更新付款退货信息:", taskRecord = true)
    public AjaxResult batchUpdatePolicy(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdatePolicy(dto);
        } catch (Exception e) {
            logger.error("批量更新付款退货信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量更新描述模板
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdateDescriptionTemplate")
    @Log(title = "Listing管理-批量更新描述模板", businessType = BusinessType.UPDATE)
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.ids", operType = OperTypeEnum.SYNC_DESCRIPTION_LISTING, operDesc = "批量更新描述模板:", taskRecord = true)
    public AjaxResult batchUpdateDescriptionTemplate(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdateDescriptionTemplate(dto);
        } catch (Exception e) {
            logger.error("批量更新描述失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }



    /**
     * 批量编辑描述
     */
    @GetMapping("/batchUpdateDescription")
    public String batchUpdateDescription(@RequestParam List<Integer> listingIds, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            //按照平台分组,取出数量最多的平台
            Optional<List<GoodsHead>> optional = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform)).values().stream().sorted((o1, o2) -> o2.size() - o1.size()).findFirst();
            if (!optional.isPresent()) {
                throw new BusinessException("该选择有效Listing.");
            }
            platform = optional.get().get(0).getPlatform();
            listingIds = optional.get().stream().map(GoodsHead::getId).collect(Collectors.toList());
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            mmap.put("listingEditVO", listingServiceByPlatformType.getListingsEditDTOSByListingIds(listingIds));
        } catch (Exception e) {
            logger.error("查看Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
            return prefix + "/ebay_batch_edit_description";
        } else {
            return prefix + "/am_batch_edit_description";
        }
    }


    /**
     * 批量编辑五点描述
     */
    @GetMapping("/batchUpdateFiveDescription")
    public String batchUpdateFiveDescription(@RequestParam List<Integer> listingIds, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            //按照平台分组,取出数量最多的平台
            Optional<List<GoodsHead>> optional = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform)).values().stream().sorted((o1, o2) -> o2.size() - o1.size()).findFirst();
            if (!optional.isPresent()) {
                throw new BusinessException("该选择有效Listing.");
            }
            platform = optional.get().get(0).getPlatform();
            listingIds = optional.get().stream().map(GoodsHead::getId).collect(Collectors.toList());
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            mmap.put("listingEditVO", listingServiceByPlatformType.getListingsEditDTOSByListingIds(listingIds));
        } catch (Exception e) {
            logger.error("查看Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
            return "error/service";
        } else {
            return prefix + "/am_batch_edit_five_description";
        }
    }

    /**
     * 批量编辑
     *
     * @param listingIds
     * @param mmap
     * @return
     */
    @GetMapping("/listingBatchEdit")
    public String listingBatchEdit(@RequestParam List<Integer> listingIds, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            //按照平台分组,取出数量最多的平台
            Optional<List<GoodsHead>> optional = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform)).values().stream().sorted((o1, o2) -> o2.size() - o1.size()).findFirst();
            if (!optional.isPresent()) {
                throw new BusinessException("该选择有效Listing.");
            }
            platform = optional.get().get(0).getPlatform();
            listingIds = optional.get().stream().map(GoodsHead::getId).collect(Collectors.toList());
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            mmap.put("listingEditVO", listingServiceByPlatformType.getListingsEditDTOSByListingIds(listingIds));
        } catch (Exception e) {
            logger.error("查看自动Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
            return prefix + "/ebay_batch_edit_publish";
        } else {
            return prefix + "/am_batch_edit_publish";
        }
    }


    /**
     * 批量编辑
     *
     * @param listingIds
     * @param mmap
     * @return
     */
    @GetMapping("/v2/listingBatchEdit")
    public String listingBatchEditV2(@RequestParam List<Integer> listingIds, ModelMap mmap) {
        String platform = "";
        try {
            if (Objects.isNull(listingIds)) {
                throw new BusinessException("该选择有效Listing.");
            }
            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
            if (Objects.isNull(heads)) {
                throw new BusinessException("该选择有效Listing.");
            }
            //按照平台分组,取出数量最多的平台
            Map<String, List<GoodsHead>> platformMap = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform));
            if (platformMap.size() > 1) {
                // 数量相等，取最大ID的platform
                if (platformMap.get(PlatformTypeEnum.AM.name()).size() == platformMap.get(PlatformTypeEnum.EB.name()).size()) {
                    platform = heads.stream().max(Comparator.comparing(GoodsHead::getId)).get().getPlatform();
                } else {
                    platform = platformMap.get(PlatformTypeEnum.AM.name()).size() > platformMap.get(PlatformTypeEnum.EB.name()).size() ? PlatformTypeEnum.AM.name() : PlatformTypeEnum.EB.name();
                }
            } else {
                platform = platformMap.keySet().iterator().next();
            }
            listingIds = platformMap.get(platform).stream().map(GoodsHead::getId).collect(Collectors.toList());
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            mmap.put("listingEditVO", listingServiceByPlatformType.getListingsEditDTOSByListingIds(listingIds));
        } catch (Exception e) {
            logger.error("查看自动Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
            return prefix + "/ebay_batch_edit_publish";
        } else {
            return prefix + "/v2/am_batch_edit_publish";
        }
    }



    @GetMapping("/getALlImagesByHeadId/{headId}/{brandCode}")
    public String getALlImagesByGoodsId(@PathVariable Long headId,@PathVariable String brandCode, ModelMap mmap) {
        SysUser sysUser = ShiroUtils.getSysUser();
        if (Objects.isNull(headId)) {
            mmap.put("images", new ArrayList<>());
            return "publication/goods/property/image_list";
        }
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(Integer.valueOf(String.valueOf(headId)));

        Pattern pattern = Pattern.compile("(.*)(?=\\()");
        Matcher matcher = pattern.matcher(goodsHead.getPdmGoodsCode());
        String pdmGoodsCode= matcher.find() ? matcher.group() : goodsHead.getPdmGoodsCode();

        List<GoodsImage> goodsImages = goodsImageService.selectGoodsImageByGoodsCode(pdmGoodsCode, null,brandCode);
        for (GoodsImage goodsImage : goodsImages) {
            goodsImage.setType(ImageType.getTypeNameByType(Integer.valueOf(goodsImage.getType())));
        }
        List<GoodsResource> resourceList = goodsResourceService.getOnlineAllResource(pdmGoodsCode, PlatformTypeEnum.AM.name(),sysUser);
        if (CollUtil.isNotEmpty(resourceList)) {
            resourceList.stream().forEach(resource -> {
                GoodsImage goodsImage = new GoodsImage();
                goodsImage.setImageUrl(resource.getResourceUrl());
                goodsImage.setImageName(resource.getResourceName());
                goodsImage.setType(resource.getResourceType());
                goodsImages.add(goodsImage);
            });
        }
        List<String> types = goodsImages.stream().map(GoodsImage::getType).distinct().collect(Collectors.toList());
        mmap.put("images", goodsImages);
        mmap.put("imagesTypes", types);
        return "publication/goods/property/image_list";
    }

    @GetMapping("/getALlImagesByTemuGoodsId/{pdmGoodsCode}/{brandCode}")
    public String getALlImagesByTemuGoodsId(@PathVariable String pdmGoodsCode,@PathVariable String brandCode, ModelMap mmap) {
        SysUser sysUser = ShiroUtils.getSysUser();
        if (Objects.isNull(pdmGoodsCode)) {
            mmap.put("images", new ArrayList<>());
            return "publication/goods/property/image_list";
        }
//        TemuGoodsHead temuGoodsHead = temuGoodsHeadService.selectTemuGoodsHeadById(headId.toString());
        List<GoodsImage> goodsImages = goodsImageService.selectGoodsImageByGoodsCode(pdmGoodsCode, null,brandCode);
        for (GoodsImage goodsImage : goodsImages) {
            goodsImage.setType(ImageType.getTypeNameByType(Integer.valueOf(goodsImage.getType())));
        }
        List<GoodsResource> resourceList = goodsResourceService.getOnlineAllResource(pdmGoodsCode, PlatformTypeEnum.AM.name(),sysUser);
        if (CollUtil.isNotEmpty(resourceList)) {
            resourceList.stream().forEach(resource -> {
                GoodsImage goodsImage = new GoodsImage();
                goodsImage.setImageUrl(resource.getResourceUrl());
                goodsImage.setImageName(resource.getResourceName());
                goodsImage.setType(resource.getResourceType());
                goodsImages.add(goodsImage);
            });
        }
        List<String> types = goodsImages.stream().map(GoodsImage::getType).distinct().collect(Collectors.toList());
        mmap.put("images", goodsImages);
        mmap.put("imagesTypes", types);
        return "publication/goods/property/image_list";
    }

    @GetMapping("/getALlImagesByGoodsCode/{goodCode}/{brandCode}")
    @ResponseBody
    public AjaxResult getALlImagesByGoodsCode(@PathVariable String goodCode,@PathVariable String brandCode) {
        SysUser sysUser = ShiroUtils.getSysUser();
        if (Objects.isNull(goodCode)) {
           throw new BusinessException("请输入商品编码");
        }
        List<GoodsImage> goodsImages = goodsImageService.selectGoodsImageByGoodsCode(goodCode, null,brandCode);
        for (GoodsImage goodsImage : goodsImages) {
            goodsImage.setType(ImageType.getTypeNameByType(Integer.valueOf(goodsImage.getType())));
        }
        List<GoodsResource> resourceList = goodsResourceService.getOnlineAllResource(goodCode, PlatformTypeEnum.AM.name(),sysUser);
        if (CollUtil.isNotEmpty(resourceList)) {
            resourceList.stream().forEach(resource -> {
                GoodsImage goodsImage = new GoodsImage();
                goodsImage.setImageUrl(resource.getResourceUrl());
                goodsImage.setImageName(resource.getResourceName());
                goodsImage.setType(resource.getResourceType());
                goodsImages.add(goodsImage);
            });
        }
        List<String> types = goodsImages.stream().map(GoodsImage::getType).distinct().collect(Collectors.toList());
        Map<String, Object> resp = new HashMap<>();
        resp.put("images", goodsImages);
        resp.put("imagesTypes", types);
        return AjaxResult.success(resp);
    }

    /**
     * 页面快捷修改
     * @param dto
     * @return
     */
    @PostMapping("/quickUpdate")
    @Log(title = "Listing管理-页面快捷修改", businessType = BusinessType.UPDATE)
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.id", operType = OperTypeEnum.QUICK_EDIT, operDesc = "页面编辑Listing信息:")
    public AjaxResult quickUpdate(@RequestBody ListingQuickEditDTO dto) {
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(dto.getId());
        if (Objects.isNull(goodsHead)) {
            return AjaxResult.error("该商品不存在");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(dto.getPlatform());
            listingServiceByPlatformType.quickUpdate(dto, goodsHead);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }


    @PostMapping("/selectPublishSubStatus/{publishStatus}")
    @ResponseBody
    public AjaxResult selectPublishSubStatus(@PathVariable("publishStatus") Integer publishStatus) {
        List<Map> lists = new ArrayList<>();
        if (Objects.isNull(publishStatus)) {
            return AjaxResult.success(lists);
        }
        List<PublishStatus> subStatus = PublishStatus.getSubStatus(publishStatus);
        for (PublishStatus status : subStatus) {
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("key", String.valueOf(status.getType()));
            resultMap.put("value", String.valueOf(status.getStatusName()));
            lists.add(resultMap);
        }
        return AjaxResult.success(lists);
    }

    /**
     * 移至草稿
     *
     * @param ids
     * @return
     */
    @Log(title = "Listing管理-移至草稿", businessType = BusinessType.UPDATE)
    @PostMapping("/move2Draft")
    @ResponseBody
    public AjaxResult move2Draft(String ids) {
        try {
            Long userId = ShiroUtils.getUserId();
            return toAjax(listingInfoService.move2Draft(ids, userId));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }



    @GetMapping("/findKeyword")
    public String findKeyword(ModelMap mmap) {
        return prefix + "/findKeyword";
    }

    /**
     * 查找关键词
     * @param vo
     * @return
     */
    @Log(title = "Listing管理-查找关键词", businessType = BusinessType.UPDATE)
    @PostMapping("/findKeyword")
    @ResponseBody
    public AjaxResult findKeyword(FindKeyWordVO vo) {
        Long userId = ShiroUtils.getUserId();
        String keyword = vo.getKeyword();
        String shopCode = vo.getShopCode();
        if (ObjectUtils.isEmpty(keyword)) {
            return AjaxResult.error("请输入需要查找关键词");
        }
        if (ObjectUtils.isEmpty(shopCode)) {
            return AjaxResult.error("请选择需要查找关键词的店铺");
        }
        String[] shopCodes = shopCode.split(",");
        List<Shop> shopList = shopService.selectShopByShopCodes(shopCodes);
        if (CollectionUtils.isEmpty(shopList)) {
            return AjaxResult.error("店铺不存在或者请确认店铺已激活");
        }
        try {
            //店铺按照平台分组
            shopList.stream().collect(Collectors.groupingBy(Shop::getPlatformCode)).forEach((k, v) -> {
                IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(k);
                if (Objects.isNull(listingServiceByPlatformType)) {
                    return;
                }
                List<String> shopCodeList = v.stream().map(Shop::getShopCode).collect(Collectors.toList());
                listingServiceByPlatformType.findKeyword(userId, keyword, shopCodeList);
            });
        } catch (Exception e) {
            logger.error("查找关键词失败,FindKeyWordVO:{}", vo, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量跟卖
     * @param goodsIds
     * @param mmap
     * @return
     */
    @GetMapping("/oneKeyFollow/{goodsIds}/{isFollowSold}")
    public String oneKeyFollow(@PathVariable("goodsIds") String goodsIds,@PathVariable("isFollowSold") String isFollowSold, ModelMap mmap) {
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(Convert.toIntArray(goodsIds));
        boolean existNoAM = goodsHeadList.stream().anyMatch(goodsHead -> !Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()));
        if (existNoAM)return "error/service";
        mmap.put("goodsIds", goodsIds);
        mmap.put("isFollowSold", isFollowSold);
        return prefix + "/oneKeyFollow";
    }

    @PostMapping("/oneKeyFollow/getFollowSoldData")
    @ResponseBody
    public AjaxResult getFollowSoldData(String goodsIds) {
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(Convert.toIntArray(goodsIds));
        return AjaxResult.success(goodsHeadList);
    }



    @PostMapping("/category/validateJson")
    @ResponseBody
    public AjaxResult categoryValid(CategoryInfo categoryInfo) throws URISyntaxException {
        Integer categoryId = Integer.valueOf(categoryInfo.getPlatformCategoryId());
        String shopCode = categoryInfo.getShopCode();
        if (StringUtils.isBlank(shopCode) || "0".equals(shopCode)) {
            return AjaxResult.error("请选择店铺");
        }
        // 对页面的属性进行解析
        List<ListingAmazonAttributeLineV2> attributeLines = parseAmazonAttributeLines(categoryInfo.getAttribute());
        ListingDTO listingDTO = new ListingDTO();
        listingDTO.setPlatform(PlatformTypeEnum.AM.name());
        listingDTO.setProductCategoryCode(categoryId.toString());
        listingDTO.setShopCode(shopCode);
        listingDTO.setAmazonAttributeLines(attributeLines);
        return validateJson(listingDTO);
    }

    @PostMapping("/batchValidateJson")
    @ResponseBody
    public AjaxResult batchValidateJson(BatchListingDTO dto) throws URISyntaxException {
        try {
            PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(dto.getCategoryId()));
            List<ListingDTO> listings = dto.getListings();
            Map<String, List<AmazonErrorFieldVO>> errorMap = new HashMap<>();
            for (ListingDTO listing : listings) {
                List<ListingAmazonAttributeLineV2> attributeLineV2s = getListingAmazonAttributeLinesV2New(dto, listing, platformCategory);
                ListingDTO listingDTO = new ListingDTO();
                listingDTO.setPlatform(PlatformTypeEnum.AM.name());
                listingDTO.setProductCategoryCode(dto.getCategoryId().toString());
                listingDTO.setShopCode(dto.getShopCode());
                listingDTO.setAmazonAttributeLines(attributeLineV2s);
                listingDTO.setListPrice(listing.getListPrice());
                List<AmazonErrorFieldVO> amazonErrorFieldVOS = handerValidateJson(listingDTO);
                if (CollectionUtils.isEmpty(amazonErrorFieldVOS)) {
                    continue;
                }
                if (listing.getId() != null) {
                    errorMap.put("主键ID:" + listing.getId(), amazonErrorFieldVOS);
                } else {
                    errorMap.put(listing.getGoodsCode(), amazonErrorFieldVOS);
                }
            }
            if (CollectionUtils.isEmpty(errorMap)) {
                return AjaxResult.success();
            }

            return AjaxResult.success(errorMap);
        }catch (Exception e) {
            logger.error("批量校验json失败,BatchListingDTO:{}", dto, e);
            return AjaxResult.success();
        }
    }


    private List<ListingAmazonAttributeLineV2> getListingAmazonAttributeLinesV2New(BatchListingDTO dto, ListingDTO listing, PlatformCategory platformCategory) {
        List<ListingAmazonAttributeLineV2> amazonAttributeLines = new ArrayList<>();

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dto.getListingAttributeLine())) {
            for (String listingAmazonAttributeLineStr : dto.getListingAttributeLine()) {
                ListingAmazonAttributeLineV2 attributeLine = JSON.parseObject(listingAmazonAttributeLineStr, ListingAmazonAttributeLineV2.class);
                if (org.springframework.util.ObjectUtils.isEmpty(attributeLine)) {
                    continue;
                }
                if (attributeLine.getHeadId() != null) {
                    if (!Objects.equals(attributeLine.getHeadId().intValue(), listing.getId())) {
                        continue;
                    }
                }else {
                    if (!Objects.equals(attributeLine.getPdmGoodsCode(), listing.getGoodsCode())) {
                        continue;
                    }
                }

                if (StringUtils.isBlank(attributeLine.getTableValue())) {
                    continue;
                }
                //判断是否是平台商品编码类型
                if (isSpecialAttr(attributeLine.getPropNodePath())) {
                    continue;
                }
                ListingAmazonAttributeLineV2 line = new ListingAmazonAttributeLineV2();
                line.setCategoryId(listing.getCategoryId());
                line.setPdmGoodsCode(attributeLine.getPdmGoodsCode());
                line.setProductType(platformCategory.getProductType());
                line.setTableValue(attributeLine.getTableValue());
                line.setTableName(attributeLine.getTableName());
                line.setTableType(attributeLine.getTableType());
                line.setPropNodePath(attributeLine.getPropNodePath());
                amazonAttributeLines.add(line);
            }
        }
        return amazonAttributeLines;
    }

    private boolean isSpecialAttr(String propNodePath) {
        AmazonSpecialAttributeV2Enum[] values = AmazonSpecialAttributeV2Enum.values();
        for (AmazonSpecialAttributeV2Enum value : values) {
            if (value.toString().equalsIgnoreCase(propNodePath)) {
                return true;
            }
        }
        return false;
    }

    private List<ListingAmazonAttributeLineV2> parseAmazonAttributeLines(List<ConfigRequiredField> attribute) {
        List<ListingAmazonAttributeLineV2> attributeLines = new ArrayList<>();
        for (ConfigRequiredField configRequiredField : attribute) {
            ListingAmazonAttributeLineV2 attributeLine = new ListingAmazonAttributeLineV2();
            attributeLine.setPropNodePath(configRequiredField.getPropNodePath());
            attributeLine.setTableName(configRequiredField.getAttributeName());
            attributeLine.setTableValue(configRequiredField.getAttributeValue());
            attributeLines.add(attributeLine);
        }
        return attributeLines;
    }

    @PostMapping("validateJson")
    @ResponseBody
    public AjaxResult validateJson(ListingDTO listingDTO) throws URISyntaxException {
        if (StringUtils.isBlank(listingDTO.getShopCode())) {
            return AjaxResult.success();
        }
        try {
            List<AmazonErrorFieldVO> amazonErrorFieldVOS = handerValidateJson(listingDTO);
            return AjaxResult.success(amazonErrorFieldVOS);
        }catch (Exception e) {
            logger.error("校验json失败,ListingDTO:{}", listingDTO, e);
            return AjaxResult.success();
        }
    }
    public List<AmazonErrorFieldVO> handerValidateJson (ListingDTO listingDTO) throws URISyntaxException {
        Integer categoryId = StringUtils.isNotEmpty(listingDTO.getProductCategoryCode()) ? Integer.valueOf(listingDTO.getProductCategoryCode()) : listingDTO.getCategoryId();
        // 对页面的属性进行解析
        List<ListingAmazonAttributeLineV2> attributeLines = goodsInfoBiz.parseAmazonAttributeLines(listingDTO, ShiroUtils.getUserId(), null);
        if (CollectionUtils.isEmpty(attributeLines)) {
            return null;
        }

        String vcFlag = listingDTO.getShopCode().contains("VC") ? Constants.YesOrNo.YES : Constants.YesOrNo.NO;
        // 将页面传递过来的属性转换为json
        Map<String, Object> amazonJsonFieldMap = amazonProductBiz.getAmazonJsonFieldMap(null, categoryId, vcFlag, attributeLines, Constants.YesOrNo.YES);

        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(categoryId.longValue());
        // 从亚马逊获取类目属性
        AjaxResult ajaxResult = amazonApiHttpRequestBiz.getAMAttributesResultCache(listingDTO.getShopCode(), platformCategory.getProductType());
        if (!ajaxResult.isSuccess()) {
            throw new BusinessException("获取亚马逊类目失败");
        }
        DefinitionsDTO definitionsDTO = JSONUtil.toBean(ajaxResult.get(AjaxResult.DATA_TAG).toString(), DefinitionsDTO.class, false);
        // 获取schema的具体内容
        String resource = amazonApiHttpRequestBiz.getAMAttributesResultDetail(listingDTO.getShopCode(), platformCategory.getProductType(), "schema");

        // 校验json
        List<AmazonErrorFieldVO> amazonErrorFieldVOS = doValidateJson(definitionsDTO.getMetaSchema().getLink().getResource(), resource, amazonJsonFieldMap, platformCategory.getProductType(), platformCategory.getSite(), vcFlag);
        if (CollUtil.isNotEmpty(amazonErrorFieldVOS)) {
            // 对product_category.value和product_subcategory.value和item_type_keyword.value进行排序
            amazonErrorFieldVOS = amazonErrorFieldVOS.stream().sorted(Comparator.comparing(f -> {
                if ("product_category".equals(f.getPropNodePath())) {
                    return 0;
                }
                if ("product_subcategory".equals(f.getPropNodePath())) {
                    return 1;
                }
                if ("item_type_keyword".equals(f.getPropNodePath())) {
                    return 2;
                }
                return 3;
            })).collect(Collectors.toList());
        }
        return amazonErrorFieldVOS;
    }


    // Keywords that are informational only and do not require validation.
    static final List<String> nonValidatingKeywords = Arrays.asList("editable", "enumNames", "hidden", "minUniqueItems",
            "maxUniqueItems", "selectors", "maxUtf8ByteLength", "$lifecycle");

    /**
     * 校验json
     *
     * @param metaSource
     * @param schema
     * @param amazonJsonFieldMap
     * @param productType
     * @param site
     * @return
     * @throws URISyntaxException
     */
    public List<AmazonErrorFieldVO> doValidateJson(String metaSource, String schema, Map<String, Object> amazonJsonFieldMap, String productType, String site, String vcFlag) throws URISyntaxException {
        // Standard JSON Schema 2019-09 that Amazon Product Type Definition Meta-Schema extends from.
        JsonMetaSchema standardMetaSchema = JsonMetaSchema.getV201909();

        List<AmCategoryTemplateSmcMapping> mappings = amCategoryTemplateSmcMappingService.listMapping(null, site, vcFlag);
        Set<String> mappingFields = new HashSet<>();
        for (AmCategoryTemplateSmcMapping mapping : mappings) {
            // 取第一个.之前的字符
            String propNodePath = mapping.getPropNodePath();
            String[] split = propNodePath.split("\\.");
            mappingFields.add(split[0]);
        }

        // Build Amazon Product Type Definition Meta Schema with the standard JSON Schema 2019-09 as the blueprint.
        // Register custom keyword validation classes (see below).
        JsonMetaSchema metaSchema = JsonMetaSchema.builder(schemaId, standardMetaSchema)
                .addKeywords(nonValidatingKeywords.stream().map(NonValidationKeyword::new)
                        .collect(Collectors.toSet()))
                .build();


                // Build the JsonSchemaFactory.
        JsonSchemaFactory schemaFactory = new JsonSchemaFactory.Builder()
                .defaultMetaSchemaIri(schemaId)
                .addMetaSchema(standardMetaSchema)
                .addMetaSchema(metaSchema)
                .schemaLoaders(schemaLoaders -> new CustomUriFetcher(metaSource))
                .build();

        // Create the JsonSchema instance.
        JsonSchema luggageSchema = schemaFactory.getSchema(schema);

        // Create a JsonNode for the payload (this can be constructed in code or read from a file).
        JsonNode payload = new ObjectMapper().convertValue(amazonJsonFieldMap, JsonNode.class);

        // Validate the payload and get any resulting validation messages.
        Set<ValidationMessage> messages = luggageSchema.validate(payload);
        return parseValidMsg(messages, mappingFields);
    }


    /**
     * 解析校验信息
     * @param messages
     * @param mappingFields
     * @return
     */
    private List<AmazonErrorFieldVO> parseValidMsg(Set<ValidationMessage> messages, Set<String> mappingFields) {
        List<AmazonErrorFieldVO> errorFieldVOS = new ArrayList<>();
        Set<String> alreadSet = new HashSet<>(mappingFields);
        // required,enumError,notExist,countError
        List<String> typeNotMatch = messages.stream().filter(m -> ValidatorTypeCode.TYPE.getErrorCode().equals(m.getCode()))
                .map(m -> m.getInstanceLocation().toString()).collect(Collectors.toList());
        for (ValidationMessage message : messages) {
            if(ValidatorTypeCode.ONE_OF.getErrorCode().equals(message.getCode())) {
                continue;
            }
            String propNodePath = parseField(message.getInstanceLocation());
            if(containField(alreadSet, message.getProperty()) || containField(alreadSet, propNodePath)) {
                continue;
            }
            if(ValidatorTypeCode.MIN_LENGTH.getErrorCode().equals(message.getCode())) {
                if(StringUtils.isNotBlank(message.getInstanceNode().asText())) {
                    continue;
                }
                AmazonErrorFieldVO errorFieLd = new AmazonErrorFieldVO();
                errorFieLd.setType("part-required");
                errorFieLd.setPropNodePath(propNodePath);
                errorFieldVOS.add(errorFieLd);
                alreadSet.add(propNodePath);
            }
            // 类型不符
            if(ValidatorTypeCode.TYPE.getErrorCode().equals(message.getCode())) {
                // 有值不校验
                if(StringUtils.isNotBlank(message.getInstanceNode().asText())) {
                    continue;
                }
                // 没有值认为是必填项为空
                AmazonErrorFieldVO errorFieLd = new AmazonErrorFieldVO();
                errorFieLd.setType("part-required");
                errorFieLd.setPropNodePath(propNodePath);
                errorFieldVOS.add(errorFieLd);
                alreadSet.add(propNodePath);
            }

            if(ValidatorTypeCode.ADDITIONAL_PROPERTIES.getErrorCode().equals(message.getCode())) {
                AmazonErrorFieldVO errorFieLd = new AmazonErrorFieldVO();
                errorFieLd.setType("notExist");
                errorFieLd.setPropNodePath(message.getProperty());
                errorFieldVOS.add(errorFieLd);
                alreadSet.add(message.getProperty());
            }

            if(ValidatorTypeCode.ENUM.getErrorCode().equals(message.getCode())) {
                if(typeNotMatch.contains(message.getInstanceLocation().toString())) {
                    continue;
                }

                if(StringUtils.isBlank(message.getInstanceNode().asText())) {
                    AmazonErrorFieldVO errorFieLd = new AmazonErrorFieldVO();
                    // 部分属性必填，走全路径匹配，required走模糊匹配
                    errorFieLd.setType("part-required");
                    errorFieLd.setPropNodePath(propNodePath);
                    errorFieldVOS.add(errorFieLd);
                }else {
                    AmazonErrorFieldVO errorFieLd = new AmazonErrorFieldVO();
                    errorFieLd.setType("enumError");
                    errorFieLd.setPropNodePath(propNodePath);
//                    ArrayNode schemaNode = (ArrayNode) message.getSchemaNode();
//                    List<String> enumValues = new ArrayList<>();
//                    for (JsonNode jsonNode : schemaNode) {
//                        enumValues.add(jsonNode.asText());
//                    }
                    errorFieLd.setRecommendedValue(message.getSchemaNode().toString());
                    errorFieldVOS.add(errorFieLd);
                }
                alreadSet.add(propNodePath);
                continue;
            }
            if(ValidatorTypeCode.REQUIRED.getErrorCode().equals(message.getCode())) {
                alreadSet.add(message.getProperty());

                AmazonErrorFieldVO errorFieLd = new AmazonErrorFieldVO();
                errorFieLd.setType("required");
                errorFieLd.setPropNodePath(message.getProperty());
                errorFieldVOS.add(errorFieLd);
            }
            if (ValidatorTypeCode.MIN_ITEMS.getErrorCode().equals(message.getCode())) {
                AmazonErrorFieldVO errorFieLd = new AmazonErrorFieldVO();
                errorFieLd.setType("countError");
                errorFieLd.setPropNodePath(propNodePath);
                errorFieLd.setMinItems((Integer) message.getArguments()[0]);
                errorFieLd.setNowItems((Integer) message.getArguments()[1]);
                errorFieldVOS.add(errorFieLd);
            }
        }
        return errorFieldVOS;
    }

    private boolean containField(Set<String> alreadSet, String property) {
        if (StringUtils.isBlank(property)) {
            return false;
        }
        for (String s : alreadSet) {
            if (s.contains(property) || property.contains(s)) {
                return true;
            }
        }
        return false;
    }

    private String parseField(JsonNodePath nodePath) {
        // 使用反射访问pathSegment的值
        Field pathSegment = ReflectionUtils.findField(nodePath.getClass(), "pathSegment");
        pathSegment.setAccessible(true);
        Object value = ReflectionUtils.getField(pathSegment, nodePath);
        value = value == null ? "" : value;
        while (nodePath.getParent() != null) {
            nodePath = nodePath.getParent();
            pathSegment = ReflectionUtils.findField(nodePath.getClass(), "pathSegment");
            pathSegment.setAccessible(true);
            Object target = ReflectionUtils.getField(pathSegment, nodePath);
            if (target != null) {
                value = target + "." + value;
            }

        }
        return value.toString();
    }


    private static class CustomUriFetcher implements SchemaLoader {
        String metaSchemaPath;
        public CustomUriFetcher(String metaSchemaPath) {
            this.metaSchemaPath = metaSchemaPath;
        }
        @Override
        public InputStreamSource getSchema(AbsoluteIri absoluteIri) {
            UriSchemaLoader schemaLoader = new UriSchemaLoader();
            InputStreamSource inputStreamSource = schemaLoader.getSchema(AbsoluteIri.of(metaSchemaPath));
            return inputStreamSource;
        }
    }

    @GetMapping("/chooseListing/{site}/{categoryId}/{shopCode}")
    public String chooseListing(@PathVariable("site") String site, @PathVariable("categoryId") Integer categoryId, @PathVariable("shopCode") String shopCode, ModelMap mmap) {
        mmap.put("siteCode", site);
        mmap.put("categoryId", categoryId);
        mmap.put("shopCode", shopCode);
        return prefix + "/chooseListing";
    }



    @GetMapping("/removeCache")
    @ResponseBody
    public AjaxResult removeCache() {
        try {
            listingInfoService.clearCache();
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 检查重复ASIN
     * 检查是否存在相同ASIN的其他链接
     *
     * @param currentHeadIds 当前商品ID（排除此ID）
     * @return 相同ASIN的其他链接列表
     */
    @PostMapping("/checkDuplicateAsin")
    @ResponseBody
    public AjaxResult checkDuplicateAsin(@RequestBody List<Long> currentHeadIds) {
        if (CollUtil.isEmpty(currentHeadIds)) {
            return AjaxResult.success(new ArrayList<>());
        }
        List<GoodsHead> allOtherListings = new ArrayList<>();
        try {
            for (Long currentHeadId : currentHeadIds) {
                GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(currentHeadId.intValue());
                String asin = goodsHead.getPlatformGoodsId();
                if (StrUtil.isBlank(asin)) {
                    continue;
                }
                // 根据platform_goods_id(ASIN)查找其他链接，排除当前链接
                List<GoodsHead> otherListings = goodsHeadService.selectOtherListingsByAsin(asin, currentHeadId);
                allOtherListings.addAll(otherListings);
            }
            // 从allOtherListings中移除currentHeadIds
            allOtherListings.removeIf(goodsHead -> currentHeadIds.contains(goodsHead.getId().longValue()));
            return AjaxResult.success(allOtherListings);
        } catch (Exception e) {
            logger.error("检查重复ASIN时出错", e);
            return AjaxResult.error("检查重复ASIN时出错");
        }
    }

}
