package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.google.common.collect.Lists;
import com.suncent.smc.common.core.domain.BaseEntity;
import com.suncent.smc.common.enums.GoodsTaskTypeEnum;
import com.suncent.smc.common.utils.thread.FutureUtils;
import com.suncent.smc.common.utils.thread.LogErrorAction;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.GoodsResource;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.persistence.publication.service.IGoodsResourceService;
import com.suncent.smc.provider.biz.publication.dto.AmazonPushDTO;
import com.suncent.smc.provider.biz.image.ImageHandleBiz;
import com.suncent.smc.system.service.ISysConfigService;
import com.suncent.smc.provider.service.impl.RateLimiterManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmazonProductTaskBiz {

    @Autowired
    private AmazonProductBiz amazonProductBiz;

    @Autowired
    private IPlatformCategoryService platformCategoryService;
    
    @Autowired
    private IGoodsHeadService goodsHeadService;
    
    @Autowired
    private ThreadPoolForMonitorManager threadPoolForMonitorManager;
    
    @Autowired
    private RateLimiterManager rateLimiterManager;

    @Autowired
    private IGoodsResourceService goodsResourceService;

    @Autowired
    private ImageHandleBiz imageHandleBiz;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private PipelineConfigManager pipelineConfigManager;

    /**
     * 上传商品信息，单店铺多线程
     *
     * @param amazonPushDTO 推送DTO
     */
    public void uploadGoodsHeads(AmazonPushDTO amazonPushDTO) {
        List<GoodsHead> goodsHeadVOS = amazonPushDTO.getGoodsHeadVOS();
        if (CollUtil.isEmpty(goodsHeadVOS)) {
            return;
        }

        // 验证和记录配置信息
        pipelineConfigManager.validateConfig();

        try {
            uploadGoodsHeadsPipeline(amazonPushDTO);
        } catch (Exception e) {
            log.error("店铺:{}流水线处理失败", amazonPushDTO.getShopCode(), e);
        }
    }
 
    /**
     * 流水线并行处理商品上传
     * 图片上传和商品刊登异步并行处理
     * 优先处理无需换链的链接，然后等待换链完成
     *
     * @param amazonPushDTO 推送DTO
     */
    public void uploadGoodsHeadsPipeline(AmazonPushDTO amazonPushDTO) {
        List<GoodsHead> goodsHeadVOS = amazonPushDTO.getGoodsHeadVOS();
        if (CollUtil.isEmpty(goodsHeadVOS)) {
            return;
        }

        String operationType = amazonPushDTO.getOperationType();
        long startTime = System.currentTimeMillis();

        // 获取配置
        boolean isMonitoringEnabled = pipelineConfigManager.isMonitoringEnabled();
        int batchSize = pipelineConfigManager.getBatchSize();

        if (isMonitoringEnabled) {
            log.info("=== 店铺:{}，开始流水线并行处理亚马逊商品 ===", amazonPushDTO.getShopCode());
            log.info("店铺:{}，操作类型: {}, 总商品数量: {}, 批量大小: {}, 开始时间: {}",
                    amazonPushDTO.getShopCode(), operationType, goodsHeadVOS.size(), batchSize, new java.util.Date());
        }

        // 按图片上传需求分组处理
        Map<Boolean, List<GoodsHead>> groupedGoods = groupGoodsByImageUploadRequirement(goodsHeadVOS);

        List<GoodsHead> noImageUploadGoods = groupedGoods.getOrDefault(false, new ArrayList<>());
        List<GoodsHead> needImageUploadGoods = groupedGoods.getOrDefault(true, new ArrayList<>());

        log.info("=== 店铺:{}，商品分组完成，准备并行处理 ===", amazonPushDTO.getShopCode());
        log.info("快速通道（无需换链）: {}个商品", noImageUploadGoods.size());
        log.info("换链通道（需要换链）: {}个商品", needImageUploadGoods.size());
        // 先处理无需换链商品的快速刊登（如果有的话），减少这部分链接的等待实际，一般属于跟卖场景的链接
        if (!noImageUploadGoods.isEmpty()) {
            log.info("=== 店铺:{}，启动快速通道：处理无需换链的商品 ===", amazonPushDTO.getShopCode());
            long fastChannelStartTime = System.currentTimeMillis();
            try {
                processGoodsGroup(noImageUploadGoods, operationType, isMonitoringEnabled, batchSize, "快速通道");
            }catch (Exception e) {
                log.error("店铺:{}快速通道处理异常", amazonPushDTO.getShopCode(), e);
            }
            long fastChannelTime = System.currentTimeMillis() - fastChannelStartTime;
            log.info("快速通道处理完成，耗时:{}ms", fastChannelTime);
        }

        // 再启动需要换链商品的处理（如果有的话）
        if (!needImageUploadGoods.isEmpty()) {
            log.info("=== 店铺:{}，启动换链通道：处理需要换链的商品 ===", amazonPushDTO.getShopCode());
            long imageChannelStartTime = System.currentTimeMillis();
            try {
                processGoodsGroup(needImageUploadGoods, operationType, isMonitoringEnabled, batchSize, "换链通道");
            }catch (Exception e) {
                log.error("店铺:{}换链通道处理异常", amazonPushDTO.getShopCode(), e);
            }
            long imageChannelTime = System.currentTimeMillis() - imageChannelStartTime;
            log.info("换链通道处理完成，耗时:{}ms", imageChannelTime);
        }
        log.info("店铺:{}，所有亚马逊商品流水线处理已提交，操作类型:{}，总商品数量:{}，耗时:{}ms", amazonPushDTO.getShopCode(), operationType, goodsHeadVOS.size(), System.currentTimeMillis() - startTime);
    }

    /**
     * 按图片上传需求分组商品
     *
     * @param goodsHeadList 商品列表
     * @return Map<Boolean, List<GoodsHead>> - key: true表示需要图片上传，false表示无需图片上传
     */
    private Map<Boolean, List<GoodsHead>> groupGoodsByImageUploadRequirement(List<GoodsHead> goodsHeadList) {
        Map<Boolean, List<GoodsHead>> groupedGoods = new HashMap<>();

        for (GoodsHead goodsHead : goodsHeadList) {
            try {
                // 获取商品图片资源
                List<GoodsResource> goodsResourceList = getGoodsResourceList(goodsHead);
                // 过滤需要上传的图片
                List<GoodsResource> imagesToUpload = filterImagesToUpload(goodsResourceList);

                // 根据是否有图片需要上传进行分组
                boolean needImageUpload = !imagesToUpload.isEmpty();
                groupedGoods.computeIfAbsent(needImageUpload, k -> new ArrayList<>()).add(goodsHead);

            } catch (Exception e) {
                log.error("分组商品时获取图片资源失败，商品ID:{}，默认归入需要换链组", goodsHead.getId(), e);
                // 出错时默认归入需要图片上传组，确保安全处理
                groupedGoods.computeIfAbsent(true, k -> new ArrayList<>()).add(goodsHead);
            }
        }

        return groupedGoods;
    }

    /**
     * 处理商品分组
     *
     * @param goodsList 商品列表
     * @param operationType 操作类型
     * @param isMonitoringEnabled 是否启用监控
     * @param batchSize 批次大小
     * @param groupName 分组名称（用于日志）
     */
    private void processGoodsGroup(List<GoodsHead> goodsList, String operationType,
                                  boolean isMonitoringEnabled, int batchSize, String groupName) {
        if (CollUtil.isEmpty(goodsList)) {
            return;
        }

        // 按修改时间排序，最旧的在前面，最新的在后面
        goodsList.sort(Comparator.comparing(GoodsHead::getUpdateTime));

        long groupStartTime = System.currentTimeMillis();

        // 统计图片数量（仅在监控启用时执行）
        int totalImageCount = 0;
        if (isMonitoringEnabled) {
            for (GoodsHead goodsHead : goodsList) {
                List<GoodsResource> resources = getGoodsResourceList(goodsHead);
                List<GoodsResource> imagesToUpload = filterImagesToUpload(resources);
                totalImageCount += imagesToUpload.size();
            }
            log.info("{}组 - 商品数量: {}, 预计需要上传图片总数: {}", groupName, goodsList.size(), totalImageCount);
        }

        // 分批并发处理商品，避免一次性创建过多任务
        final int CONCURRENT_BATCH_SIZE = batchSize; // 每批最多100个商品并发执行

        log.info("{}组开始分批并发处理，商品总数: {}，每批并发数: {}", groupName, goodsList.size(), CONCURRENT_BATCH_SIZE);

        ThreadPoolExecutor shopConcurrentExecutor = threadPoolForMonitorManager.getThreadPoolExecutor("amazon-listing-shop-concurrent");
        final Semaphore selectedSemaphore =  new Semaphore(1);

        // 将商品列表分批处理
        List<List<GoodsHead>> batches = Lists.partition(goodsList, CONCURRENT_BATCH_SIZE);
        int processedCount = 0;

        log.info("{}组已分批，共{}批，每批最多{}个商品并发执行", groupName, batches.size(), CONCURRENT_BATCH_SIZE);

        // 逐批处理，每批内部并发执行
        for (List<GoodsHead> batch : batches) {
            CompletableFuture<Void> productTask = CompletableFuture.runAsync(() -> {
                try {
                    selectedSemaphore.acquire();

                    for (GoodsHead goodsHead : batch) {
                        try {
                            processSingleProductPipeline(goodsHead, operationType);

                            if (isMonitoringEnabled) {
                                log.info("{}组 - 商品ID:{} 处理完成", groupName, goodsHead.getId());
                            }
                        } catch (Exception ex) {
                            log.error("{}组 - 流水线处理失败，将使用传统方式处理", groupName, ex);
                            // 降级处理
                            try {
                                executeProductListing(goodsHead, operationType);
                            } catch (Exception fallbackException) {
                                log.error("{}组 - 商品ID:{} 降级处理也失败", groupName, goodsHead.getId(), fallbackException);
                            }
                        }

                    }
                } catch (Exception e) {
                    log.error("{}组 - 本批处理异常", groupName, e);
                } finally {
                    selectedSemaphore.release();
                }
            }, shopConcurrentExecutor);

            try {
                // 设置批次超时时间（每批次最多2小时）
                FutureUtils.orTimeout(productTask, 2, TimeUnit.HOURS).join();

                processedCount += batch.size();

                if (isMonitoringEnabled) {
                    log.info("{}组 -本批商品数: {}，累计处理: {}/{}",
                        groupName, batch.size(), processedCount, goodsList.size());
                }

            } catch (Exception e) {
                // 取消当前批次未完成的任务
                productTask.cancel(true);
                log.error("{}组 - 本批处理异常", groupName, e);
            }
        }

        log.info("{}组所有批次处理完成，总商品数: {}，实际处理数: {}", groupName, goodsList.size(), processedCount);

        long groupTime = System.currentTimeMillis() - groupStartTime;
        if (isMonitoringEnabled) {
            log.info("{}组处理完成 - 商品数量: {}, 耗时: {}ms, 预计图片数: {}",
                    groupName, goodsList.size(), groupTime, totalImageCount);
        }
    }

    /**
     * 单个商品的流水线处理（使用CompletableFuture.allOf()替代协调器）
     *
     * @param goodsHead 商品信息
     * @param operationType 操作类型
     */
    private void processSingleProductPipeline(GoodsHead goodsHead, String operationType) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("商品ID:{} 开始流水线处理，时间:{}", goodsHead.getId(), startTime);

            // 1. 获取商品的图片资源
            List<GoodsResource> goodsResourceList = getGoodsResourceList(goodsHead);

            // 2. 过滤需要上传的图片
            List<GoodsResource> imagesToUpload = filterImagesToUpload(goodsResourceList);

            String useAplusUpload = configService.selectConfigByKey("use_aplus_upload");

            if (imagesToUpload.isEmpty() || ObjUtil.equals(useAplusUpload,"false")) {
                // 无图片需要上传，直接执行商品刊登
                log.info("商品ID:{} 无图片需要上传，直接执行刊登", goodsHead.getId());
                executeProductListing(goodsHead, operationType);
                return;
            }

            // 3. 创建所有图片上传任务
            List<CompletableFuture<Void>> imageFutures = imagesToUpload.stream()
                    .map(image -> {
                        try {
                            return imageHandleBiz.uploadImageAsync(image, goodsHead);
                        } catch (Exception e) {
                            // 不抛出异常，而是返回一个失败的CompletableFuture<Void>
                            // 这样单个图片失败不会影响其他图片的处理
                            log.error("创建图片上传任务失败，商品ID:{}，图片ID:{}", goodsHead.getId(), image.getId(), e);
                            CompletableFuture<Void> failedFuture = new CompletableFuture<>();
                            failedFuture.completeExceptionally(e);
                            return failedFuture;
                        }
                    })
                    .collect(Collectors.toList());

            int timeoutMinutes = pipelineConfigManager.getTimeoutMinutes();

            // 4. 等待所有图片上传完成后执行商品刊登（异步，不阻塞）
            long beforeSequence = System.currentTimeMillis();
            FutureUtils.orTimeout(FutureUtils.sequence(imageFutures), timeoutMinutes, TimeUnit.MINUTES)
                    .whenComplete((result, throwable) -> {
                        long completionTime = System.currentTimeMillis();
                        if (throwable != null) {
                            log.error("商品ID:{} 图片上传异常，继续执行刊登: {}，完成时间:{}",
                                    goodsHead.getId(), throwable.getMessage(), completionTime);
                        } else {
                            log.info("商品ID:{} 所有图片上传完成，开始刊登，完成时间:{}", goodsHead.getId(), completionTime);
                        }
                        executeProductListing(goodsHead, operationType);
                    });

            long afterSequence = System.currentTimeMillis();
            log.info("商品ID:{} sequence调用耗时:{}ms (应该很短)", goodsHead.getId(), afterSequence - beforeSequence);

            long endTime = System.currentTimeMillis();
            log.info("商品ID:{} 已启动流水线处理，图片数量:{}，方法耗时:{}ms，当前活跃线程数:{}",
                    goodsHead.getId(), imagesToUpload.size(), endTime - startTime,
                    threadPoolForMonitorManager.getThreadPoolExecutor("amazon-pipeline-listing").getActiveCount());

        } catch (Exception e) {
            log.error("商品ID:{} 流水线处理失败", goodsHead.getId(), e);
            // 失败时直接执行商品刊登
            executeProductListing(goodsHead, operationType);
        }
    }

    private String process(Integer goodsHeadId, String operationType) {
        try {
            log.info("goodsHeadId:{}, operationType:{}", goodsHeadId, operationType);
            GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(goodsHeadId);
            PlatformCategory categoryInfo = platformCategoryService.selectPlatformCategoryById(goodsHead.getCategoryId().longValue());
            AmazonPushDTO amazonPushDTO = AmazonPushDTO.builder()
                    .shopCode(goodsHead.getShopCode())
                    .goodsHeadVOS(Collections.singletonList(goodsHead))
                    .operationType(operationType)
                    .goodsTaskTypes(goodsTaskTypes)
                    .categoryInfo(categoryInfo)
                    .build();
            
            // 直接执行商品上传，外层已有资源保护
            amazonProductBiz.uploadGoodsHeads(amazonPushDTO);
            return goodsHeadId + "_" + operationType;
        } catch (Exception e) {
            log.error("process error", e);
            return goodsHeadId + "_" + operationType;
        }
    }

    static List<GoodsTaskTypeEnum> goodsTaskTypes=new ArrayList<>();
    static {
        goodsTaskTypes.add(GoodsTaskTypeEnum.FIND_REPLACEMENT_UPDATES);
        goodsTaskTypes.add(GoodsTaskTypeEnum.TIMING_PUBLISH);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_MODIFY);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_EDIT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELISTING);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELETE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_IMPORT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.CIRCULATE_TIMED_TASK);
        goodsTaskTypes.add(GoodsTaskTypeEnum.ONE_KEY_FOLLOW);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS);
        goodsTaskTypes.add(GoodsTaskTypeEnum.TODO_TEXT_UPDATE);
    }

    /**
     * 获取商品的图片资源列表
     *
     * @param goodsHead 商品信息
     * @return 图片资源列表
     */
    private List<GoodsResource> getGoodsResourceList(GoodsHead goodsHead) {
        return goodsResourceService.selectListingGoodsResourceByHeadId(goodsHead.getId());
    }

    /**
     * 过滤需要上传的图片
     *
     * @param goodsResourceList 原始图片资源列表
     * @return 需要上传的图片列表
     */
    private List<GoodsResource> filterImagesToUpload(List<GoodsResource> goodsResourceList) {
        if (goodsResourceList == null || goodsResourceList.isEmpty()) {
            return new ArrayList<>();
        }

        // 过滤出需要上传的图片（非空且不是已经上传的亚马逊图片）
        return goodsResourceList.stream()
                .filter(resource -> resource.getResourceUrl() != null
                        && !resource.getResourceUrl().trim().isEmpty()
                        && !resource.getResourceUrl().startsWith("https://m.media-amazon.com/"))
                .collect(Collectors.toList());
    }





    /**
     * 执行商品刊登
     *
     * @param goodsHead 商品信息
     * @param operationType 操作类型
     */
    private void executeProductListing(GoodsHead goodsHead, String operationType) {
        try {
            log.info("开始执行商品刊登，商品ID:{}，操作类型:{}", goodsHead.getId(), operationType);

            // 获取分类信息
            PlatformCategory categoryInfo = platformCategoryService.selectPlatformCategoryById(goodsHead.getCategoryId().longValue());

            // 构建推送DTO
            AmazonPushDTO amazonPushDTO = AmazonPushDTO.builder()
                    .shopCode(goodsHead.getShopCode())
                    .goodsHeadVOS(Collections.singletonList(goodsHead))
                    .operationType(operationType)
                    .goodsTaskTypes(goodsTaskTypes)
                    .categoryInfo(categoryInfo)
                    .build();

            // 根据操作类型选择线程池
            String rateLimiterName = Arrays.asList("上架", "跟卖").contains(operationType) ? "amazon-push-scheduled-upload" : "amazon-push-scheduled-update";

            // 使用速率限制器执行商品刊登
            rateLimiterManager.executeWithSharedRateLimit(rateLimiterName + goodsHead.getShopCode(), rateLimiterName, () -> {
                amazonProductBiz.uploadGoodsHeads(amazonPushDTO);
                return null;
            });

            log.info("商品刊登完成，商品ID:{}，操作类型:{}", goodsHead.getId(), operationType);

        } catch (Exception e) {
            log.error("商品刊登失败，商品ID:{}，操作类型:{}", goodsHead.getId(), operationType, e);

            // 标记商品处理失败
            try {
                GoodsHead updateGoodsHead = new GoodsHead();
                updateGoodsHead.setId(goodsHead.getId());
                updateGoodsHead.setPublishingHandler("处理失败");
                goodsHeadService.updateListingGoodsHead(updateGoodsHead);
            } catch (Exception ex) {
                log.error("标记商品处理失败时出错，商品ID:{}", goodsHead.getId(), ex);
            }
        }
    }
}
