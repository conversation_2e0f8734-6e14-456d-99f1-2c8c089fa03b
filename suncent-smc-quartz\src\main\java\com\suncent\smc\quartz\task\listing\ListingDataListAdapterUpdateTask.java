package com.suncent.smc.quartz.task.listing;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.persistence.ads.domain.FitmentDemand;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.entity.Goods;
import com.suncent.smc.persistence.pdm.service.IGoodsService;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.provider.biz.publication.PDMHttpRequestBiz;
import com.suncent.smc.provider.biz.publication.service.impl.AmazonPlatformListingServiceImpl;
import com.suncent.smc.provider.biz.publication.service.impl.EbayPlatformListingServiceImpl;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 扫描数据组sku清单 扫到对应的数据 做不同的处理
 * <AUTHOR>
 */
@Component
@Slf4j
public class ListingDataListAdapterUpdateTask {

    @Autowired
    private IAdsService adsService;
    @Autowired
    private IGoodsHeadService goodsHeadService;
    @Autowired
    private AmazonPlatformListingServiceImpl amazonPlatformListingService;
    @Autowired
    @Qualifier("ebayPlatformListingServiceImpl")
    private EbayPlatformListingServiceImpl ebayPlatformListingService;
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private PDMHttpRequestBiz pdmHttpRequestBiz;

    @XxlJob("ListingDataListAdapterUpdateTask")
    public void listingDataListAdapterUpdateTask() {
        log.info("ListingDataListAdapterUpdateTask开始执行");
        long startTime = System.currentTimeMillis();

        // 定义批处理大小
        final int BATCH_SIZE = 100;

        // 创建线程池
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                4, 8, 60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                new ThreadPoolExecutor.CallerRunsPolicy());

        try {
            // 处理类型1：SKU+ASIN+PN
            processByType(1, BATCH_SIZE, executor);

            // 处理类型2：SKU+ASIN
            processByType(2, BATCH_SIZE, executor);

            // 处理类型3：仅SKU
            processByType(3, BATCH_SIZE, executor);

            // 等待所有任务完成
            executor.shutdown();
            executor.awaitTermination(30, TimeUnit.MINUTES);

            log.info("ListingDataListAdapterUpdateTask执行完成，耗时：{}ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("ListingDataListAdapterUpdateTask执行异常", e);
        } finally {
            if (!executor.isShutdown()) {
                executor.shutdownNow();
            }
        }
    }


    /**
     * 核心处理逻辑
     * @param heads
     * @param pnStr
     */
    public void handleHeadList(List<GoodsHead> heads,String pnStr){
        if (CollUtil.isEmpty(heads)){
            return;
        }

        Map<String, List<GoodsHead>> collect = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform));
        for (Map.Entry<String, List<GoodsHead>> entry : collect.entrySet()) {
            List<GoodsHead> headList = entry.getValue();
            if (CollUtil.isEmpty(headList)){
                continue;
            }

            //适配更新  -- AM
            if (ObjUtil.equals(PlatformTypeEnum.AM.name(), entry.getKey())){
                Map<String, Goods> pdmGoodsMap = getPdmGoodsMap(headList);
                if (ObjUtil.isEmpty(pdmGoodsMap)){
                    continue;
                }
                refreshAMAdapter(pnStr, headList, pdmGoodsMap);
            }

            //适配更新  -- EB
            if (ObjUtil.equals(PlatformTypeEnum.EB.name(), entry.getKey())){
                ebayPlatformListingService.refreshEbAdapter(headList);
            }
        }
    }

    private void refreshAMAdapter(String pnStr, List<GoodsHead> headList, Map<String, Goods> pdmGoodsMap) {
        for (GoodsHead head : headList) {
            //pdm没有这个sku
            if (ObjUtil.isEmpty(pdmGoodsMap) || !pdmGoodsMap.containsKey(head.getPdmGoodsCode())) {
                continue;
            }
            Goods goods = pdmGoodsMap.get(head.getPdmGoodsCode());

            //有未处理完数据  就不重复处理了
            int i = adsService.countByPnAndAsin(head.getPlatformGoodsId());
            if (i >= 1) {
                continue;
            }

            //刷新PN处理
            amazonPlatformListingService.refreshPnCode(-1L,head,goods, pnStr, StrUtil.isBlank(pnStr) ? "1" : "0");
        }
    }

    private Map<String, Goods> getPdmGoodsMap(List<GoodsHead> headList) {
        List<String> pdmGoodCodes = headList.stream().map(f -> f.getPdmGoodsCode()).distinct().collect(Collectors.toList());
        SaleGoodsDTO query = new SaleGoodsDTO();
        query.setGoodCodeList(pdmGoodCodes);
        List<Goods> goods = goodsService.selectGoodsByGoodCodeList(query);
        if (CollUtil.isEmpty(goods)){
            return new HashMap<>();
        }
        Map<String, Goods> pdmGoodsMap = goods.stream().collect(Collectors.toMap(e -> e.getGoodsCode(), e -> e));
        return pdmGoodsMap;
    }


    public String getPlatformCode(String platform){
        if (ObjUtil.equals(PlatformTypeEnum.AM.name(), platform)){
            return PlatformTypeEnum.AM.name();
        }
        if (ObjUtil.equals(PlatformTypeEnum.EB.name(), platform)){
            return PlatformTypeEnum.EB.name();
        }
        return null;
    }

    /**
     * 按类型分批处理数据
     *
     * @param type      数据类型：1-SKU+ASIN+PN, 2-SKU+ASIN, 3-仅SKU
     * @param batchSize 批处理大小
     * @param executor  线程池
     */
    private void processByType(int type, int batchSize, ThreadPoolExecutor executor) {
        int offset = 0;
        List<FitmentDemand> batch;

        do {
            // 分页查询指定类型的数据
            batch = adsService.selectFitmentDemandListByType(type, offset, batchSize);

            if (CollUtil.isNotEmpty(batch)) {
                // 创建一个新的批次列表，避免线程间共享同一个列表
                final List<FitmentDemand> currentBatch = new ArrayList<>(batch);

                // 提交到线程池处理
                executor.submit(() -> processBatch(type, currentBatch));

                offset += batchSize;
            }
        } while (CollUtil.isNotEmpty(batch) && batch.size() == batchSize);
    }

    /**
     * 处理一个批次的数据
     *
     * @param type  数据类型
     * @param batch 批次数据
     */
    private void processBatch(int type, List<FitmentDemand> batch) {
        try {
            for (FitmentDemand fitmentDemand : batch) {
                try {
                    processOneDemand(type, fitmentDemand);
                } catch (Exception e) {
                    // 单个处理失败不影响整批
                    adsService.updateFitmentDemandStatus("2", fitmentDemand.getId());
                    log.error("处理适配更新失败,paramJSON:{}", JSON.toJSONString(fitmentDemand), e);
                }
            }
        } catch (Exception e) {
            log.error("批量处理适配更新失败,batchSize:{}", batch.size(), e);
        }
    }

    /**
     * 处理单个需求记录
     *
     * @param type          数据类型
     * @param fitmentDemand 需求记录
     */
    private void processOneDemand(int type, FitmentDemand fitmentDemand) {
        GoodsHead head = new GoodsHead();

        // 根据类型设置不同的查询条件
        if (type == 1) {
            // SKU+ASIN+PN
            head.setPlatform(PlatformTypeEnum.AM.name());
            head.setPdmGoodsCode(fitmentDemand.getGoodsCode());
            head.setPlatformGoodsId(fitmentDemand.getAsin());

            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadList(head);
            if (CollUtil.isEmpty(heads)) {
                adsService.updateFitmentDemandStatus("2", fitmentDemand.getId());
                return;
            }

            handleHeadListOptimized(heads, fitmentDemand.getPartNumber());
        } else if (type == 2) {
            // SKU+ASIN
            head.setPlatform(getPlatformCode(fitmentDemand.getPlatformCode()));
            head.setPdmGoodsCode(fitmentDemand.getGoodsCode());
            head.setPlatformGoodsId(fitmentDemand.getAsin());

            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadList(head);
            if (CollUtil.isEmpty(heads)) {
                adsService.updateFitmentDemandStatus("2", fitmentDemand.getId());
                return;
            }

            handleHeadListOptimized(heads, null);
        } else if (type == 3) {
            // 仅SKU
            head.setPlatform(getPlatformCode(fitmentDemand.getPlatformCode()));
            head.setPdmGoodsCode(fitmentDemand.getGoodsCode());

            List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadList(head);
            if (CollUtil.isEmpty(heads)) {
                adsService.updateFitmentDemandStatus("2", fitmentDemand.getId());
                return;
            }

            handleHeadListOptimized(heads, null);
        }

        // 处理成功，更新状态
        adsService.updateFitmentDemandStatus("1", fitmentDemand.getId());

        // 同步至PDM
        pdmHttpRequestBiz.updateGoodsBrandImage(Lists.newArrayList(fitmentDemand.getGoodsCode()));
    }

    /**
     * 优化后的头部列表处理方法
     *
     * @param heads 商品头列表
     * @param pnStr PN字符串
     */
    private void handleHeadListOptimized(List<GoodsHead> heads, String pnStr) {
        // 按平台分组
        Map<String, List<GoodsHead>> collect = heads.stream()
                .collect(Collectors.groupingBy(GoodsHead::getPlatform));

        // 预先获取所有需要的PDM商品信息
        Set<String> allPdmGoodsCodes = heads.stream()
                .map(GoodsHead::getPdmGoodsCode)
                .collect(Collectors.toSet());

        // 批量查询PDM商品信息
        Map<String, Goods> pdmGoodsMap = new HashMap<>();
        if (!allPdmGoodsCodes.isEmpty()) {
            SaleGoodsDTO query = new SaleGoodsDTO();
            query.setGoodCodeList(new ArrayList<>(allPdmGoodsCodes));
            List<Goods> goods = goodsService.selectGoodsByGoodCodeList(query);
            if (CollUtil.isNotEmpty(goods)) {
                pdmGoodsMap = goods.stream()
                        .collect(Collectors.toMap(Goods::getGoodsCode, g -> g, (g1, g2) -> g1));
            }
        }

        // 处理不同平台的商品
        for (Map.Entry<String, List<GoodsHead>> entry : collect.entrySet()) {
            List<GoodsHead> headList = entry.getValue();
            if (CollUtil.isEmpty(headList)) {
                continue;
            }

            // 亚马逊平台处理
            if (ObjUtil.equals(PlatformTypeEnum.AM.name(), entry.getKey())) {
                if (ObjUtil.isEmpty(pdmGoodsMap)) {
                    continue;
                }

                // 批量获取ASIN列表
                List<String> asinList = headList.stream()
                        .map(GoodsHead::getPlatformGoodsId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 批量查询已有的处理记录
                Map<String, Integer> asinCountMap = new HashMap<>();
                if (!asinList.isEmpty()) {
                    asinCountMap = adsService.countByPnAndAsinBatch(asinList);
                }

                // 处理每个商品头
                for (GoodsHead head : headList) {
                    // PDM没有这个SKU
                    if (!pdmGoodsMap.containsKey(head.getPdmGoodsCode())) {
                        continue;
                    }

                    Goods goods = pdmGoodsMap.get(head.getPdmGoodsCode());

                    // 有未处理完数据就不重复处理
                    Integer count = asinCountMap.getOrDefault(head.getPlatformGoodsId(), 0);
                    if (count >= 1) {
                        continue;
                    }

                    // 刷新PN处理
                    amazonPlatformListingService.refreshPnCode(-1L, head, goods, pnStr,
                            StrUtil.isBlank(pnStr) ? "1" : "0");
                }
            }

            // eBay平台处理
            if (ObjUtil.equals(PlatformTypeEnum.EB.name(), entry.getKey())) {
                ebayPlatformListingService.refreshEbAdapter(headList);
            }
        }
    }

}
