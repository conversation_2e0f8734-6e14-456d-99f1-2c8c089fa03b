package com.suncent.smc.persistence.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suncent.smc.common.annotation.DataSource;
import com.suncent.smc.common.enums.DataSourceType;
import com.suncent.smc.persistence.bi.entity.BiAsinRefundRatePj;
import com.suncent.smc.persistence.bi.entity.BiSalesAnalysis;
import com.suncent.smc.persistence.bi.entity.BiSkuRefundRatePj;
import com.suncent.smc.persistence.bi.mapper.BiDataServiceMapper;
import com.suncent.smc.persistence.bi.service.IBiDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/6/28 10:06
 * @Version 1.0
 */
@Service
@DataSource(DataSourceType.BI)
@Slf4j
public class AutoBiDataServiceImpl implements IBiDataService {

    @Autowired
    private BiDataServiceMapper biDataServiceMapper;

 
    /**
     * 获取公司维度sku的退货退款率
     *
     * @return
     * @Date 2023/6/28 10:18
     * <AUTHOR>
     */
    @Override
    public List<Map<String, String>> getCompanyListingDetail() {
        return biDataServiceMapper.getCompanyListingDetail();
    }

    @Override
    public List<Map<String, Objects>> queryFrontSaleTotalByShop(String shopCode,  Long size,List<String> asinList) {
        return biDataServiceMapper.queryFrontSaleTotalByShop(shopCode,size,asinList);
    }
 
    @Override
    public List<Map<String,Objects>> querySaleAndOrderTotal(List<String> asinList, String shopCode) {
        List<Map<String, Objects>> returnList = new ArrayList<>();
        if (CollUtil.isEmpty(asinList)) {
            return returnList;
        }
        // asinList 切片1000个一组
        List<List<String>> asinListList = CollUtil.split(asinList, 1500);
        for (List<String> list : asinListList) {
            List<Map<String, Objects>> maps = biDataServiceMapper.querySaleAndOrderTotal(list, shopCode);
            returnList.addAll(maps);
        }
        return returnList;
    }

    @Override
    public List<BiSkuRefundRatePj> queryTodaySkuRefundRateData() {
        return biDataServiceMapper.queryTodaySkuRefundRateData();
    }

    @Override
    public List<BiAsinRefundRatePj> queryTodayListingRefundRateData() {
        return biDataServiceMapper.queryTodayListingRefundRateData();
    }
 
    @Override
    public List<BiSalesAnalysis> queryTodaySalesAnalysis(String shopCode) {
        // 分页查询
        int pageNum = 1;
        int pageSize = 1000;
        PageHelper.startPage(pageNum, pageSize);
        List<BiSalesAnalysis> list = biDataServiceMapper.queryTodaySalesAnalysis(shopCode);
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        int pages = new PageInfo<>(list).getPages();
        while (pageNum < pages) {
            pageNum++;
            PageHelper.startPage(pageNum, pageSize);
            List<BiSalesAnalysis> pageList = biDataServiceMapper.queryTodaySalesAnalysis(shopCode);
            if (CollUtil.isEmpty(pageList)) {
                break;
            }
            list.addAll(pageList);
        }
        log.info("查询公司维度sku的退货退款率数据完成，共查询{}条数据", list.size());
        return list;
    }

    @Override
    public List<BiSalesAnalysis> queryTodaySalesAnalysisPaged(String shopCode, int offset, int limit) {
        return biDataServiceMapper.queryTodaySalesAnalysisPaged(shopCode, offset, limit);
    }

    @Override
    public BiSalesAnalysis getTodaySalesAnalysisByAsin(String shopCode,String asin) {
        return biDataServiceMapper.getTodaySalesAnalysisByAsin(shopCode,asin);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSource(DataSourceType.BI)
    public List<Map<String, Object>> listStockToSalesRatio(List<String> skuList) {
        return biDataServiceMapper.listStockToSalesRatio(skuList);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSource(DataSourceType.BI)
    public List<Map<String, Object>> getYesterdaySale() {
        return biDataServiceMapper.getYesterdaySale();
    }

    @Override
    public List<String> listAsinSales() {
        return biDataServiceMapper.listAsinSales();
    }

    /**
     * 分批获取未处理的ASIN列表的销量详情
     * @return 销量详情列表
     */
    @Override
    public List<Map<String, String>> getCompanyListingDetailByBatch(List<String> asinList) {
        if (CollUtil.isEmpty(asinList)) {
            return new ArrayList<>();
        }
        
        // 获取销量详情
        return biDataServiceMapper.getCompanyListingDetailByAsinList(asinList);
    }


}
