<!DOCTYPE html>
<html lang="zh">
<head>
    <th:block th:include="include :: header('批量新建Ebay刊登(AI生成)')"/>
    <th:block th:include="include :: jquery-smartwizard-css"/>
    <th:block th:include="include :: select2-css"/>
    <link th:href="@{/css/fancybox.css}" rel="stylesheet"/>
    <style type="text/css">
        /* 如果要让工具栏固定在页面底部,使用下面的样式,不需要的可以注释 */
        .sw > .toolbar-bottom {
            z-index: 100;
            bottom: 0px;
            left: 0;
            width: 100%;
            position: fixed;
            text-align: right;
            background: #fff;
            box-shadow: 0 -2px 6px 1px hsla(223, 8%, 83%, .5);
            border-top: 1px solid #e3e4e8;
        }

        /* 如果设置了是否自动调节高度为false,需要加滚动条 */
        /*.sw > .tab-content {*/
        /*    overflow-x: auto;*/
        /*    overflow-y: auto;*/
        /*    !*height: 800px !important;*!*/
        /*}*/

        /* 解决工具栏无法固定底部的问题（如果页面没有animated类可以不写这部分代码） */
        .animated {
            animation-fill-mode: none;
            -webkit-animation-fill-mode: none;
            -moz-animation-fill-mode: none;
            -o-animation-fill-mode: none;
        }

        #showimg {
            /*display: flex;*/
            justify-content: flex-start;
        }

        #showimg li {
            width: 150px;
            height: 150px;
            position: relative;
            overflow: hidden;
            display: inline-block;
            margin-right: 5px;
        }

        #showimg li img.showimg {
            position: absolute;
            text-align: center;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 6;
        }

        #showimg li:first-child img.left {
            opacity: .6;
            cursor: no-drop;
        }

        #showimg li:last-child img.right {
            opacity: .6;
            cursor: no-drop;
        }

        .showdiv {
            position: absolute;
            z-index: 7;
            bottom: 0;
            width: calc(100% - 20px);
            padding: 10px;
            display: flex;
            justify-content: space-around;
            background: rgba(0, 0, 0, .6);
        }

        .showdiv img {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }


        /* 改进按钮样式 */
        .btn-success {
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4) !important;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(23, 162, 184, 0.3);
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }
    </style>
</head>
<body class="gray-bg">

<div class="wrapper wrapper-content animated fadeInRight" style="height: 100%;">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox">
                <div class="ibox-content">
                    <div class="row select-list" style="padding-left: 15px; margin-bottom: 10px;">
                        <ul style="float: right">
                            <li>
                                <div class="btn-group-sm" role="group">
                                    <a class="btn btn-info" id="prev-btn"> 上一步 </a>
                                    <a class="btn btn-success" id="next-btn"> 下一步 </a>
                                    <a class="btn btn-danger" id="reset-btn"> 重置 </a>
                                </div>
                            </li>
                        </ul>

                    </div>
                    <div id="smartwizard">
                        <ul class="nav">
                            <li class="nav-item">
                                <a class="nav-link" href="#step-1"> 基本信息 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-2"> 价格&库存 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-3"> 属性 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-4"> 包裹信息</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-5"> 长描述模板 </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#step-6"> 图片 </a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div id="step-1" class="tab-pane" role="tabpanel" aria-labelledby="step-1">

                                <div>
                                    <form class="form form-horizontal m-t" th:object="${saleGoodsVO}">
                                        <div class="form-group">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div class="col-sm-3" align="center">
                                                        <a class="btn btn-success" onclick="findCategoryid()">
                                                            <i class="fa fa-plus"></i> 查找分销品类
                                                        </a>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label is-required">站点：</label>
                                                <div class="col-sm-9">
                                                    <select name="siteCode"
                                                            id="siteCode"
                                                            data-placeholder="请选择站点"
                                                            data-tags="true"
                                                            th:field="*{siteCode}"
                                                            class="form-control" >
                                                        <option
                                                            th:each="keyValueEntity:${@siteService.selectSiteAllList()}"
                                                            th:value="${keyValueEntity.key}"
                                                            th:text="${keyValueEntity.value}"></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label font-noraml is-required">店铺：</label>
                                                <div class="col-sm-9">
                                                    <select name="shopCode" id="shopCode"
                                                            class="form-control select2-multiple"
                                                            data-none-selected-text="请选择" required>
                                                        <option value="">请选择</option>

                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">

                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label">刊登类型：</label>
                                                <div class="col-sm-9">
                                                    <div class="radio check-box">
                                                        <label>
                                                            <input type="radio" value="3" name="publishType" checked="">
                                                            <i></i>固定</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label is-required">币种：</label>
                                                <div class="col-sm-9">
                                                    <input type="text" class="form-control"
                                                           name="currency" id="currency" value="USD" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label is-required">品牌：</label>
                                                <div class="col-sm-9">
                                                    <select id="brandCode" name="brandCode" class="form-control m-b"
                                                            data-none-selected-text="请选择品牌"  required>
                                                        <option value="">请选择</option>
                                                        <option
                                                                th:each="keyValueEntity:${@cdpBaseConfig.getBrandAllKVList()}"
                                                                th:value="${keyValueEntity.value}"
                                                                th:text="${keyValueEntity.value}"></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <label class="col-sm-3 control-label is-required">物品所在地：</label>
                                                <div class="col-sm-8">
                                                    <select id="location" class="form-control m-b"
                                                            name="location" required>

                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-sm-12">
                                                <!--                                    平台第一品类-->
                                                <div class="form-group">
                                                    <div class="col-sm-6">
                                                        <label class="col-sm-3 control-label is-required">平台第一品类：</label>
                                                        <div class="col-sm-9">
                                                            <select name="firstCategoryid" class="form-control"
                                                                    id="firstCategoryid"
                                                                    data-none-selected-text="请选择" required>
                                                                <option value="">--请选择平台第一品类--</option>

                                                            </select>
                                                        </div>
                                                        <label class="control-label" id="categoryOneLabel"></label>
                                                        <label class="control-label" id="categoryOneLabelEn"></label>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <label class="col-sm-3 control-label">平台第二品类(收费)：</label>
                                                        <div class="col-sm-9">
                                                            <select name="secondCategoryid" class="form-control"
                                                                    id="secondCategoryid"
                                                                    data-none-selected-text="请选择">
                                                                <option value="">--请选择平台第二品类--</option>

                                                            </select>
                                                        </div>
                                                        <label class="control-label" id="categoryTwoLabel"></label>
                                                        <label class="control-label" id="categoryTwoLabelEn"></label>
                                                    </div>
                                                </div>

                                                <!--                                    商铺第一品类-->
                                                <div class="form-group">
                                                    <div class="col-sm-6">
                                                        <label class="col-sm-3 control-label">店铺第一品类：</label>
                                                        <div class="col-sm-9">
                                                            <input type="text" class="form-control"
                                                                   placeholder="请填写品类名称"
                                                                   name="firstShopCategory">
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <label class="col-sm-3 control-label">店铺第二品类：</label>
                                                        <div class="col-sm-9">
                                                            <input type="text" class="form-control"
                                                                   placeholder="请填写品类名称"
                                                                   name="secondShopCategory">
                                                        </div>
                                                    </div>
                                                </div>
                                                 <!-- 品牌 -->
                                                 <div class="form-group">
                                                     <div class="col-sm-6">
                                                         <label class="col-sm-3 control-label is-required">商品状况：</label>
                                                         <div class="col-sm-9">
                                                             <select name="condition" class="form-control m-b"
                                                                     th:with="type=${@dict.getType('amazon_publication_goods_condition')}">
                                                                 <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                                         th:value="${dict.dictValue}"></option>
                                                             </select>
                                                         </div>
                                                     </div>

                                                 </div>
                                            </div>
                                        </div>


                                    </form>
                                </div>
                            </div>



                            <div id="step-2" class="tab-pane" role="tabpanel" aria-labelledby="step-2">
                                <div>
                                    <div class="form-group">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-2" align="center">
                                                    <a class="btn btn-info" id="analysisPrice"> 价格解析 </a>
                                                </div>
                                                <div class="col-sm-5">
                                                    <textarea type="text" class="form-control analysisPrice"  style="margin-bottom: 3px"  placeholder="excel将平台商品编码按升序排列,再将对应价格列复制过来，即可以一键解析."></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 一键应用功能 -->
                                    <div class="form-group" style="margin-top: 15px;">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-2" align="center">
                                                    <a class="btn btn-primary" id="batchApplyValues">
                                                        <i class="fa fa-magic"></i> 一键应用
                                                    </a>
                                                </div>
                                                <div class="col-sm-2">
                                                    <select class="form-control" id="batchApplyType" data-placeholder="请选择字段类型">
                                                        <option value="">请选择字段类型</option>
                                                        <option value="price">价格</option>
                                                        <option value="stock">库存</option>
                                                        <option value="startSelling">起卖数量</option>
                                                    </select>
                                                </div>
                                                <div class="col-sm-3">
                                                    <input type="text" class="form-control" id="batchApplyValue" placeholder="请输入要应用的数值" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <form class="form form-horizontal m-t">
                                        <div class="ibox-content">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div id="stock-price-data">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div id="step-3" class="tab-pane" role="tabpanel" aria-labelledby="step-3">
                                <!--属性信息-->
                                <form class="form form-horizontal m-t">
                                    <div class="ibox-title">
                                        <h6>属性信息</h6>
                                    </div>
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="btn-group-sm" id="toolbar2" role="group">
                                                <a class="btn btn-success" onclick="addColumn('bootstrap-table')">
                                                    <i class="fa fa-plus"></i> 新增
                                                </a>
                                            </div>

                                            <div class="col-sm-12 select-table table-striped">
                                                <table id="bootstrap-table" data-height="450" data-virtual-scroll="true"></table>
                                            </div>
                                            <div style="height: 30px"></div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div id="step-4" class="tab-pane" role="tabpanel" aria-labelledby="step-4">
                                <div>
                                    <form class="form form-horizontal m-t">
                                        <div class="ibox-content">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div id="logistics-data">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>


                            <div id="step-5" class="tab-pane" role="tabpanel" aria-labelledby="step-6">
                                <div>
                                    <!-- 一键应用描述模板区域 -->
                                    <div class="form-group" style="margin-bottom: 20px;">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="col-sm-2" align="center">
                                                    <a class="btn btn-primary" id="applyDescriptionTemplate">
                                                        <i class="fa fa-copy"></i> 一键应用描述模板
                                                    </a>
                                                </div>
                                                <div class="col-sm-5">
                                                    <select class="form-control" id="batchDescriptionTemplate" data-placeholder="请选择描述模板">
                                                        <option value="">请选择描述模板</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <form class="form form-horizontal m-t">
                                        <div class="ibox-content">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div id="long-desc-data">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div id="step-6" class="tab-pane" role="tabpanel" aria-labelledby="step-7">
                                <div>
                                    <form class="form form-horizontal m-t">
                                        <div class="row">
                                            <div class="col-sm-2" align="center">
                                                <label for="fillImage" class="btn btn-info">一键填充</label>
                                                <input type="file" id="fillImage" style="display: none;" multiple="multiple" />
                                            </div>
                                            <div class="col-sm-2" align="center">
                                                <a class="btn btn-danger" onclick="clearAllImages()"> 一键清除</a>
                                            </div>
                                            <div class="col-sm-12">
                                                <h4 style="color: rgba(246,2,24,0.84)">默认第一张图为主图</h4>
                                            </div>
                                        </div>
                                        <div class="ibox-content">
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <div id="image-data">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: jquery-smartwizard-js"/>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: bootstrap-table-fixed-columns-js" />
<script th:src="@{/js/jquery-ui-1.10.4.min.js}"></script>
<script th:src="@{/extend/js/batch/ebayAddInfoAI.js?version=20250730}"></script>
<script th:src="@{/extend/js/batch/ebayAttributeV2.js?version=20250730}"></script>
<script th:src="@{/extend/js/batch/batchCommon.js?version=2024110701}"></script>
<script th:src="@{/extend/js/ebaySite/ebaySiteCommon.js?version=20231128}"></script>
<script th:src="@{/extend/js/single/ebayCommon.js?version=20240122}"></script>
<script th:src="@{/js/fancybox.umd.js}"></script>
<script th:inline="javascript">
    let prefixRequiredField = ctx + "publication/goods/property";
    let prefix = ctx + "publication/goods";
    var category_prefix = ctx + "configuration/category";
    let saleGoodsDTOList = [[${saleGoodsVO.saleGoodsDTOList}]];
    let siteCode = [[${saleGoodsVO.siteCode}]];
    let platform = 'EB';
    var goodsInfoList;
    var goodsDetails;
    var safetyCompliance;
    var fd; //FormData方式发送请求

    var transmitAttributeArr = new Array();
    //标识类型
    const checkTypeValue = 'add'
    var sourceType = "AI"

    function setDataInfo(data) {
        $('.form').each(function (index, form) {
            // 这里可以使用$.common.formToJSON(formId);  需要在form上加id
            $.each($(form).serializeArray(), function (i, field) {
                if (data[field.name]) {
                    data[field.name] += ("," + field.value);
                } else {
                    data[field.name] = field.value;
                }
            });
        });
        data["platform"] = "EB";
        data['tag'] = "batchSave"
        data['useAI'] = 'Y'
        $.each(saleGoodsDTOList, function (index, item) {
            //取出每个商品的图片
            let imageSrcArr = [];
            let currentImage = $("ul#showui" + index + " li");
            for (let i = 0; i < currentImage.length; i++) {
                let usid = currentImage[i].getElementsByTagName('img')[1];
                let imageSrc = $("#" + usid.id + "").attr("src");
                imageSrcArr.push(imageSrc);
                data["listings[" + index + "].imgArrs[" + i + "]"] = imageSrc;
            }
            //取出每个商品的其他信息
            data["listings[" + index + "].goodsCode"] = item.goodsCode;
        });

        //设置属性
        refreshTransmitAttributeData();
        for (let i = 0; i < transmitAttributeArr.length; i++) {
            data["listingAttributeLine[" + i + "]"] = JSON.stringify(transmitAttributeArr[i]);
        }
    }

    function previewTemplate(index, element) {
        //如果是新模板,则调用新模板的预览方法
        var templateHtml = "";
        // 通过name=listings[index].templateSelect获取模板id
        const templateId = $('[name="listings[' + index + '].descriptionId"]').val();
        const desc = $('[name="listings[' + index + '].detailDescription"]').val();
        const title = $('[name="listings[' + index + '].itemTitle"]').val();
        templateHtml = renderTemplateHtmlNew(index, templateId, desc, title);

        var win = window.open();
        win.document.write(templateHtml);
        win.document.close();
    }

    function renderTemplateHtmlNew(index, templateId, desc, title) {
        console.log(index, templateId, desc);
        //获取图片信息
        let imageSrcArr = [];
        for (let i = 0; i < $("ul#showui li").length; i++) {
            imageSrcArr.push($("#" + $("ul#showui li")[i].getElementsByTagName('img')[1].id + "").attr("src"));
        }
        //获取属性信息
        sub.editRow();
        const data = $("#bootstrap-table").bootstrapTable('getData');
        let attributeJsonArr = [];
        for (let j = 0; j < data.length; j++) {
            attributeJsonArr.push({
                "name": data[j].attributeCode,
                "value": data[j].attributeValue
            });
        }

        //将数据封装到renderTemplateHtmlData对象中
        var renderTemplateHtmlData = {
            "onlineDesc": desc,
            "imageSrcArr": imageSrcArr,
            "attributeJsonArr": attributeJsonArr,
            "templateId": templateId,
            "title": title
        }

        var html = "";
        $.ajax({
            cache: true,
            type: "POST",
            datatype: "json",
            contentType: "application/json",
            url: ctx + "template/description/renderTemplateToHtml",
            data: JSON.stringify(renderTemplateHtmlData),
            async: false,
            error: function (request) {
                $.modal.alertError("系统错误");
            },
            success: function (data) {
                if (data.code == web_status.SUCCESS) {
                    html = data.msg;
                }
            }
        });
        return html;
    }


    /**
     * 处理模板内容
     * @returns {string}
     */
    function renderTemplateHtml() {
        const templateId = $('#templateSelect').val();
        const warpDiv = document.createElement('div');
        if (!templateId) {
            warpDiv.innerHTML = getGrapesHtml();
        }

        return warpDiv.outerHTML;
    }


    //查找品类
    function findCategoryid() {
        //清空第一平台品类的值
        $("#firstCategoryid").val("");
        //获取站点值
        let siteCode = $("#siteCode").val();

        $.modal.open("查找品类", category_prefix + "/findCategoryid/EB/" + siteCode, '1000', '800', function (index, layero) {
            var category = layero.find("iframe")[0].contentWindow.getSelectedData();
            if (category) {
                // 尝试在 select 中找到对应的 option
                var $option = $("#firstCategoryid option[value='" + category.platformCategoryId + "']");

                if ($option.length > 0) {
                    // 如果找到对应的 option，则选中它
                    $option.prop("selected", true);
                } else {
                    // 如果没有找到对应的 option，则追加一个新的 option
                    $("#firstCategoryid").append("<option value='" + category.platformCategoryId + "'>" + category.categoryName + "</option>");
                    // 选中新追加的 option
                    $("#firstCategoryid").val(category.platformCategoryId);
                }

                $('#categoryOneLabel').text(category.categoryDetail);
                $('#categoryOneLabelEn').text(category.categoryEnDetail);
                $("#isDistribution").val(category.isDistribution);
                // 在更新完值后手动触发 change 事件
                $("#firstCategoryid").trigger('change');

                $.modal.close(index);
            }
        });
    }

    function submit() {
        // 最终提交前进行完整的表单验证
        var validationErrors = [];

        // 验证标题必填
        $('input[name*="].title"]').each(function () {
            if (!$(this).val() || $(this).val().trim() === '') {
                var goodsCode = $(this).closest('.form-group').find('.control-label').text();
                validationErrors.push('商品 ' + goodsCode + ' 的标题不能为空');
            }
        });

        // 验证描述必填
        $('textarea[name*="].detailDescription"]').each(function () {
            if (!$(this).val() || $(this).val().trim() === '') {
                var goodsCode = $(this).closest('.form-group').find('.control-label').text();
                validationErrors.push('商品 ' + goodsCode + ' 的描述不能为空');
            }
        });

        // 验证价格必填
        $('input[name*="].standardPrice"]').each(function () {
            if (!$(this).val() || $(this).val().trim() === '') {
                var goodsCode = $(this).closest('.form-group').find('.control-label').text();
                validationErrors.push('商品 ' + goodsCode + ' 的价格不能为空');
            }
        });

        // 如果有验证错误，显示错误信息并阻止提交
        if (validationErrors.length > 0) {
            var errorMessage = '请完善以下必填信息：\n\n' + validationErrors.join('\n');
            $.modal.alertError(errorMessage);
            return false;
        }

        var data = {};
        setDataInfo(data);

        let config = {
            url: prefix + "/batchTempSaveListing",
            type: "post",
            dataType: "json",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
            },
            success: function (e) {
                let listingPrefix = ctx + "publication/listing";
                if (e.code === 0) {
                    top.layer.confirm("生成草稿成功，请移至listing管理草稿页面！", {
                        icon: 3,
                        title: "系统提示",
                        btn: ['确认', '取消']
                    }, function (index) {
                        $.modal.close(index);
                        $.modal.closeTab();
                        $.modal.openTab('Listing管理', listingPrefix);
                    }, function () {
                        $.modal.closeTab();
                    });
                } else {
                    // $.modal.msgError("批量生成草稿失败，请联系管理员");
                    if (e.msg == null || e.msg == '') {
                        $.modal.alertError("批量生成草稿失败,请重新检查数据或联系管理员.");
                    } else {
                        $.modal.alertError(e.msg);
                    }
                    $.modal.closeLoading();
                }
            }
        };
        $.ajax(config);

    }


    function submitSave() {
        var data = {};
        setDataInfo(data);

        let config = {
            url: prefix + "/batchTempSaveListing",
            type: "post",
            dataType: "json",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
            },
            success: function (e) {
                let listingPrefix = ctx + "publication/listing";
                if (e.code === 0) {
                    top.layer.confirm("临时保存成功，请移至刊登任务管理页面再次修改！", {
                        icon: 3,
                        title: "系统提示",
                        btn: ['确认', '取消']
                    }, function (index) {
                        $.modal.close(index);
                        $.modal.closeTab();
                        $.modal.openTab('Listing管理', listingPrefix);
                    }, function () {
                        $.modal.closeTab();
                    });
                } else {
                    // $.modal.msgError("批量生成草稿失败，请联系管理员");
                    if (e.msg == null || e.msg == '') {
                        $.modal.alertError("临时保存失败,请重新检查数据或联系管理员.");
                    } else {
                        $.modal.alertError(e.msg);
                    }
                    $.modal.closeLoading();
                }
            }
        };
        $.ajax(config);

    }

    //站点选择
    $("#siteCode").change(function () {
        var siteCode = $(this).val();
        //清空店铺
        $("#shopCode").empty();
        $("#firstCategoryid").empty();
        $("#secondCategoryid").empty();

        //初始化店铺
        initShopCode("EB", siteCode, null);
        initCategoryInfo(siteCode, null, null);
        initCurrencyBySiteCode(siteCode)
        initLocationOfTheItem(siteCode, null, "location");
    });

    // 一键应用描述模板功能
    function applyDescriptionTemplateToAll() {
        var selectedTemplateId = $('#batchDescriptionTemplate').val();
        if (!selectedTemplateId) {
            $.modal.msgError('请先选择描述模板');
            return;
        }

        // 获取所有描述模板选择框并设置为选中的模板
        $('select[name*="].descriptionId"]').each(function () {
            $(this).val(selectedTemplateId);
        });

        $.modal.msg('已成功应用描述模板到所有商品', 2000);
    }

    // 初始化描述模板下拉框 - 复用现有的getTemplateHtml逻辑
    function initBatchDescriptionTemplate() {
        var shopCode = $('#shopCode').val();
        if (!shopCode) {
            return;
        }

        $.ajax({
            url: ctx + 'template/description/listByShopCode',
            type: 'post',
            dataType: 'json',
            data: {shopCode: shopCode},
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    var options = '<option value="">请选择描述模板</option>';
                    for (let item of result.data) {
                        options += '<option value="' + item.id + '">' + item.templateName + '</option>';
                    }
                    $('#batchDescriptionTemplate').html(options);
                }
            },
            error: function () {
                console.log('加载描述模板失败');
            }
        });
    }

    // 一键应用数值功能
    function batchApplyValuesToAll() {
        var selectedType = $('#batchApplyType').val();
        var applyValue = $('#batchApplyValue').val();

        if (!selectedType) {
            $.modal.msgError('请先选择字段类型');
            return;
        }

        if (!applyValue || applyValue.trim() === '') {
            $.modal.msgError('请输入要应用的数值');
            return;
        }

        // 验证数值格式
        if (isNaN(applyValue) || parseFloat(applyValue) < 0) {
            $.modal.msgError('请输入有效的正数');
            return;
        }

        var fieldSuffix = '';
        var fieldName = '';

        switch (selectedType) {
            case 'price':
                fieldSuffix = '.standardPrice';
                fieldName = '价格';
                break;
            case 'stock':
                fieldSuffix = '.stockOnSalesQty';
                fieldName = '库存';
                break;
            case 'startSelling':
                fieldSuffix = '.startSellingCount';
                fieldName = '起卖数量';
                break;
            default:
                $.modal.msgError('未知的字段类型');
                return;
        }

        // 获取所有对应字段的输入框并设置值
        var targetInputs = $('input[name$="' + fieldSuffix + '"]');
        if (targetInputs.length === 0) {
            $.modal.msgError('未找到对应的字段，请确保已加载商品数据');
            return;
        }

        targetInputs.each(function () {
            $(this).val(applyValue);
        });

        $.modal.msg('已成功将' + fieldName + '批量设置为 ' + applyValue, 2000);
    }

    // 绑定批量重新生成AI描述按钮事件
    $(document).on('click', '#batchRegenerateDescriptions', function () {
        batchRegenerateDescriptions();
    });

    // 绑定一键应用描述模板按钮事件
    $(document).on('click', '#applyDescriptionTemplate', function () {
        applyDescriptionTemplateToAll();
    });

    // 绑定一键应用数值按钮事件
    $(document).on('click', '#batchApplyValues', function () {
        batchApplyValuesToAll();
    });

    // 监听店铺选择变化，重新加载描述模板
    $(document).on('change', '#shopCode', function () {
        // 延迟一下，确保getTemplateHtml()先执行完成
        setTimeout(function () {
            initBatchDescriptionTemplate();
        }, 500);
    });

    $(function () {
        // 初始化店铺
        initShopCode("EB", "US", null);
        initCategoryInfo("US", null, null);
        initLocationOfTheItem($("#siteCode").val(), null, "location");
        getTemplateHtml();
        rendering();

        // 品牌可以填入自定义值
        $('#brandCode').select2({
            tags: true
        });

        // 延迟初始化描述模板，确保店铺已选择和getTemplateHtml()执行完成
        setTimeout(function () {
            initBatchDescriptionTemplate();
        }, 1500);

    });

</script>
</body>
<script th:src="@{/js/jquery.tmpl.js}"></script>
<th:block th:include="include :: select2-js"/>
<script id="goodsInfoTpl" type="text/x-jquery-tmpl">
    <div>
    <select name="listingAmazonAttributeLinesInfo[${index}].tableName" id="goodsInfoAttribute" class="form-control" style="width: 100%;">
        <option value="" selected>请选择</option>
         {{each(rowIndex,data) list}}
             <option value="${data.attributeName}" data-type="${data.attributeMemo}" {{if type===data.attributeName}}selected{{/if}} >${data.attributeName}</option>
         {{/each}}
    </select>
    </div>

</script>
<script id="goodsDetailsTpl" type="text/x-jquery-tmpl">
    <div>
    <select name="listingAmazonAttributeLinesDetail[${index}].tableName" id="goodsDetailsAttribute" class="form-control" style="width: 100%;">
        <option value="" selected>请选择</option>
         {{each(rowIndex,data) list}}
             <option value="${data.attributeName}" data-type="${data.attributeMemo}" {{if type===data.attributeName}}selected{{/if}}>${data.attributeName}</option>
         {{/each}}
    </select>
    </div>

</script>
<script id="safetyComplianceTpl" type="text/x-jquery-tmpl">
<div>
<select name="listingAmazonAttributeLinesSafety[${index}].tableName" id="goodsSafetyAttribute" class="form-control" style="width: 100%;">
    <option value="" selected>请选择</option>
     {{each(rowIndex,data) list}}
         <option value="${data.attributeName}" data-type="${data.attributeMemo}" {{if type===data.attributeName}}selected{{/if}}>${data.attributeName}</option>
     {{/each}}
</select>
</div>

</script>
<script id="attributeImport" type="text/x-jquery-tmpl">
<div align='center'>
<select name="listingAmazonAttributeLinesInfo[${index}].tableValue" id="tableValue" class="form-control" style="width: 100%;">
    <option value="">请选择</option>
     {{each(rowIndex,data) list}}
         <option class='form-control' value="${data}"  {{if type===data}}selected{{/if}} >${data}</option>
     {{/each}}
</select>
</div>

</script>
<script id="attributeDetails" type="text/x-jquery-tmpl">
<div align='center'>
<select name="listingAmazonAttributeLinesDetail[${index}].tableValue" id="tableValue" class="form-control" style="width: 100%;">
    <option value="">请选择</option>
     {{each(rowIndex,data) list}}
         <option class='form-control'  value="${data}"  {{if type===data}}selected{{/if}} >${data}</option>
     {{/each}}
</select>
</div>

</script>
<script id="attributeSafety" type="text/x-jquery-tmpl">
<div align='center'>
<select name="listingAmazonAttributeLinesSafety[${index}].tableValue" id="tableValue" class="form-control" style="width: 100%;">
    <option value="">请选择</option>
     {{each(rowIndex,data) list}}
         <option class='form-control'  value="${data}"  {{if type===data}}selected{{/if}} >${data}</option>
     {{/each}}
</select>
</div>

</script>
</html>
