package com.suncent.smc.persistence.pdm.domain.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @since 2023-01-10 15:20:00
 */
@NoArgsConstructor
@Data
public class CreateMappingDTO {

    //商品编码
    private String goodsCode;
    //店铺编码
    private String shopCode;
    //平台商品编码
    private String platformSku;
    //平台销售编码
    private String asin;
    //PN码
    private String platformPn;
    //操作人
    private String operators;
    //部门
    private String deptId;
    //创建人(userId)
    private String createBy;
    //fnSku
    private String fnSku;
    //系统
    private String systemType;

    private String price;

    private String brandCode;

    //刊登类型，0FBM，1FBA，3固定，４拍卖，5VCDF，6VCPO，7temu
    private Integer publishType;

    //刊登状态，0未刊登，草稿状态 1刊登中 2在售状态 3更新中 4更新失败 5下架中 6非在售 7下架失败 8刊登失败',
    private Integer publishStatus;
}
