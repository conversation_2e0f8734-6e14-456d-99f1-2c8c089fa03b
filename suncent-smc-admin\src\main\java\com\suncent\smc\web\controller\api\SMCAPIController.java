package com.suncent.smc.web.controller.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.suncent.smc.common.core.controller.BaseController;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.persistence.inventory.domain.InventoryUpdateBlack;
import com.suncent.smc.persistence.inventory.service.IInventoryExcludeRecordService;
import com.suncent.smc.persistence.inventory.service.IInventoryUpdateBlackService;
import com.suncent.smc.persistence.publication.domain.dto.GoodsHeadCountDTO;
import com.suncent.smc.persistence.publication.domain.dto.HighCostDTO;
import com.suncent.smc.persistence.publication.domain.dto.ItemsPerInnerPackDTO;
import com.suncent.smc.persistence.publication.domain.dto.VCPriceDTO;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadCountVO;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.provider.biz.publication.dto.EbayDescriptionGenerationResponseDTO;
import com.suncent.smc.provider.biz.publication.dto.EbayTitleGenerationResponseDTO;
import com.suncent.smc.provider.biz.publication.service.ListingInfoService;
import com.suncent.smc.provider.biz.publication.service.impl.AmazonPlatformListingServiceImpl;
import com.suncent.smc.common.api.ApiCloudHelper;
import com.suncent.smc.common.api.domain.ShortDescriptionApiRequest;
import com.suncent.smc.common.api.domain.ShortDescriptionApiResponse;
import com.suncent.smc.web.controller.domain.TitleGenerationCallbackRequest;
import com.suncent.smc.web.controller.domain.DescriptionGenerationCallbackRequest;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.persistence.configuration.store.service.IShortDescriptionApiService;
import com.suncent.smc.persistence.publication.domain.entity.AiGenerationTask;
import com.suncent.smc.persistence.publication.service.IAiGenerationTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * smc 统一对外接口
 */
@Controller
@RequestMapping("/smc/api")
@Slf4j
public class SMCAPIController extends BaseController {
    @Autowired
    @Lazy
    private ListingInfoService listingInfoService;
    @Autowired
    private IGoodsHeadService goodsHeadService;
    @Autowired
    private IInventoryExcludeRecordService inventoryExcludeRecordService;
    @Autowired
    private IInventoryUpdateBlackService inventoryUpdateBlackService;
    @Autowired
    private IShortDescriptionApiService shortDescriptionApiService;
    @Autowired
    private ApiCloudHelper apiCloudHelper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private AmazonPlatformListingServiceImpl amazonPlatformListingService;
    @Autowired
    private IAiGenerationTaskService aiGenerationTaskService;

    @PostMapping("/queryLatestListing")
    @ResponseBody
    public AjaxResult queryLatestListing(@RequestParam("sku") String sku,@RequestParam(required = false,value = "category") String category) {
        try {
            return AjaxResult.success(listingInfoService.queryLatestListing(sku,category));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * AI标题生成回调接口
     *
     * @param request 回调请求参数
     * @return 处理结果
     */
    @PostMapping("/titleGenerationCallback")
    @ResponseBody
    public AjaxResult titleGenerationCallback(@RequestBody TitleGenerationCallbackRequest request) {
        try {
            // 验证签名 - 使用与getShortDescriptionConfig相同的保护机制
//            apiCloudHelper.checkSign(request);

            log.info("接收到AI标题生成回调，taskCorrelationId: {}", request.getTaskCorrelationId());

            // 参数校验
            if (StringUtils.isEmpty(request.getTaskCorrelationId())) {
                return AjaxResult.error("taskCorrelationId不能为空");
            }

            if (request.getResult() == null) {
                return AjaxResult.error("生成结果不能为空");
            }

            // 先保存结果到数据库
            if ("success".equals(request.getStatus())) {
                // 更新任务状态为已完成，并存储AI结果
                aiGenerationTaskService.updateTaskStatusAndResult(
                    request.getTaskCorrelationId(),
                    AiGenerationTask.TaskStatus.COMPLETED.getCode(),
                    request.getResult().toJSONString(),
                    AiGenerationTask.TaskStatus.PROCESSING.getCode()
                );

                log.info("AI标题生成成功，结果已保存到数据库，taskCorrelationId: {}", request.getTaskCorrelationId());
            } else {
                // 更新任务状态为失败
                aiGenerationTaskService.updateTaskStatusAndResult(
                    request.getTaskCorrelationId(),
                    AiGenerationTask.TaskStatus.FAILED.getCode(),
                    request.getErrorMessage(),
                        AiGenerationTask.TaskStatus.PROCESSING.getCode()
                );

                log.error("AI标题生成失败，taskCorrelationId: {}, 错误信息: {}",
                    request.getTaskCorrelationId(), request.getErrorMessage());
            }
            // 记录回调成功日志
            log.info("AI标题生成回调处理成功，taskCorrelationId: {}, 结果已存储",
                    request.getTaskCorrelationId());
            return AjaxResult.success();

        } catch (Exception e) {
            logger.error("AI标题生成回调处理失败", e);
            return AjaxResult.error("回调处理失败: " + e.getMessage());
        }
    }


    /**
     * AI描述生成回调接口
     *
     * @param request 回调请求参数
     * @return 处理结果
     */
    @PostMapping("/descriptionGenerationCallback")
    @ResponseBody
    public AjaxResult descriptionGenerationCallback(@RequestBody DescriptionGenerationCallbackRequest request) {
        try {
            // 验证签名 - 使用与getShortDescriptionConfig相同的保护机制
//            apiCloudHelper.checkSign(request);

            log.info("接收到AI描述生成回调，taskCorrelationId: {}", request.getTaskCorrelationId());

            // 参数校验
            if (StringUtils.isEmpty(request.getTaskCorrelationId())) {
                return AjaxResult.error("taskCorrelationId不能为空");
            }

            if (request.getResult() == null) {
                return AjaxResult.error("生成结果不能为空");
            }

            // 先保存结果到数据库
            if ("success".equals(request.getStatus())) {
                // 更新任务状态为已完成，并存储AI结果
                aiGenerationTaskService.updateTaskStatusAndResult(
                    request.getTaskCorrelationId(),
                    AiGenerationTask.TaskStatus.COMPLETED.getCode(),
                    request.getResult().toJSONString(),
                    AiGenerationTask.TaskStatus.PROCESSING.getCode()
                );

                log.info("AI描述生成成功，结果已保存到数据库，taskCorrelationId: {}", request.getTaskCorrelationId());
                // 注意：不在这里直接同步，而是由定时任务扫描处理
            } else {
                // 更新任务状态为失败
                aiGenerationTaskService.updateTaskStatusAndResult(
                    request.getTaskCorrelationId(),
                    AiGenerationTask.TaskStatus.FAILED.getCode(),
                    request.getErrorMessage(),
                    AiGenerationTask.TaskStatus.PROCESSING.getCode()
                );

                log.error("AI描述生成失败，taskCorrelationId: {}, 错误信息: {}",
                    request.getTaskCorrelationId(), request.getErrorMessage());
            }

            // 记录回调成功日志
            log.info("AI描述生成回调处理成功，taskCorrelationId: {}, 结果已存储",
                    request.getTaskCorrelationId());

            return AjaxResult.success("回调处理成功");

        } catch (Exception e) {
            logger.error("AI描述生成回调处理失败", e);
            return AjaxResult.error("回调处理失败: " + e.getMessage());
        }
    }

    private EbayDescriptionGenerationResponseDTO parseEbayDescriptionGenerationResponseDTO(JSONObject jsonResponse) {
        EbayDescriptionGenerationResponseDTO responseDTO = new EbayDescriptionGenerationResponseDTO();

        Map<String, EbayDescriptionGenerationResponseDTO.GoodsDescriptionInfo> dataMap = new HashMap<>();

        for (String goodsCode : jsonResponse.keySet()) {
            JSONObject goodsData = jsonResponse.getJSONObject(goodsCode);
            EbayDescriptionGenerationResponseDTO.GoodsDescriptionInfo descInfo =
                new EbayDescriptionGenerationResponseDTO.GoodsDescriptionInfo();

            descInfo.setPdContent(goodsData.getString("pd_content"));

            dataMap.put(goodsCode, descInfo);
        }

        responseDTO.setData(dataMap);
        return responseDTO;
    }

    /**
     * 根据商品编码获取短描述模块配置
     *
     * @param request 请求参数
     * @return 短描述配置信息
     */
    @PostMapping("/getShortDescriptionConfig")
    @ResponseBody
    public AjaxResult getShortDescriptionConfig(@RequestBody ShortDescriptionApiRequest request) {
        try {
            // 验证签名
            apiCloudHelper.checkSign(request);

            // 获取短描述配置
            ShortDescriptionApiResponse response = shortDescriptionApiService.getShortDescriptionConfig(request);

            return AjaxResult.success(response == null ? new JSONObject() : response);
        } catch (Exception e) {
            logger.error("获取短描述配置失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/queryListing")
    @ResponseBody
    public AjaxResult queryListing(@RequestBody GoodsHead goodsHead) {
        try {
            return AjaxResult.success(goodsHeadService.selectListingGoodsHeadVOList(goodsHead));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/countOnlineListing")
    @ResponseBody
    public AjaxResult countOnlineListing(@RequestBody GoodsHeadCountDTO goodsHeadCountDTO) {
        try {
            if (goodsHeadCountDTO == null || CollUtil.isEmpty(goodsHeadCountDTO.getSkus())) {
                return AjaxResult.error("skus不能为空");
            }
            List<String> skus = goodsHeadCountDTO.getSkus();
            skus = skus.stream().distinct().collect(Collectors.toList());

            List<GoodsHeadCountVO> goodsHeadCountVOS = goodsHeadService.countOnlineListing(skus);
            if (CollUtil.isNotEmpty(goodsHeadCountVOS)) {
                goodsHeadCountVOS = goodsHeadCountVOS.stream().filter(goodsHeadCountVO -> goodsHeadCountVO.getOnlineCount() != null && goodsHeadCountVO.getOnlineCount() > 0).collect(Collectors.toList());
            }
            // 分组
            return AjaxResult.success(goodsHeadCountVOS);
        } catch (Exception e) {
            logger.error("countOnlineListing error", e);
            return AjaxResult.error(e.getMessage());
        }
    }


    @PostMapping("/queryItemsPerInnerPack")
    @ResponseBody
    public AjaxResult queryItemsPerInnerPack(@RequestBody List<ItemsPerInnerPackDTO> itemsPerInnerPackDTO) {
        try {
            if (CollUtil.isEmpty(itemsPerInnerPackDTO)) {
                return AjaxResult.error("查询箱规参数不能为空");
            }
            StringBuilder error = new StringBuilder();
            for (int i = 0; i < itemsPerInnerPackDTO.size(); i++) {
                ItemsPerInnerPackDTO dto = itemsPerInnerPackDTO.get(i);
                if (StrUtil.isBlank(dto.getShopCode())) {
                    error.append("第").append(i + 1).append("行shopCode不能为空;");
                }
                if (StrUtil.isBlank(dto.getAsin())) {
                    error.append("第").append(i + 1).append("行asin不能为空;");
                }
                if (StrUtil.isBlank(dto.getPlatformGoodsCode())) {
                    error.append("第").append(i + 1).append("行platformGoodsCode不能为空;");
                }
            }
            if (error.length() > 0) {
                return AjaxResult.error(error.toString());
            }
            // 超过200，提示错误
            if (itemsPerInnerPackDTO.size() > 200) {
                return AjaxResult.error("查询箱规参数不能超过200条");
            }

            return AjaxResult.success(listingInfoService.queryItemsPerInnerPack(itemsPerInnerPackDTO));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 查询库存黑名单
     */
    @PostMapping("/queryInventoryBlackList")
    @ResponseBody
    public AjaxResult queryInventoryBlackList(@RequestBody GoodsHead goodsHead) {
        try {
            if (ObjUtil.isEmpty(goodsHead)) {
                return AjaxResult.error("查询条件不能为空");
            }
            String platform = goodsHead.getPlatform();
            String siteCode = goodsHead.getSiteCode();
            String shopCode = goodsHead.getShopCode();
            List<String> platformGoodsCodeList = goodsHead.getPlatformGoodsCodeList();
            if (ObjUtil.isEmpty(platform)) {
                return AjaxResult.error("平台不能为空!");
            }
            if (ObjUtil.isEmpty(siteCode)) {
                return AjaxResult.error("站点不能为空!");
            }
            if (ObjUtil.isEmpty(shopCode)) {
                return AjaxResult.error("店铺不能为空!");
            }
            if (ObjUtil.isEmpty(platformGoodsCodeList)) {
                return AjaxResult.error("平台商品编码不能为空!");
            }
            List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadList(goodsHead);
            if (ObjectUtil.isEmpty(goodsHeads)) {
                return AjaxResult.error("smc不存在该链接信息!");
            }
            // 查询库存黑名单
            List<Long> goodsList = goodsHeads.stream().map(GoodsHead::getId).map(Long::valueOf).collect(Collectors.toList());
            if (ObjectUtil.isEmpty(goodsList)) {
                return AjaxResult.error("smc不存在该链接信息!");
            }
            List<InventoryUpdateBlack> inventoryUpdateBlacks = inventoryUpdateBlackService.selectInventoryUpdateBlackListByHeadIds(goodsList, shopCode);
            if (ObjectUtil.isEmpty(inventoryUpdateBlacks)) {
                return AjaxResult.error("smc不存在该店铺的库存黑名单数据!");
            } else {
                List<Long> excludeDataList = inventoryUpdateBlacks.stream().map(InventoryUpdateBlack::getHeadId).collect(Collectors.toList());
                if (excludeDataList.stream().anyMatch(goodsList::contains)) {
                    return AjaxResult.success("该链接商品在库存黑名单中！");
                } else {
                    return AjaxResult.error("该链接商品不在库存黑名单中！");
                }
            }
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }



    @PostMapping("/queryHighCostByAsin")
    @ResponseBody
    public AjaxResult queryHighCostByAsin(@RequestBody List<HighCostDTO> highCostDTOS) {
        try {
            if (CollUtil.isEmpty(highCostDTOS)) {
                return AjaxResult.error("查询HighCost不能为空");
            }
            StringBuilder error = new StringBuilder();
            for (int i = 0; i < highCostDTOS.size(); i++) {
                HighCostDTO dto = highCostDTOS.get(i);
                if (StrUtil.isBlank(dto.getShopCode())) {
                    error.append("第").append(i + 1).append("行shopCode不能为空;");
                }
                if (StrUtil.isBlank(dto.getPublishType())) {
                    error.append("第").append(i + 1).append("行publishType不能为空;");
                }
                if (StrUtil.isBlank(dto.getAsin())) {
                    error.append("第").append(i + 1).append("行asin不能为空;");
                }
            }
            if (error.length() > 0) {
                return AjaxResult.error(error.toString());
            }
            // 超过2000，提示错误
            if (highCostDTOS.size() > 2000) {
                return AjaxResult.error("查询HighCost不能超过2000条");
            }
            return AjaxResult.success(listingInfoService.queryHighCostByAsin(highCostDTOS));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/queryVCPrice")
    @ResponseBody
    public AjaxResult queryVCPrice(@RequestBody List<VCPriceDTO> vcPriceDTOS) {
        try {
            if (CollUtil.isEmpty(vcPriceDTOS)) {
                return AjaxResult.error("查询VCPrice参数不能为空");
            }
            StringBuilder error = new StringBuilder();
            for (int i = 0; i < vcPriceDTOS.size(); i++) {
                VCPriceDTO dto = vcPriceDTOS.get(i);
                if (StrUtil.isBlank(dto.getAsin())) {
                    error.append("第").append(i + 1).append("行asin不能为空;");
                }
                if (StrUtil.isBlank(dto.getSku())) {
                    error.append("第").append(i + 1).append("行sku不能为空;");
                }
            }
            if (error.length() > 0) {
                return AjaxResult.error(error.toString());
            }
            // 超过1000，提示错误
            if (vcPriceDTOS.size() > 1000) {
                return AjaxResult.error("查询VCPrice不能超过1000条");
            }
            return AjaxResult.success(listingInfoService.queryVCPrice(vcPriceDTOS));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
