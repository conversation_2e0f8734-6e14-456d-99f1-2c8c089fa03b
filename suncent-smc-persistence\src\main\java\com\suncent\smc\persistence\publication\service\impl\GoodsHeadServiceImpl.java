package com.suncent.smc.persistence.publication.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import com.google.common.collect.Lists;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.persistence.aplus.domain.dto.NotRelationAsinDto;
import com.suncent.smc.persistence.aplus.domain.vo.NotRelationAsinVO;
import com.suncent.smc.persistence.cdp.service.IBrandService;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.inventory.service.IInventoryExcludeRecordService;
import com.suncent.smc.persistence.publication.domain.dto.HeadPublishUpdateFailGoods;
import com.suncent.smc.persistence.publication.domain.dto.ListingDTO;
import com.suncent.smc.persistence.publication.domain.dto.ScheduledPublishDTO;
import com.suncent.smc.persistence.publication.domain.dto.VCPriceDTO;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.ListingLog;
import com.suncent.smc.persistence.publication.domain.entity.VcLinkErrorDataVO;
import com.suncent.smc.persistence.publication.domain.vo.*;
import com.suncent.smc.persistence.publication.mapper.GoodsHeadMapper;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.template.domain.entity.TemplateEbayDescription;
import com.suncent.smc.persistence.template.service.ITemplateEbayDescriptionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.suncent.smc.common.utils.DateUtils.YYYY_MM_DD_HH_MM;
import static com.suncent.smc.common.utils.DateUtils.parseDate;
import static com.suncent.smc.common.utils.ShiroUtils.getUserId;

/**
 * SMC-商品主Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-12
 */
@Service
@Slf4j
public class GoodsHeadServiceImpl implements IGoodsHeadService {
    @Autowired
    private GoodsHeadMapper goodsHeadMapper;
    @Autowired
    IShopService shopService;
    @Autowired
    IListingPullDateService listingPullDateService;
    @Autowired
    IBrandService brandService;
    @Autowired
    public RedisService redisService;
    @Autowired
    public IListingLogService listingLogService;
    @Autowired
    public IGoodsTaskService goodsTaskService;
    @Autowired
    public IGoodsTaskInfoService goodsTaskInfoService;
    @Autowired
    @Lazy
    private IListingEbayAdaptiveService listingEbayAdaptiveService;

    public static final String TIME_PUBLISHING = "TIME_PUBLISHING:";
    @Autowired
    private IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;
    @Resource
    private ITemplateEbayDescriptionService templateEbayDescriptionService;
    @Autowired
    IGoodsDescriptionService goodsDescriptionService;
    @Lazy
    @Autowired
    private IInventoryExcludeRecordService inventoryExcludeRecordService;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Autowired
    private CacheManager cacheManager;

    private Cache<Integer, Integer> publishTypeCache;

    @PostConstruct
    public void init() {
        QuickConfig publishTypeConfig = QuickConfig.newBuilder("GoodsHead:getPublishType").expire(Duration.ofHours(6)).localLimit(1000).cacheNullValue(false).cacheType(CacheType.BOTH).build();
        publishTypeCache = cacheManager.getOrCreateCache(publishTypeConfig);
    }

    /**
     * 查询SMC-商品主
     *
     * @param id SMC-商品主主键
     * @return SMC-商品主
     */
    @Override
    public GoodsHead selectListingGoodsHeadById(Integer id) {
        return goodsHeadMapper.selectListingGoodsHeadById(id);
    }

    /**
     * 查询店铺编码
     *
     * @param id SMC-商品主主键
     * @return SMC-商品主
     */
    @Override
    public String selectShopCodeById(Integer id) {
        return goodsHeadMapper.selectShopCodeById(id);
    }

    /**
     * 查询SMC-商品主列表
     *
     * @param listingGoodsHead SMC-商品主
     * @return SMC-商品主
     */
    @Override
    public List<GoodsHead> selectListingGoodsHeadList(GoodsHead listingGoodsHead) {
        return goodsHeadMapper.selectListingGoodsHeadList(listingGoodsHead);
    }

    /**
     * 查询SMC-商品主列表
     *
     * @param listingGoodsHead SMC-商品主
     * @return SMC-商品主
     */
    @Override
    public List<GoodsHeadVO> selectListingGoodsHeadVOList(GoodsHead listingGoodsHead) {
        return goodsHeadMapper.selectListingGoodsHeadVOList(listingGoodsHead);
    }

    @Override
    public List<GoodsHeadVO> selectListingGoodsHeadVOListCount(GoodsHead listingGoodsHead) {
        return goodsHeadMapper.selectListingGoodsHeadVOListCount(listingGoodsHead);
    }

    /**
     * 新增SMC-商品主
     *
     * @param listingGoodsHead SMC-商品主
     * @return 结果
     */
    @Override
    public int insertListingGoodsHead(GoodsHead listingGoodsHead) {
        listingGoodsHead.setCreateTime(DateUtils.getNowDate());
        int i = goodsHeadMapper.insertListingGoodsHead(listingGoodsHead);
        if (listingGoodsHead.getId() != null) {
            listingPullDateService.upsertListingPullDate(Long.valueOf(listingGoodsHead.getId()), listingGoodsHead.getPlatform());
        }
        return i;
    }

    /**
     * 修改SMC-商品主
     *
     * @param listingGoodsHead SMC-商品主
     * @return 结果
     */
    @Override
    public int updateListingGoodsHead(GoodsHead listingGoodsHead) {
        listingGoodsHead.setUpdateTime(DateUtils.getNowDate());
        return goodsHeadMapper.updateListingGoodsHead(listingGoodsHead);
    }

    /**
     * 批量删除SMC-商品主
     *
     * @param ids 需要删除的SMC-商品主主键
     * @return 结果
     */
    @Override
    public int deleteListingGoodsHeadByIds(String ids) {
        return goodsHeadMapper.deleteListingGoodsHeadByIds(Convert.toIntArray(ids));
    }

    /**
     * 删除SMC-商品主信息
     *
     * @param id SMC-商品主主键
     * @return 结果
     */
    @Override
    public int deleteListingGoodsHeadById(Integer id) {
        return goodsHeadMapper.deleteListingGoodsHeadById(id);
    }

    /**
     * 删除SMC-商品主信息
     *
     * @param ids 需要删除的SMC-商品主键
     * @return 结果
     */
    @Override
    public int deleteListingGoodsHeadByIdList(String ids) {
        return goodsHeadMapper.deleteListingGoodsHeadByIdList(Convert.toIntArray(ids));
    }


    @Override
    public int deleteListingGoodsHeadByIds(Integer[] ids) {
        return goodsHeadMapper.deleteListingGoodsHeadByIdList(ids);
    }


    /**
     * 批量下架Listing
     *
     * @param ids 需要下架的Listing主键
     * @return 结果
     */
    @Override
    public int stopPublishListingByIds(String ids) {
        return goodsHeadMapper.stopPublishListingByIds(Convert.toIntArray(ids));
    }

    /**
     * 批量刊登Listing
     *
     * @param ids SMC-商品主主键
     * @return 结果
     */
    @Override
    public int publishListingByIds(String ids) {
        return goodsHeadMapper.publishListingByIds(Convert.toIntArray(ids));
    }

    /**
     * 查询SMC-商品主列表
     *
     * @param ids 需要查询的Listing主键
     * @return SMC-商品集合
     */
    @Override
    public List<GoodsHead> selectListingGoodsHeadByIds(@Param("ids") Integer[] ids) {
        return goodsHeadMapper.selectListingGoodsHeadByIds(ids);
    }

    /**
     * 批量新增SMC-商品主
     *
     * @param goodsHeadList SMC-商品主
     * @return 结果
     */
    @Override
    public int insertListingGoodsHeads(List<GoodsHead> goodsHeadList) {
        return goodsHeadMapper.insertListingGoodsHeads(goodsHeadList);
    }

    /**
     * 查询SMC-商品主
     *
     * @param id SMC-商品主主键
     * @return SMC-商品主
     */
    @Override
    public Map<String, String> selectGoodsHeadMapById(Integer id) {
        return goodsHeadMapper.selectGoodsHeadMapById(id);
    }



    @Override
    public GoodsHead getGoodsHeadParam(ListingDTO listingDTO) {
        // 基础头部数据
        GoodsHead goodsHead = new GoodsHead();
        BeanUtil.copyProperties(listingDTO, goodsHead);
        goodsHead.setCategoryIds(listingDTO.getCategoryIds());
        goodsHead.setDescTemplateName(listingDTO.getDescribeTemplateName());
        goodsHead.setNow(DateUtils.getDate());
        //获取该用户对应的店铺code
        Long userId = getUserId();
        List<String> shopCodes = new ArrayList<>();
        List<String> shopCodeByUser = shopService.getShopCodeByUserId(userId);
        // 默认无数据权限 shopCode -1
        shopCodes.add("-1");
        if (StringUtils.isNotBlank(listingDTO.getShopCode())) {
            shopCodes.clear();
            // 多店铺筛选
            shopCodes.addAll(Arrays.asList(listingDTO.getShopCode().split(",")));
            goodsHead.setShopCode(null);
        } else {
            shopCodes.clear();
            shopCodes.addAll(shopCodeByUser);
        }
        if (!CollectionUtils.isEmpty(shopCodes)) {
            goodsHead.setShopCodes(shopCodes);
        }
        // 处理部分属性
        if (StringUtils.isNotBlank(listingDTO.getBrandName())) {
            List<String> brandNameList = Arrays.asList(listingDTO.getBrandName().split(" "));
            goodsHead.setBrandCodeList(brandNameList);
        }
        goodsHead.setPdmGoodsCode(listingDTO.getGoodsCode());
        //处理适配状态
        if (ObjectUtils.isNotEmpty(listingDTO.getAdaptationStatus()) && listingDTO.getAdaptationStatus().contains(",")) {
            List<String> collect = Arrays.stream(listingDTO.getAdaptationStatus().split(",")).collect(Collectors.toList());
            goodsHead.setAdaptationStatusList(collect);
            goodsHead.setAdaptationStatus(null);
        }
        //处理时间
        if (ObjectUtils.isNotEmpty(listingDTO.getTimeType())) {
            Map<String, Object> params = new HashMap<>();
            Integer timeType = listingDTO.getTimeType();
            Object beginTime = listingDTO.getParams().get("beginTime");
            Object endTime = listingDTO.getParams().get("endTime");

            TimeTypeEnum key = TimeTypeEnum.getKey(timeType);
            if (ObjUtil.isNotNull(key)){
                switch (key) {
                    case CREATE_TIME:
                        params.put("beginTime", beginTime);
                        params.put("endTime", endTime);
                        break;
                    case UODATE_TIME:
                        params.put("beginUpTime", beginTime);
                        params.put("endUpTime", endTime);
                        break;
                    case UP_TIME:
                        params.put("beginOnLineTime", beginTime);
                        params.put("endOnLineTime", endTime);
                        break;
                    case OFF_TIME:
                        params.put("beginOffTime", beginTime);
                        params.put("endOffTime", endTime);
                        break;
                }
            }
            goodsHead.setParams(params);
        }
        //处理编码
        if (ObjectUtils.isNotEmpty(listingDTO.getCodeType())) {
            CodeTypeEnum key = CodeTypeEnum.getKey(listingDTO.getCodeType());
            if (ObjUtil.isNotNull(key)){
                switch (key) {
                    case ID:
                        goodsHead.setIds(listingDTO.getCodeValue());
                        break;
                    case GOODS_CODE:
                        goodsHead.setPdmGoodsCode(listingDTO.getCodeValue());
                        break;
                    case PLATFORM_GOOD_CODE:
                        goodsHead.setPlatformGoodsCode(listingDTO.getCodeValue());
                        break;
                    case PLATFORM_SALE_CODE:
                        goodsHead.setPlatformGoodsId(listingDTO.getCodeValue());
                        break;
                    case PN_CODE:
                        goodsHead.setPnCode(listingDTO.getCodeValue());
                        break;
                }
            }
        }
        //处理刊登天数
        if (ObjectUtils.isNotEmpty(listingDTO.getPublishDayStart())){
            Map<String, Object> params = goodsHead.getParams();
            params.put("publishDayStart",listingDTO.getPublishDayStart());
            goodsHead.setParams(params);
        }
        if (ObjectUtils.isNotEmpty(listingDTO.getPublishDayEnd())){
            Map<String, Object> params = goodsHead.getParams();
            params.put("publishDayEnd",listingDTO.getPublishDayEnd());
            goodsHead.setParams(params);
        }
        //处理主链接
        if (ObjectUtils.isNotEmpty(listingDTO.getIsMain())){
            goodsHead.setIsMain(listingDTO.getIsMain());
        }
        String listingPerformance = listingDTO.getListingPerformance();
        //处理listing表现
        if (ObjectUtils.isNotEmpty(listingPerformance)){
            String[] split = listingPerformance.split(",");
            List<String> strings = Arrays.asList(split);
            // sku_rate_label 和 listing_rate_label使用对应的字段
            List<String> skuRateLabel = strings.stream().filter(s -> s.contains("sku_rate_label")).map(s -> s.replace("sku_rate_label_", "")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(skuRateLabel)){
                goodsHead.setSkuRateLabel(String.join(",", skuRateLabel));
            }
            List<String> listingRateLabel = strings.stream().filter(s -> s.contains("listing_rate_label")).map(s -> s.replace("listing_rate_label_", "")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(listingRateLabel)){
                goodsHead.setListingRateLabel(String.join(",", listingRateLabel));
            }
            strings = strings.stream().filter(s -> !s.contains("sku_rate_label") && !s.contains("listing_rate_label")).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(strings)) {
                goodsHead.setListingPerformance(String.join(",", strings));
            }else {
                goodsHead.setListingPerformance(null);
            }
        }
        //处理库存黑名单
        goodsHead.setIsInventoryBlack(listingDTO.getIsInventoryBlack());
        goodsHead.setIsRedLineBlack(listingDTO.getIsRedLineBlack());
        goodsHead.setIsCouponBlack(listingDTO.getIsCouponBlack());

        //处理业务分类查询条件
        goodsHead.setOperationClassificationList(listingDTO.getOperationClassificationList());



        // 一次性查询id 大于10000条 直接置为空
        if( StringUtils.countMatches(goodsHead.getIds(), " ") > 10000 ){
            goodsHead.setIds(null);
        }
        return goodsHead;
    }


    private void blackQuery(GoodsHead goodsHead, ListingDTO listingDTO) {
        if (StrUtil.isNotBlank(goodsHead.getBlackSku()) || StrUtil.isNotBlank(goodsHead.getBlackGoodsId())) {
            return;
        }
        listingDTO.setIsInventoryBlack(1);
        handleQueryNew(listingDTO);
        goodsHead.setIds(listingDTO.getIds()); 
    }

    /**
     * 获取AM没有asin的商品
     *
     * @return
     */
    @Override
    public List<GoodsHead> selectAmGoodsHeads(GoodsHead goodsHead) {
        return goodsHeadMapper.selectAmGoodsHeads(goodsHead);
    }

    @Override
    public List<GoodsHead> selectListingByPdmGoodsCode(String[] codes) {
        return goodsHeadMapper.selectListingByPdmGoodsCode(codes);
    }

    @Override
    public int updateListingByIds(String[] ids) {
        return goodsHeadMapper.updateListingByIds(ids);
    }


    @Override
    public String scheduledPublishToShop(ScheduledPublishDTO dto) {
        String userId = String.valueOf(getUserId());
        if (StringUtils.isEmpty(dto.getIds())) {
            return "请选择要刊登的商品！";
        }

        Integer[] idsArr = Convert.toIntArray(dto.getIds());
        String scheduledBeginTime = dto.getScheduledBeginTime();
        String model = dto.getModel();
        if (StringUtils.isEmpty(model)) {
            return "请选择刊登模式！";
        }
        if (ObjectUtils.isEmpty(scheduledBeginTime)) {
            return "请选择刊登时间！";
        }

        List<GoodsHead> goodsHeadList = selectListingGoodsHeadByIds(idsArr);
        if (CollectionUtils.isEmpty(goodsHeadList)) {
            return "没有找到要刊登的商品！";
        }
        if (StrUtil.isBlank(dto.getPlatform())) {
            GoodsHead head = goodsHeadList.get(0);
            dto.setPlatform(head.getPlatform());
        }
        // 获取每个商品的刊登时间
        Map<Integer, ScheduledPublishDTO> scheduledTimeMap = getScheduledMap(dto, goodsHeadList);

        List<Integer> ids = goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList());
        Map<Integer, List<GoodsHead>> headMap = goodsHeadList.stream().collect(Collectors.groupingBy(GoodsHead::getId));

        goodsTaskService.insertGoodsPendingProcessingTask(dto.getPlatform(), GoodsTaskTypeEnum.TIMING_PUBLISH, ids, userId);
        for (Integer id : ids) {
            String key = TIME_PUBLISHING + id;
            if (redisService.exists(key)) {
//                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(id), CollUtil.newArrayList(GoodsTaskTypeEnum.TIMING_PUBLISH), GoodsTaskSubStatusEnum.ERROR, "重复提交定时刊登.");
//                continue;
                redisService.deleteObject(key);
            }
            List<GoodsHead> heads = headMap.get(id);
            if (CollUtil.isEmpty(heads)) {
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(id), CollUtil.newArrayList(GoodsTaskTypeEnum.TIMING_PUBLISH), GoodsTaskSubStatusEnum.ERROR, "提交定时刊登无数据,请重新检查.");
                continue;
            }
            GoodsHead selectHead = heads.get(0);
            String publishStatusName = PublishStatus.getPublishStatusName(selectHead.getPublishStatus());
            if (Objects.equals(selectHead.getPlatform(), PlatformTypeEnum.AM.name()) && ObjectUtils.isNotEmpty(selectHead.getPlatformGoodsId())) {
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(id), CollUtil.newArrayList(GoodsTaskTypeEnum.TIMING_PUBLISH), GoodsTaskSubStatusEnum.ERROR, "非在售的AM平台的listing不允许定时刊登.");
                continue;
            }
            String begin = scheduledTimeMap.get(id).getScheduledBeginTime();
            String end = Objects.isNull(scheduledTimeMap.get(id).getScheduledEndTime()) ? begin : scheduledTimeMap.get(id).getScheduledEndTime();
            GoodsHead goodsHead = new GoodsHead();
            goodsHead.setId(id);
            goodsHead.setPublishStatus(PublishStatus.TOBEPUBLISHING.getType());
            //清空销售编码
            if (ObjectUtils.isNotEmpty(selectHead.getPlatformGoodsId())) {
                listingEbayAdaptiveService.writerEbayAdapter(selectHead.getPlatformGoodsId(), goodsHead.getId(),goodsHead.getShopCode());
            }
            goodsHead.setAsinNullFlag(1);
            updateListingGoodsHead(goodsHead);

            ScheduledPublishDTO scheduledPublishDTO = new ScheduledPublishDTO();
            scheduledPublishDTO.setGoodsHeadId(String.valueOf(id));
            scheduledPublishDTO.setScheduledBeginTime(begin);
            scheduledPublishDTO.setScheduledEndTime(end);
            redisService.setCacheObject(key, JSON.toJSONString(scheduledPublishDTO));

            ListingLog listingLog = new ListingLog();
            listingLog.setStatus(0);
            listingLog.setDetails("Listing新增定时刊登任务，任务开始时间:" + begin + ",任务结束时间:" + end+",数据由["+publishStatusName+"]移至[定时刊登中]");
            listingLog.setListingId(Integer.valueOf(id));
            listingLog.setOperName(userId);
            listingLog.setOperTime(new Date());
            listingLogService.insertListingLog(listingLog);
        }

        return "";
    }

    private Map<Integer, ScheduledPublishDTO> getScheduledMap(ScheduledPublishDTO dto, List<GoodsHead> goodsHeadList) {
        Map<Integer, ScheduledPublishDTO> scheduledTimeMap = new HashMap<>();
        //goodsHeadList 按照site分组
        Map<String, List<GoodsHead>> siteMap = goodsHeadList.stream().collect(Collectors.groupingBy(GoodsHead::getSiteCode));
        siteMap.forEach((k, v) -> {
            if (PlatformTypeEnum.EB.name().equals(dto.getPlatform())) {
                //ebay把时间转成对应站点时区时间
                dto.setScheduledBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM,
                        DateUtils.addHours(parseDate(dto.getScheduledBeginTime()), PlatformSiteEnum.getTimeZone(PlatformTypeEnum.EB.name(), k))));
                dto.setScheduledEndTime(DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM,
                        DateUtils.addHours(parseDate(dto.getScheduledBeginTime()), PlatformSiteEnum.getTimeZone(PlatformTypeEnum.EB.name(), k))));
            }
            String scheduledBeginTime = dto.getScheduledBeginTime();
            String model = dto.getModel();
            Integer intervalTime = dto.getIntervalTime();
            Integer intervalSize = dto.getIntervalSize();


            final Date[] beginTime = {parseDate(scheduledBeginTime)};


            for (int i = 1; i <= v.size(); i++) {

                GoodsHead goodsHead = v.get(i - 1);
                if (Objects.equals(model, "1")) {
                    // 定时刊登
                    scheduledTimeMap.put(goodsHead.getId(), dto);
                } else if (Objects.equals(model, "2")) {
                    // 间隔刊登
                    ScheduledPublishDTO newDto = new ScheduledPublishDTO();
                    newDto.setScheduledBeginTime(DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM, beginTime[0]));
                    newDto.setScheduledEndTime(DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM, beginTime[0]));
                    if (i % intervalSize == 0) {
                        beginTime[0] = DateUtils.addMinutes(beginTime[0], intervalTime);
                    }
                    scheduledTimeMap.put(goodsHead.getId(), newDto);
                }
            }
        });

        return scheduledTimeMap;
    }

    @Override
    public int updateListingGoodsHeadByIdList(GoodsHead goodsHead) {
        goodsHead.setUpdateTime(new Date());
        return goodsHeadMapper.updateListingGoodsHeadByIdList(goodsHead);
    }

    @Override
    public int updateListingPublishStatusByIds(Integer[] ids) {
        return goodsHeadMapper.updateListingPublishStatusByIds(ids);
    }

    /**
     * 查询Listing主表
     *
     * @param goodsHead
     * @return
     * @Date 2023/6/15 16:38
     * <AUTHOR>
     */
    @Override
    public List<GoodsHeadVO> getListingGoodsHeadVOList(GoodsHead goodsHead) {
        return goodsHeadMapper.getListingGoodsHeadVOList(goodsHead);
    }

    @Override
    public Integer selectCountByTitle(GoodsHead goodsHead) {
        return goodsHeadMapper.selectCountByTitle(goodsHead);
    }

    @Override
    public List<GoodsHeadHomePageVO> getGroundingListing(GoodsHeadHomePageVO goodsHeadHomePageVO) {
        return goodsHeadMapper.getGroundingListing(goodsHeadHomePageVO);
    }

    @Override
    public List<GoodsHeadHomePageVO> getSalesingListing(GoodsHeadHomePageVO goodsHeadHomePageVO) {
        return goodsHeadMapper.getSalesingListing(goodsHeadHomePageVO);
    }

    @Override
    public List<GoodsHeadHomePageVO> getOffListing(GoodsHeadHomePageVO goodsHeadHomePageVO) {
        return goodsHeadMapper.getOffListing(goodsHeadHomePageVO);
    }

    @Override
    public Integer selectCountByAutoSku(GoodsHead queryExist) {
        return goodsHeadMapper.selectCountByAutoSku(queryExist);
    }

    @Override
    public List<GoodsHead> selectListBySkuList(GoodsHead goodsHead) {
        return goodsHeadMapper.selectListBySkuList(goodsHead);
    }

    @Override
    public List<GoodsHead> selectOnlineListing(String platform) {
        GoodsHead queryGoodsHead = new GoodsHead();
        queryGoodsHead.setPlatform(platform);
        queryGoodsHead.setPublishStatusList(PublishStatus.getSaleStatus());
        return goodsHeadMapper.selectListingGoodsHeadList(queryGoodsHead);
    }

    @Override
    public List<GoodsHead> selectOnlineListing() {
        GoodsHead queryGoodsHead = new GoodsHead();
        queryGoodsHead.setPublishStatusList(PublishStatus.getSaleStatus());
        return goodsHeadMapper.selectListingGoodsHeadList(queryGoodsHead);
    }
    @Override
    public List<GoodsHead> selectOnlineListing(String shopCode, List<String> skus) {

        return goodsHeadMapper.selectOnlineListing(shopCode, skus);
    }

    @Override
    public List<GoodsHead> selectAllListing(String shopCode, List<String> skus) {
        return goodsHeadMapper.selectAllListing(shopCode, skus);
    }


    @Override
    public List<GoodsHead> selectOnlineListingByPlatfromAndShopCode(String platform, String shopCode) {
        return goodsHeadMapper.selectOnlineListingByPlatfromAndShopCode(platform, shopCode);
    }

    @Override
    public List<String> selectGoodsCodeSale(String userName) {
        return goodsHeadMapper.selectGoodsCodeSale(userName);
    }

    @Override
    public List<String> selectAllGoodsCode() {
        return goodsHeadMapper.selectAllGoodsCode();
    }

    @Override
    public int deleteListingGoodsHeadByIds(String remark, Integer[] ids) {
        return goodsHeadMapper.updateListingRemarkByIds(remark, ids);
    }

    @Override
    public List<GoodsHeadVO> listingGoodsHeadVOList(Integer[] idArray) {
        return goodsHeadMapper.listingGoodsHeadVOList(idArray);
    }

    @Override
    public List<Integer> selectSaleAndOrderList(ListingDTO listingDTO) {
        return goodsHeadMapper.selectSaleAndOrderList(listingDTO);
    }

    @Override
    public int clearPlatformGoodId(GoodsHead goodsHead) {
        return goodsHeadMapper.clearPlatformGoodId(goodsHead);
    }

    @Override
    public  List<GoodsHead>  selectNewOnlineTimeByShopCodeAndSize(List<String> platformItemIds, @Param("shopCode") String shopCode, @Param("size") Long size) {
        return goodsHeadMapper.selectNewOnlineTimeByShopCodeAndSize(platformItemIds,  shopCode,  size);
    }

    @Override
    public  List<GoodsHead>  selectHeadByShopCodeAndShopCategory(String shopCode,String shopCategory) {
        return goodsHeadMapper.selectHeadByShopCodeAndShopCategory(  shopCode,  shopCategory);
    }

    @Override
    public List<GoodsHead> selectFailListing() {
        return goodsHeadMapper.selectFailListing();
    }

    @Override
    public int deleteFailListing() {
        return goodsHeadMapper.deleteFailListing();
    }

    @Override
    public List<GoodsHead> selectHeadListByShopAndTitle(GoodsHead listingGoodsHead) {
        return goodsHeadMapper.selectHeadListByShopAndTitle(listingGoodsHead);
    }

    @Override
    public List<GoodsHead> selectHeadsByPublishStatusAndUpdateTime(HeadPublishUpdateFailGoods dto) {
        return goodsHeadMapper.selectHeadsByPublishStatusAndUpdateTime(dto);
    }

    @Override
    public GoodsHead queryLatestListing(List<String> skuList) {
        return goodsHeadMapper.queryLatestListing(skuList);
    }

    @Override
    public List<GoodsHead> selectListByPlatformGoodsCodes(List<String> platformGoodsCodes) {
        return goodsHeadMapper.selectListByPlatformGoodsCodes ( platformGoodsCodes);
    }

    @Override
    public List<GoodsHead> selectListInventoryNoZeroBySkuList(GoodsHead query) {
        return goodsHeadMapper.selectListInventoryNoZeroBySkuList(query);
    }

    @Override
    public List<GoodsHeadVO> selectListingGoodsByShopAndSku(List<String> shopCodeList, List<String> skus,Long userId) {
        return goodsHeadMapper.selectListingGoodsByShopAndSku(shopCodeList,skus,userId);
    }

    @Override
    public List<String> selectGoodsCodeSaleByShop(List<String> shopCodeList,Long userId) {
        return goodsHeadMapper.selectGoodsCodeSaleByShop(shopCodeList,userId);
    }

    @Override
    public List<GoodsHeadVO> selectListingUpdateFailureByShop(List<String> shopCodeList,Long userId) {
        return goodsHeadMapper.selectListingUpdateFailureByShop(shopCodeList,userId);
    }


    @Override
    public List<GoodsHead> selectListingZeroQty(String platform, String site, String shopCode) {
        return goodsHeadMapper.selectListingZeroQty(platform, site, shopCode);
    }

    @Override
    public List<NotRelationAsinVO> selectNotRelationAPlusListing(NotRelationAsinDto nNotRelationAsinDto) {
        return goodsHeadMapper.selectNotRelationAPlusListing(nNotRelationAsinDto);
    }

    @Override
    public int updataAsinRefundRateLabel(String platform, String platformSaleCode, String refundRateLabel) {
        //对platform进行转换
        if (Objects.equals("AMAZON", platform)) {
            platform = "AM";
        }
        if (Objects.equals("EBAY", platform)) {
            platform = "EB";
        }
        return goodsHeadMapper.updataAsinRefundRateLabel(platform, platformSaleCode, refundRateLabel);
    }

    @Override
    public int updataSkuRefundRateLabel(String platform, String sku, String refundRateLabel) {
        if (Objects.equals("AMAZON", platform)) {
            platform = "AM";
        }
        if (Objects.equals("EBAY", platform)) {
            platform = "EB";
        }
        return goodsHeadMapper.updataSkuRefundRateLabel(platform, sku, refundRateLabel);
    }

    @Override
    public List<GoodsHead> selectFailTodoListing(GoodsHead goodsHead) {
        return goodsHeadMapper.selectFailTodoListing(goodsHead);
    }

    @Override
    public Integer countByPublishFailByShopCodes(List<String> shopCodeList) {
        return goodsHeadMapper.countByPublishFailByShopCodes(shopCodeList);
    }

    @Override
    public List<String> selectAsinByShopCodeAndAsinIsNotNull(String shopCode) {
        return goodsHeadMapper.selectAsinByShopCodeAndAsinIsNotNull(shopCode);
    }

    @Override
    public int updateByAMReport(String quantity, String price, Integer publishStatus, String sellerSku, String asin,String shopCode) {
        return goodsHeadMapper.updateByAMReport(quantity, price, publishStatus, sellerSku, asin,shopCode);
    }

    @Override
    public List<GoodsHeadVO> selectListingGoodsByShopCodeAndSku(List<String> shopCodes, String platformSku, String goodsCode) {
        return goodsHeadMapper.selectListingGoodsByShopCodesAndSku(shopCodes, platformSku, goodsCode);
    }

    @Override
    public Integer countListingGoodsByUserAndSku(List<String> shopCodes, String platformSku, String goodsCode) {
        return goodsHeadMapper.countListingGoodsByShopCodesAndSku(shopCodes, platformSku, goodsCode);
    }

    @Override
    public Integer batchUpdateSettlementPrice(List<GoodsHead> goodsHeadList) {
        return goodsHeadMapper.batchUpdateSettlementPrice(goodsHeadList);
    }
    @Override
    public Integer batchUpdateSettlementPriceV2() {
        return goodsHeadMapper.batchUpdateSettlementPriceV2();
    }

    @Override
    public Integer batchUpdateRedlinePrice(List<GoodsHead> goodsHeadList) {
        return goodsHeadMapper.batchUpdateRedLinePrice(goodsHeadList);
    }

    @Override
    public Integer batchUpdateRedlinePriceV2(List<GoodsHead> goodsHeadList) {
        return goodsHeadMapper.batchUpdateRedlinePriceV2(goodsHeadList);
    }

    @Override
    public Integer updateRedLinePrice(BigDecimal redLinePrice,Integer publishType,String pdmGoodsCode) {
        return goodsHeadMapper.updateRedLinePrice(redLinePrice,publishType,pdmGoodsCode);
    }

    @Override
    public List<GoodsHead> selectPriceTodoListing() {
        return goodsHeadMapper.selectPriceTodoListing();
    }

    @Override
    public List<GoodsHeadPriceVo> getGoodsHeadPriceVoList(List<String> goodsCodeList) {
        return goodsHeadMapper.getGoodsHeadPriceVoList(goodsCodeList);
    }

    @Override
    public int clearOriginalPrice(GoodsHead goodsHead) {
        return goodsHeadMapper.clearOriginalPrice(goodsHead);
    }

    @Override
    public List<Integer> selectListIds(GoodsHead listingGoodsHead) {
        return goodsHeadMapper.selectListIds(listingGoodsHead);
    }

    @Override
    public List<GoodsHeadVO> countGoodsHeadVOList(GoodsHead goodsHeadParam) {
        return goodsHeadMapper.countGoodsHeadVOList(goodsHeadParam);
    }

    @Override
    public int updateOperators(String operators, String sitCode, String shopCode, String platformSku, String goodsCode) {
        return goodsHeadMapper.updateOperators(operators, sitCode, shopCode, platformSku, goodsCode);
    }

    @Override
    public List<GoodsHeadCountVO> countOnlineListing(List<String> skus) {
        return goodsHeadMapper.countOnlineListing(skus);
    }

    @Override
    public int createTempTableAndInsertData() {
        return goodsHeadMapper.createTempTableAndInsertData();
    }

    @Override
    public int clearAMainTable() {
        return goodsHeadMapper.clearAMainTable();
    }
    @Override
    public int updateMainTable() {
        return goodsHeadMapper.updateMainTable();
    }

    @Override
    public int dropTempTable() {
        return goodsHeadMapper.dropTempTable();
    }

    @Override
    public List<GoodsHeadVO> selectListingByShopCode(List<String> shopList) {
        return goodsHeadMapper.selectListingByShopCode(shopList);
    }

    @Override
    public int selectListingCount(List<Integer> goodsId) {
        return goodsHeadMapper.selectListingCount(goodsId);
    }

    @Override
    public List<GoodsHead> selectAmListingByPlatformGoodsIdList(List<String> asinList) {
        return goodsHeadMapper.selectAmListingByPlatformGoodsIdList(asinList);
    }

    @Override
    public int updateZeroInventory(List<Integer> goodsId) {
        return goodsHeadMapper.updateZeroInventory(goodsId);
    }

    @Override
    public List<GoodsHeadCountVO> countOnlineListingGroup(List<String> goodsCodes) {
        return goodsHeadMapper.countOnlineListingGroup(goodsCodes);
    }

    @Override
    public List<GoodsHead> selectAMOnlineListingByAsinAndPlatformSku(String asin, String platformSku, String shopCode, String sitCode) {
        return goodsHeadMapper.selectAMOnlineListingByAsinAndPlatformSku(asin, platformSku, shopCode, sitCode);
    }

    @Override
    public List<GoodsHead> selectOnlineListingByAsinAndPlatformSku(String asin, String platformSku, String shopCode, String sitCode, String platformCode) {
        return goodsHeadMapper.selectOnlineListingByAsinAndPlatformSku(asin, platformSku, shopCode, sitCode, platformCode);
    }

    @Override
    public int updateHeadsAdapterByItemId(List<GoodsHead> goodsHeads) {
        if (CollUtil.isEmpty(goodsHeads)){
            return 0;
        }
        List<GoodsHead> collect = goodsHeads.stream()
                .filter(goodsHead ->
                        ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()) && ObjUtil.isNotEmpty(goodsHead.getAdaptationStatus()))
                .collect(Collectors.toList());
        return goodsHeadMapper.updateHeadsAdapterByItemId(collect);
    }

    @Override
    public List<GoodsHead> listNeedUpdateItemsPerInnerPackByLastId(Integer publishType, List<String> platformSkus, Integer lastId) {
        return goodsHeadMapper.listNeedUpdateItemsPerInnerPack(publishType, platformSkus, lastId);
    }

    @Override
    public List<GoodsHead> listNeedSyncRealTimeSales(List<String> platformGoodsCodes) {
        return goodsHeadMapper.listNeedSyncRealTimeSales(platformGoodsCodes);
    }
    @Override
    public List<GoodsHead> selectVCDFListing(List<String> sellerSkuList) {
        return goodsHeadMapper.selectVCDFListing(sellerSkuList);
    }

    @Override
    public int updateInventoryByGoodsId(Long goodsId, Integer inventory) {
        if (ObjUtil.isEmpty(goodsId) || ObjUtil.isEmpty(inventory)) {
            return 0;
        }
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setId(Math.toIntExact(goodsId));
        goodsHead.setStockOnSalesQty(new BigDecimal(inventory));
        return goodsHeadMapper.updateListingGoodsHead(goodsHead);
    }

    @Override
    public int updateVcInventoryBySellerSku(List<String> sellerSkuLit) {
        if (ObjUtil.isEmpty(sellerSkuLit)) {
            return 0;
        }
        return goodsHeadMapper.updateVcInventoryBySellerSku(sellerSkuLit);
    }

    @Override
    public List<GoodsHead> selectVCDFListingBySku(List<String> skuList, String shopCode) {
        return goodsHeadMapper.selectVCDFListingBySku(skuList, shopCode);
    }

    @Override
    public List<GoodsHead> listFailListing(String userId) {

        return goodsHeadMapper.listFailListing(userId);
    }


    @Override
    public List<String> listFollowSoldAsin(String publishType, List<String> goodsId) {

        return goodsHeadMapper.listFollowSoldAsin(publishType,goodsId);
    }


    @Override
    public List<GoodsHead> selectDeleteListingGoodsHeadList(String sellerSku, String shopCode, Integer publishType, String asin,Integer day) {
        return goodsHeadMapper.selectDeleteListingGoodsHeadList(sellerSku, shopCode, publishType, asin, day);
    }

    @Override
    public void updateHeads(List<GoodsHead> goodsHeads) {
        if (CollUtil.isEmpty(goodsHeads)){
            return;
        }
        goodsHeads.forEach(goodsHead -> {
            goodsHeadMapper.updateListingGoodsHead(goodsHead);
        });
    }

    @Override
    public List<GoodsHead> listWaitAdaptByLastId(Integer lastId) {
        return goodsHeadMapper.listWaitAdaptByLastId(lastId);
    }

    @Override
    public List<GoodsHead> listNeedSyncSeriesByLastId(Integer lastId) {
        return goodsHeadMapper.listNeedSyncSeriesByLastId(lastId);
    }

    @Override
    public GoodsHead selectLastGoodsHeadByAsin(String followAsin, Integer id) {
        return goodsHeadMapper.selectLastGoodsHeadByAsin(followAsin, id);
    }

    @Override
    public List<GoodsHead> listNeedUpdatePublishStatus(Integer lastId) {
        return goodsHeadMapper.listNeedUpdatePublishStatus(lastId);
    }

    @Override
    public List<GoodsHead> listNeedUpdatePublishStatusPaged(int offset, int limit) {
        return goodsHeadMapper.listNeedUpdatePublishStatusPaged(offset, limit);
    }

    @Override
    public String getFollowVcPoAsin(GoodsHead goodsHead) {
        // 非VCPO的链接，返回false
        if (!goodsHead.getShopCode().contains("VC") || !goodsHead.getPublishType().equals(6) || !goodsHead.getPlatform().equals(PlatformTypeEnum.AM.name())) {
            return null;
        }
        return listingAmazonAttributeLineV2Service.getFollowAsin(goodsHead.getId());
    }

    @Override
    public int countByPlatformGoodsCodeAndShopCodeAndPublishType(String platformSku, String shopCode, Integer publishType, Integer id) {
        return goodsHeadMapper.countByPlatformGoodsCodeAndShopCodeAndPublishType(platformSku, shopCode, publishType, id);
    }

    @Override
    public List<GoodsHead> selectAMList(String createBy, String shopCode, String categoryId) {
        return goodsHeadMapper.selectAMList(createBy, shopCode, categoryId);
    }

    @Override
    public List<GoodsHead> selectGoodsHeadByShopCodeAndAsin(String shopCode, List<String> asinList) {
        return goodsHeadMapper.selectGoodsHeadByShopCodeAndAsin(shopCode, asinList);
    }

    @Override
    public List<GoodsHead> selectScAsinList(Integer minId, Integer maxId) {
        return goodsHeadMapper.selectScAsinList(minId, maxId);
    }

    @Override
    public Integer selectMaxIdBefore270Days() {
        return goodsHeadMapper.selectMaxIdBefore270Days();
    }

    @Override
    public void insertScAsinList(List<String> asinList) {
        goodsHeadMapper.insertScAsinList(asinList);
    }

    @Override
    public List<String> listTempAsinList() {
        return goodsHeadMapper.listTempAsinList();
    }

    @Override
    public void deleteTempAsinList(List<String> alreadyHandleAsinList) {
        goodsHeadMapper.deleteTempAsinList(alreadyHandleAsinList);
    }

    @Override
    public List<GoodsHead> selectGoodsHeadByAsin(List<String> asinList, String platform) {
        return goodsHeadMapper.selectGoodsHeadByAsin(asinList, platform);
    }

    @Override
    public void deleteTempAsinListAll() {
        goodsHeadMapper.deleteTempAsinListAll();
    }

    @Override
    public Long getNeedBackupStartId() {
        return goodsHeadMapper.getNeedBackupStartId();
    }

    @Override
    public List<GoodsHead> listNeedBackup(Long lastId, Long maxId) {
        return goodsHeadMapper.listNeedBackup(lastId, maxId);
    }

    @Override
    public List<GoodsHeadCountVO> listMainBrandListing(List<String> goodsCodeList, List<String> mainBrandList) {
        return goodsHeadMapper.listMainBrandListing(goodsCodeList, mainBrandList);
    }

    @Override
    public List<GoodsHead> selectAMListingByBrand(String brand, Long lastId, Date beforeDate) {
        return goodsHeadMapper.selectAMListingByBrand(brand, lastId, beforeDate);
    }

    @Override
    public List<GoodsHead> selectGoodsHeadByShopCodeAndPlatformGoodsCode(String shopCode,String publishType, List<String> platformSkus) {
        return goodsHeadMapper.selectGoodsHeadByShopCodeAndPlatformGoodsCode(shopCode, publishType,platformSkus);
    }

    @Override
    public void handleQueryNew(ListingDTO listingDTO) {
        if (StringUtils.isNotBlank(listingDTO.getDescribeTemplateName())) {
            //模糊查询SMC-描述-模板对象 sc_smc_template_ebay_description
            TemplateEbayDescription templateEbayDescription = new TemplateEbayDescription();
            templateEbayDescription.setTemplateName(listingDTO.getDescribeTemplateName());
            List<TemplateEbayDescription> templateEbayDescriptions = templateEbayDescriptionService.selectTemplateEbayDescriptionList(templateEbayDescription);
            if (CollectionUtils.isEmpty(templateEbayDescriptions)) {
                return;
            }
            //过滤模板id
            List<Long> descriptionIds = templateEbayDescriptions.stream().map(TemplateEbayDescription::getId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(descriptionIds)) {
                return;
            }
            //根据模板id查询头表id
            List<Integer> headIds = goodsDescriptionService.selectHeadIdByDescriptionIds(descriptionIds);
            if (CollUtil.isEmpty(headIds)) {
                return;
            }
            String strip = StringUtils.strip(headIds.toString(), "[]");
            if (StringUtils.isNotBlank(strip)) {
                listingDTO.setIds(strip);
            }
        }
    }
    @Override
    public List<VcLinkErrorDataVO> listVcErrorData(Long userId, String period) {
        return goodsHeadMapper.listVcErrorData(userId, period);
    }

    /**
     * 查询需要同步的ebay的listing
     * @return
     */
    @Override
    public List<GoodsHead> queryNeedPullEbayListings() {
        return goodsHeadMapper.queryNeedPullEbayListings();
    }


    @Override
    public List<GoodsHead> queryNeedPullAmazonListings() {
        return goodsHeadMapper.queryNeedPullAmazonListings();
    }

    @Override
    public void updateListingOffSale(String itemId, String accountCode, Integer publishStatus) {
        goodsHeadMapper.updateListingOffSale(itemId, accountCode, publishStatus);
    }

    @Override
    public List<VCPriceVO> queryMonitorPoolVCPrice(List<VCPriceDTO> vcPriceDTOS) {
        return goodsHeadMapper.queryMonitorPoolVCPrice(vcPriceDTOS);
    }

    @Override
    public void updateGoodHeadMain() {
        try (SqlSession sqlSession = sqlSessionFactory.openSession(true)) {
            // 使用同一个连接，防止连接变化获取不到临时表
            GoodsHeadMapper mapper = sqlSession.getMapper(GoodsHeadMapper.class);
            try {
                // 删除和创建临时表
                // 删除临时表
                mapper.dropTempTable();
                // 创建临时表并插入数据
                mapper.createTempTableAndInsertData();
                mapper.clearAMainTable();
                mapper.updateMainTable();
            }catch (Exception ex) {
                log.error("ListingLabelTask updateGoodHeadMain 更新商品主表出现异常", ex);
            }
            finally {
                mapper.dropTempTable();
            }
        }
    }

    @Override
    public Integer getPublishType(Integer headId) {
        Integer publishType = publishTypeCache.get(headId);
        if (publishType == null) {
            GoodsHead goodsHead = goodsHeadMapper.selectListingGoodsHeadById(headId);
            if (goodsHead != null) {
                publishType = goodsHead.getPublishType();
                publishTypeCache.put(headId, publishType);
            }
        }
        return publishType;
    }

    @Override
    public List<GoodsHead> listByPlatformAndShopCode(String platform, String shopCode, List<String> platformSkuList) {
        return goodsHeadMapper.listByPlatformAndShopCode(platform, shopCode, platformSkuList);
    }

    @Override
    public String selectBrandByAsin(String asin) {
        return goodsHeadMapper.selectBrandByAsin(asin);
    }


    /**
     * 红线价白名单 临时表
     * @param pdmGoodsCodes
     */
    @Override
    public void insertTempRedLineWhiteSkus(List<String> pdmGoodsCodes){
        if (CollUtil.isEmpty(pdmGoodsCodes)){
            return;
        }
        goodsHeadMapper.truncateTempRedLineWhiteSkus();
        List<List<String>> partition = Lists.partition(pdmGoodsCodes, 1000);
        partition.forEach(list -> {
            goodsHeadMapper.insertTempRedLineWhiteSkus(list);
        });
    }

    /**
     * 根据ASIN查询其他链接
     *
     * @param asin ASIN值
     * @param currentHeadId 当前商品ID（排除此ID）
     * @return 其他链接信息列表
     */
    @Override
    public List<GoodsHead> selectOtherListingsByAsin(String asin, Long currentHeadId) {
        return goodsHeadMapper.selectOtherListingsByAsin(asin, currentHeadId);
    } 

    @Override
    public void clearTempAsinSalesTable() {
        goodsHeadMapper.clearTempAsinSalesTable();
    }

    @Override
    public void insertAsinSales(List<String> asinList) {
        if (CollUtil.isEmpty(asinList)) {
            return;
        }
        List<List<String>> partition = Lists.partition(asinList, 5000);
        partition.forEach(list -> {
            goodsHeadMapper.insertAsinSales(list);
        });
    }

    @Override
    public List<String> listAsinSales(int batchSize) {
        return goodsHeadMapper.listAsinSales(batchSize);
    }

    /**
     * 物理删除临时表中已处理的ASIN列表
     * @param asinList ASIN列表
     */
    @Override
    public void deleteAsinSales(List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return;
        }
        goodsHeadMapper.deleteAsinSales(asinList);
    }

    @Override
    public String selectListingGoodsHeadByAsinAndShopCode(String asin, String shopCode, String sku, Integer publishType) {
        return goodsHeadMapper.selectListingGoodsHeadByAsinAndShopCode(asin, shopCode, sku, publishType);
    }

    @Override
    public List<GoodsHead> listVCAvailableAsins(GoodsHead queryCondition) {
        return goodsHeadMapper.listVCAvailableAsins(queryCondition);
    }

    @Override
    public List<GoodsHead> selectHeadIdAndPdmCodeByAsinList(List<String> asinList) {
        if (CollUtil.isEmpty(asinList)) {
            return new ArrayList<>();
        }
        return goodsHeadMapper.selectHeadIdAndPdmCodeByAsinList(asinList);
    }
}