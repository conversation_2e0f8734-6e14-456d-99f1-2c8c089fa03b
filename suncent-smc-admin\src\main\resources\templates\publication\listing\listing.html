<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('Listing信息列表')"/>
    <th:block th:include="include :: select2-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: virtual-select-css"/>
    <link th:href="@{/css/fancybox.css}" rel="stylesheet"/>
    <style>
        .bck_color {
            background-color: rgba(74, 165, 234, 1);
            color: white;
        }
        .div-select {
            height: 30px;
            width: 235px;
            display: inline-block;
        }
        .select_bck_color {
            background-color: rgba(70, 121, 178, 1);
            color: white;
        }

        .select-list li.show-4 label:not(.radio-box){
            float: left;
            width: 92px;
            margin: 5px 0px 0px 0px;
            text-align:right;
        }
        .select-list .show-4 li {
            margin: 0px;
            width: 300px
        }
        .select-list .show-4 .select2-container--bootstrap{
            width: 300px !important;
        }
        .select-list .show-4.multiple .select2-container--bootstrap {
            width: 300px !important;
        }
        .select-list .show-4.multiple-max .select2-container--bootstrap {
            width: 300px !important;
        }
        .select-list li.show-4 input {
            width: 300px;
        }
        .select-list li p, .select-list li label:not(.radio-box){
            width: 80px;
        }
        .select-list .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
            width: 235px;
        }
        .option-select {
            width:210px
        }
        .child-class {
            width:110px !important
        }
        .item-content {
            margin-top: 3px;
            white-space: nowrap;
        }
        .item-title {
            width: 45ch;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.5;
        }
        .item-row {
            margin-top: 3px;
            display: flex;
            justify-content: space-between;
            width: 100%;
        }
        .item-left {
        }
        .item-right {
            min-width: 120px; /* 为右侧设置最小宽度 */
            text-align: left; /* 确保左对齐 */
        }
        
        /* 为右侧的标签添加固定宽度 */
        .item-right-label {
            display: inline-block;
            width: 40px; /* 根据实际文本长度调整 */
            text-align: right;
        }

    </style>
</head>

<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <input name="publishStatus" id="publishStatus" type="hidden"/>
                <input name="publishStatusFlag" id="publishStatusFlag" value="0" type="hidden"/>
                <div class="select-list">
                    <ul>
                        <li>
                            <label >平台：</label>
                            <select name="platform" id="platform" class="noselect2 selectpicker"    data-none-selected-text="" title="请选择平台" >
                                <option class="option-select" value="">请选择</option>
                                <option class="option-select" value="AM">AM</option>
                                <option value="EB"class="option-select">EB</option>
                            </select>
                        </li>
                        <li>
                            <label >站点：</label>
                            <select name="siteCode"
                                    id="siteCode"
                                    data-allow-clear="true"
                                    data-none-selected-text="请选择站点"
                                    data-tags="true"
                                    class="noselect2 selectpicker" multiple>
<!--                                <option value="">请选择</option>-->
                                <option class="option-select"
                                        th:each="keyValueEntity:${@siteService.selectSiteAllList()}"
                                        th:value="${keyValueEntity.key}"
                                        th:text="${keyValueEntity.value}"></option>
                            </select>
                        </li>
                        <li>
                            <label >店铺：</label>
                            <div class="div-select">
                                <div name="shopCode" id="shop-select"></div>
                            </div>
                        </li>
                        <li>
                            <label >品类：</label>
                            <div class="div-select">
                                <div name="categoryIds" id="category-select"></div>
                            </div>
                        </li>
                        <li>
                            <label>业务分类：</label>
                            <div class="div-select">
                                <div name="operationClassificationList" id="operation-classification-select"></div>
                            </div>
                        </li>
                        <li>
                            <label >创建人：</label>
                            <div class="div-select">
                                <div name="createBy" id="createBy-select"></div>
                            </div>
                        </li>
                        <li style="height:30px">
                            <label >编码：</label>
                            <select   data-none-selected-text=""title="请选择编码"  class="child-class noselect2 selectpicker"  name="codeType" id="codeType">
                                <option value="0" style="width: 112px;">请选择编码</option>
                                <option value="1" style="width: 112px;">主键</option>
                                <option value="2"style="width: 112px;">商品编码</option>
                                <option value="3">平台商品编码</option>
                                <option value="4">平台销售编码</option>
                                <option value="5"style="width: 112px;">PN码</option>
                            </select>
                            <input  style="width: 450px" type="text" name="codeValue"  id="codeValue" placeholder="编码，支持多个查询空格分开"/>
                        </li>
                        <li>
                            <label >标题：</label>
                            <input type="text"style="width: 236px"  name="title" id="title" placeholder="Listing标题"/>
                        </li>
                        <li>
                            <label >刊登类型：</label>
                            <select name="publishTypeList" id="publishType"  class="noselect2 selectpicker"
                                    th:with="type=${@dict.getType('publication_goods_publish_type')}" data-allow-clear="true"
                            data-none-selected-text="请选择刊登类型" multiple>
                                <option class="option-select"th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label >适配状态：</label>
                            <select name="adaptationStatus" id="adaptationStatus" class="noselect2 selectpicker"data-allow-clear="true"
                                    data-none-selected-text="请选择适配状态" >
<!--                                    data-none-selected-text="请选择适配状态" multiple>-->
                                <option value="" class="option-select" >请选择适配状态</option>
                                <option value="待适配" class="option-select" >待适配</option>
                                <option value="适配成功"class="option-select">适配成功</option>
<!--                                <option value="适配失败"class="option-select">适配失败</option>-->
                                <option value="适配异常"class="option-select">适配异常</option>
                                <option value="无需适配"class="option-select">无需适配</option>
<!--                                <option value="已适配"class="option-select">已适配</option>-->
                                <option value="缺适配"class="option-select">缺适配</option>
                            </select>
                        </li>
                        <li style="height: 30px">
                            <label >时间：</label>
                            <select data-none-selected-text=""title="请选择时间"  class="child-class noselect2 selectpicker" name="timeType"id="timeType">
                                <option value="">请选择时间</option>
                                <option value="1">创建时间</option>
                                <option value="2">更新时间</option>
                                <option value="3">上架时间</option>
                                <option value="4">下架时间</option>
                            </select>
                            <input style="width: 212px;" type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginTime]"/>
                            <span style="padding: 0px 8px;">-</span>
                            <input style="width: 212px;" type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endTime]"/>
                        </li>
                        <li>
                            <label  >售价：</label>
                            <input style="width: 104px;" type="number"name="standardPriceStart"  id="standardPriceStart" placeholder="请输入售价"
                            />
                            <span style="padding: 0px 8px;">-</span>
                            <input style="width: 104px;" type="number"name="standardPriceEnd"  id="standardPriceEnd" placeholder="请输入售价"
                            />
                        </li>
                        <li>
                            <label >在售库存：</label>
                            <input style="width: 104px;" type="number"name="stockOnSalesQtyStart"  id="stockOnSalesQtyStart" placeholder="请输入库存"
                            />
                            <span style="padding: 0px 8px;">-</span>
                            <input style="width: 104px;" type="number"name="stockOnSalesQtyEnd"  id="stockOnSalesQtyEnd" placeholder="请输入库存"
                            />
                        </li>

                        <li>
                            <label >实际库存：</label>
                            <input style="width: 104px;" type="number"name="actualStockOnSalesQtyStart"  id="actualStockOnSalesQtyStart" placeholder="请输入库存"
                            />
                            <span style="padding: 0px 8px;">-</span>
                            <input style="width: 104px;" type="number"name="actualStockOnSalesQtyEnd"  id="actualStockOnSalesQtyEnd" placeholder="请输入库存"
                            />
                        </li>


                        <li style="height:30px">
                            <label  >销量：</label>
                            <select data-none-selected-text=""title="请选择销量"  class="child-class noselect2 selectpicker" name="saleType"id="saleType">
                                <option value="">请选择销量</option>
                                <option value="1">30天销量</option>
                                <option value="2">60天销量</option>
                                <option value="3">90天销量</option>
                                <option value="4">一直未售出</option>
                                <option value="5">30天未售出</option>
                                <option value="6">60天未售出</option>
                                <option value="7">90天未售出</option>
                            </select>
                            <input style="width: 130px;" type="number"name="saleValueStart"  id="saleValueStart" placeholder="请输入销量"
                            />
                            <span style="padding: 0px 8px;">-</span>
                            <input style="width: 130px;" type="number"name="saleValueEnd"  id="saleValueEnd" placeholder="请输入销量"
                            />

                        </li>
                        <li style="height:30px">
                            <label >订单量：</label>
                            <select  data-none-selected-text=""title="请选择订单量"  class="child-class noselect2 selectpicker"    name="orderType" id="orderType">
                                <option value="">请选择订单量</option>
                                <option value="1">30天订单量</option>
                                <option value="2">60天订单量</option>
                                <option value="3">90天订单量</option>
                            </select>
                            <input style="width: 130px;" type="number"name="orderValueStart"  id="orderValueStart" placeholder="请输入订单量"
                            />
                            <span style="padding: 0px 8px;">-</span>
                            <input style="width: 130px" type="number"name="orderValueEnd"  id="orderValueEnd" placeholder="请输入订单量"
                            />
                        </li>
                        <li>
                            <label >送检结果：</label>
                            <select name="censorship"  id="censorship" class="noselect2 selectpicker"data-allow-clear="true"
                                    data-none-selected-text="请选择送检结果" multiple >
<!--                                <option value="">请选择</option>-->
                                <option value="0"class="option-select">送检失败</option>
                                <option value="1" class="option-select">送检成功</option>
                                <option value="2"class="option-select">尚未送检</option>
                            </select>
                        </li>
                        <li>
                            <label >描述模板：</label>
                            <input type="text" style="width: 235px"name="describeTemplateName" id="describeTemplateName" placeholder="请输入描述模板"/>
                        </li>


                        <li>
                            <label >品牌：</label>
                            <input type="text" style="width: 402px"name="brandName" id="brandName" placeholder="请输入品牌,允许多个品牌查询空格分开"/>
                        </li>
                        <li>
                            <label >刊登天数：</label>
                            <input style="width: 185px;" type="number"name="publishDayStart"  id="publishDayStart" placeholder="请输入刊登天数"
                            />
                            <span style="padding: 0px 10px;">-</span>
                            <input style="width: 185px;" type="number"name="publishDayEnd"  id="publishDayEnd" placeholder="请输入刊登天数"
                            />
                        </li>
                        <li>
                            <label >刊登方式：</label>
                            <select style="width: 104px;" name="smcFlagList" id="smcFlag" class="noselect2 selectpicker" data-allow-clear="true"
                                    data-none-selected-text="请选择刊登方式"multiple >
                                <option value="0,3"class="option-select">SMC刊登</option>
                                <option value="1"class="option-select">非SMC刊登</option>
                                <option value="2"class="option-select">自动刊登</option>
                            </select>
                        </li>
                        <li>
                            <label >红线价：</label>
                            <select name="redLinePriceFlag" id="redLinePriceFlag" class="noselect2 selectpicker" data-allow-clear="true"
                                    data-none-selected-text="请选择红线价方式" >
                                <option value=""class="option-select">请选择红线价</option>
                                <option value="0"class="option-select">低于红线价</option>
                                <option value="1"class="option-select">高于红线价</option>
                            </select>
                        </li>
                        <li>
                            <label >标签：</label>
                            <select name="listingPerformance" id="listingLabel" class="noselect2 selectpicker" data-allow-clear="true" multiple
                                    data-none-selected-text="请选择listing标签">
<!--                                <option value=""class="option-select">请选择listing表现</option>-->
                                <!-- 不能显示独立站专卖、库存黑名单、红线价黑名单、主链接 -->
                                <option class="option-select" th:each="dict : ${@dict.getType('listing_label_list')}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                        th:unless="${dict.dictValue == 'self_独立站专卖' or dict.dictValue == 'self_主链接' or dict.dictValue == 'self_库存更新黑名单' or dict.dictValue == 'self_红线价管控白名单'}"></option>
                            </select>
                        </li>
                        <li>
                            <label >表现：</label>
                            <select name="listingPerformance" id="listingPerformance" class="noselect2 selectpicker" data-allow-clear="true" multiple
                                    data-none-selected-text="请选择listing表现">
                                <!--                                <option value=""class="option-select">请选择listing表现</option>-->
                                <option class="option-select" th:each="dict : ${@dict.getType('listing_performance_list')}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label >A+模板：</label>
                            <input type="text" style="width: 235px"name="aplusTemplateName" id="aplusTemplateName" placeholder="请输入A+模板名称"/>
                        </li>
                        <li>
                            <label >主链接：</label>
                            <select name="isMain" id="isMain" class="noselect2 selectpicker" data-allow-clear="true"
                                    data-none-selected-text="请选择主链接方式" >
                                <option value=""class="option-select">请选择主链接</option>
                                <option value="0"class="option-select">非主链接</option>
                                <option value="1"class="option-select">是主链接</option>
                            </select>
                        </li>
                        <li>
                            <label >系列：</label>
                            <input type="text" style="width: 235px" name="productSeries" id="productSeries" placeholder="请输入系列"/>
                        </li>
                        <li>
                            <label>型号：</label>
                            <input type="text" style="width: 235px" name="productModel" id="productModel" placeholder="请输入型号"/>
                        </li>
                        <li>
                            <label>库存黑名单：</label>
                            <select class="noselect2 selectpicker" data-allow-clear="true" data-none-selected-text="请选择主链接方式"
                                    id="isInventoryBlack"
                                    name="isInventoryBlack">
                                <option class="option-select" value="">请选择库存黑名单</option>
                                <option class="option-select" value="0">非库存黑名单</option>
                                <option class="option-select" value="1">是库存黑名单</option>
                            </select>
                        </li>
                        <li>
                            <label>红线价白名单：</label>
                            <select class="noselect2 selectpicker" data-allow-clear="true" data-none-selected-text="请选择主链接方式"
                                    id="isRedLineBlack"
                                    name="isRedLineBlack">
                                <option class="option-select" value="">请选择红线价白名单</option>
                                <option class="option-select" value="0">非红线价白名单</option>
                                <option class="option-select" value="1">是红线价白名单</option>
                            </select>
                        </li>
                        <li>
                            <label>Coupon：</label>
                            <select class="noselect2 selectpicker" data-allow-clear="true" data-none-selected-text="请选择主链接方式"
                                    id="isCouponBlack"
                                    name="isCouponBlack">
                                <option class="option-select" value="">请选择Coupon黑名单</option>
                                <option class="option-select" value="0">非Coupon黑名单</option>
                                <option class="option-select" value="1">是Coupon黑名单</option>
                            </select>
                        </li>
                        <li>
                            <label>可用状态：</label>
                            <select class="noselect2 selectpicker" data-allow-clear="true" data-none-selected-text="请选择可用状态"
                                    id="currentAvailability"
                                    name="currentAvailability">
                                <option class="option-select" value="">请选择可用状态</option>
                                <option class="option-select" value="1">Available</option>
                                <option class="option-select" value="2">Temporarily unavailable</option>
                                <option class="option-select" value="3">Permanently unavailable</option>
                            </select>
                        </li>
                        <li class="show-4">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="listingTable()"><i
                                    class="fa fa-search"></i> 搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 search-collapse" id="statusLabel">
            <a class="btn bck_color" onclick="chooseStatus(this,'')">ALL(<span id="allCount">0</span>)</a>
            <a class="btn bck_color" onclick="chooseStatus(this,'0')">草稿(<span id="draftCount">0</span>)</a>
            <a class="btn bck_color" onclick="chooseStatus(this,'1')">刊登中(<span id="publishCount">0</span>)</a>
            <a class="btn select_bck_color" onclick="chooseStatus(this,'2')">在售(<span id="saleCount">0</span>)</a>
            <a class="btn bck_color" onclick="chooseStatus(this,'3')">更新中(<span id="updatingCount">0</span>)</a>
            <a class="btn bck_color" onclick="chooseStatus(this,'4')">更新失败(<span
                    id="updateFailedCount">0</span>)</a>
            <a class="btn bck_color" onclick="chooseStatus(this,'5')">下架中(<span id="offShelfCount">0</span>)</a>
            <a class="btn bck_color" onclick="chooseStatus(this,'6')">非在售(<span id="noSaleCount">0</span>)</a>
            <a class="btn bck_color" onclick="chooseStatus(this,'7')">下架失败(<span id="offShelfFailedCount">0</span>)</a>
            <a class="btn bck_color" onclick="chooseStatus(this,'8')">刊登失败(<span
                    id="publishFailedCount">0</span>)</a>
            <a class="btn bck_color" onclick="chooseStatus(this,'9')">删除失败(<span
                id="deleteOnlineFailedCount">0</span>)</a>
            <div class="div-select" style="width: 150px;">
                <div name="publishSubStatus" id="publishSubStatus"></div>
            </div>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-info multiple disabled" onclick="publishAll()"  id="buttonPublish" shiro:hasPermission="publication:listing:publish">
                <i class="fa fa-plus"></i> 批量刊登
            </a>
            <a class="btn btn-success  VCDisabled" onclick="batchCensorship()" shiro:hasPermission="publication:listing:batchCensorship">
                <i class="fa fa-plus"></i> 批量送检
            </a>

            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-primary btn-sm dropdown-toggle VCDisabled">定时刊登 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a href="#" class="font-bold VCDisabled" id="buttonScheduledPublish" onclick="buttonScheduledPublish()">定时刊登</a>
                    </li>
                    <li>
                        <a href="#" class="font-bold VCDisabled" id="createCirculateScheduledPublish" onclick="createCirculateScheduledPublish()">创建循环刊登任务</a>
                    </li>
                    <li>
                        <a href="#" class="font-bold VCDisabled" id="selectCirculateScheduledPublish" onclick="selectCirculateScheduledPublish()">选择循环刊登任务</a>
                    </li>
                </ul>
            </div>

            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-success btn-sm dropdown-toggle">复制 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a href="#" class="font-bold disabled" shiro:hasPermission="publication:listing:copy" id="buttonCopy"  onclick="copyAll()">Listing复制</a>
                    </li>
                    <li>
                        <a href="#" class="font-bold disabled" shiro:hasPermission="publication:listing:copy"  onclick="oneKeyFollow(true)">VC复制</a>
                    </li>
                </ul>
            </div>
            <a class="btn btn-warning multiple disabled VCDisabled" onclick="stopPublishAll()" id="buttonStopPublish" shiro:hasPermission="publication:listing:stopPublish">
                <i class="fa fa-plus"></i> 批量下架
            </a>
            <a class="btn btn-danger multiple disabled" onclick="removeAll()"  id="buttonRemove" shiro:hasPermission="publication:listing:remove">
                <i class="fa fa-remove"></i> 批量删除
            </a>
            <a class="btn btn-danger multiple disabled VCDisabled" onclick="batchCancelTimePublish()"  id="batchCancelTimePublish" >
                <i class="fa fa-remove"></i> 批量取消定时任务
            </a>
            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-primary btn-sm dropdown-toggle " >批量修改 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" >
                    <li>
                        <a class="btn disabled VCDisabled" id="buttonBatchEdit" onclick="batchModify.openBatchEdit()" shiro:hasPermission="publication:listing:edit">
                            <i class="fa"></i> 批量自定义修改
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled SCDisabled" id="buttonBatchEditByExcel" onclick="batchModify.openBatchEditByExcel()" shiro:hasPermission="publication:listing:edit">
                            <i class="fa"></i> 批量更新属性(AM)
                        </a>
                    </li>
                    <li>
                        <!--amazon暂时不支持批量修改属性 暂时用shiro:hasPermission="publication:syncLostListing:sync"控制-->
                        <a class="btn disabled VCDisabled" id="buttonBatchUpdateAttribute" onclick="batchUpdateAttribute()"
                           shiro:hasPermission="publication:syncLostListing:sync">
                            <i class="fa"></i> 批量更新属性(Ebay)
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled" id="buttonBatchEditListing" onclick="batchEditListing()" shiro:hasPermission="publication:listing:batchEdit">
                            <i class="fa"></i> 批量编辑
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled" id="buttonBatchUpdateTitle" onclick="batchUpdateTitle()" shiro:hasPermission="publication:listing:batchEdit">
                            <i class="fa"></i> 批量更新标题
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled VCDisabled" id="buttonBatchUpdateAMPriceAndStock" onclick="batchUpdatePriceAndStock('AM')" shiro:hasPermission="publication:listing:batchEdit">
                            <i class="fa"></i> 批量更新价格库存(AM)
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled VCDisabled" id="buttonBatchUpdateEBPriceAndStock" onclick="batchUpdatePriceAndStock('EB')" shiro:hasPermission="publication:listing:batchEdit">
                            <i class="fa"></i> 批量更新价格库存(EB)
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled " id="batchUpdateStock" onclick="batchUpdateStock()" shiro:hasPermission="publication:listing:batchEditVCStock">
                            <i class="fa"></i> 批量更新库存(VC)
                        </a>
                    </li>

                    <li>
                        <a class="btn disabled " id="buttonBatchUpdatePictures" onclick="batchUpdatePictures()"
                           shiro:hasPermission="publication:listing:batchEdit">
                            <i class="fa"></i> 批量更新图片
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled VCDisabled" id="buttonBatchUpdateEbayAdaptive" onclick="batchUpdateEbayAdaptive('1')"
                           shiro:hasPermission="publication:listing:batchEdit">
                            <i class="fa"></i> 批量更新适配
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled " id="buttonBatchUpdateFiveDescription" onclick="buttonBatchUpdateFiveDescription()"
                           shiro:hasPermission="publication:goods:addAmazon">
                            <i class="fa"></i> 批量更新五点
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled " id="buttonBatchUpdateDescription" onclick="batchUpdateDescription()"
                           shiro:hasPermission="publication:listing:batchEdit">
                            <i class="fa"></i> 批量更新描述
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled SCDisabled" id="buttonBatchUpdateAvailability" onclick="batchUpdateAvailability()"
                           shiro:hasPermission="publication:listing:batchEditAvailability">
                            <i class="fa"></i> 批量修改可用状态
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled VCDisabled" id="batchUpdateVideo" onclick="batchUpdateVideo()"
                           shiro:hasPermission="publication:syncLostListing:sync">
                            <i class="fa"></i> 批量更新视频
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled" id="batchUpdatePlatformSku" onclick="batchModify.openBatchEditPlatformSku()"
                           shiro:hasPermission="publication:goods:addAmazon">
                            <i class="fa"></i> 批量更新平台SKU
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled VCDisabled" id="batchUpdateShipping" onclick="batchUpdateShipping()"
                           shiro:hasPermission="publication:syncLostListing:sync">
                            <i class="fa"></i> 批量更新物流信息
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled VCDisabled" id="batchUpdateDescriptionTemplate" onclick="batchUpdateDescriptionTemplate()"
                           shiro:hasPermission="publication:syncLostListing:sync">
                            <i class="fa"></i> 批量更新描述模板
                        </a>
                    </li>
                    <li>
                        <a class="btn disabled VCDisabled" id="batchUpdatePolicy" onclick="batchUpdatePolicy()"
                           shiro:hasPermission="publication:syncLostListing:sync">
                            <i class="fa"></i> 批量更新付款退货信息
                        </a>
                    </li>

                </ul>
            </div>

            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-success btn-sm dropdown-toggle " shiro:hasPermission="publication:listing:sync" >同步平台商品 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a href="#" class="font-bold VCDisabled" onclick="syncShopListing()">同步店铺商品</a>
                    </li>
                    <li>
                        <a href="#" class="font-bold" id="buttonBatchPull"
                           onclick="batchSyncListing()">同步选中的商品</a>
                    </li>
                    <li>
                        <a href="#" class="font-bold VCDisabled" onclick="batchSyncLostListing()"
                           shiro:hasPermission="publication:syncLostListing:sync">同步缺失的商品</a>
                    </li>
                </ul>
            </div>
            <a class="btn btn-info multiple disabled VCDisabled" id="buttonRelistItem" onclick="batchRelistItem()"
               shiro:hasPermission="publication:listing:batchRelistItem">
                <i class="fa fa-plus"></i> 重上架商品
            </a>

            <a class="btn btn-success " onclick="findReplaceUpdate()" id="findReplaceUpdate"
               shiro:hasPermission="publication:listing:findReplaceUpdate">
                <i class="fa fa-check"></i> 查找替换更新
            </a>
            <a class="btn btn-success  multiple disabled VCDisabled" onclick="move2Draft()" id="move2Draft">
                <i class="fa fa-check"></i> 移至草稿
            </a>
            <a class="btn btn-success VCDisabled" onclick="findKeyword()" id="findKeyword">
                <i class="fa fa-check"></i> 查询关键词
            </a>
            <a class="btn btn-warning" onclick="exportSelected()" shiro:hasPermission="publication:listing:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-info" onclick="importListing()"
               shiro:hasPermission="publication:listing:importUpdateListing">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-success VCDisabled" onclick="todo()" id="todo"
               shiro:hasPermission="publication:listing:todo">
                <i class="fa fa-check"></i> 运营待办
            </a>
            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-danger btn-sm dropdown-toggle">添加黑名单 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="font-bold" id="stockUpdateBlackList" onclick="stockUpdateBlackList()" shiro:hasPermission="publication:listing:stockUpdateBlackList">
                            添加库存更新黑名单
                        </a>
                    </li>
                    <li>
                        <a class="font-bold" id="redLinePriceBlackList" onclick="redLinePriceBlackList()">
                            添加红线价管控白名单
                        </a>
                    </li>
                    <li>
                        <a class="font-bold" id="couponBlackList" onclick="couponBlackList()">
                            添加coupon更新黑名单
                        </a>
                    </li>
                </ul>
            </div>
            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-danger btn-sm dropdown-toggle">移除黑名单 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="font-bold" id="delStockUpdateBlackList" onclick="delStockUpdateBlackList()"  shiro:hasPermission="publication:listing:stockUpdateBlackList">
                            移除库存更新黑名单
                        </a>
                    </li>
                    <li>
                        <a class="font-bold" id="delRedLinePriceBlackList" onclick="delRedLinePriceBlackList()">
                            移除红线价管控白名单
                        </a>
                    </li>
                    <li>
                        <a class="font-bold" id="delCouponBlackList" onclick="delCouponBlackList()">
                            移除coupon更新黑名单
                        </a>
                    </li>
                </ul>
            </div>

            <a class="btn btn-success multiple disabled VCDisabled" onclick="oneKeyFollow()" id="oneKeyFollow"
               shiro:hasPermission="publication:goods:addAmazon">
                <i class="fa fa-plus"></i> 批量跟卖
            </a>
            <a class="btn btn-success multiple disabled" id="batchUpdatePlatformNode" onclick="batchModify.openBatchEditPlatformNode()"
               shiro:hasPermission="publication:goods:addAmazon">
                <i class="fa fa-plus"></i> 批量更新平台节点
            </a>
            <!-- 批量加入链接监控 -->
<!--            <a class="btn btn-success multiple disabled" id="batchAddLinkMonitor" onclick="batchAddLinkMonitor()"-->
<!--               shiro:hasPermission="publication:goods:addAmazon">-->
<!--                <i class="fa fa-plus"></i> 批量加入链接监控-->
<!--            </a>-->

            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-success btn-sm dropdown-toggle">批量加入链接监控 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="font-bold" id="batchAddLinkMonitor" onclick="batchAddLinkMonitor()" shiro:hasPermission="publication:goods:addAmazon">
                            批量加入链接监控
                        </a>
                    </li>
                    <li>
                        <a class="font-bold" id="batchAddLinkAdapterMonitor" onclick="batchUpdateEbayAdaptive('2')" shiro:hasPermission="publication:list:adapterMonitor">
                            监控适配状态
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 批量移除链接监控 -->
            <a class="btn btn-danger multiple disabled" id="batchRemoveLinkMonitor" onclick="batchRemoveLinkMonitor()"
               shiro:hasPermission="publication:goods:addAmazon">
                <i class="fa fa-remove"></i> 批量移除链接监控
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="listing-table"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: bootstrap-table-editable-js" />
<th:block th:include="include :: virtual-select-js"/>
<th:block th:include="include :: jQuery-upload-js"/>
<th:block th:include="include :: loadingoverlay-min-js"/>
<script th:src="@{/js/fancybox.umd.js}"></script>
<script th:inline="javascript">
    // 基础配置数据 - 先使用同步加载确保页面可用
    var publishType = [[${@dict.getType('publication_goods_publish_type')}]];
    var adaptStatus = [[${@dict.getType('sys_adaptation_status')}]]
    var isMainData = [[${@dict.getType('is_main_listing')}]]
    var cartData = [[${@dict.getType('cart_flag')}]]
    var canNotUpdateStatus = [[${@sysBaseConfig.getNoUpdateStatus()}]]
    var listingPerformanceData = [[${@dict.getType('listing_performance_list')}]]
    var listingLabelData = [[${@dict.getType('listing_label_list')}]]
    var prefix = ctx + "publication/listing";
    var prefix_upload = prefix+"/importUpdateListing"
    var prefix_goods = ctx + "publication/goods";
    var prefix_timed_task = ctx + "publication/timedTask";
    var jumpShopCode = [[${jumpShopCode}]];
    var jumpHeadIds = [[${jumpHeadIds}]];
    var jumpPlatform = [[${jumpPlatform}]];
    var currentUserId = [[${currentUserId}]];
    var publishSubStatus;

    // 临时回退到同步加载，确保页面可用
    var shopCodeData = [[${@cdpBaseConfig.getShopAllKVList()}]];
    var categoryIdsData = [[${@smcBaseConfig.getPlatformCategoryByPlatformCode('null')}]];
    var operationClassificationData = [[${operationClassificationList}]];
    var userDatas = [[${@sysBaseConfig.getSysUserNameAllKVList()}]];

    // 安全处理数据，防止null.map()错误
    let shopCode = [];
    if (shopCodeData && Array.isArray(shopCodeData)) {
        shopCode = shopCodeData.map(o => {
            return {label: o.value, value: o.key}
        });
    }

    let categoryId = [];
    if (categoryIdsData && Array.isArray(categoryIdsData)) {
        categoryId = categoryIdsData.map(o => {
            return {label: o.value, value: o.key}
        });
    }

    let operationClassification = [];
    if (operationClassificationData && Array.isArray(operationClassificationData)) {
        operationClassification = operationClassificationData.map(o => {
            return {label: o.value, value: o.key}
        });
    }

    let createBy = [];
    if (userDatas && Array.isArray(userDatas)) {
        createBy = userDatas.map(o => {
            return {label: o.value, value: o.key}
        });
    }

    // 异步加载基础数据函数（备用方案，当前未使用）
    // 当前使用同步加载确保页面稳定性

    // 异步加载相关函数已移除，当前使用同步加载方式

    // 异步数据加载函数已移除，使用同步方式确保页面稳定

    $(function () {
        console.log('页面开始初始化...');
        console.log('数据统计 - 店铺:', shopCode.length, '类目:', categoryId.length, '业务分类:', operationClassification.length, '用户:', createBy.length);

        try {
            virtualSelectRender(document.querySelectorAll("div#shop-select"), shopCode);
            virtualSelectRender(document.querySelectorAll("div#category-select"), categoryId);
            virtualSelectRender(document.querySelectorAll("div#operation-classification-select"), operationClassification);
            virtualSelectRender(document.querySelectorAll("div#createBy-select"), createBy);

            if ($.common.isNotEmpty(jumpShopCode)) {
                $("#shop-select")[0].setValue(jumpShopCode)
            }
            if ($.common.isNotEmpty(jumpPlatform)) {
                $('#platform').val(jumpPlatform).trigger('change');
            }
            if ($.common.isNotEmpty(jumpHeadIds)) {
                $('#codeType').val("1").trigger('change');
                $('#codeValue').val(jumpHeadIds);
            }
            if ($.common.isNotEmpty(currentUserId)) {
                $("#createBy-select")[0].setValue(currentUserId)
            }

            console.log('页面初始化完成');
        } catch (error) {
            console.error('页面初始化失败:', error);
        }
    })

    /**
     * 渲染虚拟下拉框
     * @param elements
     * @param optionList
     * @param multiple
     */
    function virtualSelectRender(elements, optionList, multiple = true,placeholder) {
        if (!elements || !elements.length) {
            return;
        }
        elements.forEach(item => {
            VirtualSelect.init({
                ele: item,
                options: optionList,
                showValueAsTags: false,
                disableAllOptionsSelectedText: true,
                alwaysShowSelectedOptionsCount: true,
                placeholder: placeholder?placeholder:'请选择',
                noOptionsText: '没有任何选项',
                noSearchResultsText: '未找到任何结果',
                searchPlaceholderText: '输入关键字搜索',
                optionsSelectedText: '项已选中',
                optionSelectedText: '项已选中',
                allOptionsSelectedText: '全选',
                clearButtonText: '清空',
                multiple: multiple,
                moreText: '更多',
                search: true,
                zIndex: 900
            });
        })
    }
    //刊登
    function publishAll(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要刊登选中的" + rows.length + "条Listing吗?", function () {
            var url = table.options.publishUrl;
            var data = {"ids": rows.join()};
            $.operate.submit(url, "post", "json", data,function(){
                publishStatusCountCallBack();
            });
        });
    } //刊登
    //批量送检
    function batchCensorship(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要送检选中的" + rows.length + "条Listing吗?", function () {
            var config = {
                url: prefix_goods + "/handleBatchCensorship",
                type: "post",
                dataType: "json",
                data:  {"ids": rows.join()},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍候...");
                },
                success: function (e) {
                    if (e.code ===0){
                        if (e.data.length!=0){
                        $.modal.openNoYes("送检结果", prefix_goods+ "/censorship/" + "/" + e.data, '1000', '600');
                        }else{
                            $.modal.alertSuccess(`操作成功`);
                        }
                        $.modal.closeLoading();
                    }else{
                        $.modal.alertError(`送检失败，` + e.msg);
                        $.modal.closeLoading();
                    }
                }
            };
            $.ajax(config)
        });
    }

    //定时刊登
    function buttonScheduledPublish(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var url = table.options.scheduledPublishUrl;
        $.modal.open("确认要定时刊登选中的" + rows.length + "条Listing吗?", url + "?ids=" + rows.join(),'800', '700', function (index, layero) {
            var data = layero.find("iframe")[0].contentWindow.validateSelections();
            if (data==false){
                return;
            }
            $.modal.close(index);
            $.operate.save(prefix + "/scheduledPublishToShop", data,function(){
                publishStatusCountCallBack();
            });

        });
    }
    //创建循环定时刊登
    function createCirculateScheduledPublish(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        var url = prefix_timed_task + "/task_add";
        $.modal.open("创建循环定时刊登任务", url + "?ids=" +(rows.length == 0 ? 0 : rows.join()) ,'800', '700', function (index, layero) {
            var data = layero.find("iframe")[0].contentWindow.validateSelections();
            if (data==false){
                return;
            }
            $.operate.save(prefix_timed_task + "/task_add", data,function(result){
                if (result.code == web_status.SUCCESS) {
                    $.modal.close(index);
                    publishStatusCountCallBack();
                } else {
                    $.modal.alertError(result.msg);
                }
            });

        });
    }
    //选择循环定时任务
    function selectCirculateScheduledPublish(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        var url = prefix_timed_task + "/task_edit";
        $.modal.open("选择循环定时刊登任务", url + "?ids=" + (rows.length == 0 ? null : rows.join()),'800', '700', function (index, layero) {
            var data = layero.find("iframe")[0].contentWindow.validateSelections();
            if (data==false){
                return;
            }
            $.operate.save(prefix_timed_task + "/task_edit", data,function(result){
                if (result.code == web_status.SUCCESS) {
                    $.modal.close(index);
                    publishStatusCountCallBack();
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        });
    }

    //复制
    function copyAll() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.open("批量复制", table.options.prefix + "/copyListing/" + rows.join(),'1200', '600',function(index, layero){
            var data = layero.find("iframe")[0].contentWindow.getSelections();
            if (data==false){
                return;
            }
            $.modal.close(index);
            $.operate.save(prefix + "/copyToShop", data,function(){
                publishStatusCountCallBack();
            });

        });

    }

    function oneKeyFollow(isVCCopy = false) {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        if (!isVCCopy)  {
            let data = $.table.selectRows();
            for (let i = 0; i < data.length; i++) {
                if ($.common.isEmpty(data[i].platformGoodsId)){
                    $.modal.alertWarning("主键为"+data[i].id+"的平台销售编码为空,请检查");
                    return;
                }
            }
        }
        const tabName = isVCCopy ? "VC复制" : "";
        let url = table.options.prefix + "/oneKeyFollow/" + rows.join();
        if (isVCCopy) {
            url += "/0"
        }else {
            url += "/1"
        }
        $.modal.open(tabName, url,'1200', '600',function(index, layero){
            //增加loading 到最外层
            var indexLoad = top.layer.load(
                1, {
                    shade: [0.1, '#fff'] //0.1透明度的白色背景
                }
            )
            var data = layero.find("iframe")[0].contentWindow.getSelections();
            if (data==false){
                return;
            }
            $.operate.save(prefix + "/copyToShop", data,function(result){
                if (result.code == web_status.SUCCESS) {
                    $.modal.close(index);
                    publishStatusCountCallBack();
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.close(indexLoad);
            });

        });
    }

    function stockUpdateBlackList() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        // 组合弹窗内容，包含提示信息和单选按钮
        let html = `
        <div class="form-horizontal m" style="width: 400px; margin: 0 auto;">
            <div class="row">
                <div class="form-group">
                    <p style="color:black;font-size: 14px; text-align:center;">确认把选中的 <b>${rows.length}</b> 条数据加入黑名单吗?</p>
                </div>
                <div class="form-group">
                    <label class="col-sm-4 control-label" style="margin-top: 4px;color:red;font-size: 12px">库存是否需要调零：</label>
                    <div class="col-sm-8">
                        <div class="radio-box">
                            <input type="radio" id="isZeroY" name="isZero" checked value="Y">
                            <label for="isZeroY">是</label>
                        </div>
                        <div class="radio-box">
                            <input type="radio" id="isZeroN" name="isZero" value="N">
                            <label for="isZeroN">否</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>`;

        let width = '500px';
        let height = '220px';

        top.layer.open({
            type: 1,
            title: "系统提示",
            area: [width, height],
            content: html,
            btn: ['确定', '取消'],
            yes: function (index, layero) {
                let isZero = layero.find("input[name='isZero']:checked").val();
                var url = ctx + 'configuration/exclude/addByType';
                var data = {
                    "excludeData": rows.join(),
                    "excludeType": 1,
                    "stockZero": isZero
                };
                $.operate.submit(url, "post", "json", data, function () {
                    top.layer.close(index);
                    publishStatusCountCallBack();
                });
            }
        });
    }


    function delStockUpdateBlackList() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认把选中的" + rows.length + "条数据移除黑名单吗?", function () {
            var url = ctx + 'configuration/exclude/delByType';
            var data = {
                "excludeData": rows.join(),
                "excludeType": 1,
            };
            $.operate.submit(url, "post", "json", data, function () {
                publishStatusCountCallBack();
            });
        });
    }


    function redLinePriceBlackList() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var now = new Date();
        // var oneYearLater = new Date();
        // oneYearLater.setFullYear(now.getFullYear() + 1);

        var formatDateTime = function (date) {
            var year = date.getFullYear();
            var month = (date.getMonth() + 1).toString().padStart(2, '0');
            var day = date.getDate().toString().padStart(2, '0');
            var hours = date.getHours().toString().padStart(2, '0');
            var minutes = date.getMinutes().toString().padStart(2, '0');
            var seconds = date.getSeconds().toString().padStart(2, '0');
            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
        };

        var nowFormatted = formatDateTime(now);
        var oneYearLaterFormatted = formatDateTime(now);

        var content =
            '<form class="form-horizontal" id="redLineWhitelistForm">' +
            '    <div class="form-group">' +
            '        <label class="col-sm-3 control-label"><span class="text-danger">*</span> 生效时间：</label>' +
            '        <div class="col-sm-8">' +
            '            <div class="input-group date">' +
            '                <input type="text" class="form-control" id="effectTime" name="effectTime" value="' + nowFormatted + '" required>' +
            '                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>' +
            '            </div>' +
            '            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 白名单生效的开始时间</span>' +
            '        </div>' +
            '    </div>' +
            '    <div class="form-group">' +
            '        <label class="col-sm-3 control-label"><span class="text-danger">*</span> 结束时间：</label>' +
            '        <div class="col-sm-8">' +
            '            <div class="input-group date">' +
            '                <input type="text" class="form-control" id="endTime" name="endTime" value="' + oneYearLaterFormatted + '" required>' +
            '                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>' +
            '            </div>' +
            '            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 白名单失效的结束时间</span>' +
            '        </div>' +
            '    </div>' +
            '    <div class="form-group">' +
            '        <label class="col-sm-3 control-label"><span class="text-danger">*</span> 添加原因：</label>' +
            '        <div class="col-sm-8">' +
            '            <textarea class="form-control" id="reason" name="reason" placeholder="请输入添加原因" required></textarea>' +
            '            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请简要说明添加白名单的原因</span>' +
            '        </div>' +
            '    </div>' +
            '    <div class="form-group">' +
            '        <div class="col-sm-12 text-center" style="color: #666; margin-top: 10px;">' +
            '            <i class="fa fa-exclamation-circle"></i> 所有带 <span class="text-danger">*</span> 的字段为必填项' +
            '        </div>' +
            '    </div>' +
            '</form>';

        layer.open({
            type: 1,
            title: '<i class="fa fa-list-alt"></i> 确认加入红线价管控白名单',
            area: ['550px', '430px'],
            content: content,
            scrollbar: false,
            btn: ['确认', '取消'],
            btnAlign: 'c',
            yes: function (index, layero) {
                var effectTime = layero.find('#effectTime').val();
                var endTime = layero.find('#endTime').val();
                var reason = layero.find('#reason').val().trim();

                if (!effectTime) {
                    $.modal.alertWarning("请选择生效时间");
                    return;
                }
                if (!endTime) {
                    $.modal.alertWarning("请选择结束时间");
                    return;
                }
                if (!reason) {
                    $.modal.alertWarning("请输入添加原因");
                    return;
                }

                var effectDate = new Date(effectTime);
                var endDate = new Date(endTime);
                if (endDate <= effectDate) {
                    $.modal.alertWarning("结束时间必须大于生效时间");
                    return;
                }

                layer.close(index);

                var confirmMsg = "确认把选中的" + rows.length + "条数据加入红线价管控白名单" +
                    "<br>生效时间: " + effectTime +
                    "<br>结束时间: " + endTime +
                    "<br>添加原因: " + reason;

                $.modal.confirm(confirmMsg, function () {
                    var url = ctx + 'configuration/exclude/addRedLinePriceBlackList';
                    var data = {
                        "excludeData": rows.join(),
                        "params[effectTime]": effectTime,
                        "params[endTime]": endTime,
                        "params[reason]": reason
                    };
                    $.operate.submit(url, "post", "json", data, function () {
                        publishStatusCountCallBack();
                    });
                });
            },
            success: function (layero, index) {
                layui.use('laydate', function () {
                    var laydate = layui.laydate;

                    laydate.render({
                        elem: layero.find('#effectTime')[0],
                        type: 'datetime',
                        trigger: 'click',
                        theme: 'molv',
                        format: 'yyyy-MM-dd HH:mm:ss'
                    });

                    laydate.render({
                        elem: layero.find('#endTime')[0],
                        type: 'datetime',
                        trigger: 'click',
                        theme: 'molv',
                        format: 'yyyy-MM-dd HH:mm:ss'
                    });
                });

                // 美化样式
                $(layero).find('.layui-layer-title').css({
                    'background-color': '#f8f8f8',
                    'border-bottom': '1px solid #eee',
                    'height': '45px',
                    'line-height': '45px',
                    'font-size': '14px'
                });

                $(layero).find('.layui-layer-btn').css({
                    'border-top': '1px solid #eee',
                    'padding': '10px'
                });

                $(layero).find('.layui-layer-btn a').css({
                    'padding': '5px 15px',
                    'border-radius': '3px'
                });

                $(layero).find('.layui-layer-btn0').css({
                    'background-color': '#1ab394',
                    'border-color': '#1ab394'
                });

                $(layero).find('.layui-layer-content').css({
                    'overflow': 'hidden',
                    'padding': '15px'
                });

                $(layero).find('form').css('padding-right', '10px');

                $(layero).find('.input-group').css('cursor', 'pointer').on('click', function (e) {
                    e.stopPropagation();
                    $(this).find('input').trigger('click');
                });

                $(layero).find('.input-group-addon').on('click', function (e) {
                    e.stopPropagation();
                    $(this).prev('input').trigger('click');
                });
            }
        });
    }

    function couponBlackList() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认把选中的" + rows.length + "条数据加入Coupon黑名单吗?", function () {
            var url = ctx + 'configuration/exclude/addCouponBlackList';
            var data = {
                "excludeData": rows.join()
            };
            $.operate.submit(url, "post", "json", data, function () {
                publishStatusCountCallBack();
            });
        });
    }

    function delRedLinePriceBlackList() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认把选中的" + rows.length + "条数据移除红线价管控白名单吗?", function () {
            var url = ctx + 'configuration/exclude/delRedLinePriceBlackList';
            var data = {
                "excludeData": rows.join()
            };
            $.operate.submit(url, "post", "json", data, function () {
                publishStatusCountCallBack();
            });
        });
    }

    function delCouponBlackList() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认把选中的" + rows.length + "条数据移除Coupon黑名单吗?", function () {
            var url = ctx + 'configuration/exclude/delCouponBlackList';
            var data = {
                "excludeData": rows.join()
            };
            $.operate.submit(url, "post", "json", data, function () {
                publishStatusCountCallBack();
            });
        });
    }

    // 导出数据
    function exportSelected() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        $.modal.openOptions({
            title: $.common.isNotEmpty(rows)?"导出勾选" + rows.length + "条数据":"导出全部" + table.options.modalName + "的数据",
            url: prefix + "/exportColumns",
            shadeClose: false,
            height: 600,
            yes: function (index, layero){
                var iframeWin = layero.find('iframe')[0];
                let selectNames = iframeWin.contentWindow.submitHandler(index, layero);
                var dataParam =[];
                if ($.common.isNotEmpty(rows)) {
                    dataParam.push({ "name": "ids", "value": rows.join() });
                }else {
                    var params = $("#" + table.options.id).bootstrapTable('getOptions');
                    dataParam = $("#formId" ).serializeArray();
                    dataParam.push({"name": "orderByColumn", "value": params.sortName});
                    dataParam.push({"name": "isAsc", "value": params.sortOrder});
                }
                //将selectNames数组和dataParam数组合并
                var newArr = dataParam.concat(selectNames);
                $.modal.loading("正在导出数据，请稍候...");
                $.modal.close(index);
                $(".layui-layer-btn0").addClass("layui-btn-disabled").attr("disabled","disabled");
                $.post(table.options.exportUrl, newArr, function (result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                });
            }
        });
    }
    //下架
    function stopPublishAll(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要下架选中的" + rows.length + "条Listing吗?", function () {
            var url = table.options.stopPublishUrl;
            var data = {"ids": rows.join()};
            $.operate.submit(url, "post", "json", data,function(){
                publishStatusCountCallBack();
            });
        });
    }
    //批量同步店铺商品
    function syncShopListing(){
        table.set();
        var url = table.options.syncListingUrl;

        $.modal.open("批量同步店铺listing", url,'800', '500',function(index, layero){
            var data = layero.find("iframe")[0].contentWindow.getSelections();
            $.modal.close(index);
            $.operate.save(url, data,function(){
                publishStatusCountCallBack();
            });
        });
    }

    //批量同步选中的商品
    function batchSyncListing(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要同步选中的" + rows.length + "条Listing吗?", function () {
            var url = table.options.syncListingUrl;
            var data = {"goodsIds": rows.join()};
            $.operate.submit(url, "post", "json", data,function(){
                publishStatusCountCallBack();
            });
        });
    }

    //
    $(document).on("click",".editable",function (){
        let input = $(this).parents("td").find("input");
        setTimeout(function(){
            //清空input框的值 以免影响下次的值
            input.val("");
        },150)
    });
    /**
     * 同步缺失的listing
     */
    function batchSyncLostListing() {
        table.set();
        var url = table.options.syncLostListingUrl;

        $.modal.open("批量同步店铺缺失listing", url,'800', '400',function(index, layero){
            var data = layero.find("iframe")[0].contentWindow.getSelections();
            $.modal.close(index);
            $.operate.save(url, data,function(){
                publishStatusCountCallBack();
            });
        });
    }
    //定时重上架
    function batchRelistItem(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = table.options.scheduledRelistItemUrl;
        $.modal.open("重上架商品", url + "?ids=" + rows.join(),'800', '700', function (index, layero) {
            var data = layero.find("iframe")[0].contentWindow.validateRelSelections();
            if (data==false){
                return;
            }
            $.modal.close(index);
            $.operate.save(prefix + "/saveRelistItem", data,function(){
                publishStatusCountCallBack();
            });

        });
    }
    //查找替换更新
    function  findReplaceUpdate(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        var url = table.options.findReplaceUpdateUrl;
        $.modal.open("查找替换更新", url ,'800', '600', function (index, layero) {
            var data = layero.find("iframe")[0].contentWindow.getWords();
            var findWord = layero.find("iframe")[0].contentWindow.getFindWord();
            var replaceWord = layero.find("iframe")[0].contentWindow.getReplaceWord();
            if (data == false) {
                return;
            }
            data.push({"name": "headIds", "value": rows.join()});
            console.log(data);
            $.modal.close(index);
            $.modal.confirm("确认要更新替换选中的" + rows.length + "条数据吗; 查找词:" + findWord + "替换成:" + replaceWord, function () {
                $.operate.save(prefix + "/findReplaceUpdate", data, function () {
                    publishStatusCountCallBack();
                });
            });
        });
    }

    //移至草稿
    function move2Draft() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认把选中的" + rows.length + "条数据移至草稿吗?", function () {
            var url = table.options.move2DraftUrl;
            var data = {"ids": rows.join()};
            $.operate.submit(url, "post", "json", data, function () {
                publishStatusCountCallBack();
            });
        });

    }
    //查找关键词
    function findKeyword(){
        table.set();
        var url = table.options.findKeywordUrl;

        $.modal.open("查找关键词", url,'800', '400',function(index, layero){
            var data = layero.find("iframe")[0].contentWindow.getSelections();
            $.modal.close(index);
            $.operate.save(url, data,function(){
                publishStatusCountCallBack();
            });
        });
    }
    //运营待办
    function todo() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.open("运营待办", table.options.prefix + "/todo/" + rows.join(),'1200', '600',function(index, layero){
            var data = layero.find("iframe")[0].contentWindow.getSelections();
            if (data==false){
                return;
            }
            $.modal.close(index);
            $.operate.save(table.options.todoUrl, data,function(){
                publishStatusCountCallBack();
            });
        });
    }

    //删除
    function removeAll() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认要删除选中的" + rows.length + "条数据吗?", function () {
            var url = table.options.removeUrl;
            var data = {"ids": rows.join()};
            $.operate.submit(url, "post", "json", data,function(){
                publishStatusCountCallBack();
            });
        });
    }
    //批量取消定时刊登
    function batchCancelTimePublish() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认把选中的" + rows.length + "条数据取消定时刊登吗?", function () {
            var url = table.options.cancelScheduledPublish;
            var data = {"ids": rows.join()};
            $.operate.submit(url, "post", "json", data,function(){
                publishStatusCountCallBack();
            });
        });
    }
    //批量更新标题
    function batchUpdateTitle(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.openTab("批量更新标题详情",prefix + "/batchUpdateTitle?listingIds="+rows.join());
    }
    //批量更新价格库存
    function batchUpdatePriceAndStock(platform){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        // 判断是否包含ebay的listing
        let selectRows = $.table.selectRows();
        if (platform == 'AM') {
            // 判断是否包含亚马逊的listing
            let amListingRows = selectRows.filter(o => o.platform == 'AM');
            if (amListingRows.length == 0 ) {
                $.modal.alertWarning("请选择亚马逊的listing");
                return;
            }
            // VC和SC不能同时选中
            let vcListingCount = amListingRows.filter(o => o.shopCode.includes('VC')).length;
            let scListingCount = amListingRows.filter(o => o.shopCode.includes('SC')).length;
            if (vcListingCount > 0 && scListingCount > 0) {
                $.modal.alertWarning("不能同时更新VC和SC的listing的价格和库存");
                return;
            }
            
            $.modal.openOptions({
                title: "批量更新价格库存详情",
                url: prefix + "/batchUpdatePriceAndStock?listingIds=" +rows.join(),
                width: $(window).width() - 200,
                height: 800,
                shadeClose: false,
                yes: function (index, layero){
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(index, layero);
                }
            });
        } else if (platform == 'EB') {
            // 只支持在售链接
            let unOnSaleRows = selectRows.filter(o => o.platform == 'EB');
            if (unOnSaleRows.length == 0 ) {
                $.modal.alertWarning("请选择ebay的listing");
                return;
            }
            
            $.modal.openTab("批量更新价格库存详情",prefix + "/batchUpdatePriceAndStock?listingIds="+rows.join());
        }
    
    }
    //批量更新库存
    function batchUpdateStock(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        // 判断是否包含亚马逊的listing
        let selectRows = $.table.selectRows();
        // 只支持在售链接
        let unOnSaleRows = selectRows.filter(o => o.platform == 'AM' && (o.platformGoodsId == null || o.platformGoodsId == '') && o.shopCode == 'VC1' && o.publishType == '5');
        if (unOnSaleRows.length > 0 ) {
            $.modal.alertWarning("请选择VCDF店铺的在售数据");
            return;
        }
        $.modal.openFull("批量更新库存(VC)", prefix + "/batchUpdateStock?listingIds=" + selectRows.map(o => o.id).join());
    }
    //批量更新属性
    function batchUpdateAttribute(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.openTab("批量更新属性详情",prefix + "/batchUpdateAttribute?listingIds="+rows.join());
    }
    //批量更新图片
    function batchUpdatePictures(){
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.openTab("批量更新图片",prefix + "/batchUpdatePictures?listingIds=" + rows.join());
    }


    //批量更新适配
    function batchUpdateEbayAdaptive(type) {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        //弹窗提示 会清空所选择listing的适配信息,从ads重新会去适配信息,在售的listing会更新到线上
        let platforms = $.table.selectSecondColumns();
        if (platforms.length != 1) {
            $.modal.alertWarning("只能选择同一平台的listing进行更新或监控");
            return;
        }
        let platform = platforms[0];
        if (platform != 'EB' && platform != 'AM') {
            $.modal.alertWarning("只能选择ebay、amazon平台的listing进行适配更新或监控");
            return;
        }
        $.modal.confirm("listing适配信息将重置更新，是否继续操作？", function () {
            var url = prefix + "/batchUpdateEbayAdaptive"
            if ($.common.equals('2', type)){
                url  = prefix + "/batchMonitorAmazonAdaptive"
            }
            var data = {
                ids: rows.join(),
                platform: platform
            };
            $.operate.submit(url, "post", "json", data, function () {
                publishStatusCountCallBack();
            });
        });
    }

    //批量更新视频
    function batchUpdateVideo() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.openTab("批量更新视频",prefix + "/batchUpdateVideo?listingIds=" + rows.join());
    }


    //批量更新物流信息
    function batchUpdateShipping() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        let platforms = $.table.selectSecondColumns();
        if (platforms.length != 1) {
            $.modal.alertWarning("只能选择同一平台的listing进行更新");
            return;
        }
        let platform = platforms[0];
        $.modal.confirm("listing更新物流信息按照店铺物流模板重置更新，是否继续操作？", function () {
            var url = prefix + "/batchUpdateShipping"
            var data = {
                ids: rows.join(),
                platform: platform
            };
            $.operate.submit(url, "post", "json", data, function () {
                publishStatusCountCallBack();
            });
        });
    }

    //批量更新付款退货信息
    function batchUpdatePolicy() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        let platforms = $.table.selectSecondColumns();
        if (platforms.length != 1) {
            $.modal.alertWarning("只能选择同一平台的listing进行更新");
            return;
        }
        let platform = platforms[0];
        $.modal.confirm("listing更新付款退货信息按照店铺付款退货重置更新，是否继续操作？", function () {
            var url = prefix + "/batchUpdatePolicy"
            var data = {
                ids: rows.join(),
                platform: platform
            };
            $.operate.submit(url, "post", "json", data, function () {
                publishStatusCountCallBack();
            });
        });
    }

    //批量更新描述
    function batchUpdateDescriptionTemplate() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        let platforms = $.table.selectSecondColumns();
        if (platforms.length != 1) {
            $.modal.alertWarning("只能选择同一平台的listing进行更新");
            return;
        }
        let platform = platforms[0];
        $.modal.confirm("listing描述按照应用描述模板数据批量更新，是否继续操作？", function () {
            var url = prefix + "/batchUpdateDescriptionTemplate"
            var data = {
                ids: rows.join(),
                platform: platform
            };
            $.operate.submit(url, "post", "json", data, function () {
                publishStatusCountCallBack();
            });
        });
    }

    


    //批量更新描述
    function batchUpdateDescription() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.openTab("批量更新描述",prefix + "/batchUpdateDescription?listingIds=" + rows.join());
    }
    
    //批量修改可用状态
    function batchUpdateAvailability() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        // 判断是否包含亚马逊的listing
        let selectRows = $.table.selectRows();
        // 过滤掉非AM平台的
        selectRows = selectRows.filter(o => o.platform == 'AM');
        if (selectRows.length == 0) {
            $.modal.alertWarning("请至少选择一条亚马逊listing");
            return;
        }
        // 过滤可用状态
        selectRows = selectRows.filter(o => o.publishStatus == 2 || o.publishStatus == 6 || o.publishStatus == 4)
        if (selectRows.length == 0) {
            $.modal.alertWarning("请至少选择一条状态为在售或非在售的亚马逊listing");
            return;
        }
        // 只支持VC
        if (selectRows.some(o => !o.shopCode.includes('VC'))) {
            $.modal.alertWarning("只支持VC店铺的listing");
            return;
        }
        // 弹窗提示
        $.modal.openOptions ( {
            title: "批量修改可用状态",
            url: prefix + "/batchUpdateAvailability?ids=" + selectRows.map(o => o.id).join(),
            width: 1200,
            height: 400,
            yes:  function (index, layero){
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.submitHandler(index, layero);
            }
        });
    }

    //批量更新五点描述
    function buttonBatchUpdateFiveDescription() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.openTab("批量更新五点描述",prefix + "/batchUpdateFiveDescription?listingIds=" + rows.join());

    }

    //批量编辑
    function batchEditListing() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        // 判断是否包含亚马逊的listing
        let selectRows = $.table.selectRows();

        // 判断是否包含不允许修改的链接
        let canNotUpdateStatusRows = selectRows.filter(o => canNotUpdateStatus.includes(parseInt(o.publishStatus)));
        if (canNotUpdateStatusRows.length > 0) {
            $.modal.alertWarning("主键ID：" + canNotUpdateStatusRows.map(o => o.id).join(",") + "状态不允许修改");
            return;
        }



        //    //按照平台分组,取出数量最多的平台
        // Optional<List<GoodsHead>> optional = heads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform)).values().stream().sorted((o1, o2) -> o2.size() - o1.size()).findFirst();
            // if (!optional.isPresent()) {
                // throw new BusinessException("该选择有效Listing.");
            // }
            // platform = optional.get().get(0).getPlatform();

        // 与上面后端的逻辑保持一致

        const platformCountMap = selectRows.reduce((acc, cur) => {
            acc[cur.platform] = (acc[cur.platform] || 0) + 1
            return acc
        }, {})
        
        const amazonCount = platformCountMap.AM || 0
        const ebayCount = platformCountMap.EB || 0
        let platform = '';
        if (amazonCount == ebayCount) {
            // 数量相等，取最大ID的platform
            platform = selectRows.sort((a, b) => b.id - a.id)[0].platform;
        } else if (amazonCount > ebayCount) {
            platform = 'AM';
        } else {
            platform = 'EB';
        }

        if (platform == 'AM') {
            // 不能同时包含VC和SC
            let shopCode = selectRows.map(o => o.shopCode);
            let shopCodeSet = new Set(shopCode);
            let containsVC = shopCode.some(o => o.includes("VC"));
            // 任意一个店铺code包含VC
            if (shopCodeSet.size > 1 && containsVC) {
                $.modal.alertWarning("不能同时选择VC和SC的链接");
                return;
            }

            let categoryIds = selectRows.filter(o => o.platform == 'AM').map(o => o.categoryId);
            let categoryIdSet = new Set(categoryIds);
            if (categoryIdSet.size > 1) {
                $.modal.alertWarning("请选择同一产品品类的数据");
                return;
            }
        }

        $.modal.openFullNoYes("批量编辑详情", prefix + "/v2/listingBatchEdit?listingIds=" + selectRows.map(o => o.id).join());
    }

    function batchAddLinkMonitor() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        // 只能选择亚马逊的链接
        let selectRows = $.table.selectRows();
        let amazonRows = selectRows.filter(o => o.platform == 'AM');
        if (amazonRows.length == 0) {
            $.modal.alertWarning("请至少选择一条亚马逊链接");
            return;
        }

        // 弹窗提示
        $.modal.confirm("选中的" + amazonRows.length + "条链接将加入链接监控，是否继续操作？", function () {
            $.operate.submit(prefix_goods + "/batchAddMonitor", "post", "json", {
                listingIds: amazonRows.map(o => o.id).join()
            }, function () {
                $.table.refresh();
            });
        });
    }

    function batchRemoveLinkMonitor() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        // 只能选择亚马逊的链接 
        let selectRows = $.table.selectRows();
        let amazonRows = selectRows.filter(o => o.platform == 'AM');
        if (amazonRows.length == 0) {
            $.modal.alertWarning("请至少选择一条亚马逊链接");
            return;
        }
        // 弹窗提示
        $.modal.confirm("选中的" + amazonRows.length + "条链接将移除链接监控，是否继续操作？", function () {
            $.operate.submit(prefix_goods + "/batchRemoveMonitor", "post", "json", {
                listingIds: amazonRows.map(o => o.id).join()
            }, function () {
                $.table.refresh();
            });
        });
    }
    // 批量修改
    const batchModify = {
        noStatus: [1001,1,3,5,9001,9002,],
        ids: '',
        // 处理按钮状态
        handleBtnStatus:(statusList) => {
            const canBatch = statusList.length ? statusList.some(o => batchModify.noStatus.includes(o)) : true
            $('#buttonBatchEdit').toggleClass('disabled', canBatch);
            $('#buttonBatchEditListing').toggleClass('disabled', canBatch);
            $('#buttonBatchEditByExcel').toggleClass('disabled', canBatch);
            $('#buttonBatchUpdateTitle').toggleClass('disabled', canBatch);
            $('#buttonBatchUpdateAMPriceAndStock').toggleClass('disabled', canBatch);
            $('#buttonBatchUpdateEBPriceAndStock').toggleClass('disabled', canBatch);
            $('#batchUpdateStock').toggleClass('disabled', canBatch);
            $('#buttonBatchUpdateAttribute').toggleClass('disabled', canBatch);
            $('#batchUpdateVideo').toggleClass('disabled', canBatch);
            $('#buttonBatchUpdatePictures').toggleClass('disabled', canBatch);
            $('#buttonBatchUpdateEbayAdaptive').toggleClass('disabled', canBatch);
            $('#buttonBatchUpdateDescription').toggleClass('disabled', canBatch);
            $('#buttonBatchUpdateAvailability').toggleClass('disabled', canBatch);
            $('#buttonBatchUpdateFiveDescription').toggleClass('disabled', canBatch);
            $('#batchUpdatePlatformSku').toggleClass('disabled', canBatch);
            $('#batchUpdatePolicy').toggleClass('disabled', canBatch);
            $('#batchUpdateDescriptionTemplate').toggleClass('disabled', canBatch);
            $('#batchUpdateShipping').toggleClass('disabled', canBatch);

            $('#batchUpdatePlatformNode').toggleClass('disabled', canBatch);
        },
        disableVCHandle:(publishTypes, siteCode,platform) => {
            // shopCode 属于VC店铺 并且 siteCode 不是US
            const siteCodes =$.common.isNotEmpty(siteCode) ? siteCode.filter(o => !$.common.equals(o,"US")) : [];
            let platformCurrent = $.common.isNotEmpty(platform) ? platform.filter(o => $.common.equals(o,"AM")) : [];

            const canHandle = publishTypes.includes('5') || publishTypes.includes('6') || ($.common.isNotEmpty(siteCodes)&& $.common.isNotEmpty(platformCurrent));
            $(".VCDisabled").attr("disabled", canHandle).css("pointer-events",canHandle ? "none" : "auto");
        
            //非US站点 禁止刊登、删除等等操作
            if ($.common.isNotEmpty(siteCodes) && $.common.isNotEmpty(platformCurrent) ){
                $('#buttonPublish').attr('disabled', true).css("pointer-events", "none");
                $('#buttonRemove').attr('disabled', true).css("pointer-events", "none");
                $('#buttonBatchUpdateTitle').attr('disabled', true).css("pointer-events", "none");
                $('#buttonBatchUpdateFiveDescription').attr('disabled', true).css("pointer-events", "none");
                $('#buttonBatchUpdatePictures').attr('disabled', true).css("pointer-events", "none");
                $('#buttonBatchUpdateDescription').attr('disabled', true).css("pointer-events", "none");
                $('#batchUpdatePlatformNode').attr('disabled', true).css("pointer-events", "none");
                $('#buttonCopy').attr('disabled', true).css("pointer-events", "none");
            }else {
                $('#buttonPublish').attr('disabled', false).css("pointer-events", "auto");
                $('#buttonRemove').attr('disabled', false).css("pointer-events", "auto");
                $('#buttonBatchUpdateTitle').attr('disabled', false).css("pointer-events", "auto");
                $('#buttonBatchUpdateFiveDescription').attr('disabled', false).css("pointer-events", "auto");
                $('#buttonBatchUpdatePictures').attr('disabled', false).css("pointer-events", "auto");
                $('#buttonBatchUpdateDescription').attr('disabled', false).css("pointer-events", "auto");
                $('#batchUpdatePlatformNode').attr('disabled', false).css("pointer-events", "auto");
                $('#buttonCopy').attr('disabled', false).css("pointer-events", "auto");
            }
        },
        openBatchEdit() {
            table.set('listing-table');
            let rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
            if (!rows.length) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            $.modal.openOptions({
                title: "批量修改",
                url: prefix + "/batchEdit?ids=" + rows.join(','),
                width: $(window).width() - 200,
                height: $(window).height() - 50,
                shadeClose: false,
                yes: function (index, layero){
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(index, layero);
                }
            });
        },
        openBatchEditPlatformSku() {
            table.set('listing-table');
            let rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
            if (!rows.length) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            // 判断是否包含亚马逊的listing
            let selectRows = $.table.selectRows();
            // 过滤掉AM平台的
            selectRows = selectRows.filter(o => o.platform == 'AM');
            if (selectRows.length == 0) {
                $.modal.alertWarning("请至少选择一条亚马逊listing");
                return;
            }
            // 过滤状态非0
            selectRows = selectRows.filter(o => o.publishStatus == 0);
            if (selectRows.length == 0) {
                $.modal.alertWarning("请至少选择一条状态为草稿的亚马逊listing");
                return;
            }

            $.modal.openOptions({
                title: "批量更新平台SKU",
                url: prefix + "/batchUpdatePlatformSku?ids=" + rows.join(','),
                width: $(window).width() - 200,
                height: $(window).height() - 50,
                shadeClose: false,
                yes: function (index, layero){
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(index, layero);
                }
            });
        },
        openBatchEditByExcel() {
            table.set('listing-table');
            let rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
            if (!rows.length) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            // 判断是否包含亚马逊的listing
            let selectRows = $.table.selectRows();

            let productType = selectRows.map(o => o.productType);
            let productTypeSet = new Set(productType);
            if (productTypeSet.size > 1) {
                $.modal.alertWarning("请选择同一产品类型的数据");
                return;
            }
            let platform = selectRows.map(o => o.platform);
            let ebayCount = platform.filter(o => o == 'EB').length;
            if (ebayCount > 0) {
                $.modal.alertWarning("不支持批量编辑ebay链接的属性");
                return;
            }
          

            // 不能同时包含VC和SC
            let shopCode = selectRows.map(o => o.shopCode);
            let shopCodeSet = new Set(shopCode);
            let containsVC = shopCode.some(o => o.includes("VC"));
            // 任意一个店铺code包含VC
            if (shopCodeSet.size > 1 && containsVC) {
                $.modal.alertWarning("不能同时选择VC和SC的链接");
                return;
            }

            $.modal.openOptions({
                title: "批量修改属性",
                url: prefix + "/batchEditByExcel?ids=" + selectRows.map(o => o.id).join(','),
                width: $(window).width() - 200,
                height: $(window).height() - 50,
                shadeClose: false,
                yes: function (index, layero){
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(index, layero);
                }
            });
        },
        openBatchEditPlatformNode() {
            table.set('listing-table');
            let rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
            if (!rows.length) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            // 判断是否包含亚马逊的listing
            let selectRows = $.table.selectRows();
            // 过滤掉AM平台的
            selectRows = selectRows.filter(o => o.platform == 'AM');
            if (selectRows.length == 0) {
                $.modal.alertWarning("请至少选择一条亚马逊listing");
                return;
            }
            // 过滤状态非0
            selectRows = selectRows.filter(o => o.publishStatus == 2  || o.publishStatus == 4 || o.publishStatus == 6);
            if (selectRows.length == 0) {
                $.modal.alertWarning("请至少选择一条状态为在售的亚马逊listing");
                return;
            }

            $.modal.openOptions({
                title: "批量更新平台节点",
                url: prefix + "/batchUpdatePlatformNode?ids=" + rows.join(','),
                width: $(window).width() - 200,
                height: $(window).height() - 50,
                shadeClose: false,
                yes: function (index, layero){
                    var iframeWin = layero.find('iframe')[0];
                    iframeWin.contentWindow.submitHandler(index, layero);
                }
            });
        },
    };
    // 全局禁用/恢复页面状态
    function togglePageState(isLoading) {
        if (isLoading) {
            $.modal.loading("正在查询，请稍后...");
        } else {
            $.modal.closeLoading();
        }

    }
    function listingTable() {
        $.table.destroy();
        togglePageState(true);
        var options = {
            id: "listing-table",
            prefix: prefix ,
            url: prefix + "/list",
            removeUrl: prefix + "/remove",
            stopPublishUrl: prefix + "/stopPublish",
            publishUrl: prefix + "/publish",
            scheduledPublishUrl: prefix + "/scheduledPublish",
            scheduledRelistItemUrl: prefix + "/scheduledRelistItem",
            findReplaceUpdateUrl: prefix + "/findReplaceUpdate",
            exportUrl: prefix + "/export",
            importUrl: prefix_upload,
            // importTemplateUrl: prefix + "/importTemplate",
            updateUrl: prefix + "/edit/{id}",
            cancelScheduledPublish: prefix + "/cancelScheduledPublish",
            syncListingUrl: prefix + "/syncListing",
            syncLostListingUrl: prefix + "/syncLostListing",
            findKeywordUrl: prefix + "/findKeyword",
            relistItemUrl: prefix + "/relistItem",
            move2DraftUrl: prefix + "/move2Draft",
            todoUrl: ctx + "backlog/add",
            sortName: "id",
            sortOrder: "desc",
            modalName: "Listing管理",
            uniqueId: "id",
            onEditableSave: onEditableSave,
            pageList: [10, 25, 50, 100, 500],
            onLoadSuccess: function () {
                // 数据加载成功时，恢复页面
                togglePageState(false);
            },
            onLoadError: function () {
                // 数据加载失败时，恢复页面
                togglePageState(false);
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键',
                    sortable: true

                },{
                    field: 'platform',
                    title: '平台',
                    hidden: true
                },
                {
                    field: 'mainImageUrl',
                    title: 'Listing主图',
                    align: 'center',
                    valign: 'middle',
                    formatter: function (value, row, index) {
                        // return $.table.imageView($.common.isEmpty(value) ? 'https://oss.cloud.suncentgroup.com/badbe99ebfbe4dc2ad90a756bacd9d7c.jpg' : value );
                        var actions = [];
                        actions.push('<div style="width: 64px;height: 64px">');
                        actions.push("<a data-fancybox=\"\" href="+value+">");
                        if ($.common.isNotEmpty(value)) {
                            actions.push("<img class='scale-img' style='width: 64px;height: 64px;border-radius: 6px; object-fit: cover;' data-height='auto' data-width='auto' data-target='_self' src='"+value+"'/>");
                        } else {
                            actions.push("<img class='scale-img' style='width: 64px;height: 64px;border-radius: 6px; object-fit: cover;' data-height='auto' data-width='auto' data-target='%_self' src='https://oss.cloud.suncentgroup.com/badbe99ebfbe4dc2ad90a756bacd9d7c.jpg' />");
                        }
                        actions.push("</a>");
                        actions.push("</div>");
                        return actions.join('');
                    }
                },
                {
                    title: '编码',
                    formatter: function (value, row, index) {
                        var actions = [];
                        let pn = $.common.isEmpty(row.partNumber)?"-":row.partNumber;
                        let pdmGoodsCode = $.common.isEmpty(row.pdmGoodsCode)?"-":row.pdmGoodsCode;
                        let platformGoodsCode = $.common.isEmpty(row.platformGoodsCode)?"-":row.platformGoodsCode;
                        actions.push("<div class='item-content'>商品编码: "+$.table.tooltip(pdmGoodsCode, 18,'open')+"</div>");
                        actions.push("<div class='item-content'>平台SKU: "+$.table.tooltip(platformGoodsCode, 18,'open')+"</div>");
                        actions.push("<div class='item-content'>销售编码: "+$.common.sprintf('<a href="%s" target="_blank" class="tooltip-show" data-toggle="tooltip" >%s</a>', row.platformPrefix+row.platformGoodsId,row.platformGoodsId)+"</div>");
                        actions.push("<div class='item-content'>PN编码: "+pn+"</div>");
                        return actions.join('');
                    }
                },
                {
                    title: 'Listing信息',
                    formatter: function (value, row, index) {
                        var actions = [];
                        let title = $.common.isEmpty(row.title)?"-":row.title;
                        let brandCode = $.common.isEmpty(row.brandCode)?"-":row.brandCode;
                        let publishTypeValue = $.common.isEmpty(row.publishType)?"-":row.publishType;
                        let publishStatusName = $.common.isEmpty(row.publishStatusName)?"-":row.publishStatusName;
                        let adaptationStatus = $.common.isEmpty(row.adaptationStatus)?"-":row.adaptationStatus;
                        let describeTemplateName = $.common.isEmpty(row.describeTemplateName)?"-":row.describeTemplateName;
                        let censorship = (row.censorship==2)?"未送检":(row.censorship == 1 ? "成功" : "失败");
                        let scheduledBeginTime = $.common.isEmpty(row.scheduledBeginTime)?"":row.scheduledBeginTime;
                        let scheduledTaskName = $.common.isEmpty(row.scheduledTaskName)?"":row.scheduledTaskName;
                        let listingPerformance= $.common.isEmpty(row.listingPerformance)?"":row.listingPerformance;
                        let isMain= $.common.isEmpty(row.isMain)?"-":row.isMain;
                        let aplusName = $.common.isEmpty(row.aplusName)?"-":row.aplusName;
                        let categoryEnName = $.common.isEmpty(row.categoryEnName) ? "-" : row.categoryEnName;
                        let platformCategoryId = $.common.isEmpty(row.platformCategoryId) ? "-" : row.platformCategoryId;
                        let categoryDetail = $.common.isEmpty(row.categoryDetail) ? "-" : row.categoryDetail;
                        let categoryEnDetail = $.common.isEmpty(row.categoryEnDetail) ? "-" : row.categoryEnDetail;
                        actions.push("<div class='item-title'>"+$.table.tooltip(title, 90,'open')+"</div>");
                        // 需要将刊登类型和适配同一行显示
                        let publishTypeText = $.table.selectDictLabel(publishType, publishTypeValue);
                        let adaptationText = "";
                        if ( $.common.isNotEmpty(adaptationStatus) ){
                            if (adaptationStatus.includes(",")){
                                adaptationStatus = adaptationStatus.split(",")[0];
                            }
                            if (adaptationStatus.includes("-")){
                                adaptationStatus = adaptationStatus.split("-")[0]
                            }
                            adaptationText = $.table.selectDictLabel(adaptStatus, adaptationStatus);
                            // adaptationText 上新增 点击事件
                            adaptationText = "<span class='item-right-label' onclick='logInfoAdapter(\""+row.id+"\")'>" + adaptationText + "</span>";
                        }
                        actions.push("<div class='item-row'><div class='item-left'>刊登类型: " + publishTypeText + "</div><div class='item-right'><span class='item-right-label'>适配:</span>" + adaptationText + " </div></div>");

                       // 状态和品牌
                        actions.push("<div class='item-row'><div class='item-left'>状态: "+publishStatusName+"</div><div class='item-right'><span class='item-right-label'>品牌:</span> "+brandCode+"</div></div>");
                       
                        if (!(row.platform == "AM")){
                            actions.push("<div class='item-row'><div class='item-left'>送检结果: "+censorship+"</div></div>");
                        }
                        //品类线
                        actions.push("<div class='item-row'><div class='item-left'>品类线: <span style='color:blue; cursor:pointer;' onclick='showCategoryDetail(\""
                            + categoryEnDetail + "\", \"" + categoryEnName + "\", \"" + platformCategoryId + "\", \"" + categoryDetail + "\")'>"
                            + categoryEnName + "</span></div></div>");
                        if (!(row.platform == "AM")){
                            actions.push("<div class='item-row'><div class='item-left'>描述模板: "+ $.table.tooltip(describeTemplateName, 18,'open')+"</div></div>");
                        }else {
                            actions.push("<div class='item-row'><div class='item-left'>A+模板: "+ $.table.tooltip(aplusName, 18,'open')+"</div></div>"   );
                            
                            if (row.shopCode.includes("VC")) {
                                // 添加可用状态信息（仅针对亚马逊平台）
                                let availabilityDetail = $.common.isEmpty(row.availabilityDetail) ? "-" : row.availabilityDetail;
                                let availabilityInfo = '-';
                                if (availabilityDetail != null) {
                                    let startDate = $.common.isEmpty(availabilityDetail.startDate) ? "" : availabilityDetail.startDate;
                                    let endDate = $.common.isEmpty(availabilityDetail.nextDate) ? "" : availabilityDetail.nextDate;
                                    let dateinfo = "";
                                    if (startDate == "" && endDate == "") {
                                        dateinfo = "";
                                    } else if (startDate == "" && endDate != "") {
                                        dateinfo = "(至" + endDate + ")";
                                    } else if (startDate != "" && endDate == "") {
                                        dateinfo = "(自" + startDate + ")";
                                    } else {
                                        dateinfo = "(自" + startDate + "至" + endDate + ")";
                                    }
                                    if (availabilityDetail.currentAvailability == 1) {
                                        availabilityInfo = "Available";
                                    } else if (availabilityDetail.currentAvailability == 2) {
                                        // 状态+时间
                                        availabilityInfo = "Temporarily unavailable" + dateinfo;
                                    } else if (availabilityDetail.currentAvailability == 3) {
                                        availabilityInfo = "Permanently unavailable" + dateinfo;
                                    }
                                }
                                if ($.common.isNotEmpty(availabilityDetail.rpaStatus) && availabilityDetail.rpaStatus == 1) {
                                    availabilityInfo = "处理中";
                                }

                                actions.push("<div class='item-row'><div class='item-left'>可用状态: "+ (availabilityInfo) + "</div></div>");
                            }
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '店铺信息',
                    formatter: function (value, row, index) {
                        var actions = [];
                        let siteName = $.common.isEmpty(row.siteName)?"-":row.siteName;
                        let shopName = $.common.isEmpty(row.shopName)?"-":row.shopName;
                        actions.push("<div class='item-content'>站点: "+siteName+"</div>");
                        actions.push("<div class='item-content'>店铺: "+shopName+"</div>");
                        return actions.join('');
                    }
                },
                {
                    field: 'standardPrice',
                    title: '价格信息',
                    formatter: function (value, row, index) {
                        var actions = [];
                        let settlementPrice = $.common.isEmpty(row.settlementPrice)?"-":row.settlementPrice;
                        let redLinePrice = $.common.isEmpty(row.redLinePrice)?"-":row.redLinePrice;
                        let originalPrice = $.common.isEmpty(row.originalPrice)?"-":row.originalPrice;
                        let standardPrice = $.common.isEmpty(row.standardPrice)?"-":row.standardPrice;
                        let price = row.shopCode.includes("VC") ? row.listPrice : row.standardPrice;
                        if (price == null) {
                            price = "-";
                        }
                        let listPrice = $.common.isEmpty(row.listPrice)?price:row.listPrice;

                        // 当前售价（可编辑）
                        if (row.platform === "AM") {
                            if (row.shopCode.includes("VC")) {
                                // 显示List Price
                                actions.push("<div class='item-content'>List Price: <a href='#' class='editable-standardPrice' data-pk='" + row.id + "' data-value='" + price + "' data-platform='" + row.platform + "'>" + price + "</a>" + "</div>");
                            } else {
                                actions.push("<div class='item-content'>Your Price: <a href='#' class='editable-standardPrice' data-pk='" + row.id + "' data-value='" + price + "' data-platform='" + row.platform + "' style='color: " + row.priceColor + "'>" + price + "</a>" + "</div>");
                                actions.push("<div class='item-content'>List Price: " + listPrice + "</div>");
                                // 显示sale price(开始时间-结束时间)
                                if ($.common.isNotEmpty(row.salePrice)) {
                                    let salePriceHtml = "";
                                    if ($.common.isNotEmpty(row.salePriceBegin) && $.common.isNotEmpty(row.salePriceEnd)) {
                                        // 获取date的格式，不要全部的字符串，会影响样式
                                        salePriceHtml = row.salePrice + "(" + row.salePriceBegin.substring(0, 10) + ")-(" + row.salePriceEnd.substring(0, 10) + ")";
                                    } else if ($.common.isNotEmpty(row.salePriceBegin)) {
                                        salePriceHtml = row.salePrice + "(" + row.salePriceBegin.substring(0, 10) + ")";
                                    } else if ($.common.isNotEmpty(row.salePriceEnd)) {
                                        salePriceHtml = row.salePrice + "(" + row.salePriceEnd.substring(0, 10) + ")";
                                    } else {
                                        salePriceHtml = row.salePrice;
                                    }
                                    actions.push("<div class='item-content'>Sale Price: " + salePriceHtml + "</div>");
                                } else {
                                    actions.push("<div class='item-content'>Sale Price: -</div>");
                                }
                            }
                        } else {
                            actions.push("<div class='item-content'>当前售价: <a href='#' class='editable-standardPrice' data-pk='" + row.id + "' data-value='" + price + "' data-platform='" + row.platform + "' style='color: " + row.priceColor + "'>" + price + "</a>" + "</div>");
                        }
                        
                        // 非VC，但是是AM平台，显示结算价
                        if(!row.shopCode.includes("VC") && row.platform === "AM") {
                            actions.push("<div class='item-content'>结算价: "+settlementPrice+"</div>");
                        }
                        actions.push("<div class='item-content'>红线价: "+redLinePrice+"</div>");
                        if(row.platform === "AM" && row.shopCode.includes("VC")) {
                            actions.push("<div class='item-content' >Cost Price: "+ "<span style='color: " + row.priceColor + "'>" + standardPrice + "</span>" + "</div>");
                        }
                        if (row.platform === "EB") {
                            actions.push("<div class='item-content'>刊登原价: "+originalPrice+"</div>");
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'stockOnSalesQty',
                    title: '库存信息',
                    sortable: true,
                    formatter: function (value, row, index) {
                        var actions = [];
                        let realStock = $.common.isEmpty(row.realStockOnSalesQty) ? "-" : row.realStockOnSalesQty;
                        let stockOnSales = value;
                        
                        if (row.publishType == 1) {
                            stockOnSales = "-";
                        }
                        
                        // 在售库存（可编辑）
                        actions.push("<div class='item-content'>在售库存: <a href='#' class='editable-stock' data-pk='" + row.id + "' data-value='" + stockOnSales + "' data-platform='" + row.platform + "'>" + stockOnSales + "</a></div>");
                        // 实际库存
                        actions.push("<div class='item-content'>实际: " + realStock + "</div>");
                        
                        return actions.join('');
                    }
                },
                {
                  title: '销售信息',
                    formatter: function (value, row, index) {
                        var actions = [];
                        
                        // 销量数据
                        var s1 = row.salesVolume30==null?'-':row.salesVolume30;
                        var s2 = row.salesVolume60==null?'-':row.salesVolume60;
                        var s3 = row.salesVolume90==null?'-':row.salesVolume90;
                        
                        // 订单量数据
                        var o1 = row.orderQuantity30==null?'-':row.orderQuantity30;
                        var o2 = row.orderQuantity60==null?'-':row.orderQuantity60;
                        var o3 = row.orderQuantity90==null?'-':row.orderQuantity90;
                        
                        // 销量信息
                        actions.push("<div class='item-content'>销量(30天): "+s1+"</div>");
                        actions.push("<div class='item-content'>销量(60天): "+s2+"</div>");
                        actions.push("<div class='item-content'>销量(90天): "+s3+"</div>");
                        
                        // 订单量信息
                        actions.push("<div class='item-content'>订单(30天): "+o1+"</div>");
                        actions.push("<div class='item-content'>订单(60天): "+o2+"</div>");
                        actions.push("<div class='item-content'>订单(90天): "+o3+"</div>");
                        
                        return actions.join('');
                    },
                },
                {
                    title: '创建信息',
                    formatter: function (value, row, index) {
                        var actions = [];
                        let createBy = $.common.isEmpty(row.createBy)?"-":row.createBy;
                        var label = '';
                        $(userDatas).each(function(i,site){
                            if(site.key == createBy){
                                label = site.value;
                                return false;
                            }
                        });
                        label = createBy == '-1' ?  'system' : label;
                        let createTime = $.common.isEmpty(row.createTime)?"-":row.createTime;
                        let updateTime = $.common.isEmpty(row.updateTime)?"-":row.updateTime;
                        let onlineTime = $.common.isEmpty(row.onlineTime)?"-":row.onlineTime;
                        let offTime = $.common.isEmpty(row.offTime)?"-":row.offTime;
                        actions.push("<div class='item-content'>创建人: "+label+"</div>");
                        actions.push("<div class='item-content'>创建时间: "+createTime+"</div>");
                        actions.push("<div class='item-content'>更新时间: "+updateTime+"</div>");
                        actions.push("<div class='item-content'>上架时间: "+onlineTime+"</div>");
                        actions.push("<div class='item-content'>下架时间: "+offTime+"</div>");
                        return actions.join('');
                    }
                },
                {
                    title: "标签",
                    field: 'listingLabelList',
                    width: 100,
                    formatter: function (value, row, index) {
                        value = row.listingPerformance;
                        let content = "-";
                        if (value != null) {
                            value = JSON.parse(value);
                            let actions = [];
                            for (let key in value) {
                                // 如果key在listingLabelData中存在，则添加到actions中
                                if (value[key] != null) {   
                                    if (listingLabelData.some(item => item.dictValue === value[key])) {
                                        // 距离上面3px
                                        actions.push(`<div class='item-content'>${$.table.selectDictLabel(listingLabelData, value[key])}</div>`);
                                    }else if ( value[key].indexOf("NetPPM") != -1  || value[key].indexOf("LP异常") != -1){
                                        actions.push(`<div class='item-content'>${value[key]}</div>`);
                                    }
                                }
                            }
                            content = actions.join('');
                        }
                        return `<a href="#" onclick="showListingPerformanceModal('${row.id}')" class="listing-performance-detail" data-listing-id="${row.id}">${content}</a>`;
                    }
                },
                {
                    title: "表现",
                    field: 'listingPerformance',
                    width: 100,
                    formatter: function (value, row, index) {
                        let content = "-";
                        if (value != null) {
                            value = JSON.parse(value);
                            let actions = [];
                            for (let key in value) {
                                if (value[key] != null) {
                                    if (listingPerformanceData.some(item => item.dictValue === value[key])) {
                                        actions.push(`<div class='item-content'>${$.table.selectDictLabel(listingPerformanceData, value[key])}</div>`);
                                    }
                                }
                            }
                            content = actions.join('');
                        }
                        return `<a href="#" onclick="showListingPerformanceModal('${row.id}')" class="listing-performance-detail" data-listing-id="${row.id}">${content}</a>`;

                    }
                },
                // {
                //     field: 'createBy',
                //     title: '创建人',
                //     formatter: function (value, row, index) {
                //         var label = '';
                //         $(userDatas).each(function(i,site){
                //             if(site.key == value){
                //                 label = site.value;
                //                 return false;
                //             }
                //         });
                //         if (value == '-1'){
                //             return 'system';
                //         }
                //         if (label == '')
                //             return value;
                //         else
                //             return label;
                //     }
                // },
                // {
                //     field: 'createTime',
                //     title: '创建时间',
                //     sortable: true
                // },
                // {
                //     field: 'updateTime',
                //     title: '更新时间',
                //     sortable: true,
                //     visible: false
                // },
                // {
                //     field: 'onlineTime',
                //     title: '上架时间',
                //     sortable: true
                // },
                // {
                //     field: 'offTime',
                //     title: '下架时间',
                //     sortable: true,
                // },
                {
                    field: 'taskName',
                    title: '任务编号',
                    visible: false
                },
                {
                    field: 'remark',
                    title: '平台反馈',
                    formatter: function (value, row, index) {
                        if ($.common.isEmpty(value)) {
                            return '-'
                        }
                        try {
                            let labels = row.listingPerformance;
                            if (labels != null) {
                                labels = JSON.parse(labels);
                                for (let key in labels) {
                                // 如果key在listingLabelData中存在，则添加到actions中
                                if (labels[key] != null && labels[key].indexOf("链接异常") != -1) {
                                    return "<div class='item-title'>"+$.table.tooltip(value, 50,'open')+"</div>";
                                }
                                }
                            }
                            return '-'
                        } catch (e) {
                            return '-'
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 120,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.publishStatusName=='定时刊登') {
                            actions.push('<a style="margin-left:-5px;margin-bottom: -20px;" class="btn btn-success btn-xs" href="javascript:void(0)" onclick="cancelTimePublish(\'' + row.id + '\')" ><i class="fa fa-edit"></i>取消定时刊登</a> </br></br>');
                        }
                        // let canEditStatus =[1, 3, 5]
                        // row.publishStatus如果以1，3，5开头，不可以编辑
                        let b = row.publishStatus.toString().startsWith('1') || row.publishStatus.toString().startsWith('3') || row.publishStatus.toString().startsWith('5');
                        let b1 = !$.common.equals(row.siteCode,"US") && $.common.equals(row.platform,"AM");
                        if (! ( b || b1) ) {
                            //amazon编辑走新页面
                            if (!row.platform === "EB") {
                                actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editOpenOptions(\'' + row.id + '\' , \'' + false + '\')" shiro:hasPermission="publication:listing:edit"><i class="fa fa-edit"></i>编辑</a> ');
                            }else{
                                actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editOpenOptions(\'' + row.id + '\' , \'' + true + '\')" shiro:hasPermission="publication:listing:edit"><i class="fa fa-edit"></i>编辑</a> ');
                            }
                        }
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="logInfo(\'' + row.id + '\')"><i class="fa fa-edit"></i>日志</a>');
                        return actions.join('');
                    }
                }],
            // 全选
            onCheckAll: function (rowsAfter,rowsBefore){
                batchModify.handleBtnStatus(rowsAfter.map(o => o.publishStatus));
                batchModify.disableVCHandle(rowsAfter.map(o => o.publishType),rowsAfter.map(o => o.siteCode),rowsAfter.map(o => o.platform));
            },
            // 全反选
            onUncheckAll: function (rowsAfter,rowsBefore){
                batchModify.handleBtnStatus(rowsAfter.map(o => o.publishStatus));
                batchModify.disableVCHandle(rowsAfter.map(o => o.publishType),rowsAfter.map(o => o.siteCode),rowsAfter.map(o => o.platform));
            },
            // 单选
            onCheck: function (row,$element) {
                let rows = $("#" + table.options.id).bootstrapTable('getSelections')
                batchModify.handleBtnStatus(rows.map(o => o.publishStatus));
                batchModify.disableVCHandle(rows.map(o => o.publishType),rows.map(o => o.siteCode),rows.map(o => o.platform));
            },
            // 反选
            onUncheck: function (row,$element) {
                let rows = $("#" + table.options.id).bootstrapTable('getSelections')
                batchModify.handleBtnStatus(rows.map(o => o.publishStatus));
                batchModify.disableVCHandle(rows.map(o => o.publishType),rows.map(o => o.siteCode),rows.map(o => o.platform));
            },
        };
        $.table.init(options);

        // 节点数量
        publishStatusCountCallBack();

        //列表渲染完成后执行
        $("#listing-table").on("post-body.bs.table", function () {
            imageLoading("scale-img");
        });
    };


    /**
     * 快速修改数据
     * @param field
     * @param row
     * @param rowIndex
     * @param oldValue
     * @param $el
     */
    function onEditableSave (field, row, rowIndex, oldValue, $el) {
        //修改数据
        $.ajax({
            type: "post",
            url: prefix + "/quickUpdate",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(row),
            dataType: 'JSON',
            success: function (data, status) {
                if (data.code == 0) {
                    $.modal.msgSuccess(data.msg);
                    togglePageState(true);
                    $.table.search();
                    publishStatusCountCallBack();
                } else {
                    // $.modal.msgError(data.msg);
                    top.layer.msg(data.msg, {icon: $.modal.icon(modal_status.FAIL), time: 2000, shift: 5});
                }
            }
        });
    }

    $(function () {
        listingTable();

        publishSubStatusCallBack(2,true);

         // 表格加载完成后初始化可编辑功能
         $("#listing-table").on("post-body.bs.table", function() {
            $(".editable-standardPrice").editable({
                type: 'text',
                pk: function() {
                    return $(this).data('pk');
                },
                name: 'standardPrice',
                url: prefix + '/quickUpdate',
                title: '售价',
                validate: function(value) {
                    if (value.length > 20) {
                        return '售价不能超过20个字符';
                    }
                    if (value.length == 0) {
                        return '售价不能为空';
                    }
                    if (isNaN(value) || value < 0) {
                        return '售价必须是正数';
                    }
                },
                params: function(params) {
                    // 将参数格式化为与原有接口兼容的格式
                    var data = {};
                    var pk = params.pk;
                    data.id = pk;
                    // 获取表格当前行的完整数据
                    var rowData = $("#listing-table").bootstrapTable('getRowByUniqueId', pk);
                    if (rowData) {
                        // 合并行数据到请求数据中
                        $.extend(data, rowData);
                    }
                    data[params.name] = params.value;
                    
                    return JSON.stringify(data);
                },
                ajaxOptions: {
                    contentType: 'application/json; charset=utf-8'
                },
                success: function(response, newValue) {
                    if (response.code == 0) {
                        $.modal.msgSuccess(response.msg);
                        togglePageState(true);
                        $.table.search();
                        publishStatusCountCallBack();
                    } else {
                        top.layer.msg(response.msg, {icon: $.modal.icon(modal_status.FAIL), time: 2000, shift: 5});
                    }
                }
            });
            
            // 初始化库存编辑功能
            $(".editable-stock").editable({
                type: 'text',
                pk: function() {
                    return $(this).data('pk');
                },
                name: 'stockOnSalesQty',
                url: prefix + '/quickUpdate',
                title: '库存',
                validate: function(value) {
                    // 必须是数字
                    if (value.length > 20) {
                        return '库存不能超过20个字符';
                    }
                    if (value.length == 0) {
                        return '库存不能为空';
                    }
                    if (isNaN(value) || value < 0 || !Number.isInteger(parseFloat(value))) {
                        return '库存必须是正整数';
                    }
                },
                params: function(params) {
                    // 将参数格式化为与原有接口兼容的格式
                    var data = {};
                    data.id = params.pk;
                     // 获取表格当前行的完整数据
                    var rowData = $("#listing-table").bootstrapTable('getRowByUniqueId', params.pk);
                    if (rowData) {
                        // 合并行数据到请求数据中
                        $.extend(data, rowData);
                    }
                    data[params.name] = params.value;
                    
                    return JSON.stringify(data);
                },
                ajaxOptions: {
                    contentType: 'application/json; charset=utf-8'
                },
                success: function(response, newValue) {
                    if (response.code == 0) {
                        $.modal.msgSuccess(response.msg);
                        togglePageState(true);
                        $.table.search();
                        publishStatusCountCallBack();
                    } else {
                        top.layer.msg(response.msg, {icon: $.modal.icon(modal_status.FAIL), time: 2000, shift: 5});
                    }
                }
            });
        });
        //图片放大
        Fancybox.bind('[data-fancybox]', {});
    });

    function logInfo(id) {
        var options = {
            title: "日志信息",
            url: prefix + "/detail/" + id,
            width: $(window).width() - 200,
            height: $(window).height() - 50,
            yes: false,
            btn: ['<i class="fa fa-close"></i> 关闭'],
        };
        $.modal.openOptions(options);
    }

    function logInfoAdapter(id) {
        var options = {
            title: "适配日志信息",
            url: prefix + "/adapterDetail/" + id,
            width: $(window).width() - 200,
            height: $(window).height() - 50,
            yes: false,
            btn: ['<i class="fa fa-close"></i> 关闭'],
        };
        $.modal.openOptions(options);
    }


    function showListingPerformanceModal(listingId) {
        var options = {
            title: listingId+" listing表现详情",
            url: prefix+'/getListingPerformanceDetails/'+listingId,
            width: $(window).width() - 200,
            height: $(window).height() - 50,
            yes: false,
            btn: ['<i class="fa fa-close"></i> 关闭'],
        };
        $.modal.openOptions(options);
    }

    // 显示可用状态详情
    function showAvailabilityDetail(headId) {
        $.ajax({
            type: "get",
            url: prefix + '/getAvailabilityInfo',
            data: {
                "headId": headId
            },
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                    var detail = result.data;
                    if (detail) {
                        // 构建详情内容
                        var content = '<div class="row">';
                        
                        // 当前状态
                        content += '<div class="col-sm-12">';
                        content += '<p><strong>当前状态:</strong> ';
                        if (detail.currentAvailability == 1) {
                            content += 'Available';
                        } else if (detail.currentAvailability == 2) {
                            content += 'Temporarily unavailable';
                        } else if (detail.currentAvailability == 3) {
                            content += 'Permanently unavailable';
                        } else {
                            content += '未知';
                        }
                        content += '</p>';
                        
                        // 目标状态
                        if (detail.targetAvailability) {
                            content += '<p><strong>目标状态:</strong> ';
                            if (detail.targetAvailability == 1) {
                                content += 'Available';
                            } else if (detail.targetAvailability == 2) {
                                content += 'Temporarily unavailable';
                            } else if (detail.targetAvailability == 3) {
                                content += 'Permanently unavailable';
                            } else {
                                content += '未知';
                            }
                            content += '</p>';
                        }
                        
                        // 不可用开始日期
                        if (detail.startDate) {
                            content += '<p><strong>不可用开始日期:</strong> ' + detail.startDate + '</p>';
                        }
                        
                        // 下一个可用日期
                        if (detail.nextDate) {
                            content += '<p><strong>下一个可用日期:</strong> ' + detail.nextDate + '</p>';
                        }
                        
                        content += '</div></div>';
                        
                        $.modal.open('可用状态详情', content);
                    } else {
                        $.modal.alertWarning("未找到可用状态详情");
                    }
                } else {
                    $.modal.alertError(result.msg);
                }
            }
        });
    }

    function reset() {
        $('#platform').val("").trigger('change');
        $('#siteCode').val("").trigger('change');
        $('#shopCode').val("").trigger('change');
        $('#createBy').val("").trigger('change');
        $('#categoryIds').val("").trigger('change');
        $('#censorship').val("").trigger('change');
        $('#adaptationStatus').val("").trigger('change');
        $('#publishType').val("").trigger('change');
        $('#beginOffTime').val("").trigger('change');
        $('#endOffTime').val("").trigger('change');
        $('#goodsCode').val("").trigger('change');
        $('#platformGoodsCode').val("").trigger('change');
        $('#platformGoodsId').val("").trigger('change');
        $('#title').val("").trigger('change');
        $('#startTime').val("").trigger('change');
        $('#endTime').val("").trigger('change');
        $('#beginOnLineTime').val("").trigger('change');
        $('#endOnLineTime').val("").trigger('change');
        $('#ids').val("").trigger('change');
        $('#smcFlag').val("").trigger('change');
        $('#timeType').val("").trigger('change');
        $('#codeType').val("").trigger('change');
        $('#orderType').val("").trigger('change');
        $('#saleType').val("").trigger('change');
        $('#codeValue').val("").trigger('change');
        $('#orderValue').val("").trigger('change');
        $('#saleValueStart').val("").trigger('change');
        $('#saleValueEnd').val("").trigger('change');
        $('#orderValueStart').val("").trigger('change');
        $('#orderValueEnd').val("").trigger('change');
        $('#describeTemplateName').val("").trigger('change');
        $('#standardPriceStart').val("").trigger('change');
        $('#standardPriceEnd').val("").trigger('change');
        $('#stockOnSalesQtyStart').val("").trigger('change');
        $('#stockOnSalesQtyEnd').val("").trigger('change');
        $('#actualStockOnSalesQtyStart').val("").trigger('change');
        $('#actualStockOnSalesQtyEnd').val("").trigger('change');
        $('#publishDayStart').val("").trigger('change');
        $('#publishDayEnd').val("").trigger('change');
        $('#redLinePriceFlag').val("").trigger('change');
        $('#brandName').val("").trigger('change');
        $("#shop-select")[0].setValue("");
        $("#category-select")[0].setValue("");
        $("#operation-classification-select")[0].setValue("");
        $("#createBy-select")[0].setValue("");
        $('#cartLabel').val("").trigger('change');
        $('#isMain').val("").trigger('change');
        $('#listingPerformance').val("").trigger('change');
        $('#listingLabel').val("").trigger('change');
        $('#aplusTemplateName').val("").trigger('change');
        $('#productSeries').val("").trigger('change');
        $('#productModel').val("").trigger('change');
        $('#isInventoryBlack').val("").trigger('change');
        $('#isRedLineBlack').val("").trigger('change');
        $('#isCouponBlack').val("").trigger('change');
        $('#currentAvailability').val("").trigger('change');
        document.querySelector('#publishSubStatus').reset();
        // $.form.reset();
    }

    // 刊登节点状态选择
    function chooseStatus(e, status) {
        $("#statusLabel a").removeClass("select_bck_color");
        $("#statusLabel a").addClass("bck_color");
        $(e).addClass("select_bck_color");
        $("#publishStatus").val(status);
        $("#publishStatusFlag").val(1);
        togglePageState(true);
        $.table.search();

        $("#buttonPublish").attr("disabled", false).css("pointer-events", "auto");
        $("#buttonScheduledPublish").attr("disabled", false).css("pointer-events", "auto");
        $("#buttonCopy").attr("disabled", false).css("pointer-events", "auto");
        $("#buttonStopPublish").attr("disabled", false).css("pointer-events", "auto");
        $("#buttonRemove").attr("disabled", false).css("pointer-events", "auto");
        $("#buttonBatchPull").attr("disabled", true).css("pointer-events", "none");
        $("#buttonRelistItem").attr("disabled", true).css("pointer-events", "none");
        $("#move2Draft").attr("disabled", false).css("pointer-events", "auto");

        switch (status) {
            case "0":   //草稿
                $("#buttonStopPublish").attr("disabled", true).css("pointer-events", "none");
                $("#move2Draft").attr("disabled", true).css("pointer-events", "none");
                break;
            case "1":   //刊登中
                $("#buttonPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonScheduledPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonStopPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonRemove").attr("disabled", true).css("pointer-events", "none");
                $("#move2Draft").attr("disabled", true).css("pointer-events", "none");
                break;
            case "2"://在售状态
                $("#buttonPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonScheduledPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonBatchPull").attr("disabled", false).css("pointer-events", "auto");
                $("#buttonRelistItem").attr("disabled", false).css("pointer-events", "auto");
                $("#move2Draft").attr("disabled", true).css("pointer-events", "none");
                break;
            case "3":   //更新中
                $("#buttonPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonScheduledPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonStopPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonRemove").attr("disabled", true).css("pointer-events", "none");
                $("#move2Draft").attr("disabled", true).css("pointer-events", "none");
                break;
            case "4":   //更新失败
                $("#buttonPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonScheduledPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonBatchPull").attr("disabled", false).css("pointer-events", "auto");
                $("#buttonRelistItem").attr("disabled", false).css("pointer-events", "auto");
                $("#move2Draft").attr("disabled", true).css("pointer-events", "none");
                break;
            case "5":   //下架中
                $("#buttonPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonScheduledPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonStopPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonRemove").attr("disabled", true).css("pointer-events", "none");
                $("#move2Draft").attr("disabled", true).css("pointer-events", "none");

                break;
            case "6":   //非在售
                // $("#buttonPublish").attr("disabled",true).css("pointer-events","none");
                // $("#buttonScheduledPublish").attr("disabled",true).css("pointer-events","none");
                $("#buttonStopPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonRelistItem").attr("disabled", false).css("pointer-events", "auto");
                $("#buttonBatchPull").attr("disabled", false).css("pointer-events", "auto");
                $("#move2Draft").attr("disabled", false).css("pointer-events", "auto");

                break;
            case "7":   //下架失败
                $("#buttonPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonScheduledPublish").attr("disabled", true).css("pointer-events", "none");
                $("#move2Draft").attr("disabled", true).css("pointer-events", "none");


                break;
            case "8": //刊登失败
                $("#buttonStopPublish").attr("disabled", false).css("pointer-events", "auto");
                break;
            case "9": //删除失败
                $("#buttonPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonScheduledPublish").attr("disabled", true).css("pointer-events", "none");
                $("#buttonStopPublish").attr("disabled", true).css("pointer-events", "none");
                $("#move2Draft").attr("disabled", true).css("pointer-events", "none");
                break;
        }

        publishStatusCountCallBack();

        publishSubStatusCallBack(status,false);
    }


    /**
     * 子状态列表查询
     * @param status
     * @param flag
     */
    function publishSubStatusCallBack(status,flag) {
        publishSubStatus=status;
        var configs = {
            url: prefix + "/selectPublishSubStatus/" + (status?status:'-1'),
            type: "post",
            dataType: "json",
            success: function (result) {
                if (result.code == 0) {
                    let publishSubStatus = result.data.map(o => {
                        return {label: o.value, value: o.key}
                    });
                    if (flag){
                        // 初始化
                        virtualSelectRender(document.querySelectorAll("div#publishSubStatus"), publishSubStatus,false,"请选择子状态");
                    }else {
                        document.querySelector('#publishSubStatus').setOptions([]);
                        document.querySelector('#publishSubStatus').setOptions(publishSubStatus);
                    }
                }
            }
        };
        $.ajax(configs);
    }

    function showCategoryDetail(categoryEnDetail, categoryEnName, platformCategoryId, categoryDetail) {
        const contentHtml = `
        <div style="padding: 20px;">
            <p><strong>平台类目ID:</strong> ${platformCategoryId}</p>
            <p><strong>品类线:</strong> ${categoryDetail}</p>
            <p><strong>英文品类线:</strong> ${categoryEnDetail}</p>
        </div>
    `;

        layer.open({
            type: 1,
            title: '品类线详细信息',
            content: contentHtml,
            area: ['600px', '300px'], // 调整高度以适应更多内容
            btn: ['关闭'],
            btnAlign: 'c',
            yes: function (index, layero) {
                layer.close(index);
            }
        });
    }


    document.querySelector('#publishSubStatus').addEventListener('change', function() {
        let value = this.value;
        $("#publishStatusFlag").val(1);
        if ($.common.isEmpty(value)){
            $("#publishStatus").val(publishSubStatus);
            return;
        }
        $("#publishStatus").val(value);
        togglePageState(true);
        $.table.search();

        publishStatusCountCallBack();
    });

    // 刊登节点数量回显
    function publishStatusCountCallBack() {
        let data = $("#formId").serialize();
        var configs = {
            url: prefix + "/selectPublishStatusCount",
            // url: prefix + "/list",
            type: "post",
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.code == 0) {
                    $("#allCount").text(result.data.allCount);
                    $("#draftCount").text(result.data.draftCount);
                    $("#publishCount").text(result.data.publishCount);
                    $("#saleCount").text(result.data.saleCount);
                    $("#updatingCount").text(result.data.updatingCount);
                    $("#updateFailedCount").text(result.data.updateFailedCount);
                    $("#offShelfCount").text(result.data.offShelfCount);
                    $("#noSaleCount").text(result.data.noSaleCount);
                    $("#offShelfFailedCount").text(result.data.offShelfFailedCount);
                    $("#publishFailedCount").text(result.data.publishFailedCount);
                    $("#deleteOnlineFailedCount").text(result.data.deleteOnlineCount);
                }
            }
        };
        $.ajax(configs);
    }

    function importListing() {
        $.table.importUpdateExcel('importUpdateTpl');
    }

    // 取消定时刊登
    function cancelTimePublish(id) {
        if (id == null) {
            return;
        }
        $.modal.confirm("取消定时刊登会返回至草稿,是否确认操作?", function () {
            var url = table.options.cancelScheduledPublish;
            var data = {"ids": id};
            $.operate.submit(url, "post", "json", data,function(){
                publishStatusCountCallBack();
            });
        });
    }

    // 编辑
    function editOpenOptions(id,vcFlag) {
        if (id == null) {
            return;
        }
        callback = function (index, layero) {
            var iframeWin = layero.find('iframe')[0];
            iframeWin.contentWindow.submitHandler(index, layero);
        }
        if (vcFlag == 'true') {
            //新开页签
            window.open(prefix + "/v2/edit/" + id);
        }else {
            window.open(prefix + "/edit/" + id);
        }
    }

    $("#orderType").change(function () {
        $("#orderValueStart").val("");
        $("#orderValueEnd").val("");
    });
    $("#saleType").change(function () {
        var saleType = $(this).val();
        if (saleType == 4 || saleType == 5 || saleType == 6 || saleType == 7) {
            // 默认0
            $('#saleValueStart').val(0);
            $('#saleValueEnd').val(0);
            $('#saleValueStart').attr('readonly', true);
            $('#saleValueEnd').attr('readonly', true);
        } else {
            // 默认空
            $("#saleValueStart").val("");
            $("#saleValueEnd").val("");
            $('#saleValueStart').attr('readonly', false);
            $('#saleValueEnd').attr('readonly', false);
        }
    });
    $("#timeType").change(function () {
        $("#startTime").val("");
        $("#endTime").val("");
    });
    $("#codeType").change(function () {
        $("#codeValue").val("");
    });


</script>
</body>
<!-- 导入区域 -->
<script id="importTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="file" name="file"/>
            <!--            <div class="mt10 pt5">-->
            <!--                <input type="checkbox" id="updateSupport" name="updateSupport" title="如果登录账户已经存在，更新这条数据。"> 是否更新已经存在的用户数据-->
            <!--                &nbsp;	<a onclick="$.table.importTemplate()" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>-->
            <!--            </div>-->
            <font color="red" class="pull-left mt10">
                提示：仅允许导入"xls"或"xlsx"格式文件！
            </font>
        </div>
    </form>
</script>

<script id="importUpdateTpl" type="text/template">
    <form enctype="multipart/form-data" class="mt20 mb10">
        <div class="col-xs-offset-1">
            <input type="file" id="importFile" name="importFile"/>
                <div class="panel-body">
                    <p>模板下载： <a href="https://suncent-cloud.oss-cn-shenzhen.aliyuncs.com/smcTemplate/smc_importing_template.xlsx"  download>smc导入修改模板文件下载</a></p>
                    <p style="color: red">注意：支持 xlsx 文件</p>
                    <p style="color: red">更新的数据字段置空，会默认保留原数据</p>
                    <p style="color: red">当文件中数据存在异常时，全部的导入都会失败！</p>
                    <p style="color: red">可通过导出的数据，作为导入修改的模板数据！</p>
                </div>
        </div>
    </form>
</script>
</html>