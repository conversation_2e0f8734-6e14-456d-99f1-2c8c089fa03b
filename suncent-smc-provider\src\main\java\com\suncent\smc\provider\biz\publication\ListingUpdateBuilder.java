package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.EnvUtils;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.common.CommonUtilsSmc;
import com.suncent.smc.persistence.ebay.domain.EbayGoodsHeadV2;
import com.suncent.smc.persistence.ebay.service.IEbayListingGoodsHeadV2Service;
import com.suncent.smc.persistence.error.parser.ConfigurableErrorParser;
import com.suncent.smc.persistence.publication.domain.dto.ItemDTO;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.UpdateRecord;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.provider.biz.todo.SMCTodoBiz;
import com.suncent.smc.provider.update.HandlerListingUpdateModuleComposite;
import com.suncent.smc.provider.update.ListingUpdateModuleResolver;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import java.util.Objects;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * listing修改构造者
 */
@Slf4j
@Component
public class ListingUpdateBuilder {

    @Autowired
    private IGoodsHeadService goodsHeadService;
    @Autowired
    private HandlerListingUpdateModuleComposite handlerListingUpdateModuleComposite;
    @Autowired
    private IListingLogService listingLogService;
    @Resource
    @Qualifier("amazonListingUpdateTask")
    @Lazy
    public BaseAmazonProductTask amazonProductTask;
    @Autowired
    private BaseAmazonProductUpdateV2Task baseAmazonProductUpdateV2Task;
    @Resource
    @Lazy
    public BaseEbayProductBiz ebayProductTask;
    @Autowired
    private IGoodsTaskInfoService goodsTaskInfoService;
    @Autowired
    private IRelatedPromotionsMappingService relatedPromotionsMappingService;

    @Autowired
    protected SMCTodoBiz smcTodoBiz;

    @Autowired
    IUpdateRecordService updateRecordService;

    @Autowired
    protected ThreadPoolForMonitorManager threadPoolForMonitorManager;
    @Autowired
    private IAdsService adsService;
    @Autowired
    private ConfigurableErrorParser configurableErrorParser;
    @Autowired
    IEbayListingGoodsHeadV2Service ebayListingGoodsHeadV2Service;
    /**
     * 构建ItemDTO
     *
     * @param itemDTOList
     * @return
     */
    public List<ItemDTO> buildItem(List<ItemDTO> itemDTOList, boolean todoFlag) {
        if (ObjectUtils.isEmpty(itemDTOList)) {
            return null;
        }
        List<GoodsTaskTypeEnum> goodsTaskTypeEnums = CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_IMPORT, GoodsTaskTypeEnum.SYNC_POLILY_LISTING, GoodsTaskTypeEnum.SYNC_SHIPPING_LISTING, GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE, GoodsTaskTypeEnum.TODO_TEXT_UPDATE);

        for (ItemDTO itemDTO : itemDTOList) {
            GoodsHead goodsHead = itemDTO.getGoodsHead();
            EbayGoodsHeadV2 ebayGoodsHead = itemDTO.getEbayGoodsHead();
            List<String> moduleType = itemDTO.getModuleType();

            if (ObjectUtils.isEmpty(goodsHead) && ObjectUtils.isEmpty(ebayGoodsHead)) {
                continue;
            }

            // 兼容字段获取
            Long headId = goodsHead != null ? goodsHead.getId() : ebayGoodsHead.getId();
            String shopCode = goodsHead != null ? goodsHead.getShopCode() : ebayGoodsHead.getShopCode();
            String platform = goodsHead != null ? goodsHead.getPlatform() : ebayGoodsHead.getPlatform();
            String siteCode = goodsHead != null ? goodsHead.getSiteCode() : ebayGoodsHead.getSiteCode();
            String updateBy = goodsHead != null ? goodsHead.getUpdateBy() : ebayGoodsHead.getUpdateBy();

            if (ObjectUtils.isEmpty(moduleType)) {
                continue;
            }

            // 1. 更新任务状态
            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(headId),
                    goodsTaskTypeEnums,
                    GoodsTaskSubStatusEnum.PROCESSING, "");
            // 2. 设置快捷更新状态
            if (goodsHead != null) {
                GoodsHead updatedGoodsHead = new GoodsHead();
                updatedGoodsHead.setId(goodsHead.getId());
                updatedGoodsHead.setPublishStatus(PublishStatus.UPDATING_QUICK.getType());
                goodsHeadService.updateListingGoodsHead(updatedGoodsHead);
            }
            if (ebayGoodsHead != null) {
                EbayGoodsHeadV2 updateGoodsHead = new EbayGoodsHeadV2();
                updateGoodsHead.setId(ebayGoodsHead.getId());
                updateGoodsHead.setPublishStatus(String.valueOf(PublishStatus.UPDATING_QUICK.getType()));
                ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(updateGoodsHead);
            }

            //更新待办数据表 处理状态 -> 处理中
            if (todoFlag) {
                smcTodoBiz.updateTodoStatusByListingUpdate(Math.toIntExact(headId), TodoStatusEnum.HANDLE_STATUS);
            }

            //amazon V2
            List<String> moduleTypeNew = getModuleType(moduleType, shopCode, platform, siteCode);
            if (CollUtil.isEmpty(moduleTypeNew)) {
                continue;
            }
            itemDTO.setModuleType(moduleTypeNew);
            try {
                for (String key : moduleTypeNew) {
                    ListingUpdateModuleResolver impl = handlerListingUpdateModuleComposite.getListingUpdateResolverByModule(platform + key);
                    itemDTO = impl.build(itemDTO);
                }
            } catch (Exception e) {
                log.error("平台为:{},店铺为:{},头表id:{},构建ItemDTO失败", platform, shopCode, headId, e);

                if (goodsHead != null) {
                    GoodsHead updatedGoodsHead = new GoodsHead();
                    updatedGoodsHead.setId(goodsHead.getId());
                    updatedGoodsHead.setPublishStatus(PublishStatus.UPDATING_FAIL.getType());
                    goodsHeadService.updateListingGoodsHead(updatedGoodsHead);
                }
                if (ebayGoodsHead != null) {
                    EbayGoodsHeadV2 updateGoodsHead = new EbayGoodsHeadV2();
                    updateGoodsHead.setId(ebayGoodsHead.getId());
                    updateGoodsHead.setPublishStatus(String.valueOf(PublishStatus.UPDATING_FAIL.getType()));
                    ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(updateGoodsHead);
                }

                listingLogService.insertErrorListingLog("组装数据失败", updateBy, Math.toIntExact(headId), e.getMessage());
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(headId),
                        goodsTaskTypeEnums,
                        GoodsTaskSubStatusEnum.ERROR, "组装数据失败:" + e.getMessage());
            }
        }

        //初始化更新记录
        this.initUpdateRecord(itemDTOList);
        return itemDTOList;
    }

    /**
     * 处理单个ItemDTO
     */
    private ItemDTO processSingleItem(ItemDTO itemDTO, boolean todoFlag, List<GoodsTaskTypeEnum> goodsTaskTypeEnums) {
        GoodsHead goodsHead = itemDTO.getGoodsHead();
        EbayGoodsHeadV2 ebayGoodsHead = itemDTO.getEbayGoodsHead();
        List<String> moduleType = itemDTO.getModuleType();

        if (ObjectUtils.isEmpty(goodsHead) && ObjectUtils.isEmpty(ebayGoodsHead)) {
            return null;
        }

        // 兼容字段获取
        Long headId = goodsHead != null ? goodsHead.getId() : ebayGoodsHead.getId();
        String shopCode = goodsHead != null ? goodsHead.getShopCode() : ebayGoodsHead.getShopCode();
        String platform = goodsHead != null ? goodsHead.getPlatform() : ebayGoodsHead.getPlatform();
        String siteCode = goodsHead != null ? goodsHead.getSiteCode() : ebayGoodsHead.getSiteCode();
        String updateBy = goodsHead != null ? goodsHead.getUpdateBy() : ebayGoodsHead.getUpdateBy();

        if (ObjectUtils.isEmpty(moduleType)) {
            return null;
        }

        // 1. 更新任务状态
        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(headId),
                goodsTaskTypeEnums,
                GoodsTaskSubStatusEnum.PROCESSING, "");
        // 2. 设置快捷更新状态
        if (goodsHead != null) {
            GoodsHead updatedGoodsHead = new GoodsHead();
            updatedGoodsHead.setId(goodsHead.getId());
            updatedGoodsHead.setPublishStatus(PublishStatus.UPDATING_QUICK.getType());
            goodsHeadService.updateListingGoodsHead(updatedGoodsHead);
        }
        if (ebayGoodsHead != null) {
            EbayGoodsHeadV2 updateGoodsHead = new EbayGoodsHeadV2();
            updateGoodsHead.setId(ebayGoodsHead.getId());
            updateGoodsHead.setPublishStatus(String.valueOf(PublishStatus.UPDATING_QUICK.getType()));
            ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(updateGoodsHead);
        }

        //更新待办数据表 处理状态 -> 处理中
        if (todoFlag) {
            smcTodoBiz.updateTodoStatusByListingUpdate(Math.toIntExact(headId), TodoStatusEnum.HANDLE_STATUS);
        }

        //amazon V2
        List<String> moduleTypeNew = getModuleType(moduleType, shopCode, platform, siteCode);
        if (CollUtil.isEmpty(moduleTypeNew)) {
            return null;
        }
        itemDTO.setModuleType(moduleTypeNew);
        try {
            for (String key : moduleTypeNew) {
                ListingUpdateModuleResolver impl = handlerListingUpdateModuleComposite.getListingUpdateResolverByModule(platform + key);
                itemDTO = impl.build(itemDTO);
            }
        } catch (Exception e) {
            log.error("平台为:{},店铺为:{},头表id:{},构建ItemDTO失败", platform, shopCode, headId, e);

            if (goodsHead != null) {
                GoodsHead updatedGoodsHead = new GoodsHead();
                updatedGoodsHead.setId(goodsHead.getId());
                updatedGoodsHead.setPublishStatus(PublishStatus.UPDATING_FAIL.getType());
                goodsHeadService.updateListingGoodsHead(updatedGoodsHead);
            }
            if (ebayGoodsHead != null) {
                EbayGoodsHeadV2 updateGoodsHead = new EbayGoodsHeadV2();
                updateGoodsHead.setId(ebayGoodsHead.getId());
                updateGoodsHead.setPublishStatus(String.valueOf(PublishStatus.UPDATING_FAIL.getType()));
                ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(updateGoodsHead);
            }

            listingLogService.insertErrorListingLog("组装数据失败", updateBy, Math.toIntExact(headId), e.getMessage());
            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(headId),
                    goodsTaskTypeEnums,
                    GoodsTaskSubStatusEnum.ERROR, "组装数据失败:" + e.getMessage());
            return null;
        }

        return itemDTO;
    }

    private List<String> getModuleType(List<String> moduleType, String shopCode, String platform, String siteCode) {
        if (!ObjUtil.equals(platform, PlatformTypeEnum.AM.name())) {
            return moduleType;
        }
        if (StrUtil.isBlank(shopCode)) {
            return moduleType;
        }
        if (!CommonUtilsSmc.isNewVersion(siteCode, shopCode)) {
            return moduleType;
        }

        List<String> returnModuleType = new ArrayList<>();
        for (String key : moduleType) {
            if (key.endsWith("_V2")) {
                returnModuleType.add(key);
                continue;
            }
            String moduleName = ListingModuleType.getModuleName(key + "_V2");
            if (StrUtil.isBlank(moduleName)) {
                continue;
            }
            returnModuleType.add(moduleName);
        }

        if (CollUtil.isEmpty(returnModuleType)) {
            return moduleType;
        }
        return returnModuleType;
    }


    /**
     * 更新listing 到api
     *
     * @param itemDTOList
     */
    public void updateApi(List<ItemDTO> itemDTOList) {
        if (ObjectUtils.isEmpty(itemDTOList)) {
            return;
        }
        //判断itemDTOList 中的moduleType是否为空 如果为空则不更新
        if (itemDTOList.stream().anyMatch(itemDTO -> ObjectUtils.isEmpty(itemDTO.getModuleType()))) {
            return;
        }
        itemDTOList = this.buildItem(itemDTOList, true);
        //itemDTOList 根据head的platfrom分组 拿到platfrom为key itemDTOList为value的map
        Map<String, List<ItemDTO>> platformGroupedMap = itemDTOList.stream()
                .collect(Collectors.groupingBy(item -> {
                    if (item.getGoodsHead() != null && item.getGoodsHead().getPlatform() != null) {
                        return item.getGoodsHead().getPlatform();
                    } else if (item.getEbayGoodsHead() != null && item.getEbayGoodsHead().getPlatform() != null) {
                        return item.getEbayGoodsHead().getPlatform();
                    } else {
                        return null;
                    }
                }));

        if (CollectionUtils.isEmpty(platformGroupedMap)) {
            return;
        }
        if (!EnvUtils.isProdProfile()) {
            log.info("非生产环境，不更新listing到api");
            return;
        }
        ThreadPoolExecutor listingUpdatePool = threadPoolForMonitorManager.getThreadPoolExecutor("listingUpdatePool");
        ThreadPoolExecutor amListingUpdatePool = threadPoolForMonitorManager.getThreadPoolExecutor("amListingUpdatePool");
        platformGroupedMap.forEach((k, v) -> {
            ThreadPoolExecutor poolExecutor;
            if (PlatformTypeEnum.AM.name().equals(k)) {
                poolExecutor = amListingUpdatePool;
            } else {
                poolExecutor = listingUpdatePool;
            }
            List<List<ItemDTO>> partition = Lists.partition(v, 50);
            for (List<ItemDTO> itemDTOS : partition) {
                poolExecutor.execute(() -> {
                    try {
                        if (ObjUtil.equals(PlatformTypeEnum.AM.name(), k)) {
                            //vc链接走 v2接口
                            List<ItemDTO> vcItemDTOS = itemDTOS.stream()
                                    .filter(itemDTO -> CommonUtilsSmc.isNewVersion(itemDTO.getGoodsHead().getSiteCode(), itemDTO.getGoodsHead().getShopCode()) || itemDTO.getModuleType().contains(ListingModuleType.JSON_AMAZON_ADAPTIVE.name()))
                                    .collect(Collectors.toList());
                            updateV2Api2AM(vcItemDTOS);

                            //sc链接走 文件接口
                            List<ItemDTO> scItemDTOS = itemDTOS.stream()
                                    .filter(itemDTO -> !CommonUtilsSmc.isNewVersion(itemDTO.getGoodsHead().getSiteCode(), itemDTO.getGoodsHead().getShopCode()) && !itemDTO.getModuleType().contains(ListingModuleType.JSON_AMAZON_ADAPTIVE.name()))
                                    .collect(Collectors.toList());
                            updateApi2AM(scItemDTOS);
                        }

                        if (ObjUtil.equals(PlatformTypeEnum.EB.name(), k)) {
                            updateApi2EB(itemDTOS);
                        }
                    } catch (Exception e) {
                        log.error("部分更新失败listing到api失败", e);
                    }
                });
            }

        });

    }

    /**
     * 部分更新补偿初始化
     *
     * @param itemDTOList
     */
    public void initUpdateRecord(List<ItemDTO> itemDTOList) {
        if (ObjectUtils.isEmpty(itemDTOList)) {
            return;
        }
        //如果itemDTOList中的goodsHead全为空则不处理
        if (itemDTOList.stream().allMatch(itemDTO -> ObjectUtils.isEmpty(itemDTO.getGoodsHead()))) {
            return;
        }
        //查询updateRecordService 是否有更新中的记录,如果有则不插入
        List<Integer> listingIds = itemDTOList.stream().map(itemDTO -> itemDTO.getGoodsHead().getId()).collect(Collectors.toList());
        List<Integer> listingIdsInUpdate = updateRecordService.selectListingIdsInUpdate(listingIds);
        if (ObjUtil.isEmpty(listingIdsInUpdate)) {
            itemDTOList.forEach(this::intoUpdateRecord);
        } else {
            itemDTOList = itemDTOList.stream().filter(itemDTO -> !listingIdsInUpdate.contains(itemDTO.getGoodsHead().getId())).collect(Collectors.toList());
            itemDTOList.forEach(this::intoUpdateRecord);
        }
    }

    /**
     * 插入更新记录
     *
     * @param itemDTO
     */
    private void intoUpdateRecord(ItemDTO itemDTO) {
        GoodsHead goodsHead = itemDTO.getGoodsHead();
        if (ObjUtil.isEmpty(goodsHead)) {
            return;
        }
        UpdateRecord updateRecord = new UpdateRecord();

        updateRecord.setPlatform(goodsHead.getPlatform());
        updateRecord.setSite(goodsHead.getSiteCode());
        updateRecord.setShopCode(goodsHead.getShopCode());
        updateRecord.setListingId(goodsHead.getId());
        updateRecord.setModule(JSON.toJSONString(itemDTO.getModuleType()));
        //待处理
        updateRecord.setStatus(SMCCommonEnum.TO_BE_PROCESSED.getValue());
        updateRecord.setType(0);
        updateRecord.setDelFlag(0);
        updateRecord.setCreateTime(DateUtils.getNowDate());
        updateRecord.setUpdateTime(DateUtils.getNowDate());
        updateRecordService.insertUpdateRecord(updateRecord);
    }

    private void updateApi2EB(List<ItemDTO> v) {
        List<GoodsTaskTypeEnum> goodsTaskTypes = Arrays.asList(
                GoodsTaskTypeEnum.FIND_REPLACEMENT_UPDATES,
                GoodsTaskTypeEnum.BATCH_MODIFY,
                GoodsTaskTypeEnum.BATCH_EDIT,
                GoodsTaskTypeEnum.BATCH_DELETE,
                GoodsTaskTypeEnum.BATCH_IMPORT,
                GoodsTaskTypeEnum.SYNC_SHIPPING_LISTING,
                GoodsTaskTypeEnum.SYNC_DESCRIPTION_LISTING,
                GoodsTaskTypeEnum.SYNC_POLILY_LISTING,
                GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE,
                GoodsTaskTypeEnum.SYNC_UPDATE_ATTRIBUTE,
                GoodsTaskTypeEnum.RED_PRICE_UPDATE,
                GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE,
                GoodsTaskTypeEnum.BATCH_UPDATE_TITLE,
                GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK,
                GoodsTaskTypeEnum.BATCH_UPDATE_PRICE,
                GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE,
                GoodsTaskTypeEnum.BATCH_UPDATE_DESCRIPTION,
                GoodsTaskTypeEnum.BATCH_UPDATE_VIDEO
        );

        for (ItemDTO itemDTO : v) {
            GoodsHead goodsHead = itemDTO.getGoodsHead();
            EbayGoodsHeadV2 ebayGoodsHead = itemDTO.getEbayGoodsHead();

            // 判断调用哪个方法
            boolean useV2 = goodsHead == null && ebayGoodsHead != null;

            Long headId = useV2 ? ebayGoodsHead.getId() : goodsHead.getId();
            String shopCode = useV2 ? ebayGoodsHead.getShopCode() : goodsHead.getShopCode();
            String updateBy = useV2 ? ebayGoodsHead.getUpdateBy() : goodsHead.getUpdateBy();

            try {
                List<String> moduleType = itemDTO.getModuleType();
                boolean contains = moduleType != null && (moduleType.contains(ListingModuleType.ADAPTIVE.name()) || moduleType.contains(ListingModuleType.ADAPTIVE_V2.name()));

                updateRecordService.updateUpdateRecordStatus(Math.toIntExact(headId), SMCCommonEnum.IN_PROCESS.getValue());

                String errMsg;
                if (useV2) {
                    errMsg = ebayProductTask.updateEbayV2(itemDTO, contains);
                } else {
                    errMsg = ebayProductTask.updateEbay(itemDTO, contains);
                }

                if (StrUtil.isNotEmpty(errMsg)) {
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(headId), goodsTaskTypes, GoodsTaskSubStatusEnum.ERROR, "更新至API失败:" + errMsg);
                    updateRecordService.updateUpdateRecordStatus(Math.toIntExact(headId), SMCCommonEnum.PROCESSING_FAIL.getValue());
                    relatedPromotionsMappingService.updateRelatedPromotionsMappingStatusByHeadId(String.valueOf(headId), "更新至API失败:" + errMsg);
                    continue;
                }

                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(headId), goodsTaskTypes, GoodsTaskSubStatusEnum.NORAML, "");
                updateRecordService.updateUpdateRecordStatus(Math.toIntExact(headId), SMCCommonEnum.PROCESSING_COMPLETE.getValue());

            } catch (Exception e) {
                log.error("平台为:EB, 店铺为:{}, 头表id:{}, listing更新失败", shopCode, headId, e);
                if (useV2) {
                    EbayGoodsHeadV2 updateEbayGoodsHead = new EbayGoodsHeadV2();
                    updateEbayGoodsHead.setId(headId);
                    updateEbayGoodsHead.setPublishStatus(String.valueOf(PublishStatus.UPDATING_FAIL.getType()));
                    ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(updateEbayGoodsHead);
                } else {
                    GoodsHead update = new GoodsHead();
                    update.setId(Math.toIntExact(headId));
                    update.setPublishStatus(PublishStatus.UPDATING_FAIL.getType());
                    goodsHeadService.updateListingGoodsHead(update);
                }
                listingLogService.insertErrorListingLog("更新失败", updateBy, Math.toIntExact(headId), e.getMessage());
                updateRecordService.updateUpdateRecordStatus(Math.toIntExact(headId), SMCCommonEnum.PROCESSING_FAIL.getValue());
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(headId), goodsTaskTypes, GoodsTaskSubStatusEnum.ERROR, "更新至API失败:" + e.getMessage());
            }
        }
    }

    private void updateApi2AM(List<ItemDTO> v) {
        List<GoodsTaskTypeEnum> goodsTaskTypes=new ArrayList<>();
        goodsTaskTypes.add(GoodsTaskTypeEnum.FIND_REPLACEMENT_UPDATES);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_MODIFY);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_EDIT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELETE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_IMPORT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_DESCRIPTION);
        goodsTaskTypes.add(GoodsTaskTypeEnum.TODO_TEXT_UPDATE);
        try {
            //对v进行分组 以goodhead中 shopcode+categoryid分组
            Map<String, List<ItemDTO>> shopCodeAndCategoryGroupedMap = v.stream().collect(
                    Collectors.groupingBy(ItemDTO -> ItemDTO.getGoodsHead().getShopCode() + ItemDTO.getGoodsHead().getCategoryId()));
            shopCodeAndCategoryGroupedMap.forEach((k1, v1) -> {
                //对v转化成map ,key为headid value为amazonListingMap
                Map<Integer, Map<String, String>> amazonListingMap = v1.stream()
                        .filter(item -> item.getGoodsHead() != null && item.getAmazonListingMap() != null)
                        .collect(Collectors.toMap(ItemDTO -> ItemDTO.getGoodsHead().getId(), ItemDTO::getAmazonListingMap));

                v1.stream().map(ItemDTO::getGoodsHead).forEach( goodsHead->updateRecordService.updateUpdateRecordStatus(goodsHead.getId(),SMCCommonEnum.IN_PROCESS.getValue()));
                amazonProductTask.prodocutTask(v1.stream().map(ItemDTO::getGoodsHead).collect(Collectors.toList()), OperationTypeEnum.UPDATE.getName(), amazonListingMap);
            });
        } catch (Exception e) {
            log.error("平台为:AM,失败数量:{},导入更新失败listing到api失败", v.size(), e);
            v.stream().map(ItemDTO::getGoodsHead).forEach(goodsHead -> {
                goodsHead.setPublishStatus(PublishStatus.UPDATING_FAIL.getType());
                goodsHeadService.updateListingGoodsHead(goodsHead);
                listingLogService.insertErrorListingLog("更新失败", goodsHead.getUpdateBy(), goodsHead.getId(), e.getMessage());
                updateRecordService.updateUpdateRecordStatus(goodsHead.getId(),SMCCommonEnum.PROCESSING_FAIL.getValue());
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), goodsTaskTypes, GoodsTaskSubStatusEnum.ERROR,"更新至API失败:"+e.getMessage());
            });
        }
    }



    private void updateV2Api2AM(List<ItemDTO> vcItemDTOS) {
        List<GoodsTaskTypeEnum> goodsTaskTypes=new ArrayList<>();
        goodsTaskTypes.add(GoodsTaskTypeEnum.FIND_REPLACEMENT_UPDATES);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_MODIFY);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_EDIT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELETE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_IMPORT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_DESCRIPTION);
        goodsTaskTypes.add(GoodsTaskTypeEnum.TODO_TEXT_UPDATE);

        for (ItemDTO vcItemDTO : vcItemDTOS) {
            GoodsHead goodsHead = vcItemDTO.getGoodsHead();
            List<ListingModuleType> moduleTypeEnum = ListingModuleType.getModuleTypeEnum(vcItemDTO.getModuleType());
            List<String> moduleTypeName = moduleTypeEnum.stream().map(ListingModuleType::getModuleTypeName).collect(Collectors.toList());
            try {
                updateRecordService.updateUpdateRecordStatus(goodsHead.getId(),SMCCommonEnum.IN_PROCESS.getValue());

                baseAmazonProductUpdateV2Task.updateProductTask(goodsHead, vcItemDTO.getAttributesParam(), moduleTypeEnum, ObjUtil.isNotEmpty(goodsHead.getCreateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                //任务
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), goodsTaskTypes, GoodsTaskSubStatusEnum.NORAML,"");
            } catch (Exception e) {
                log.error("headId:{},部分更新失败,更新模块有：",goodsHead.getId(),JSON.toJSONString(moduleTypeName), e);

                GoodsHead update = new GoodsHead();
                update.setId(goodsHead.getId());
                update.setPublishStatus(PublishStatus.UPDATING_FAIL.getType());
                goodsHeadService.updateListingGoodsHead(update);
                String errorMsg =  e.getMessage();
                String parseErrorMsg = configurableErrorParser.parseErrorMsg(errorMsg);
                if (StrUtil.isNotBlank(parseErrorMsg)) {
                    errorMsg = parseErrorMsg;
                }
                //日志
                listingLogService.insertErrorListingLog("部分更新失败,更新模块有："+JSON.toJSONString(moduleTypeName), goodsHead.getUpdateBy(), goodsHead.getId(), errorMsg);
                //统计
                updateRecordService.updateUpdateRecordStatus(goodsHead.getId(),SMCCommonEnum.PROCESSING_FAIL.getValue());
                //任务
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), goodsTaskTypes, GoodsTaskSubStatusEnum.ERROR,"更新至API_V2失败:"+e.getMessage());

                if (moduleTypeEnum.contains(ListingModuleType.JSON_AMAZON_ADAPTIVE)) {
                    //取 e.getMessage() 前200个字符
                    String error = ObjUtil.isNotEmpty(e.getMessage()) && e.getMessage().length() > 200 ? e.getMessage().substring(0, 200) : e.getMessage();
                    adsService.updateItDemandStatusByAsin(Lists.newArrayList(goodsHead.getPlatformGoodsId()),"5",error);
                }
            }
        }

    }

}
