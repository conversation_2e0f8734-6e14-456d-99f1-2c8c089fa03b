package com.suncent.smc.persistence.configuration.category.service;

import com.suncent.smc.persistence.configuration.category.domain.entity.CategoryInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ICategoryInfoService {

    /**
     * 查询类目配置
     *
     * @param id 类目配置主键
     * @return 类目配置
     */
    public CategoryInfo selectCategoryInfoById(String id);

    /**
     * 查询类目配置列表
     *
     * @param categoryInfo 类目配置
     * @return 类目配置集合
     */
    public List<CategoryInfo> selectCategoryInfoList(CategoryInfo categoryInfo);

    /**
     * 新增类目配置
     *
     * @param categoryInfo 类目配置
     * @return 结果
     */
    public int insertCategoryInfo(CategoryInfo categoryInfo);

    /**
     * 修改类目配置
     *
     * @param categoryInfo 类目配置
     * @return 结果
     */
    public int updateCategoryInfo(CategoryInfo categoryInfo);

    /**
     * 批量删除类目配置
     *
     * @param ids 需要删除的类目配置主键集合
     * @return 结果
     */
    public int deleteCategoryInfoByIds(String ids);

    /**
     * 删除类目配置信息
     *
     * @param id 类目配置主键
     * @return 结果
     */
    public int deleteCategoryInfoById(String id);

    /**
     * 根据类目ID查询类目配置信息
     * @param categoryId
     * @return
     */
    List<CategoryInfo> getCategoryInfoListByCategoryId(String categoryId);



    void batchUpdateAttrMappingByFile(MultipartFile file);
}
