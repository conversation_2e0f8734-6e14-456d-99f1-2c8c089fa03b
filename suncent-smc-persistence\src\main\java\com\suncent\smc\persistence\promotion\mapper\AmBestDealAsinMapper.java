package com.suncent.smc.persistence.promotion.mapper;

import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsin;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * BD促销ASINMapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface AmBestDealAsinMapper {
    /**
     * 查询BD促销ASIN
     * 
     * @param id BD促销ASIN主键
     * @return BD促销ASIN
     */
    public AmBestDealAsin selectAmBestDealAsinById(Long id);

    /**
     * 查询BD促销ASIN列表
     * 
     * @param amBestDealAsin BD促销ASIN
     * @return BD促销ASIN集合
     */
    public List<AmBestDealAsin> selectAmBestDealAsinList(AmBestDealAsin amBestDealAsin);

    /**
     * 新增BD促销ASIN
     * 
     * @param amBestDealAsin BD促销ASIN
     * @return 结果
     */
    public int insertAmBestDealAsin(AmBestDealAsin amBestDealAsin);

    /**
     * 修改BD促销ASIN
     *
     * @param amBestDealAsin BD促销ASIN
     * @return 结果
     */
    public int updateAmBestDealAsin(AmBestDealAsin amBestDealAsin);

    /**
     * UPSERT BD促销ASIN（插入或更新，基于 promotion_id + platform_goods_id 唯一约束）
     *
     * @param amBestDealAsin BD促销ASIN
     * @return 结果
     */
    public int upsertAmBestDealAsin(AmBestDealAsin amBestDealAsin);

    /**
     * 删除BD促销ASIN
     * 
     * @param id BD促销ASIN主键
     * @return 结果
     */
    public int deleteAmBestDealAsinById(Long id);

    /**
     * 批量删除BD促销ASIN
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAmBestDealAsinByIds(String[] ids);

    /**
     * 根据BD记录ID查询ASIN列表
     *
     * @param refBestDealId BD记录ID
     * @return BD促销ASIN集合
     */
    public List<AmBestDealAsin> selectAmBestDealAsinByRefId(Long refBestDealId);

    /**
     * 根据BD记录ID列表批量查询ASIN列表
     *
     * @param refBestDealIds BD记录ID列表
     * @return BD促销ASIN集合
     */
    public List<AmBestDealAsin> selectAmBestDealAsinByRefIds(@Param("refBestDealIds") List<Long> refBestDealIds);

    /**
     * 根据BD记录ID删除ASIN记录
     * 
     * @param refBestDealId BD记录ID
     * @return 结果
     */
    public int deleteAmBestDealAsinByRefId(Long refBestDealId);

    /**
     * 根据ASIN查询BD促销记录
     *
     * @param platformGoodsId ASIN
     * @return BD促销ASIN集合
     */
    public List<AmBestDealAsin> selectAmBestDealAsinByAsin(String platformGoodsId);

    /**
     * 根据促销ID查询BD促销ASIN列表（去重，每个promotionId+asin组合只返回最新记录）
     *
     * @param promotionId 促销ID
     * @return BD促销ASIN集合
     */
    public List<AmBestDealAsin> selectLatestAmBestDealAsinByPromotionId(@Param("promotionId") String promotionId);

    /**
     * 清理重复的ASIN记录（保留最新记录）
     *
     * @return 清理的记录数
     */
    public int cleanupDuplicateAsinRecords();

    /**
     * 批量更新缺失的head_id和pdm_goods_code
     *
     * @return 更新的记录数
     */
    public int fillMissingGoodsHeadData();

    /**
     * 原子插入ASIN记录（如果不存在则插入，存在则忽略）
     *
     * @param amBestDealAsin BD促销ASIN
     * @return 结果（1=插入成功，0=已存在或失败）
     */
    public int insertIgnoreAmBestDealAsin(AmBestDealAsin amBestDealAsin);

    /**
     * 使用SELECT FOR UPDATE锁定记录进行查询
     *
     * @param promotionId     促销ID
     * @param platformGoodsId ASIN
     * @return BD促销ASIN记录
     */
    public AmBestDealAsin selectForUpdateByPromotionIdAndAsin(
            @Param("promotionId") String promotionId,
            @Param("platformGoodsId") String platformGoodsId);
}
