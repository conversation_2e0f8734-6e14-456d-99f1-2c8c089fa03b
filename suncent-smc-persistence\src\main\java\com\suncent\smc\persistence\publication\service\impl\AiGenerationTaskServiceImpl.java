package com.suncent.smc.persistence.publication.service.impl;

import com.alibaba.fastjson.JSON;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.persistence.publication.domain.entity.AiGenerationTask;
import com.suncent.smc.persistence.publication.mapper.AiGenerationTaskMapper;
import com.suncent.smc.persistence.publication.service.IAiGenerationTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AI内容生成任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class AiGenerationTaskServiceImpl implements IAiGenerationTaskService {
    
    @Autowired
    private AiGenerationTaskMapper aiGenerationTaskMapper;

    /**
     * 查询AI内容生成任务
     * 
     * @param id AI内容生成任务主键
     * @return AI内容生成任务
     */
    @Override
    public AiGenerationTask selectAiGenerationTaskById(String id) {
        return aiGenerationTaskMapper.selectAiGenerationTaskById(id);
    }

    /**
     * 根据任务关联ID查询AI内容生成任务
     * 
     * @param taskCorrelationId 任务关联ID
     * @return AI内容生成任务
     */
    @Override
    public AiGenerationTask selectAiGenerationTaskByCorrelationId(String taskCorrelationId) {
        return aiGenerationTaskMapper.selectAiGenerationTaskByCorrelationId(taskCorrelationId);
    }

    /**
     * 查询AI内容生成任务列表
     * 
     * @param aiGenerationTask AI内容生成任务
     * @return AI内容生成任务
     */
    @Override
    public List<AiGenerationTask> selectAiGenerationTaskList(AiGenerationTask aiGenerationTask) {
        return aiGenerationTaskMapper.selectAiGenerationTaskList(aiGenerationTask);
    }

    /**
     * 新增AI内容生成任务
     * 
     * @param aiGenerationTask AI内容生成任务
     * @return 结果
     */
    @Override
    public int insertAiGenerationTask(AiGenerationTask aiGenerationTask) {
        aiGenerationTask.setCreateTime(DateUtils.getNowDate());
        return aiGenerationTaskMapper.insertAiGenerationTask(aiGenerationTask);
    }

    /**
     * 修改AI内容生成任务
     * 
     * @param aiGenerationTask AI内容生成任务
     * @return 结果
     */
    @Override
    public int updateAiGenerationTask(AiGenerationTask aiGenerationTask) {
        aiGenerationTask.setUpdateTime(DateUtils.getNowDate());
        return aiGenerationTaskMapper.updateAiGenerationTask(aiGenerationTask);
    }

    /**
     * 根据任务关联ID更新任务状态和结果
     * 
     * @param taskCorrelationId 任务关联ID
     * @param taskStatus 任务状态
     * @param aiResult AI生成结果
     * @return 结果
     */
    @Override
    public int updateTaskStatusAndResult(String taskCorrelationId, String taskStatus, String aiResult, String beforeStatus) {
        return aiGenerationTaskMapper.updateTaskStatusAndResult(taskCorrelationId, taskStatus, aiResult, beforeStatus);
    }

    /**
     * 批量删除AI内容生成任务
     * 
     * @param ids 需要删除的AI内容生成任务主键集合
     * @return 结果
     */
    @Override
    public int deleteAiGenerationTaskByIds(String ids) {
        return aiGenerationTaskMapper.deleteAiGenerationTaskByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除AI内容生成任务信息
     * 
     * @param id AI内容生成任务主键
     * @return 结果
     */
    @Override
    public int deleteAiGenerationTaskById(String id) {
        return aiGenerationTaskMapper.deleteAiGenerationTaskById(id);
    }

    /**
     * 查询待处理的任务列表
     * 
     * @param taskType 任务类型
     * @return 待处理任务列表
     */
    @Override
    public List<AiGenerationTask> selectPendingTasks(String taskType) {
        return aiGenerationTaskMapper.selectPendingTasks(taskType);
    }

    /**
     * 创建AI生成任务
     *
     * @param taskCorrelationId 任务关联ID
     * @param taskType 任务类型
     * @param goodsCodes 商品编码列表
     * @param shopCode 店铺编码
     * @param brandCode 品牌编码
     * @param createBy 创建人
     * @return 创建的任务
     */
    @Override
    public AiGenerationTask createAiGenerationTask(String taskCorrelationId, String taskType,
                                                  Integer taskId, String shopCode, List<String> goodsCodes,
                                                  String brandCode, String createBy) {
        AiGenerationTask task = new AiGenerationTask();
        task.setTaskCorrelationId(taskCorrelationId);
        task.setTaskType(taskType);
        task.setTaskStatus(AiGenerationTask.TaskStatus.PENDING.getCode());
        task.setTaskId(taskId);
        task.setShopCode(shopCode);
        task.setGoodsCodes(JSON.toJSONString(goodsCodes));
        task.setBrandCode(brandCode);
        task.setDelFlag(0);
        task.setProcessStatus(0); // 初始状态为未处理
        task.setCreateBy(createBy);
        task.setCreateTime(DateUtils.getNowDate());

        insertAiGenerationTask(task);
        return task;
    }

    /**
     * 更新任务处理状态
     *
     * @param taskCorrelationId 任务关联ID
     * @param processStatus 处理状态
     * @return 结果
     */
    @Override
    public int updateProcessStatus(String taskCorrelationId, Integer processStatus) {
        return aiGenerationTaskMapper.updateProcessStatus(taskCorrelationId, processStatus);
    }

    @Override
    public int countTaskByStatus(Integer taskId, int processStatus) {
        return aiGenerationTaskMapper.countTaskByStatus(taskId, processStatus);
    }
}
