package com.suncent.smc.persistence.cdp.service.impl;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.template.QuickConfig;
import com.suncent.smc.common.annotation.DataSource;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.enums.DataSourceType;
import com.suncent.smc.persistence.cdp.domain.entity.CateClassify;
import com.suncent.smc.persistence.cdp.domain.entity.CateProduct;
import com.suncent.smc.persistence.cdp.mapper.CateProductMapper;
import com.suncent.smc.persistence.cdp.service.ICateProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品-产品类别Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-04
 */
@Slf4j
@Service
@DataSource(DataSourceType.PDM)
public class CateProductServiceImpl implements ICateProductService, InitializingBean {
    @Autowired
    private CateProductMapper cateProductMapper;
    @Autowired
    private CacheManager cacheManager;

    private Cache<String, List<KeyValueEntity>> cateProductCache;


    @Override
    public List<KeyValueEntity> getCateProduct() {
        List<CateProduct> cateProductList = cateProductMapper.getCateProduct();
        return keyValueListByCateProduct(cateProductList);
    }



    private List<KeyValueEntity> keyValueListByCateProduct(List<CateProduct> cateProductList) {
        if (CollectionUtils.isEmpty(cateProductList)) {
            return new ArrayList<>();
        }
        return cateProductList.stream().map(cateProduct -> {
            KeyValueEntity keyValueEntity = new KeyValueEntity();
            keyValueEntity.setKey(cateProduct.getCategoryCode());
            keyValueEntity.setValue(cateProduct.getCategoryName());
            return keyValueEntity;
        }).collect(Collectors.toList());
    }


    @Override
    public List<KeyValueEntity> getCateClassify() {
        List<CateClassify> cateClassifies = cateProductMapper.selectCateClassifyList(new CateClassify());
        return keyValueListByCateClassifies(cateClassifies);
    }

    private List<KeyValueEntity> keyValueListByCateClassifies(List<CateClassify> cateProductList) {
        if (CollectionUtils.isEmpty(cateProductList)) {
            return new ArrayList<>();
        }
        return cateProductList.stream().map(cateProduct -> {
            KeyValueEntity keyValueEntity = new KeyValueEntity();
            keyValueEntity.setKey(cateProduct.getCategoryCode());
            keyValueEntity.setValue(cateProduct.getCategoryName());
            return keyValueEntity;
        }).collect(Collectors.toList());
    }


    @Override
    public List<CateProduct> selectAllCateProduct() {
        return cateProductMapper.selectAllCateProduct();
    }

    @Override
    public List<CateProduct> selectList(CateProduct cateProduct) {
        return cateProductMapper.selectList(cateProduct);
    }

    @Override
    public List<KeyValueEntity> getCateProductCache() {
        List<KeyValueEntity> caches = cateProductCache.get("cateProductCache");
        if (CollectionUtils.isEmpty(caches)) {
            caches = getCateProduct();
            cateProductCache.put("cateProductCache", caches);
        }
        return caches;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public CateProduct selectListByProductCategoryCode(String productCategoryCode) {
        return cateProductMapper.selectListByProductCategoryCode(productCategoryCode);
    }

    @Override
    @Cached(name = "CateProductServiceImpl:getOperationClassification", cacheType = CacheType.BOTH, expire = 60 * 60 * 2, cacheNullValue = true, localLimit = 1000)
    public List<KeyValueEntity> getOperationClassification() {
        List<String> operationClassificationList = cateProductMapper.getOperationClassification();
        return keyValueListByOperationClassification(operationClassificationList);
    }

    private List<KeyValueEntity> keyValueListByOperationClassification(List<String> operationClassificationList) {
        if (CollectionUtils.isEmpty(operationClassificationList)) {
            return new ArrayList<>();
        }
        return operationClassificationList.stream().map(operationClassification -> {
            KeyValueEntity keyValueEntity = new KeyValueEntity();
            keyValueEntity.setKey(operationClassification);
            keyValueEntity.setValue(operationClassification);
            return keyValueEntity;
        }).collect(Collectors.toList());
    }

    @Override
    public List<String> getGoodsCodesByOperationClassification(String operationClassification) {
        return cateProductMapper.getGoodsCodesByOperationClassification(operationClassification);
    }

    @Override
    public List<String> getGoodsCodesByOperationClassificationList(List<String> operationClassificationList) {
        return cateProductMapper.getGoodsCodesByOperationClassificationList(operationClassificationList);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        QuickConfig stockConfig = QuickConfig.newBuilder("PDM:CateProduct").expire(Duration.ofMinutes(15)).localLimit(1000).cacheType(CacheType.BOTH).build();
        cateProductCache = cacheManager.getOrCreateCache(stockConfig);
    }
}
