package com.suncent.smc.persistence.publication.domain.entity;

import com.suncent.smc.common.annotation.Excel;
import com.suncent.smc.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * AI内容生成任务对象 sc_smc_ai_generation_task
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AiGenerationTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 任务关联ID，外部AI系统返回的唯一标识 */
    @Excel(name = "任务关联ID")
    private String taskCorrelationId;

    /** 任务类型：TITLE-标题生成，DESCRIPTION-描述生成 */
    @Excel(name = "任务类型")
    private String taskType;

    /** 任务状态：PENDING-待处理，PROCESSING-处理中，COMPLETED-已完成，FAILED-失败 */
    @Excel(name = "任务状态")
    private String taskStatus;

    @Excel(name = "SMC任务ID")
    private Integer taskId;

    private String goodsCodes;

    /** AI生成结果，JSON格式存储完整的返回数据 */
    @Excel(name = "AI生成结果")
    private String aiResult;

    /** 店铺编码 */
    @Excel(name = "店铺编码")
    private String shopCode;

    /** 品牌编码 */
    @Excel(name = "品牌编码")
    private String brandCode;

    /** 删除标志：0-正常，1-删除 */
    private Integer delFlag;

    /** 处理状态：0-未处理，1-已处理 */
    @Excel(name = "处理状态")
    private Integer processStatus;

    private List<String> taskStatusList;
    /**
     * 任务类型枚举
     */
    public enum TaskType {
        TITLE("TITLE", "标题生成"),
        DESCRIPTION("DESCRIPTION", "描述生成");

        private final String code;
        private final String desc;

        TaskType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("PENDING", "待处理"),
        PROCESSING("PROCESSING", "处理中"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "失败");

        private final String code;
        private final String desc;

        TaskStatus(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
