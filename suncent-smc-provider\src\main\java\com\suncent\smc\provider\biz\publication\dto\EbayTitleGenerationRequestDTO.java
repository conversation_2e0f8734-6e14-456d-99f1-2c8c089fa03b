package com.suncent.smc.provider.biz.publication.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * Ebay商品标题生成API请求DTO
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
public class EbayTitleGenerationRequestDTO {

    /**
     * 店铺名（目前预留，暂不影响业务）
     */
    @NotBlank(message = "店铺名不能为空")
    private String shop;

    /**
     * 商品列表
     */
    @NotEmpty(message = "商品列表不能为空")
    @Valid
    private List<GoodsItem> goods;

    @NotBlank(message = "品牌名不能为空")
    private String brand;

    // 以下字段由后端内部设置，不需要前端传递

    /**
     * 任务关联标识符（后端生成）
     */
    private String taskCorrelationId;

    /**
     * 回调地址（后端构建）
     */
    private String callbackUrl;

    /**
     * 商品项
     */
    @Data
    public static class GoodsItem {
        
        /**
         * 商品编码，唯一标识商品
         */
        @NotBlank(message = "商品编码不能为空")
        private String goodsCode;

        /**
         * 生成标题数量，每个商品可单独指定
         */
        @NotNull(message = "生成标题数量不能为空")
        @Positive(message = "生成标题数量必须大于0")
        private Integer n;
    }
}
