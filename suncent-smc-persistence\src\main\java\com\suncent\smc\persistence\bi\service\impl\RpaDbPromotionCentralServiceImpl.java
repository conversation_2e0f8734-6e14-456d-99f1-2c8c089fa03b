package com.suncent.smc.persistence.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.suncent.smc.common.annotation.DataSource;
import com.suncent.smc.common.enums.DataSourceType;
import com.suncent.smc.persistence.bi.entity.RpaDbPromotionCentral;
import com.suncent.smc.persistence.bi.mapper.RpaDbPromotionCentralMapper;
import com.suncent.smc.persistence.bi.service.IRpaDbPromotionCentralService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * RPA库促销中心数据服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
@DataSource(DataSourceType.BI2)
@Slf4j
public class RpaDbPromotionCentralServiceImpl implements IRpaDbPromotionCentralService {

    @Autowired
    private RpaDbPromotionCentralMapper rpaDbPromotionCentralMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectByOfferingName(String offeringName) {
        if (offeringName == null || offeringName.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralMapper.selectByOfferingName(offeringName);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectByOfferingNameAndTimeRange(String offeringName, Date startTime, Date endTime) {
        if (offeringName == null || offeringName.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralMapper.selectByOfferingNameAndTimeRange(offeringName, startTime, endTime);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public RpaDbPromotionCentral selectByPromotionId(String promotionId) {
        if (promotionId == null || promotionId.trim().isEmpty()) {
            return null;
        }
        return rpaDbPromotionCentralMapper.selectByPromotionId(promotionId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectByPromotionIds(List<String> promotionIds) {
        if (CollUtil.isEmpty(promotionIds)) {
            return new ArrayList<>();
        }

        List<RpaDbPromotionCentral> result = new ArrayList<>();

        // 分批查询，每批1000条
        List<List<String>> promotionIdBatches = CollUtil.split(promotionIds, 1000);
        for (List<String> batch : promotionIdBatches) {
            List<RpaDbPromotionCentral> batchResult = rpaDbPromotionCentralMapper.selectByPromotionIds(batch);
            if (CollUtil.isNotEmpty(batchResult)) {
                result.addAll(batchResult);
            }
        }

        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Date selectMaxTimeUpByOfferingName(String offeringName) {
        if (offeringName == null || offeringName.trim().isEmpty()) {
            return null;
        }
        return rpaDbPromotionCentralMapper.selectMaxTimeUpByOfferingName(offeringName);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int countByOfferingName(String offeringName) {
        if (offeringName == null || offeringName.trim().isEmpty()) {
            return 0;
        }
        return rpaDbPromotionCentralMapper.countByOfferingName(offeringName);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectByOfferingNameWithPaging(String offeringName, int offset, int limit) {
        if (offeringName == null || offeringName.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralMapper.selectByOfferingNameWithPaging(offeringName, offset, limit);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<String> selectDistinctOfferingNames() {
        return rpaDbPromotionCentralMapper.selectDistinctOfferingNames();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectByMarketplaceIdAndOfferingName(String marketplaceId, String offeringName) {
        if (marketplaceId == null || marketplaceId.trim().isEmpty()
                || offeringName == null || offeringName.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralMapper.selectByMarketplaceIdAndOfferingName(marketplaceId, offeringName);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectForSync(String offeringName, Date lastSyncTime) {
        if (offeringName == null || offeringName.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralMapper.selectForSync(offeringName, lastSyncTime);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectBestDealPromotions() {
        return selectByOfferingName("BestDeal");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectBestDealForSync(Date lastSyncTime) {
        return selectForSync("BestDeal", lastSyncTime);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectBestDealLatestToday() {
        return rpaDbPromotionCentralMapper.selectLatestByOfferingNameToday("BestDeal");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentral> selectBestDealLatestInTimeRange(Date startTime, Date endTime) {
        return rpaDbPromotionCentralMapper.selectLatestInTimeRange("BestDeal", startTime, endTime);
    }
}
