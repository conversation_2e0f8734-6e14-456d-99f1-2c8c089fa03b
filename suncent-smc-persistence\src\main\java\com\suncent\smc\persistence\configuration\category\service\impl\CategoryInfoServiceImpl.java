package com.suncent.smc.persistence.configuration.category.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.suncent.smc.common.config.RuoYiConfig;
import com.suncent.smc.common.core.domain.entity.SysDictData;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.enums.ConfigRequiredFieldTypeEnum;
import com.suncent.smc.common.enums.PlatformSiteEnum;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.file.FileUtils;
import com.suncent.smc.persistence.cdp.service.IAttributeService;
import com.suncent.smc.persistence.cdp.service.ICateProductService;
import com.suncent.smc.persistence.configuration.category.domain.entity.CategoryInfo;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigRequiredField;
import com.suncent.smc.persistence.configuration.category.mapper.CategoryInfoMapper;
import com.suncent.smc.persistence.configuration.category.mapper.ConfigRequiredFieldMapper;
import com.suncent.smc.persistence.configuration.category.service.ICategoryInfoService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.mapper.PlatformCategoryMapper;
import com.suncent.smc.system.mapper.SysDictDataMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CategoryInfoServiceImpl implements ICategoryInfoService {

    private static final Logger log = LoggerFactory.getLogger(CategoryInfoServiceImpl.class);
    @Autowired
    private CategoryInfoMapper categoryInfoMapper;

    @Autowired
    private ICateProductService cateProductService;

    @Autowired
    private ConfigRequiredFieldMapper configRequiredFieldMapper;

    @Autowired
    private PlatformCategoryMapper platformCategoryMapper;

    @Autowired
    private IAttributeService attributeService;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;


    private final  static String EBAY_SELECT_PRODUCT_CATEGORY = "ebay_select_product_category";

    /**
     * 查询类目配置
     *
     * @param id 类目配置主键
     * @return 类目配置
     */
    @Override
    public CategoryInfo selectCategoryInfoById(String id)
    {
        return categoryInfoMapper.selectCategoryInfoById(id);
    }

    /**
     * 查询类目配置列表
     *
     * @param categoryInfo 类目配置
     * @return 类目配置
     */
    @Override
    public List<CategoryInfo> selectCategoryInfoList(CategoryInfo categoryInfo)
    {
        return categoryInfoMapper.selectCategoryInfoList(categoryInfo);
    }

    /**
     * 新增类目配置
     *
     * @param categoryInfo 类目配置
     * @return 结果
     */
    @Override
    public int insertCategoryInfo(CategoryInfo categoryInfo)
    {
        categoryInfo.setCreateTime(DateUtils.getNowDate());
        return categoryInfoMapper.insertCategoryInfo(categoryInfo);
    }

    /**
     * 修改类目配置
     *
     * @param categoryInfo 类目配置
     * @return 结果
     */
    @Override
    public int updateCategoryInfo(CategoryInfo categoryInfo)
    {
        categoryInfo.setUpdateTime(DateUtils.getNowDate());
        return categoryInfoMapper.updateCategoryInfo(categoryInfo);
    }

    /**
     * 批量删除类目配置
     *
     * @param ids 需要删除的类目配置主键
     * @return 结果
     */
    @Override
    public int deleteCategoryInfoByIds(String ids)
    {
        return categoryInfoMapper.deleteCategoryInfoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除类目配置信息
     *
     * @param id 类目配置主键
     * @return 结果
     */
    @Override
    public int deleteCategoryInfoById(String id)
    {
        return categoryInfoMapper.deleteCategoryInfoById(id);
    }

    @Override
    public List<CategoryInfo> getCategoryInfoListByCategoryId(String categoryId) {
        return categoryInfoMapper.selectByPlatformCategoryId(categoryId);
    }

    @Override
    public void batchUpdateAttrMappingByFile(MultipartFile multipartFile) {

        try {
            //获取类目属性缓存
            List<KeyValueEntity> cateProductCache = cateProductService.getCateProductCache();
            Map<String,String> cateProductMap = cateProductCache.stream().collect(Collectors.toMap(KeyValueEntity::getValue, KeyValueEntity::getKey));
            //获取数据字典
            List<SysDictData> sysDictDataList = sysDictDataMapper.selectDictDataByType(EBAY_SELECT_PRODUCT_CATEGORY);
            Map<String,String> sysDictDataMap = sysDictDataList.stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
            String site = PlatformSiteEnum.US.getSiteCodeType();
            String platformCode = PlatformTypeEnum.EB.name();
            //本地文件路径
            String path = RuoYiConfig.getUploadPath()+File.separator+"attrMapping"+File.separator+DateUtils.dateTime()+File.separator
                    +UUID.randomUUID().toString().replace("-", "")+"_"+multipartFile.getOriginalFilename();
            File file=FileUtils.convertToFile(multipartFile,path);
            // 使用 EasyExcel 读取获取所有 sheet 名称
            List<String> sheetNames = getSheetNamesWithEasyExcel(file);
            for(String sheetName : sheetNames){
                //获取当前页数据所有字段
                List<Map<Integer, Object>> sheetDataList = EasyExcel.read(file).headRowNumber(0).sheet(sheetName).doReadSync();
                //根据sheetName获取categoryCode
                String categoryCode = null;
                if (cateProductMap.containsKey(sheetName)){
                    categoryCode = cateProductMap.get(sheetName);
                }else {
                    log.warn("未在pdm 产品分类中找到:"+sheetName);
                    continue;
                }
                if (sheetDataList.size()<2){
                    log.warn("文件数据不足无法解析映射关系");
                    continue;
                }
                //新增数据字典
                if (!sysDictDataMap.containsKey(sheetName)){
                    insertDictData(sheetName, categoryCode);
                }
                //获取sheet页数据所有字段
                Map<Integer, Object> headMap = sheetDataList.get(0);
                Map<Integer,Object> dataMap = sheetDataList.get(1);
                //获取A列类目id
                String categoryIdStr = headMap.get(0).toString();
                String[] categoryIdArray = categoryIdStr.split(",");
                for (String categoryId : categoryIdArray){
                    //先根据类目id,平台，站点得到平台类目id
                    PlatformCategory platformCategory = platformCategoryMapper.selectByPlatformCodeAndSiteAndCategoryId(platformCode,site,categoryId,null);
                    Long platformCategoryId = platformCategory.getId();
                    //查询已有的必填字段
                    List<ConfigRequiredField> configRequiredFieldList=configRequiredFieldMapper.selectByCategoryIdAndPlatformCode(platformCategoryId.toString(), site,platformCode);
                    Map<String,ConfigRequiredField> requiredFieldMap = configRequiredFieldList.stream().collect(Collectors.toMap(ConfigRequiredField::getAttributeName,  Function.identity(), (existing, replacement) -> replacement));
                    //获取产品分类的商品库属性
                    List<KeyValueEntity> attributeList = attributeService.listAttribute(categoryCode);
                    Map<String,String> attributeMap = attributeList.stream().collect(Collectors.toMap(KeyValueEntity::getValue, KeyValueEntity::getKey));
                    //遍历属性，从第B列开始,绑定平台属性关系
                    bindPlatformFiledRelation(headMap, dataMap, attributeMap, requiredFieldMap, platformCategoryId, platformCode, site);
                }
                log.info("已经处理完sheet页："+sheetName);

            }
            log.info("批量更新平台属性映射关系完成！");
        } catch (Exception e) {
            throw new BusinessException("批量更新平台属性映射关系错误！",e);
        }


    }

    /**
     *
     * @param sheetName
     * @param categoryCode
     */
    private void insertDictData(String sheetName, String categoryCode) {
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType(EBAY_SELECT_PRODUCT_CATEGORY);
        sysDictData.setDictLabel(sheetName);
        sysDictData.setDictValue(categoryCode);
        sysDictData.setStatus("0");
        sysDictData.setIsDefault("Y");
        sysDictData.setCreateTime(DateUtils.getNowDate());
        sysDictData.setCreateBy("admin");
        sysDictDataMapper.insertDictData(sysDictData);
    }


    /**
     * 绑定平台属性关系
     * @param headMap
     * @param dataMap
     * @param attributeMap
     * @param requiredFieldMap
     * @param platformCategoryId
     * @param platformCode
     * @param site
     * @throws Exception
     */
    private void bindPlatformFiledRelation(Map<Integer, Object> headMap, Map<Integer, Object> dataMap, Map<String, String> attributeMap, Map<String, ConfigRequiredField> requiredFieldMap, Long platformCategoryId, String platformCode, String site) throws Exception {
        for (Integer index = 1; index < headMap.size(); index++){
            if (ObjectUtils.isEmpty(headMap.get(index))){
                continue;
            }
            String attrName = headMap.get(index).toString();
            String attrDesc = dataMap.get(index)==null?"": dataMap.get(index).toString();
            //绑定平台属性名与商品库属性
            String attributeCode = null;
            if (StringUtils.isNotEmpty(attrDesc)){
                attributeCode  = attributeMap.get(attrDesc);
            }
            if (!requiredFieldMap.containsKey(attrName)){
                //新增平台字段并绑定商品属性
                insertConfigRequiredField(attrName, attrDesc, attributeCode, platformCategoryId, platformCode, site);
            }else {
                if (StringUtils.isNotEmpty(attributeCode)){
                    ConfigRequiredField configRequiredField = requiredFieldMap.get(attrName);
                    ConfigRequiredField update = new ConfigRequiredField();
                    update.setPdmAttributeName(attributeCode);
                    update.setId(configRequiredField.getId());
                    update.setUpdateTime(DateUtils.getNowDate());
                    update.setUpdateBy("system");
                    configRequiredFieldMapper.updateConfigRequiredField(update);
                }
            }

        }
    }

    /**
     * 新增平台字段
     * @param attrName
     * @param attrDesc
     * @param attributeCode
     * @param platformCategoryId
     * @param platformCode
     * @param site
     */
    private void insertConfigRequiredField(String attrName, String attrDesc, String attributeCode, Long platformCategoryId, String platformCode, String site) throws Exception{
        //新增属性值
        ConfigRequiredField configRequiredField = new ConfigRequiredField();
        configRequiredField.setAttributeName(attrName);
        configRequiredField.setAttributeCode(attrName);
        configRequiredField.setAttributeType(ConfigRequiredFieldTypeEnum.EB_ATTRIBUTE.getValue());
        configRequiredField.setAttributeMemo(attrDesc);
        if (StringUtils.isNotBlank(attributeCode)){
            configRequiredField.setPdmAttributeName(attributeCode);
        }
        configRequiredField.setIsRequire("0");
        configRequiredField.setCategoryId(platformCategoryId.toString());
        configRequiredField.setPlatformCode(platformCode);
        configRequiredField.setSite(site);
        configRequiredField.setCreateTime(DateUtils.getNowDate());
        configRequiredField.setUpdateTime(DateUtils.getNowDate());
        configRequiredField.setCreateBy("system");
        configRequiredField.setUpdateBy("system");
        configRequiredFieldMapper.insertConfigRequiredField(configRequiredField);
    }

    /**
     * 使用EasyExcel获取Excel文件的所有sheet名称
     *
     * @param file Excel文件
     * @return sheet名称列表
     */
    private List<String> getSheetNamesWithEasyExcel(File file) {
        ExcelReader excelReader = null;
        try {
            excelReader = EasyExcel.read(file).build();
            List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
            return sheets.stream()
                    .map(ReadSheet::getSheetName)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取Excel文件所有sheet名称失败", e);
            throw new RuntimeException("获取Excel文件所有sheet名称失败");
        } finally {
            if (excelReader != null) {
                excelReader.finish();
            }
        }
    }

}
