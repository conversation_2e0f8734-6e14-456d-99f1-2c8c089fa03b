package com.suncent.smc.provider.biz.publication.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.suncent.smc.common.constant.Constants;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.*;
import com.suncent.smc.common.utils.html.EscapeUtil;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.persistence.ads.domain.AdsAmazonListingDataVIO;
import com.suncent.smc.persistence.ads.domain.FitExamineDay;
import com.suncent.smc.persistence.ads.domain.ItDemand;
import com.suncent.smc.persistence.ads.mapper.AdsItemFitCompareMapper;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.amazon.domain.AmazonWarehouseMapping;
import com.suncent.smc.persistence.amazon.domain.VcListingInventory;
import com.suncent.smc.persistence.amazon.service.IAmazonWarehouseMappingService;
import com.suncent.smc.persistence.amazon.service.IVcListingInventoryService;
import com.suncent.smc.persistence.bi.service.IOdsCrlCrlVcCatalogDataService;
import com.suncent.smc.persistence.cdp.domain.entity.Brand;
import com.suncent.smc.persistence.cdp.domain.entity.Shop;
import com.suncent.smc.persistence.common.CommonUtilsSmc;
import com.suncent.smc.persistence.configuration.category.domain.entity.CategoryInfo;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigRequiredField;
import com.suncent.smc.persistence.configuration.category.domain.entity.GoodsCategoryMapping;
import com.suncent.smc.persistence.configuration.category.service.IGoodsCategoryMappingService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreInfoService;
import com.suncent.smc.persistence.inventory.domain.InventoryUpdateBlack;
import com.suncent.smc.persistence.inventory.domain.VcInventoryRecord;
import com.suncent.smc.persistence.inventory.service.IInventoryExcludeRecordService;
import com.suncent.smc.persistence.inventory.service.IInventoryUpdateBlackService;
import com.suncent.smc.persistence.pdm.domain.dto.*;
import com.suncent.smc.persistence.pdm.domain.entity.Goods;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsImage;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsSpecifications;
import com.suncent.smc.persistence.pdm.domain.entity.MappingGoods;
import com.suncent.smc.persistence.pdm.service.IGoodsService;
import com.suncent.smc.persistence.product.domain.entity.ProductDocumentRecord;
import com.suncent.smc.persistence.publication.domain.dto.*;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingJSONFeedVO;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingUpdateFeedVO;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.temu.domain.entity.TemuGoodsHead;
import com.suncent.smc.persistence.todo.domain.entity.AmazonNodeIncorrectTodo;
import com.suncent.smc.persistence.todo.domain.entity.BiRpaNodeIncorrectTask;
import com.suncent.smc.persistence.todo.domain.entity.SmcAdaptTodo;
import com.suncent.smc.persistence.todo.domain.entity.VcPriceChangeTodo;
import com.suncent.smc.persistence.todo.service.*;
import com.suncent.smc.persistence.word.service.IWordAiUsageService;
import com.suncent.smc.provider.base.service.ICdpBaseConfigService;
import com.suncent.smc.provider.biz.bi.service.OdsCrlCrlVcCatalogDataBiz;
import com.suncent.smc.provider.biz.configuration.InventoryExcludeBiz;
import com.suncent.smc.provider.biz.configuration.domain.AmazonAttributePropertiesDTO;
import com.suncent.smc.provider.biz.configuration.domain.DefinitionsDTO;
import com.suncent.smc.provider.biz.error.ErrorDefine;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.pdm.CreatePlatformCode;
import com.suncent.smc.provider.biz.publication.*;
import com.suncent.smc.provider.biz.publication.domain.AddPlatformPublishDTO;
import com.suncent.smc.provider.biz.publication.domain.AmazonBatchUpdateInventoryVO;
import com.suncent.smc.provider.biz.publication.domain.ListingsEditVO;
import com.suncent.smc.provider.biz.publication.domain.SaleGoodsVO;
import com.suncent.smc.provider.biz.publication.dto.AmazonEditPlatSkuDTO;
import com.suncent.smc.provider.biz.publication.dto.VcDfConfirmTransactionStatus;
import com.suncent.smc.provider.biz.publication.service.AbstractBaseListingService;
import com.suncent.smc.provider.biz.publication.service.IBaseListingService;
import com.suncent.smc.provider.biz.task.PullFileRelatedTaskBiz;
import com.suncent.smc.provider.update.ListingUpdateModuleResolver;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import com.suncent.smc.system.service.ISysConfigService;
import com.suncent.smc.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.suncent.smc.common.enums.AmazonAttributeEnum.LIST_CURRENCY;
import static com.suncent.smc.common.enums.AmazonAttributeEnum.LIST_PRICE;
import static com.suncent.smc.common.enums.MonitorEnum.AM_QUICK_UPDATE;
import static com.suncent.smc.common.enums.PublishStatus.*;
import static com.suncent.smc.common.utils.DateUtils.dateTimeNow;
import static com.suncent.smc.provider.biz.configuration.domain.DefinitionsDTO.getPropertyCodesTypeMap;

/**
 * Amazon Listing管理Service 接口
 *
 * <AUTHOR>
 * @since 2023-03-27 11:03:00
 */
@Component
@Slf4j
public class AmazonPlatformListingServiceImpl extends AbstractBaseListingService implements IBaseListingService {

    @Autowired
    PlatformListingFactory platformListingFactory;
    @Autowired
    private IGoodsService goodsService;

    @Autowired
    private ThirdpartyInventoryBiz inventoryBiz;

    @Autowired
    private IPlatformCategoryService platformCategoryService;
    @Autowired
    private ICdpBaseConfigService cdpBaseConfig;
    @Autowired
    private IListingLogService listingLogService;
    @Autowired
    private IListingAdapterLogService listingAdapterLogService;
    @Autowired
    CreatePlatformCode createPlatformCode;
    @Autowired
    @Lazy
    private AmazonPlatformListingServiceImpl amazonPlatformListingService;
    @Resource
    protected IGoodsTaskInfoService goodsTaskInfoService;
    @Autowired
    private PDMHttpRequestBiz pdmHttpRequestBiz;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IAdsService adsService;
    @Autowired
    private PullFileRelatedTaskBiz pullFileRelatedTaskBiz;
    @Autowired
    private IStockArrivesTodoService stockArrivesTodoService;
    @Autowired
    private AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;
    @Autowired
    private IListingPromotionService listingPromotionService;
    @Autowired
    private IWordAiUsageService wordAiUsageService;
    @Autowired
    private IInventoryExcludeRecordService inventoryExcludeRecordService;
    @Autowired
    InventoryExcludeBiz inventoryExcludeBiz;
    @Autowired
    private IRedLinePriceTodoService redLinePriceTodoService;
    @Autowired
    private ILoseCartTodoService loseCartTodoService;
    @Autowired
    private IInventoryLowTodoService inventoryLowTodoService;
    @Autowired
    private IBiRpaNodeIncorrectTaskService biRpaNodeIncorrectTaskService;
    @Autowired
    private IAmazonNodeIncorrectTodoService amazonNodeIncorrectTodoService;
    @Autowired
    private IAmCategoryTemplateFieldService amCategoryTemplateFieldService;
    @Autowired
    private IAmCategoryTemplateFieldPropService amCategoryTemplateFieldPropService;
    @Autowired
    private IAmCategoryTemplatePrivateValueService amCategoryTemplatePrivateValueService;
    @Autowired
    private IAmCategoryTemplateFieldPropEnumService amCategoryTemplateFieldPropEnumService;
    @Autowired
    private IAmCategoryTemplateSmcMappingService amCategoryTemplateSmcMappingService;
    @Autowired
    private AmazonProductBiz amazonProductBiz;
    @Autowired
    private IGoodsCategoryMappingService goodsCategoryMappingService;
    @Autowired
    private IVcPriceChangeTodoService vcPriceChangeTodoService;
    @Autowired
    private AdsItemFitCompareMapper adsItemFitCompareMapper;
    @Autowired
    private IAmazonWarehouseMappingService amazonWarehouseMappingService;
    @Autowired
    IVcListingInventoryService vcListingInventoryService;
    @Autowired
    IInventoryUpdateBlackService inventoryUpdateBlackService;
    @Autowired
    IConfigStoreInfoService storeInfoService;
    @Autowired
    IListingPullDateService listingPullDateService;
    @Autowired
    private IListingLabelService listingLabelService;
    @Autowired
    @Qualifier("threadPoolTaskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    private static ThreadPoolExecutor pullPool = new ThreadPoolExecutor(
            5,
            5,
            1L,
            TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(2000));

    static {
        pullPool.allowCoreThreadTimeOut(true);
    }
    protected ThreadPoolExecutor batchSavePool = new ThreadPoolExecutor(
            1,
            8,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(2000));

    ThreadPoolExecutor eidtPoolConfig = new ThreadPoolExecutor(
            2,
            8,
            0L,
            TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(2000));

    ThreadPoolExecutor findKeywordPoolConfig = new ThreadPoolExecutor(2, 4, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(10000));

    ThreadPoolExecutor lostCartPoolConfig = new ThreadPoolExecutor(2, 4, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(1000));

    @Autowired
    private AmazonApiListingBiz amazonApiListingBiz;
    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    @Lazy
    private BaseAmazonProductUpdateV2Task baseAmazonProductUpdateV2Task;
    @Autowired
    private ListingInfoBiz listingInfoBiz;
    @Autowired
    private IOdsCrlCrlVcCatalogDataService odsCrlCrlVcCatalogDataService;
    @Autowired
    private OdsCrlCrlVcCatalogDataBiz odsCrlCrlVcCatalogDataBiz;
    @Autowired
    private IAmCategoryTemplateFieldRelationService amCategoryTemplateFieldRelationService;

    @Autowired
    protected DingdingMonitorInfoBiz dingdingMonitorInfoBiz;

    @Autowired
    private IMonitorListingSnapshotService monitorListingSnapshotService;
//
//    private Striped<Lock> spreadLock = Striped.lock(32);
//    private Striped<Lock> syncSpreadLock = Striped.lock(128);

    @PostConstruct
    public void init() {
        platformListingFactory.platformNodeMap.put(PlatformTypeEnum.AM.name(), this);
    }

    /**
     * 根据saleGoodId批量获取刊登需要的商品信息
     *
     * @param saleGoodIdsParam
     * @return
     */
    @Override
    public SaleGoodsVO getSaleGoodsDTOListByGoodsIds(List<Long> saleGoodIdsParam, Boolean isVc) {
        GetGoodsDetailQueryDTO goodsDetailQueryDTO = new GetGoodsDetailQueryDTO();
        goodsDetailQueryDTO.setGoodsIdList(saleGoodIdsParam);
        List<SaleGoodsDTO> returnList = pdmHttpRequestBiz.listGoodsDetail(goodsDetailQueryDTO);
        if (CollectionUtils.isEmpty(returnList)) {
            return new SaleGoodsVO();
        }

        AddPlatformPublishDTO platformPublishDTO = getAddPlatformPublishDTO(returnList, isVc);
        //三方库存
        Map<String, List<ThirdpartyFbmDTO>> thirdpartyInventoryBaseMap = platformPublishDTO.getThirdpartyInventoryBaseMap();
        //类目
        Map<String, List<PlatformCategory>> categoryMap = platformPublishDTO.getCategoryMap();
        //品牌
        List<KeyValueEntity> brandList = platformPublishDTO.getBrandList();

        for (SaleGoodsDTO goodsDto : returnList) {
            goodsDto.setGoodsSpecifications(goodsDto.getSpecifications());

            //库存
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = thirdpartyInventoryBaseMap.get(goodsDto.getGoodsCode());
            goodsDto.setStockOnSalesQty(0);
            if (CollectionUtils.isNotEmpty(thirdpartyFbmDTOList)) {
                Integer availableQty = thirdpartyFbmDTOList.stream().mapToInt(ThirdpartyFbmDTO::getSellableQty).sum();
                goodsDto.setStockOnSalesQty(availableQty);
            }

            GoodsRedPriceDTO goodsPriceInfo = goodsDto.getGoodsPriceInfo();
            if (ObjUtil.isNotEmpty(goodsPriceInfo)) {
                goodsDto.setFbaPrice(Objects.isNull(goodsPriceInfo.getFbaRedLinePrice()) ? new BigDecimal(0) : BigDecimal.valueOf(goodsPriceInfo.getFbaRedLinePrice()));
                goodsDto.setFbmPrice(Objects.isNull(goodsPriceInfo.getFbmRedLinePrice()) ? new BigDecimal(0) : BigDecimal.valueOf(goodsPriceInfo.getFbmRedLinePrice()));
                goodsDto.setVcRedLinePrice(Objects.isNull(goodsPriceInfo.getVcRedLinePrice()) ? "0" : String.valueOf(goodsPriceInfo.getVcRedLinePrice()));
            } else {
                goodsDto.setFbaPrice(new BigDecimal(0));
                goodsDto.setFbmPrice(new BigDecimal(0));
                goodsDto.setVcRedLinePrice("0");
            }


            //对应平台类目
            List<PlatformCategory> list = categoryMap.get(goodsDto.getProductCategoryCode());
            if (CollectionUtils.isNotEmpty(list)) {
                goodsDto.setProductCategoryCode(String.valueOf(list.get(0).getId()));
            }

            //品牌名
            if (CollectionUtils.isNotEmpty(brandList)) {
                brandList.forEach(brand -> {
                    if (Objects.equals(brand.getKey(), goodsDto.getBrandCode())) {
                        goodsDto.setBrandName(brand.getValue());
                        goodsDto.setBrandCode(brand.getValue());
                    }
                });
            }
            //站点
            goodsDto.setSiteCode("US");
            //图片
            List<GoodsImage> goodsImageList = goodsDto.getGoodsImageList();
            if (CollectionUtils.isNotEmpty(goodsImageList)) {
                //白底图
                goodsDto.setGoodsImageList(goodsImageList.stream().filter(goodsImage -> Objects.equals(goodsImage.getType(), ImageType.WHITE_BACKGROUND.getType().toString())).collect(Collectors.toList()));
            }

        }
        //根据goodsCode排序
        if (CollUtil.isNotEmpty(returnList)) {
            returnList = returnList.stream().sorted(Comparator.comparing(SaleGoodsDTO::getGoodsCode)).collect(Collectors.toList());
        }
        SaleGoodsVO saleGoodsVO = new SaleGoodsVO();
        saleGoodsVO.setSaleGoodsDTOList(returnList);
        saleGoodsVO.setSiteCode("US");
        return saleGoodsVO;
    }

    private AddPlatformPublishDTO getAddPlatformPublishDTO(List<SaleGoodsDTO> returnList, Boolean isVc) {
        AddPlatformPublishDTO platformPublishDTO = new AddPlatformPublishDTO();

        List<Long> saleGoodIds = returnList.stream().map(goodsDto -> goodsDto.getId()).collect(Collectors.toList());
        List<String> goodsCodes = returnList.stream().map(goodsDto -> goodsDto.getGoodsCode()).collect(Collectors.toList());
        List<String> productCategoryCode = returnList.stream().map(goodsDto -> goodsDto.getProductCategoryCode()).collect(Collectors.toList());

//        if (CollectionUtils.isNotEmpty(saleGoodIds)) {
        //商品图片
//            List<GoodsImage> goodsImages = goodsImageService.selectGoodsImageByGoodsIds(saleGoodIds, ImageType.WHITE_BACKGROUND.getType().toString());
//            Map<Long, List<GoodsImage>> goodsImageMap = goodsImages.stream().collect(Collectors.groupingBy(GoodsImage::getGoodsId));
//
//            //商品规格
//            List<GoodsSpecifications> goodsSpecifications = goodsSpecificationsService.selectGoodsSpecificationsByGoodsIds(saleGoodIds);
//            Map<Long, GoodsSpecifications> goodsSpecificationsMap = goodsSpecifications.stream().collect(Collectors.toMap(GoodsSpecifications::getGoodsId, a -> a));
//
//            platformPublishDTO.setGoodsImageMap(goodsImageMap);
//            platformPublishDTO.setGoodsSpecificationsMap(goodsSpecificationsMap);
//        }

        if (CollectionUtils.isNotEmpty(goodsCodes)) {
            //三方库存
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectStockShareAndPartGoodsCode(goodsCodes, "US", isVc);
            Map<String, List<ThirdpartyFbmDTO>> thirdpartyInventoryBaseMap = new HashMap<>();
            //默认先取us站点的库存
            if (CollUtil.isNotEmpty(thirdpartyFbmDTOList)) {
                thirdpartyInventoryBaseMap = thirdpartyFbmDTOList.stream().filter(t -> Objects.equals(t.getWhCountry(), PlatformSiteEnum.AM_US.getName()))
                        .collect(Collectors.groupingBy(ThirdpartyFbmDTO::getSku));
            }

            //价格
//            List<GoodsBiPrice> goodsBiPrices = goodsBiPriceService.selectGoodsBiPriceByGoodsCodes(goodsCodes);
//            Map<String, GoodsBiPrice> goodsBiPricesMap = goodsBiPrices.stream().collect(Collectors.toMap(GoodsBiPrice::getGoodsCode, a -> a));
//
            platformPublishDTO.setThirdpartyInventoryBaseMap(thirdpartyInventoryBaseMap);
//            platformPublishDTO.setGoodsBiPricesMap(goodsBiPricesMap);
        }

        if (CollectionUtils.isNotEmpty(productCategoryCode)) {
            //类目
            List<PlatformCategory> categoryList = platformCategoryService.selectPlatformCategoryListByPdmCodes(productCategoryCode, PlatformTypeEnum.AM.name());
            Map<String, List<PlatformCategory>> categoryMap = categoryList.stream().collect(Collectors.groupingBy(PlatformCategory::getPdmCode));

            platformPublishDTO.setCategoryMap(categoryMap);
        }

        //品牌
        List<KeyValueEntity> brandList = cdpBaseConfig.getBrandAllKVList();
        platformPublishDTO.setBrandList(brandList);

        return platformPublishDTO;
    }

    private GoodsSpecifications getGoodsSpecifications(Map<Long, GoodsSpecifications> goodsSpecificationsMap, SaleGoodsDTO goodsDto) {
        GoodsSpecifications specifications = goodsSpecificationsMap.get(goodsDto.getId());
        if (specifications == null) {
            specifications = new GoodsSpecifications();
            specifications.setLength(new BigDecimal(0));
            specifications.setWidth(new BigDecimal(0));
            specifications.setHeight(new BigDecimal(0));
            specifications.setGrossWeight(new BigDecimal(0));
            specifications.setLengthUnit("cm");
            specifications.setGrossWeightUnit("kg");
            return specifications;
        }
        if (specifications.getLength() != null) {
            specifications.setLength(specifications.getLength().setScale(2, RoundingMode.HALF_UP));
        }
        if (specifications.getWidth() != null) {
            specifications.setWidth(specifications.getWidth().setScale(2, RoundingMode.HALF_UP));
        }
        if (specifications.getHeight() != null) {
            specifications.setHeight(specifications.getHeight().setScale(2, RoundingMode.HALF_UP));
        }
        if (specifications.getGrossWeight() != null) {
            specifications.setGrossWeight(specifications.getGrossWeight().setScale(2, RoundingMode.HALF_UP));
        }
        specifications.setLengthUnit(StringUtils.isEmpty(specifications.getLengthUnit()) ? "cm" : specifications.getLengthUnit());
        specifications.setGrossWeightUnit(StringUtils.isEmpty(specifications.getGrossWeightUnit()) ? "kg" : specifications.getGrossWeightUnit());
        return specifications;
    }


    /**
     * 批量保存商品信息
     *
     * @param dto
     */
    @Override
    public List<String> saveBatchListingDTO(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return new ArrayList<>();
        }
        if (dto.getShopCode().contains("VC")) {
            throw new BusinessException("VC店铺不支持批量刊登");
        }

        String siteCode = shopService.selectSiteCodeByShopCode(dto.getShopCode());

        List<ItemDTO> itemDTOList = new ArrayList<>();
        for (ListingDTO listing : listings) {
            String goodsCode = listing.getGoodsCode();
            if (ObjUtil.isNotEmpty(goodsCode)) {
                //去除KX1ALT00100U1(0)保留KX1ALT00100U1
                listing.setSortPdmGoodsCode(listing.getGoodsCode());
                listing.setGoodsCode(goodsCode.replaceAll("\\(.*\\)", ""));
            }
            if (StrUtil.isEmpty(listing.getTitle())) {
                throw new BusinessException("商品标题不能为空");
            }
            GoodsHead goodHead = getGoodHead(dto, siteCode, listing);

            List<GoodsResource> resourceList = getResourceList(dto, listing);

            if (StrUtil.isBlank(listing.getItemDescription1())) {
                throw new BusinessException("五点描述1不能为空");
            }
            GoodsDescription goodsDescription = getGoodsDescription(listing);
            goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));

            GoodsSpecification goodsSpecification = getGoodsSpecification(listing, userId);

            //获取亚马逊商品三类属性信息
            listing.setSiteCode(siteCode);
            listing.setCategoryId(goodHead.getCategoryId());
            List<ListingAmazonAttributeLine> attributeLines = getListingAmazonAttributeLinesV2(dto, listing);

            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(goodHead);
            itemDTO.setGoodsResourceList(resourceList);
            itemDTO.setGoodDescription(goodsDescription);
            itemDTO.setGoodsSpecification(goodsSpecification);
            itemDTO.setGoodsAttributeLineList(attributeLines);
            itemDTO.setUserId(userId);
            if (Objects.nonNull(listing.getId())) {
                goodHead.setTaskName(listing.getTaskName());
                itemDTO.setAutoCreateHeadId(listing.getId());
            }
            itemDTOList.add(itemDTO);

        }
        Map<String, Set<String>> violateWord = new HashMap<>();
        List<String> checkPrice = new LinkedList<>();
        for (ItemDTO itemDTO : itemDTOList) {
//            try {
//                violateWordBiz.checkViolateWord( itemDTO.getGoodDescription() , itemDTO.getGoodsAttributeLineList(),itemDTO.getGoodsHead());
//            } catch (Exception e) {
//                assembleWord(violateWord, e.getMessage());
//            }
            try {
                GoodsHead goodsHead = itemDTO.getGoodsHead();
                //校验价格
                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(null,goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), dto.getPublishType(), goodsHead.getSiteCode(),null,goodsHead.getStandardPrice());
                goodsHead.setStandardPrice(price);
                goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
            } catch (Exception e) {
                checkPrice.add(e.getMessage());
            }
        }

        if (ObjUtil.isNotEmpty(violateWord) || CollectionUtil.isNotEmpty(checkPrice)) {
            assembleWordBefore(violateWord, checkPrice);
        }
        final String[] taskId = {null};
        List<String> businessIds = itemDTOList.stream().parallel().map(itemDTO -> {
            GoodsHead goodsHead = itemDTO.getGoodsHead();
            Integer autoCreateHeadId = itemDTO.getAutoCreateHeadId();
            try {
                ListingDTO listing = new ListingDTO();
                listing.setGoodsCode(goodsHead.getPdmGoodsCode());
                listing.setSiteCode(siteCode);

                Integer goodsId = amazonPlatformListingService.saveAmazonInfoToDB(listing, itemDTO);
                if (Objects.nonNull(autoCreateHeadId)) {
                    //更新自动创建的listing的headId
                    goodsTaskInfoService.updateConfimHeadIdByAutoHeadId(autoCreateHeadId, goodsId);
                    //修改对应task的状态
                    if (StringUtils.isEmpty(taskId[0])) {
                        taskId[0] = goodsTaskInfoService.updateGoodsTaskStatusByAutoHeadId(autoCreateHeadId, "已确认");
                    }
                }
                return String.valueOf(goodsId);
            } catch (Exception e) {
                log.error("批量新建商品编码为：[" + goodsHead.getPdmGoodsCode() + "]listing草稿失败", e);
                throw new RuntimeException(e.getMessage());
            }
        }).collect(Collectors.toList());
        return businessIds;
    }


//    private List<ListingAmazonAttributeLine> getListingAmazonAttributeLines(BatchListingDTO dto, ListingDTO listing) {
//        listing.setPlatform(dto.getPlatform());
//        listing.setFulfillmentLatency(dto.getFulfillmentLatency());
//        listing.setListingAmazonAttributeLinesInfo(dto.getListingAmazonAttributeLinesInfo());
//        listing.setListingAmazonAttributeLinesDetail(dto.getListingAmazonAttributeLinesDetail());
//        listing.setListingAmazonAttributeLinesSafety(dto.getListingAmazonAttributeLinesSafety());
//        List<ListingAmazonAttributeLine> attributeLines = goodsInfoBiz.getAmazonAttributeLines(listing);
//        return attributeLines;
//    }


    private List<ListingAmazonAttributeLine> getListingAmazonAttributeLinesV2(BatchListingDTO dto, ListingDTO listing) {
        ArrayList<ListingAmazonAttributeLine> amazonAttributeLines = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(dto.getListingAttributeLine())) {
            for (String listingAmazonAttributeLineStr : dto.getListingAttributeLine()) {
                ListingAmazonAttributeLine attributeLine = JSON.parseObject(listingAmazonAttributeLineStr, ListingAmazonAttributeLine.class);
                if (ObjectUtils.isEmpty(attributeLine)) {
                    continue;
                }
                if (!Objects.equals(attributeLine.getPdmGoodsCode(), listing.getSortPdmGoodsCode())) {
                    continue;
                }
                if (ObjectUtils.isEmpty(attributeLine.getTableValue()) || ObjectUtils.isEmpty(attributeLine.getTableType())) {
                    continue;
                }
                //判断是否是平台商品编码类型
                if (CommonUtilsSmc.isSpecial(attributeLine.getTableName())) {
                    handleSpecial(amazonAttributeLines, attributeLine, listing);
                } else {
                    ListingAmazonAttributeLine line = new ListingAmazonAttributeLine();
                    line.setPlatform(PlatformTypeEnum.AM.name());
                    line.setCategoryId(listing.getCategoryId());
                    line.setPdmGoodsCode(attributeLine.getPdmGoodsCode());
                    line.setTableValue(attributeLine.getTableValue());
                    line.setTableName(attributeLine.getTableName());
                    line.setTableType(attributeLine.getTableType());
                    amazonAttributeLines.add(line);
                }
            }
        }

        if (!ObjectUtils.isEmpty(dto.getFulfillmentLatency())) {
            ListingAmazonAttributeLine line = new ListingAmazonAttributeLine();
            line.setPlatform(PlatformTypeEnum.AM.name());
            line.setCategoryId(listing.getCategoryId());
            line.setTableName(AmazonAttributeEnum.FULFILLMENT_LATENCY.getInfo());
            line.setTableValue(String.valueOf(dto.getFulfillmentLatency()));
            line.setTableType(4);
            amazonAttributeLines.add(line);
        }
        return amazonAttributeLines;
    }

    private void handleSpecial(ArrayList<ListingAmazonAttributeLine> amazonAttributeLines, ListingAmazonAttributeLine attributeLine, ListingDTO listing) {

        //类型
        ListingAmazonAttributeLine lineType = new ListingAmazonAttributeLine();
        lineType.setPlatform(PlatformTypeEnum.AM.name());
        lineType.setCategoryId(listing.getCategoryId());
        lineType.setTableName(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE.getInfo());
        lineType.setTableValue(attributeLine.getTableName());
        lineType.setPdmGoodsCode(attributeLine.getPdmGoodsCode());
        lineType.setTableType(4);
        amazonAttributeLines.add(lineType);
        if (!attributeLine.getTableName().equals(AmazonSpecialAttributeEnum.GCID.toString())) {
            //值
            ListingAmazonAttributeLine lineValue = new ListingAmazonAttributeLine();
            lineValue.setPlatform(PlatformTypeEnum.AM.name());
            lineValue.setCategoryId(listing.getCategoryId());
            lineValue.setTableName(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID.getInfo());
            lineValue.setPdmGoodsCode(attributeLine.getPdmGoodsCode());
            lineValue.setTableValue(attributeLine.getTableValue());
            lineValue.setTableType(4);
            amazonAttributeLines.add(lineValue);
        }
    }

    /**
     * 更新商品信息
     *
     * @param listingEditDTO
     * @param itemDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAmazonInfoTODB(ListingEditDTO listingEditDTO, ItemDTO itemDTO) {
        GoodsHead goodsHead = itemDTO.getGoodsHead();
        GoodsSpecification goodsSpecification = itemDTO.getGoodsSpecification();
        GoodsDescription goodsDescription = itemDTO.getGoodDescription();
        List<ListingAmazonAttributeLine> attributeLines = itemDTO.getGoodsAttributeLineList();
        List<ListingAmazonAttributeLineV2> attributeLineV2s = itemDTO.getAmazonAttributeLines();
        goodsHead.setAttributeLines(attributeLineV2s);

        //判断是否是跟卖
        boolean isCheckTitle=true;
        // VC店铺
        String shopCode = goodsHead.getShopCode();
        if (shopCode.contains("VC")){
            if (CollUtil.isNotEmpty(attributeLineV2s) && attributeLineV2s.stream().anyMatch(x->x.getPropNodePath().equals("external_product_id.type") && ObjUtil.isNotEmpty(x.getTableValue()) && ObjUtil.equals(x.getTableValue().toUpperCase(),"ASIN"))){
                isCheckTitle = false;
            }
        }else {
            if (CollUtil.isNotEmpty(attributeLines) && attributeLines.stream().anyMatch(x->x.getTableName().equals("external_product_id_type") && ObjUtil.isNotEmpty(x.getTableValue()) &&x.getTableValue().toUpperCase().equals("ASIN"))){
                isCheckTitle = false;
            }
        }

        // 商品相关信息违禁词检测
        violateWordBiz.checkViolateWord(isCheckTitle,goodsDescription, attributeLines, goodsHead);

        goodsHeadService.updateListingGoodsHead(goodsHead);

        // 修复页面只能填写一个单位，但是数据库中有三个单位的情况
        if (StrUtil.isNotBlank(goodsSpecification.getItemLengthUnit())) {
            goodsSpecification.setItemWidthUnit(goodsSpecification.getItemLengthUnit());
            goodsSpecification.setItemHeightUnit(goodsSpecification.getItemLengthUnit());
        }
        if (StrUtil.isNotBlank(goodsSpecification.getPackageLengthUnit())) {
            goodsSpecification.setPackageWidthUnit(goodsSpecification.getPackageLengthUnit());
            goodsSpecification.setPackageHeightUnit(goodsSpecification.getPackageLengthUnit());
        }
        goodsSpecificationService.updateListingGoodsSpecification(goodsSpecification);

        goodsDescriptionService.updateListingGoodsDescription(goodsDescription);
        //处理主图附件数据
        listingInfoBiz.saveMasterImgUrl(listingEditDTO);
        //处理了8张附图数据
        listingInfoBiz.saveGoodsResourceList(listingEditDTO);

        if (CollectionUtils.isNotEmpty(attributeLines)) {
            saveAmazonAttributeLine(listingEditDTO, attributeLines);
        }

        if (CollectionUtils.isNotEmpty(attributeLineV2s)) {
            saveAmazonAttributeLineV2(listingEditDTO, attributeLineV2s, goodsHead.getCreateBy());
        }
    }


    private void saveAmazonAttributeLineV2(ListingEditDTO listingEditDTO, List<ListingAmazonAttributeLineV2> attributeLineV2s, String createBy) {
        //先批量删除
        listingAmazonAttributeLineV2Service.deleteByGoodsId(listingEditDTO.getGoodsHeadId());

        for (ListingAmazonAttributeLineV2 attributeLineV2 : attributeLineV2s) {
            attributeLineV2.setCreateBy(createBy);
            listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(attributeLineV2);
        }

    }

    private void saveAmazonAttributeLine(ListingEditDTO listingEditDTO, List<ListingAmazonAttributeLine> attributeLines) {
        //去重map
        Map<String, String> repeatVerMap = new HashMap<>();
        //先批量删除
        listingAmazonAttributeLineService.deleteListingAmazonAttributeLineByGoodId(listingEditDTO.getGoodsHeadId());

        //后批量新增
        for (ListingAmazonAttributeLine attributeLine : attributeLines) {
            if (repeatVerMap.containsKey(attributeLine.getTableName())) {
                continue;
            }
            repeatVerMap.put(attributeLine.getTableName(), null);

            ListingAmazonAttributeLine line = new ListingAmazonAttributeLine();
            line.setCreateTime(DateUtils.getNowDate());
            line.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
            line.setGoodsId(listingEditDTO.getGoodsHeadId());
            line.setPdmGoodsCode(listingEditDTO.getGoodsCode());
            line.setPlatform(listingEditDTO.getPlatform());
            line.setCategoryId(listingEditDTO.getCategoryId());
            line.setTableValue(attributeLine.getTableValue());
            line.setTableName(attributeLine.getTableName());
            line.setTableType(attributeLine.getTableType());
            line.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
            listingAmazonAttributeLineService.insertListingAmazonAttributeLine(line);
        }
    }

    /**
     * 同步listing信息
     *
     * @param ids
     */
    @Override
    public void syncListingInfo(List<Integer> ids, String userId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<GoodsHead> goodsHeadListAll = goodsHeadService.selectListingGoodsHeadByIds(ids.toArray(new Integer[0]));
        Map<String, List<GoodsHead>> shopCodeMap = goodsHeadListAll.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));
        for (String shopCode : shopCodeMap.keySet()) {
            List<GoodsHead> goodsHeadList = shopCodeMap.get(shopCode);
            Shop shop = shopService.selectShopByShopCode(shopCode);
            if (shop == null) {
                log.error("亚马逊商品同步--店铺不存在,店铺为:{}", shopCode);
                continue;
            }
            pullPoolConfig.execute(() -> doHandleSyncListingInfo(userId, goodsHeadList, shop));
        }
    }

    @Override
    public void relistItem(List<GoodsHead> goodsHeads, String userId, Map<String, List<TaskConfiguration>> headIdMap) {
    }


    /**
     *
     * @param userId
     * @param goodsHeadList
     *          platformGoodsCode platformGoodsId shopCode
     * @param shop
     */
    public List<ItemDTO> doHandleSyncListingInfo(String userId, List<GoodsHead> goodsHeadList, Shop shop) {
        if (CollectionUtils.isEmpty(goodsHeadList)) {
            return new ArrayList<>();
        }
        // 筛选可以更新的商品
        goodsHeadList = goodsHeadList.stream().filter(e -> !PublishStatus.getNoUpdateStatus().contains(e.getPublishStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsHeadList)) {
            return new ArrayList<>();
        }

        List<ItemDTO> saveOrUpdateList = new ArrayList<>();
        // 获取店铺下所有asin 其中主要有库存价格信息
        List<String> platformGoodsCodes = goodsHeadList.stream().map(GoodsHead::getPlatformGoodsCode).distinct().collect(Collectors.toList());
        List<AmazonAllListingPullDTO> reportDTOS = getAmazonAllListingsDTOS(shop,platformGoodsCodes);
        boolean fromApi = false;
        if (CollUtil.isEmpty(reportDTOS)){
            reportDTOS = amazonApiListingBiz.selectApiListing(shop.getShopCode(),platformGoodsCodes);
            fromApi = true;
            if (CollUtil.isEmpty(reportDTOS)) {
                for (GoodsHead head : goodsHeadList) {
//                    boolean isDeleted = deleteNoExistListingByApi(head);
//                    if (!isDeleted) {
                        amazonListingPullLackService.insertPullLack(head.getPlatformGoodsCode(), head.getPlatformGoodsId(), head.getShopCode());
//                    }
                }
                return saveOrUpdateList;
            }
        }
        Map<String, List<GoodsHead>> platformGoodsCodesMap = goodsHeadList.stream().collect(Collectors.groupingBy(GoodsHead::getPlatformGoodsCode));

        Map<String, List<AmazonAllListingPullDTO>> sellerSkuMap = reportDTOS
                .stream()
                .filter(f -> StringUtils.isNotBlank(f.getSellerSku()))
                .filter(f -> platformGoodsCodes.contains(f.getSellerSku()))
                .collect(Collectors.groupingBy(AmazonAllListingPullDTO::getSellerSku));

        for (String platformGoodsCode : platformGoodsCodes) {
            List<GoodsHead> heads = platformGoodsCodesMap.get(platformGoodsCode);
            if (CollUtil.isEmpty(heads)) {
                continue;
            }

            GoodsHead goodsHead = heads.get(0);
            try {
                String fnSku = null;

                // 打印日志
                log.info("开始同步商品信息,平台商品编码:{}, 是否从API获取:{}", platformGoodsCode, fromApi);
                //获取report报告
                List<AmazonAllListingPullDTO> reportDTOs = Objects.isNull(sellerSkuMap.get(platformGoodsCode)) ? null : sellerSkuMap.get(platformGoodsCode);
                if (CollUtil.isEmpty(reportDTOs)){
//                    boolean isDeleted = deleteNoExistListingByApi(goodsHead);
//                    if (!isDeleted) {
                        amazonListingPullLackService.insertPullLack(goodsHead.getPlatformGoodsCode(), goodsHead.getPlatformGoodsId(), goodsHead.getShopCode());
//                    }
                    continue;
                }

                // 当前平台商品编码对应多条报告(VC店铺的历史数据是以前人工录入，VCDF和VCPO使用的是同一个平台商品编码）
                for (AmazonAllListingPullDTO reportDTO : reportDTOs) {
                    String reportKey = shop.getShopCode() + "_" + reportDTO.getSellerSku() + "_" + reportDTO.getPublishType()  + "_" + reportDTO.getAsin();
                    RLock lock = redissonClient.getLock(reportKey);
                    boolean isLock;
                    try {
                        isLock = lock.tryLock(60, TimeUnit.SECONDS);
                    } catch (Exception e) {
                        log.error("获取锁失败，sellerSku:{},asin:{},publishType:{}", reportDTO.getSellerSku(), reportDTO.getAsin(), reportDTO.getPublishType(), e);
                        continue;
                    }
                    if (!isLock) {
                        log.info("doHandleSyncListingInfo lock fail, shopCode:{}, platformGoodsCode:{}, publishType:{}", shop.getShopCode(), reportDTO.getSellerSku(), reportDTO.getPublishType());
                        continue;
                    }
                    try {
                        // 异步并行调用前台和后台接口 - 关键性能优化点
                        CompletableFuture<AjaxResult> backendFuture = amazonApiHttpRequestBiz.getBackendDetailResultAsync(
                            shop.getShopCode(), reportDTO.getPublishType(), null, reportDTO.getSellerSku());
                        CompletableFuture<AjaxResult> frontendFuture = amazonApiHttpRequestBiz.getFrontDetailResultAsync(
                            shop.getShopCode(), reportDTO.getPublishType(), null, reportDTO.getSellerSku());

                        // 等待两个API调用完成，设置30秒超时
                        CompletableFuture<Void> allFutures = CompletableFuture.allOf(backendFuture, frontendFuture);
                        allFutures.get(60, TimeUnit.SECONDS);

                        // 获取后台接口结果
                        AjaxResult backendDetailResult = backendFuture.get();
                        if (!backendDetailResult.isSuccess()) {
                            boolean isBreak = handleErrorMsg(backendDetailResult, reportDTO, shop);
                            if (isBreak) {
                                break;
                            }
                            continue;
                        }

                        JSONObject backendDetailResultJsonObj = JSONObject.parseObject(JSON.toJSONString(backendDetailResult.get(AjaxResult.DATA_TAG)));
                        // 后台接口返回条数
                        Integer backendNumber = backendDetailResultJsonObj.getInteger("numberOfResults");
                        List<GoodsHead> goodsHeads = heads.stream().filter(f -> ObjUtil.equals(f.getPublishType(), reportDTO.getPublishType())).collect(Collectors.toList());
                        // 使用后台接口没有该链接，直接删除
                        if (backendNumber == 0) {
                            log.error("通过sku:{}获取商品详情,没有数据", platformGoodsCode);
                            // 链接删除
                            if (CollUtil.isNotEmpty(goodsHeads)) {
                                deleteNoExistListing(goodsHeads.get(0));
                            }
                            continue;
                        }

                        JSONObject backendObj = backendDetailResultJsonObj.getJSONArray("items").getJSONObject(0);
                        AmazonSummariesDTO backendSummaries = getSummaries(backendObj);
                        String asin = backendSummaries.getAsin();
                        // BI使用爬虫抓取的链接，ASIN与接口实时返回的可能不一致
                        if (!reportDTO.getAsin().equalsIgnoreCase(asin))  {
                            log.error("通过sku:{}获取商品详情,ASIN不一致,前台接口返回的ASIN为:{},后台接口返回的ASIN为:{}", platformGoodsCode, reportDTO.getAsin(), asin);
                        }

                        // 获取前台接口结果
                        AjaxResult frontDetailAjaxResult = frontendFuture.get();
                        if (!frontDetailAjaxResult.isSuccess()) {
                            boolean isBreak = handleErrorMsg(frontDetailAjaxResult, reportDTO, shop);
                            if (isBreak) {
                                break;
                            }
                            continue;
                        }

                        JSONObject data = JSONObject.parseObject(JSON.toJSONString(frontDetailAjaxResult.get(AjaxResult.DATA_TAG)));
                        Integer frontNumber = data.getInteger("numberOfResults");
                        JSONObject frontDetailResult = null;
                        if (frontNumber != 0) {
                            frontDetailResult = data.getJSONArray("items").getJSONObject(0);
                        }
                        Date createdDate = null;

                        // 前台没值，后台有值，查询删除的链接有没有，如果有就不同步
                        if (frontNumber == 0) {
                            List<GoodsHead> deleteGoodsHeadList = goodsHeadService.selectDeleteListingGoodsHeadList(reportDTO.getSellerSku(), shop.getShopCode(), reportDTO.getPublishType(), reportDTO.getAsin(), 30);
                            if (CollUtil.isNotEmpty(deleteGoodsHeadList)) {
                                continue;
                            }
                            log.error("通过sku: {} 获取商品详情,前台没有数据，后台有数据", platformGoodsCode);
                        }

                        // 前台接口未获取到值，使用后台接口给的值
                        if (frontDetailResult == null) {
                            frontDetailResult = backendObj;
                        } else {
                            //获取新版属性
                            JSONObject attributes = backendObj.getJSONObject("attributes");
                            JSONObject frontAttrs = frontDetailResult.getJSONObject("attributes");
                            if (ObjUtil.isNotEmpty(attributes)) {
                                if (frontAttrs == null) {
                                    frontDetailResult.put("attributes", attributes);
                                }else {
                                    frontAttrs.putAll(attributes);
                                }
                            }

                            if (backendObj.containsKey("procurement")) {
                                frontDetailResult.put("procurement", backendObj.get("procurement"));
                            }
                            if (backendObj.containsKey("issues")) {
                                JSONArray issues = backendObj.getJSONArray("issues");
                                if (ObjUtil.isNotEmpty(issues)) {
                                    frontDetailResult.put("issues", issues);
                                }
                            }
                        }

                        fnSku = ObjUtil.isEmpty(backendSummaries) ? null : backendSummaries.getFnSku();
                        if (shop.getShopCode().contains("VC")) {
                            // 从属性中获取GTIN
                            fnSku = getGtin(frontDetailResult);
                        }
                        createdDate = parseDate(backendSummaries.getCreatedDate(), asin);

                        AmazonSummariesDTO frontSummaries = getSummaries(frontDetailResult);
                        if (hasVariation(frontDetailResult)) {
                            throw new RuntimeException("该商品是多变体,暂不处理.");
                        }

                        //获取mapping映射
                        MappingGoods mappingGoods = mappingGoodsService.selectMappingGoodsByPlatformSku(reportDTO.getSellerSku(), shop.getShopCode());

                        //转换为goodsHead
                        goodsHead = getGoodsHead(shop, goodsHeads, reportDTO, frontDetailResult, null, mappingGoods);
                        if (ObjUtil.isEmpty(goodsHead) || (goodsHead.getPublishStatus() != null && PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus()))) {
                            continue;
                        }

                        boolean isUpdatePdm = isUpdatePdm(goodsHead, asin, frontSummaries);

                        //build相关实体类
                        GoodsHead newGoodsHead = buildGoodsHead(reportDTO, goodsHead, frontDetailResult, frontSummaries, mappingGoods, createdDate);
                        if (Objects.isNull(newGoodsHead.getCreateBy())) {
                            newGoodsHead.setCreateBy(userId);
                        }
                        newGoodsHead.setPlatformGoodsId(asin);
                        if (shop.getShopCode().contains("VC")) {
                            newGoodsHead.setFnSku(fnSku);
                        } else {
                            if (StrUtil.isNotEmpty(fnSku) && (fnSku.startsWith("X00") || fnSku.startsWith("069"))) {
                                newGoodsHead.setFnSku(fnSku);
                            }
                        }
                        newGoodsHead.setUpdateBy(userId);

                        buildItemDTO(newGoodsHead, frontDetailResult, frontDetailAjaxResult, saveOrUpdateList);

                        boolean publishTypeIsUpdate = getPublishTypeIsUpdate(newGoodsHead);

                        log.info("同步商品信息,平台商品编码:{}, 是否从API获取:{}, 组装完成, 发布类型是否更新:{}", platformGoodsCode, fromApi, publishTypeIsUpdate);
                        //更新至pdm
                        if (isUpdatePdm || ObjUtil.equals(newGoodsHead.getPdmStatus(), 0) || publishTypeIsUpdate) {
                            String partNumber = getPartNumber(frontDetailResult);
                            if (StrUtil.isNotEmpty(partNumber)) {
                                Map<String, String> partMap = new HashMap<>();
                                partMap.put("new", partNumber);
                                fixedPdmPartNumberMapping(newGoodsHead, newGoodsHead.getCreateBy(), partMap);
                            }
                        }
                    } finally {
                        // 释放锁
                        if (lock.isLocked()) {
                            lock.unlock();
                        }
                    }
                }

                // 处理SMC有链接，但是报告没有链接
                for (GoodsHead head : heads) {
                    try {
                        List<Integer> publishTypes = reportDTOs.stream().map(AmazonAllListingPullDTO::getPublishType).distinct().collect(Collectors.toList());
                        if (CollUtil.isEmpty(publishTypes)) {
                            continue;
                        }
                        List<GoodsHead> notExistListing = heads.stream().filter(f -> !publishTypes.contains(f.getPublishType())).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(notExistListing)) {
                            // 通过后台接口拉取部分前台接口拉不到的数据
                            AjaxResult backendDetailResult = amazonApiHttpRequestBiz.getBackendDetailResult(shop.getShopCode(), head.getPublishType(), null, head.getPlatformGoodsCode());
                            if (!backendDetailResult.isSuccess()) {
                                log.error("通过sku:{}获取商品详情,没有数据", platformGoodsCode);
                                continue;
                            }
                            JSONObject backendDetailResultJsonObj = JSONObject.parseObject(JSON.toJSONString(backendDetailResult.get(AjaxResult.DATA_TAG)));
                            // 后台接口返回条数
                            Integer backendNumber = backendDetailResultJsonObj.getInteger("numberOfResults");
                            // 使用后台接口没有该链接，直接删除
                            if (backendNumber == 0) {
                                log.error("通过sku:{}后台已删除", platformGoodsCode);
                                // 链接删除
                                deleteNoExistListing(head);
                            }else {
                                deleteBySC(backendDetailResultJsonObj, head);
                            }
                        }
                    } catch (Exception e) {
                        // 如果报错，则不处理
                        log.error("通过sku:{}修正SMC链接失败", platformGoodsCode, e);
                    }
                }
            } catch (Exception e) {
                String errorMsg = "通过asin获取商品详情失败:" + e.getMessage();
                if (ObjUtil.isNotEmpty(goodsHead) && ObjUtil.isNotEmpty(goodsHead.getId())){
                    listingLogService.insertErrorListingLog("同步商品失败.", userId, goodsHead.getId(), errorMsg);
                }else {
                    log.error(errorMsg, e);
                }
            }
        }

        // 同步 VC 价格
        handleListingVCListPrice(saveOrUpdateList);

        //save to db
        if (CollectionUtils.isNotEmpty(saveOrUpdateList)) {
            saveAndUpdateInfo(shop.getShopCode(), saveOrUpdateList, userId, true);
        }

        return saveOrUpdateList;
    }

    private void deleteBySC(JSONObject backendDetailResultJsonObj, GoodsHead head) {
        JSONObject backendDetailResultItem = backendDetailResultJsonObj.getJSONArray("items").getJSONObject(0);
        // SC链接存在FBM转FBA，FBA转FBM，这里判断下类型
        if (head.getShopCode().contains("VC")) {
            return;
        }
        // SC链接接口，先判断最外层的fulfillmentAvailability是否存在
        if (backendDetailResultItem.containsKey("fulfillmentAvailability")) {
            JSONArray fulfillmentAvailability = backendDetailResultItem.getJSONArray("fulfillmentAvailability");
            if (fulfillmentAvailability.size() > 0) {
                JSONObject fulfillmentAvailabilityObj = fulfillmentAvailability.getJSONObject(0);
                // fulfillmentChannelCode 为 DEFAULT 时，取 quantity
                if (fulfillmentAvailabilityObj.containsKey("fulfillmentChannelCode")) {
                    String fulfillmentChannelCode = fulfillmentAvailabilityObj.getString("fulfillmentChannelCode");
                    if ("DEFAULT".equals(fulfillmentChannelCode)) {
                        if(head != null && head.getPublishType() != null && !head.getPublishType().equals(PublishType.FBM.getType())) {
                            log.error("通过sku:{}后台刊登类型已改变", head.getPlatformGoodsCode());
                            deleteNoExistListing(head);
                        }

                    } else if (Arrays.asList("AMAZON_NA", "AMAZON_EU", "AMAZON_JP").contains(fulfillmentChannelCode)) {
                        if(head != null && head.getPublishType() != null && !head.getPublishType().equals(PublishType.FBA.getType())) {
                            log.error("通过sku:{}后台刊登类型已改变", head.getPlatformGoodsCode());
                            deleteNoExistListing(head);
                        }
                    }
                }
            }
        }
    }

    private boolean getPublishTypeIsUpdate(GoodsHead newGoodsHead) {
        try {
            if (newGoodsHead == null || newGoodsHead.getId() == null) {
                return false;
            }
            GoodsHead oldGoodsHead = goodsHeadService.selectListingGoodsHeadById(newGoodsHead.getId());
            if (!ObjUtil.equals(oldGoodsHead.getPublishType(), newGoodsHead.getPublishType())) {
                return true;
            }
        }catch (Exception e) {
            log.error("获取发布类型是否更新异常,goodsHead:{}", newGoodsHead, e);
        }
        return false;
    }

    private String getPartNumber(JSONObject frontDetailResult) {
        if (ObjUtil.isEmpty(frontDetailResult)) {
            return null;
        }
        JSONObject attributes = getAttributes(frontDetailResult);
        if (ObjUtil.isEmpty(attributes)) {
            return null;
        }
        JSONArray partNumberArray = attributes.getJSONArray("part_number");
        if (ObjUtil.isEmpty(partNumberArray)) {
            return null;
        }
        JSONObject partNumberObject = partNumberArray.getJSONObject(0);
        if (ObjUtil.isEmpty(partNumberObject)) {
            return null;
        }
        return partNumberObject.getString("value");
    }

    private String getGtin(JSONObject frontDetailResult) {
        if (ObjUtil.isEmpty(frontDetailResult)) {
            return null;
        }
        JSONObject attributes = getAttributes(frontDetailResult);
        if (ObjUtil.isEmpty(attributes)) {
            return null;
        }
        JSONArray externalProductIdArray = attributes.getJSONArray("external_product_id");
        if (ObjUtil.isEmpty(externalProductIdArray)) {
            return null;
        }
        JSONObject externalProductId = externalProductIdArray.getJSONObject(0);
        if (ObjUtil.isEmpty(externalProductId)) {
            return null;
        }
        // 判断type是否为gtin
        if (ObjUtil.equals(externalProductId.getString("type"), "gtin")) {
            return externalProductId.getString("value");
        }
        return null;
    }

    private boolean handleErrorMsg(AjaxResult frontDetailAjaxResult, AmazonAllListingPullDTO reportDTO, Shop shop) {
        String errorMsg = Convert.toStr(frontDetailAjaxResult.get(AjaxResult.MSG_TAG));
        if (!JSONUtil.isJson(errorMsg)) {
            // 其他未知错误，直接跳过
            return false;
        }
        JSONObject errorMsgObj = JSON.parseObject(errorMsg);
        JSONArray errorMsgArray = errorMsgObj.getJSONArray("errors");
        if (CollUtil.isEmpty(errorMsgArray)) {
            // 其他未知错误，直接跳过
            return false;
        }
        JSONObject realErrorMsgObj = errorMsgArray.getJSONObject(0);
        if (realErrorMsgObj.containsKey("code") && "Unauthorized".equals(realErrorMsgObj.getString("code"))) {
            return true;
        }
        if (realErrorMsgObj.containsKey("code") && "QuotaExceeded".equals(realErrorMsgObj.getString("code"))) {
            // 限流导致的错误，加入重试
            amazonListingPullLackService.insertPullLack(reportDTO.getSellerSku(), reportDTO.getAsin(), shop.getShopCode());
        }
        return false;
    }

    private Date parseDate(AmazonSummariesDTO.DateDTO dateDTO, String asin) {
        if (ObjUtil.isEmpty(dateDTO)) {
            return null;
        }
        try {
            LocalDateTime utcTime = LocalDateTime.of(Convert.toInt(dateDTO.getYear()), Convert.toInt(dateDTO.getMonthValue()), Convert.toInt(dateDTO.getDayOfMonth()), Convert.toInt(dateDTO.getHour()), Convert.toInt(dateDTO.getMinute()), Convert.toInt(dateDTO.getSecond()));
            ZonedDateTime chinaTime = utcTime.atZone(ZoneId.of("UTC"))
                    .withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
            Date date = Date.from(chinaTime.toInstant());
            return date;
        } catch (Exception e) {
            log.error(String.format("解析asin:%s的创建时间失败", asin), e);
        }
        return null;
    }

    private void deleteNoExistListing(GoodsHead head) {
        if (ObjUtil.isEmpty(head)){
            return;
        }
        if (ObjUtil.isEmpty(head.getId())){
            return;
        }
        //id 不为空 并且 创建时间是30天前
        if ( head.getCreateTime().before(DateUtils.addDays(new Date(), -30)) ){
            GoodsHead update = new GoodsHead();
            update.setId(head.getId());
            update.setDelFlag(2);
            update.setRemark("SMC系统同步链接删除");
            goodsHeadService.updateListingGoodsHead(update);

            listingLogService.insertSuccessListingLog("SMC系统同步链接删除", "-1", head.getId());
        }
    }

    private void updateHeadStatus(List<GoodsHead> heads) {
        for (GoodsHead head : heads) {
            if (ObjUtil.isEmpty(head.getId())){
                continue;
            }
            GoodsHead update = new GoodsHead();
            update.setId(head.getId());
            update.setPublishStatus(PublishStatus.OFF_SALE.getType());
            goodsHeadService.updateListingGoodsHead(update);
        }
    }

    private boolean isUpdatePdm(GoodsHead goodsHead, String asin, AmazonSummariesDTO summaries) {
        if (ObjUtil.isEmpty(goodsHead) || ObjUtil.isEmpty(summaries)) {
            return false;
        }
        String partNumber = summaries.getPartnumber();
        Integer headId = goodsHead.getId();
        String platformGoodsId = goodsHead.getPlatformGoodsId();

        if (ObjUtil.isEmpty(partNumber) || ObjUtil.isEmpty(asin) || ObjUtil.isEmpty(headId) || ObjUtil.isEmpty(platformGoodsId)) {
            return false;
        }
//        String pn = listingAmazonAttributeLineService.getPn(goodsHead.getId());
        String pn = listingAmazonAttributeLineV2Service.getPn(goodsHead.getId());
        if (!ObjUtil.equals(platformGoodsId, asin) || !ObjUtil.equals(pn, partNumber)) {
            return true;
        }
        return false;
    }


    /**
     * 新建刊登商品
     *
     * @param listingDTO
     * @return
     */
    @Override
    public AjaxResult listingSave(ListingDTO listingDTO) {
        if(listingDTO.getShopCode().contains("VC")) {
            return AjaxResult.error("VC店铺请使用VC刊登功能");
        }

        List<String> bussinessIds = new ArrayList<>();
        Long userId = ShiroUtils.getUserId();
        //1、基础数据
        List<GoodsHead> goodsHeadList = goodsInfoBiz.packagingBasis(listingDTO);

        //2、商品图片视频数据
        ArrayList<GoodsResource> goodsResourceList = goodsInfoBiz.getGoodsResourceList(listingDTO);

        //3、商品描述数据
        GoodsDescription goodsDescription = getGoodsDescription(listingDTO);
        goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));

        //4、商品规格数据
        GoodsSpecification goodsSpecification = getGoodsSpecification(listingDTO, userId);

        //获取亚马逊商品三类属性信息
        List<ListingAmazonAttributeLine> attributeLines = goodsInfoBiz.getAmazonAttributeLines(listingDTO);

        ItemDTO itemDTO = new ItemDTO();
        itemDTO.setGoodsResourceList(goodsResourceList);
        itemDTO.setGoodDescription(goodsDescription);
        itemDTO.setGoodsSpecification(goodsSpecification);
        itemDTO.setGoodsAttributeLineList(attributeLines);
        itemDTO.setUserId(userId);
        List<String> goodsIds = new ArrayList<>();

        //违禁词检测
        for (GoodsHead goodsHead : goodsHeadList) {
            violateWordBiz.checkViolateWord(true,goodsDescription, attributeLines, goodsHead);
        }

        for (GoodsHead goodsHead : goodsHeadList) {
            try {
                itemDTO.setGoodsHead(goodsHead);
                Integer goodsId = amazonPlatformListingService.saveAmazonInfoToDB(listingDTO, itemDTO);
                goodsIds.add(goodsId.toString());
                bussinessIds.add(String.valueOf(goodsId));
                //处理库存黑名单
                inventoryExcludeBiz.addListingBySkuBlack(goodsHead.getPlatform(), goodsHead.getSiteCode(), goodsHead.getShopCode(), Collections.singletonList(goodsId), userId);
            } catch (Exception e) {
                log.error("Amazon新建商品编码为：[" + listingDTO.getGoodsCode() + "]listing草稿失败", e);
                throw new RuntimeException(e.getMessage());
            }
        }

        //根据条件将待办更新为处理中
        stockArrivesTodoService.updateStatusToProcessing(listingDTO.getGoodsCode(), Convert.toStr(userId), Convert.toInt(DateUtils.parseDateToStr("yyyyMMdd", new Date())));

        //处理AI文案使用表的id
        if ("1".equals(listingDTO.getUseAI())) {
            try {
                handleAiUseId(listingDTO.getUuid(), goodsHeadList);
            } catch (Exception ex) {
                log.error("Ebay新建商品编码为：[" + listingDTO.getGoodsCode() + "]记录AI文案使用表失败", ex);
            }
        }


        // 处理一键刊登
        if (null != listingDTO.getPublishStatus()
                && PublishStatus.PUBLISHING.getType().toString().equals(listingDTO.getPublishStatus())
                && CollectionUtils.isNotEmpty(goodsIds)) {
            return listingInfoBiz.publishListingByIds(String.join(",", goodsIds), userId);
        }

        return AjaxResult.success(bussinessIds);
    }

    private void handleAiUseId(String uuid, List<GoodsHead> goodsHeadList) {
        if (CollectionUtils.isEmpty(goodsHeadList)) {
            return;
        }
        for (GoodsHead goodsHead : goodsHeadList) {
            String businessId = uuid + "#" + goodsHead.getShopCode();
            wordAiUsageService.updateBusinessId(businessId, goodsHead.getId() + "");
        }
        // 处理五点描述的文案
        List<Integer> ids = goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList());
        wordAiUsageService.updateBusinessId(uuid, ids.stream().map(String::valueOf).collect(Collectors.joining(",")));
    }


    /**
     * 修改刊登商品
     *
     * @param listingEditDTO
     * @return
     */
    @Override
    public void listingEdit(ListingEditDTO listingEditDTO) {
        // 头部基础数据
        GoodsHead goodsHead = new GoodsHead();
        BeanUtil.copyProperties(listingEditDTO, goodsHead);
        goodsHead.setId(listingEditDTO.getGoodsHeadId());
        String publishStatus1 = listingEditDTO.getPublishStatus();
        GoodsHead dbHead = goodsHeadService.selectListingGoodsHeadById(listingEditDTO.getGoodsHeadId());
        if(dbHead.getDelFlag().equals(2)) {
            throw new BusinessException("该链接已删除");
        }
        String price = commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),listingEditDTO.getGoodsCode(), listingEditDTO.getStandardPrice(), dbHead.getPublishType(), dbHead.getSiteCode(),dbHead.getOnlineTime(),dbHead.getStandardPrice());
        goodsHead.setStandardPrice(price);
        goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));

        goodsHead.setCreateBy(dbHead.getCreateBy());
        goodsHead.setShopCode(dbHead.getShopCode());
        goodsHead.setPdmGoodsCode(dbHead.getPdmGoodsCode());

        //状态流转
        if (StringUtils.isNotEmpty(publishStatus1)) {
            //如果是草稿状态则允许修改平台品类
            if (Objects.equals(publishStatus1, DRAFT.getType().toString()) && !ObjectUtils.isEmpty(listingEditDTO.getPlatformGoodsCode())) {
                goodsHead.setCategoryId(Integer.valueOf(listingEditDTO.getProductCategoryCode()));
            }
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(Integer.valueOf(publishStatus1), PlatformTypeEnum.AM.name()));

        } else {
            Integer publishStatus = dbHead.getPublishStatus();
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(publishStatus, PlatformTypeEnum.AM.name()));
        }

        // 商品图片视频数据
        GoodsResource goodsResource = new GoodsResource();
        BeanUtil.copyProperties(listingEditDTO, goodsResource);

        // 商品描述数据
        GoodsDescription goodsDescription = new GoodsDescription();
        BeanUtil.copyProperties(listingEditDTO, goodsDescription);
        goodsDescription.setId(listingEditDTO.getGoodsDescriptionId());
        goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));

        // 商品规格数据
        GoodsSpecification goodsSpecification = new GoodsSpecification();
        BeanUtil.copyProperties(listingEditDTO, goodsSpecification);
        goodsSpecification.setId(listingEditDTO.getGoodsSpecificationId());
        if (StringUtils.isNotEmpty(listingEditDTO.getIsIrregularity())) {
            if (Objects.equals(goodsSpecification.getIsIrregularity(), "是")) {
                goodsSpecification.setIsIrregularity("1");
            } else if (Objects.equals(goodsSpecification.getIsIrregularity(), "否")) {
                goodsSpecification.setIsIrregularity("2");
            }
        }

        //获取亚马逊商品三类属性信息
        List<ListingAmazonAttributeLine> attributeTable = listingInfoBiz.getAttributeTable(listingEditDTO);

        ItemDTO itemDTO = new ItemDTO();
        itemDTO.setGoodsHead(goodsHead);
        itemDTO.setGoodsSpecification(goodsSpecification);
        itemDTO.setGoodDescription(goodsDescription);
        itemDTO.setGoodsAttributeLineList(attributeTable);

        amazonPlatformListingService.updateAmazonInfoTODB(listingEditDTO, itemDTO);

        //去将待办改为处理中
        TodoStatusEnum todoStatusEnum = ObjUtil.equals(goodsHead.getPublishStatus(), UPDATING.getType()) ? TodoStatusEnum.HANDLE_STATUS : TodoStatusEnum.FINISH_STATUS;
        smcTodoBiz.updateTodoStatusByListingUpdate(goodsHead.getId(), todoStatusEnum);
    }


    /**
     * 批量删除Listing
     *
     * @param goodsHeads Listing ID集合
     * @return
     */
    @Override
    public Integer deleteListing(List<GoodsHead> goodsHeads) {
        if (CollectionUtils.isEmpty(goodsHeads)) {
            return 0;
        }
        Map<Integer, GoodsHead> headMap = goodsHeads.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
        //更新状态为删除中
        for (Integer goodsHeadId : headMap.keySet()) {
            GoodsHead goodsHead = headMap.get(goodsHeadId);
            Boolean aBoolean = PublishStatus.checkListingByStatusAndPlatform(goodsHead.getPublishStatus(), goodsHead.getPlatform());
            if (Objects.isNull(aBoolean)) {
                continue;
            }

            if (!aBoolean) {
                if (goodsHead.getPublishStatus().equals(8) && goodsHead.getPlatform().equals(PlatformTypeEnum.AM.name())) {
                    //删除亚马逊线上数据
                    GoodsHead updateHead = new GoodsHead();
                    updateHead.setId(goodsHeadId);
                    updateHead.setPublishStatus(PublishStatus.DELETEING.getType());
                    goodsHeadService.updateListingGoodsHead(updateHead);
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHeadId), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_DELETE), GoodsTaskSubStatusEnum.NORAML, "");
                    continue;
                }
                //删除本地数据
                listingService.deleteListingByIds(String.valueOf(goodsHeadId));
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHeadId), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_DELETE), GoodsTaskSubStatusEnum.NORAML, "");
                continue;
            }

            //删除亚马逊线上数据
            GoodsHead updateHead = new GoodsHead();
            updateHead.setId(goodsHeadId);
            updateHead.setPublishStatus(PublishStatus.DELETEING.getType());
            goodsHeadService.updateListingGoodsHead(updateHead);
        }

        return headMap.size();
    }


    /**
     * 通过headId获取listingEditDTO
     *
     * @param listingIds
     * @return
     */
    @Override
    public ListingsEditVO getListingsEditDTOSByListingIds(List<Integer> listingIds) {
        if (CollectionUtils.isEmpty(listingIds)) {
            throw new BusinessException("该批次未生成有效Listing，不能查看详情进行确认");
        }

// 初始化 map 并处理数据
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new)).stream()
                .map(e -> {
                    if (StringUtils.isNotEmpty(e.getTitle())) {
                        e.setTitle(e.getTitle().replaceAll("\"", "“").replaceAll("'", "’"));
                    }
                    return e;
                })
                .collect(Collectors.toMap(GoodsHead::getId, e -> e));
        boolean vcFlag = headMap.get(listingIds.get(0)).getShopCode().contains("VC");

        Map<Integer, List<GoodsDescription>> descMap = goodsDescriptionService.selectDescriptionListByGoodsIdList(listingIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsDescription::getGoodsId));

        Map<Integer, List<GoodsResource>> resourceMap = goodsResourceService.selectListingGoodsResourceByHeadIds(listingIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsResource::getGoodsId));

        Map<Integer, List<GoodsSpecification>> specificationMap = goodsSpecificationService.selectSpecificationListByGoodsIds(listingIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsSpecification::getGoodsId));

        Map<Integer, List<ListingAmazonAttributeLine>> attributeLinesAllMap = listingAmazonAttributeLineService.selectByGoodsIds(listingIds)
                .stream()
                .collect(Collectors.groupingBy(ListingAmazonAttributeLine::getGoodsId));

        Map<Integer, List<ListingAmazonAttributeLineV2>> attributeLinesAllMapV2 = listingAmazonAttributeLineV2Service.selecAttrByGoodsIds(listingIds.stream().map(Long::valueOf).collect(Collectors.toList()))
                    .stream()
                    .collect(Collectors.groupingBy(e -> e.getHeadId().intValue()));


        Map<String, String> shopCodeNameMap = new HashMap<>();
        List<ListingEditDTO> arrayList = new ArrayList<>();

// 遍历 listingIds 构建 DTO 对象
        for (Integer id : listingIds) {
            ListingEditDTO listingEditDTO = new ListingEditDTO();
            GoodsHead goodsHead = headMap.get(id);

            if (goodsHead == null) {
                continue; // 如果没有找到对应的 GoodsHead，跳过该循环
            }

            Integer goodsId = goodsHead.getId();

            // 商品基础数据
            setBasicToEditDTO(listingEditDTO, goodsHead);

            // 获取商店名称
            listingEditDTO.setShopName(shopCodeNameMap.computeIfAbsent(goodsHead.getShopCode(),
                    shopCode -> shopService.selectShopNameByShopCode(shopCode)));

            // 商品图片视频数据
            List<GoodsResource> goodsResourceList = resourceMap.get(goodsId);
            listingEditDTO.setGoodsImageList(Optional.ofNullable(goodsResourceList).map(this::getGoodsImages).orElse(Collections.emptyList()));

            // 商品描述数据
            GoodsDescription goodsDescription = Optional.ofNullable(descMap.get(goodsId))
                    .flatMap(descList -> descList.stream().findFirst())
                    .orElse(null);

            if (goodsDescription != null) {
                setDescriptionToEditDTO(listingEditDTO, goodsDescription);
            }

            // 商品规格数据
            GoodsSpecification goodsSpecification = Optional.ofNullable(specificationMap.get(goodsId))
                    .flatMap(specList -> specList.stream().findFirst())
                    .orElse(null);

            if (goodsSpecification != null) {
                setSpecificationToEditDTO(goodsSpecification, listingEditDTO);
            }

            // 商品其他数据
            listingInfoBiz.amazonPageData(listingEditDTO, goodsHead, goodsId);
            listingEditDTO.setProductCategoryCode(String.valueOf(goodsHead.getCategoryId()));


            List<ListingAmazonAttributeLineV2> linesAll = Optional.ofNullable(attributeLinesAllMapV2.get(goodsId))
                    .orElse(Collections.emptyList());
            listingEditDTO.setAmazonAttributeLines(linesAll);
            if(CollectionUtils.isNotEmpty(linesAll)) {
                String listPrice = linesAll.stream().filter(x -> x.getPropNodePath().equals("list_price.value")).map(ListingAmazonAttributeLineV2::getTableValue).findFirst().orElse(null);
                listingEditDTO.setListPrice(listPrice);
            }

            arrayList.add(listingEditDTO);
        }

        if (CollectionUtils.isEmpty(arrayList)) {
            throw new BusinessException("该批次未生成有效Listing，不能查看详情进行确认");
        }

        arrayList = arrayList.stream()
                .sorted(Comparator.comparing(ListingEditDTO::getGoodsCode))
                .collect(Collectors.toList());

        ListingsEditVO listingsEditVO = new ListingsEditVO();
        listingsEditVO.setListingEditDTOS(arrayList);

        ListingEditDTO firstItem = arrayList.get(0);
        listingsEditVO.setSiteCode(Optional.ofNullable(firstItem).map(ListingEditDTO::getSiteCode).orElse(null));
        listingsEditVO.setBrandCode(Optional.ofNullable(firstItem).map(ListingEditDTO::getBrandCode).orElse(null));
        listingsEditVO.setCondition(Optional.ofNullable(firstItem).map(ListingEditDTO::getCondition).orElse(null));
        listingsEditVO.setCategoryId(Optional.ofNullable(firstItem).map(ListingEditDTO::getCategoryId).orElse(null));
        listingsEditVO.setPublishType(Optional.ofNullable(firstItem).map(ListingEditDTO::getPublishType).orElse(null));
        listingsEditVO.setPublishTypeName(PublishType.getTypeName(listingsEditVO.getPublishType()));
        listingsEditVO.setShopCode(Optional.ofNullable(firstItem).map(ListingEditDTO::getShopCode).orElse(null));
        listingsEditVO.setFulfillmentLatency(Optional.ofNullable(firstItem).map(ListingEditDTO::getFulfillmentLatency).orElse(null));

        return listingsEditVO;
    }


    /**
     * 根据店铺进行同步listing
     *
     * @param dto
     */
    @Override
    public void syncShopListingInfo(SyncListingDTO dto) {
        List<Shop> shopList = dto.getShopList();
        String userId = dto.getUserId();
        if (CollectionUtils.isEmpty(shopList)) {
            return;
        }

        pullOnlineListByShopInlet(shopList, userId);

    }

    /**
     * 根据店铺进行同步listing 统一入口
     *
     * @param shopList
     * @param userId
     */
    public void pullOnlineListByShopInlet(List<Shop> shopList, String userId) {
        if (CollectionUtils.isEmpty(shopList)) {
            return;
        }
        //暂时只同步美国站点的
//        shopList = shopList.stream().filter(f -> Objects.equals(f.getSiteCode(), "US")).collect(Collectors.toList());
        shopList = shopList.stream().filter(shop -> PlatformSiteEnum.getNameList().contains(shop.getSiteCode())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(shopList)) {
            return;
        }

        for (Shop shop : shopList) {
            String version = shop.getShopCode() + "_" + ((int) Math.floor(Math.random() * (9999 - 1000 + 1) + 1000)) + "_" + System.currentTimeMillis();
            GoodsTask goodsTask = new GoodsTask();
            if (!ObjUtil.equals(userId, "-1")) {
                goodsTask = goodsTaskService.insertStoreSyncTask(version, PlatformTypeEnum.AM.name(), 0, userId);
            }

            String key = RedisKeyEnum.AM_SYNC_SHOP_Listing.getKey() + shop.getShopCode();
            if (redisService.exists(key)) {
                Date syncDate = redisService.getCacheObject(key);
                String remark = "该店铺正在同步中，请不要重复同步.";
                if (ObjUtil.isNotEmpty(syncDate) && DateUtil.offsetHour(syncDate, 3).getTime() > System.currentTimeMillis()) {
                    remark = "该店铺在3小时之内已同步过，请不要重复同步.";
                }
                goodsTask.setTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo());
                goodsTask.setRemark(remark);
                goodsTaskService.updateGoodsTask(goodsTask);
                log.error("该店铺正在同步中，请不要重复同步，店铺信息：{},key:{}", shop.getShopCode(), key);
                continue;
            }
            redisService.setCacheObject(key, new Date(), 2L, TimeUnit.DAYS);

            GoodsTask finalGoodsTask = goodsTask;
            pullPool.execute(() -> {
                try {
                    pullOnlineListByShop(userId, shop, version, finalGoodsTask);
                } catch (Exception e) {
                    log.error("同步listing失败，店铺信息：{}", shop, e);
                }
                Date syncDate = redisService.getCacheObject(key);
                // 超过3小时删除
                if (ObjUtil.isNotEmpty(syncDate) && DateUtil.offsetHour(syncDate, 3).getTime() < System.currentTimeMillis()) {
                    redisService.deleteObject(key);
                }
            });
        }
    }
    private boolean deleteNoExistListingByApi(GoodsHead goodsHead){
        // 通过后台接口获取数据，如果获取不到说明可以删除
        AjaxResult backendDetailResult = amazonApiHttpRequestBiz.getBackendDetailResult(goodsHead.getShopCode(), goodsHead.getPublishType(), null, goodsHead.getPlatformGoodsCode());
        if (!backendDetailResult.isSuccess()) {
            return false;
        }
        JSONObject backendDetailResultJsonObj = JSONObject.parseObject(JSON.toJSONString(backendDetailResult.get(AjaxResult.DATA_TAG)));
        // 后台接口返回条数
        Integer backendNumber = backendDetailResultJsonObj.getInteger("numberOfResults");
        if (backendNumber == 0) {
            deleteNoExistListing(goodsHead);
            return true;
        }
        return false;
    }

    /**
     * 根据店铺进行同步listing 具体实现
     *
     * @param userId
     * @param shop
     */
    public void pullOnlineListByShop(String userId, Shop shop, String version, GoodsTask goodsTask) {
        // 检查是否启用分页同步
        String paginationEnabled = sysConfigService.selectConfigByKey("amazon_listing_pagination_enabled");
        boolean usePagination = "true".equalsIgnoreCase(paginationEnabled);
        if (usePagination) {
            log.info("使用分页同步模式，店铺：{}", shop.getShopCode());
            pullOnlineListByShopWithPagination(userId, shop, version, goodsTask);
            return;
        }

        log.info("使用原有同步模式，店铺：{}", shop.getShopCode());

        //1、获取每个店铺的所有sellerSKu   和 基础数据准备 ,VC链接默认只获取最近30天的数据
        List<AmazonAllListingPullDTO> allListings = getAmazonAllListingsDTOS(shop,null);
        if (CollectionUtils.isEmpty(allListings)) {
            return;
        }
        //过滤掉15天都没有更新的数据
        String recentDayConfig = sysConfigService.selectConfigByKey("amazon_recent_day_config");
        int recentDay;
        if (StrUtil.isNotBlank(recentDayConfig)) {
            recentDay = Integer.parseInt(recentDayConfig);
        } else {
            recentDay = 15;
        }
        List<String> sellerSkuAll = allListings.stream().map(e-> getSkuKey(e.getSellerSku(), e.getPublishType())).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellerSkuAll)) {
            return;
        }
        Map<String, List<AmazonAllListingPullDTO>> sellerSkuMap = allListings.stream().filter(f -> StringUtils.isNotBlank(f.getSellerSku()))
                .filter(f -> ObjUtil.isNotEmpty(f.getPublishType()))
                .collect(Collectors.groupingBy(e-> getSkuKey(e.getSellerSku(), e.getPublishType())));


        //2、listing对应的本地数据
        Map<String, List<GoodsHead>> onlineMap = selectExistsListing(sellerSkuAll, shop);

        if (CollUtil.isEmpty(sellerSkuAll)){
            goodsTask.setTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo());
            goodsTask.setTaskNum(0);
            goodsTaskService.updateGoodsTask(goodsTask);
            return;
        }

        sellerSkuAll = sellerSkuAll.stream().distinct().collect(Collectors.toList());

        goodsTask.setTaskStatus(GoodsTaskStatusEnum.IN_PROGRESS.getInfo());
        goodsTask.setTaskNum(sellerSkuAll.size());
        goodsTaskService.updateGoodsTask(goodsTask);

        log.info("同步listing开始，店铺信息：{},版本号:{},商品数量:{}", shop.getShopCode(), version, sellerSkuAll.size());

        // 调用提取的SKU处理方法
        processSkuList(userId, shop, version, goodsTask, sellerSkuAll, sellerSkuMap, onlineMap);


        if (ObjUtil.isNotEmpty(goodsTask.getId())) {
            goodsTaskService.updateGoodsTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo(), goodsTask.getId());
        }

        allListings = null;
    }

    /**
     * 处理SKU列表的核心逻辑 (提取的公共方法)
     */
    private int processSkuList(String userId, Shop shop, String version, GoodsTask goodsTask,
                              List<String> sellerSkuAll, Map<String, List<AmazonAllListingPullDTO>> sellerSkuMap,
                              Map<String, List<GoodsHead>> onlineMap) {

        int processedCount = 0;
        boolean next = true;

        for (List<String> sellerSkus : Lists.partition(sellerSkuAll, 200)) {
            List<ItemDTO> saveOrUpdateList = new ArrayList<>();
            if (!next) {
                break;
            }

            for (String sellerSku : sellerSkus) {
                //3、通过sellerSku 去拿详情 并且组装数据
                List<GoodsHead> heads = onlineMap.get(sellerSku);
                List<AmazonAllListingPullDTO> amazonAllListingsReportDTOS = sellerSkuMap.get(sellerSku);
                if (CollectionUtils.isEmpty(amazonAllListingsReportDTOS)){
                    if (CollUtil.isNotEmpty(heads)){
                        // 日志
                        log.info("未获取到报告的listing，sku：{}", sellerSku);
                    }
                    continue;
                }
                // 正在同步
                log.info("正在同步，sellerSku:{}", sellerSku);
                if (amazonAllListingsReportDTOS.size() > 1) {
                    log.error("通过sellerSku:{}获取BI的报告数据出现一个sku多条报告,请检查数据", sellerSku);
                }
                AmazonAllListingPullDTO reportDTO = amazonAllListingsReportDTOS.get(0);
                String asin = reportDTO.getAsin();
                // SMC不存在该链接，可能标记为删除了，看24小时内是不是有删除的记录，有的话不同步
                if(CollUtil.isEmpty(heads)) {
                    List<GoodsHead> deleteGoodsHeadList = goodsHeadService.selectDeleteListingGoodsHeadList(reportDTO.getSellerSku(), shop.getShopCode(), reportDTO.getPublishType(), reportDTO.getAsin(), 1);
                    if (CollUtil.isNotEmpty(deleteGoodsHeadList)) {
                        log.error("SMC标记为删除了，不同步，sellerSku:{},asin:{},publishType:{}", reportDTO.getSellerSku(), reportDTO.getAsin(), reportDTO.getPublishType());
                        continue;
                    }
                } else {
                    GoodsHead goodsHead = heads.get(0);
                    // pullDate如果是一天内，不需要同步
                    if(ObjUtil.isNotEmpty(goodsHead.getPullDate()) && DateUtil.offsetDay(goodsHead.getPullDate(), -1).getTime() > System.currentTimeMillis()) {
                        // 日志记录
                        log.error("SMC存在该链接，且pullDate在一天内，不需要同步，sellerSku:{},asin:{},publishType:{}", reportDTO.getSellerSku(), reportDTO.getAsin(), reportDTO.getPublishType());
                        continue;
                    }
                }
                String redisKey = shop.getShopCode() + "_" + reportDTO.getSellerSku() + "_" + reportDTO.getPublishType() + "_" + asin;
                RLock lock = redissonClient.getLock(redisKey);
                boolean isLock;
                try {
                    isLock = lock.tryLock(10, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.error("获取锁失败，sellerSku:{},asin:{},publishType:{}", reportDTO.getSellerSku(), reportDTO.getAsin(), reportDTO.getPublishType(), e);
                    continue;
                }
                if (!isLock) {
                    log.info("pullOnlineListByShop lock fail, shopCode:{}, platformGoodsCode:{}, publishType:{}", shop.getShopCode(), reportDTO.getSellerSku(), reportDTO.getPublishType());
                    continue;
                }
                try {
                    // 通过后台接口拉取部分前台接口拉不到的数据
                    AjaxResult backendDetailResult = amazonApiHttpRequestBiz.getBackendDetailResult(shop.getShopCode(), reportDTO.getPublishType(), null, reportDTO.getSellerSku());
                    if (!backendDetailResult.isSuccess()) {
                        boolean isBreak = handleErrorMsg(backendDetailResult, reportDTO, shop);
                        if (isBreak) {
                            goodsTask.setTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo());
                            goodsTask.setRemark("该店铺未授权,请授权后操作.");
                            goodsTaskService.updateGoodsTask(goodsTask);
                            next = false;
                            break;
                        }
                        continue;
                    }
                    // 使用后台接口的属性
                    JSONObject backendDetailResultJsonObj = JSONObject.parseObject(JSON.toJSONString(backendDetailResult.get(AjaxResult.DATA_TAG)));
                    // 后台接口返回条数
                    Integer backendNumber = backendDetailResultJsonObj.getInteger("numberOfResults");
                    // 使用后台接口的属性
                    if (backendNumber == 0) {
                        log.error("通过sku:{}获取商品详情,没有数据", sellerSku);
                        //本地没有数据，不需要处理
                        if (CollUtil.isNotEmpty(heads)) {
                            GoodsHead goodsHead = heads.get(0);
                            deleteNoExistListing(goodsHead);
                        }
                        continue;
                    }
                    JSONObject backendObj = backendDetailResultJsonObj.getJSONArray("items").getJSONObject(0);
                    AmazonSummariesDTO backendSummaries = getSummaries(backendObj);
                    asin = backendSummaries.getAsin();
                    String fnSku;
                    //是否全量同步 false 同步 true 不同步
                    Boolean breakFlag = isIncrement(backendSummaries, heads);
                    if (breakFlag){
                        goodsTaskService.addSuccessNum(goodsTask.getId());
                        continue;
                    }

                    AjaxResult frontDetailAjaxResult = amazonApiHttpRequestBiz.getFrontDetailResult(shop.getShopCode(), reportDTO.getPublishType(), null, reportDTO.getSellerSku());
                    if (!frontDetailAjaxResult.isSuccess()) {
                        boolean isBreak = handleErrorMsg(frontDetailAjaxResult, reportDTO, shop);
                        if (isBreak) {
                            goodsTask.setTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo());
                            goodsTask.setRemark("该店铺未授权,请授权后操作.");
                            goodsTaskService.updateGoodsTask(goodsTask);
                            next = false;
                            break;
                        }
                        continue;
                    }

                    JSONObject data = JSONObject.parseObject(JSON.toJSONString(frontDetailAjaxResult.get(AjaxResult.DATA_TAG)));
                    Integer frontNumber = data.getInteger("numberOfResults");
                    JSONObject frontDetailResult = null;
                    if (frontNumber != 0) {
                        frontDetailResult = data.getJSONArray("items").getJSONObject(0);
                    }

                    // 前台没值，后台有值，查询删除的链接有没有，如果有就不同步
                    if (frontNumber == 0) {
                        List<GoodsHead> deleteGoodsHeadList = goodsHeadService.selectDeleteListingGoodsHeadList(reportDTO.getSellerSku(), shop.getShopCode(), reportDTO.getPublishType(), reportDTO.getAsin(), 30);
                        if (CollUtil.isNotEmpty(deleteGoodsHeadList)) {
                            continue;
                        }
                        log.error("通过sku: {} 获取商品详情,前台没有数据，后台有数据", sellerSku);
                    }

                    // 前台接口未获取到值，使用后台接口给的值
                    if (frontDetailResult == null) {
                        frontDetailResult = backendObj;
                    } else {
                        //获取新版属性
                        JSONObject attributes = backendObj.getJSONObject("attributes");
                        JSONObject frontAttrs = frontDetailResult.getJSONObject("attributes");
                        if (ObjUtil.isNotEmpty(attributes)) {
                            if (frontAttrs == null) {
                                frontDetailResult.put("attributes", attributes);
                            }else {
                                frontAttrs.putAll(attributes);
                            }
                        }

                        if (backendObj.containsKey("procurement")) {
                            frontDetailResult.put("procurement", backendObj.get("procurement"));
                        }
                    }

                    fnSku = ObjUtil.isEmpty(backendSummaries) ? null : backendSummaries.getFnSku();
                    if (shop.getShopCode().contains("VC")) {
                        // 从属性中获取GTIN
                        fnSku = getGtin(frontDetailResult);
                    }

                    Date createdDate = parseDate(backendSummaries.getCreatedDate(), asin);

                    AmazonSummariesDTO frontSummaries = getSummaries(frontDetailResult);
                    if (!hasVariation(frontDetailResult)) {
                        MappingGoods mappingGoods = mappingGoodsService.selectMappingGoodsByPlatformSku(reportDTO.getSellerSku(), shop.getShopCode());

                        //build相关实体类
                        GoodsHead goodsHead = getGoodsHead(shop, ObjectUtils.isEmpty(onlineMap) ? null : heads, reportDTO, frontDetailResult, version, mappingGoods);
                        if (ObjUtil.isEmpty(goodsHead)) {
                            continue;
                        }

                        boolean updatePdm = isUpdatePdm(goodsHead, asin, frontSummaries);

                        GoodsHead newGoodsHead = buildGoodsHead(reportDTO, goodsHead, frontDetailResult, frontSummaries, mappingGoods, createdDate);
                        if (Objects.isNull(newGoodsHead.getCreateBy())) {
                            newGoodsHead.setCreateBy(userId);
                        }
                        newGoodsHead.setUpdateBy(newGoodsHead.getCreateBy());
                        if (shop.getShopCode().contains("VC")) {
                            newGoodsHead.setFnSku(fnSku);
                        } else {
                            if (StrUtil.isNotEmpty(fnSku) && (fnSku.startsWith("X00") || fnSku.startsWith("069"))) {
                                newGoodsHead.setFnSku(fnSku);
                            }
                        }
                        buildItemDTO(newGoodsHead, frontDetailResult, frontDetailAjaxResult, saveOrUpdateList);
                        boolean publishTypeIsUpdate = getPublishTypeIsUpdate(newGoodsHead);
                        log.info("同步商品信息,平台商品编码:{}, 发布类型是否更新:{}", reportDTO.getSellerSku(), publishTypeIsUpdate);
                        goodsTaskService.addSuccessNum(goodsTask.getId());
                        processedCount++;

                        //更新至pdm
                        if (updatePdm || ObjUtil.equals(newGoodsHead.getPdmStatus(),0) || publishTypeIsUpdate) {
                            String partNumber = getPartNumber(frontDetailResult);
                            if (StrUtil.isNotEmpty(partNumber)) {
                                Map<String, String> partMap = new HashMap<>();
                                partMap.put("new", partNumber);
                                fixedPdmPartNumberMapping(newGoodsHead, newGoodsHead.getCreateBy(), partMap);
                            }
                        }
                    }else {
                        //todo 多变体暂不处理
                        saveLog(shop.getShopCode(), asin,"该商品是多变体,暂不处理",version);
                    }
                } catch (Exception e) {
                    log.error(String.format("亚马逊定时处理商品同步异常,shopCode:%s,asin:%s", shop.getShopCode(), asin), e);
                    savePullLogAndTodoLog(shop.getShopCode(), asin, version, ErrorDefine.UNKNOWN_ERROR, e.getMessage());
                }  finally {
                    if (lock.isLocked()) {
                        lock.unlock();
                    }
                }
            }

            // VC的价格变动的处理
            handleListingVCListPrice(saveOrUpdateList);

            //4、save to db
            if (!CollectionUtils.isEmpty(saveOrUpdateList)) {
                saveAndUpdateInfo(shop.getShopCode(), saveOrUpdateList, userId, true);

                //回写适配和pdm mapping
                for (ItemDTO itemDTO : saveOrUpdateList) {
                    GoodsHead goodsHead = itemDTO.getGoodsHead();
                    GoodsHead head = goodsHeadService.selectListingGoodsHeadById(goodsHead.getId());
                    if (ObjUtil.isEmpty(head) || StrUtil.isBlank(head.getPlatformGoodsId())) {
                        continue;
                    }
//                    markSyncSuccess(shop.getShopCode(), goodsHead.getPlatformGoodsId());

//                    if (ObjUtil.equals(head.getAdaptationStatus(), AdaptationStatusEnum.WAIT.getStatus()) || ObjUtil.equals(String.valueOf(head.getPdmStatus()), "0")) {
                    if ( ObjUtil.equals(String.valueOf(head.getPdmStatus()), "0")) {
                        log.info("同步发现该listing未适配或没回写pdm mapping,店铺:{},商品id:{}", shop.getShopCode(), head.getId());
                        List<GoodsHead> goodsHeadWrite = new ArrayList<>();
                        goodsHeadWrite.add(head);
                        amazonListingResultHandler.listingReportConsumerHandle(JSON.toJSONString(goodsHeadWrite), "publish");
                    }
                }
            }
        }

        return processedCount;
    }

    // VC价格变动的处理
    private void handleListingVCListPrice(List<ItemDTO> saveOrUpdateList) {
        if (CollectionUtils.isEmpty(saveOrUpdateList)) {
            return;
        }
        try {
            // 1、重置不符合 VC 价格， 超过三天重置的
            vcPriceChangeTodoService.updateResetVcPriceChangeTodo();
            // 2、查询已存在的 VC 价格
            List<Integer> headIdList = saveOrUpdateList.stream().map(ItemDTO::getGoodsHead).map(GoodsHead::getId).collect(Collectors.toList());
            List<VcPriceChangeTodo> vcPriceChangeTodoList = vcPriceChangeTodoService.selectVcPriceChangeTodoListByHeadIdList(headIdList);
            Map<Integer, VcPriceChangeTodo> vcPriceChangeTodoMap = CollectionUtils.isEmpty(vcPriceChangeTodoList)
                    ? new HashMap<>()
                    : vcPriceChangeTodoList.stream().collect(Collectors.toMap(VcPriceChangeTodo::getHeadId,todo -> todo,(existing, replacement) -> existing));
            // 3、过滤核心链接
            List<String> skuList = saveOrUpdateList.stream().map(ItemDTO::getGoodsHead).map(GoodsHead::getPdmGoodsCode).distinct().collect(Collectors.toList());
            List<AdsAmazonListingDataVIO> adsAmazonListingDataList = (CollectionUtils.isEmpty(skuList)) ? Collections.emptyList() : adsService.selectAdsItemFitCompareListBySkuList(skuList);
            Map<String, AdsAmazonListingDataVIO> adsAmazonMap = Optional.ofNullable(adsAmazonListingDataList).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(
                            AdsAmazonListingDataVIO::getGoodsCode,
                            amazonListingDataVIO -> amazonListingDataVIO,
                            (existing, replacement) -> existing
                    ));
            // 4、获取PDM详情
            List<GoodsDetailDTO> goodsDetailList = pdmHttpRequestBiz.getGoodsDetail(skuList);
            // 5、组装数据
            List<VcPriceChangeTodo> insertVcPriceChangeTodoList = new ArrayList<>();
            List<VcPriceChangeTodo> updateVcPriceChangeTodoList = new ArrayList<>();
            for (ItemDTO itemDTO : saveOrUpdateList) {
                // 过滤非核心链接
                AdsAmazonListingDataVIO listingDataVIO = adsAmazonMap.get(itemDTO.getGoodsHead().getPdmGoodsCode());
                if (listingDataVIO == null) {
                    // 不存在，则直接过滤
                    continue;
                }
                if (!Objects.equals(itemDTO.getGoodsHead().getPlatformGoodsId(), listingDataVIO.getAsin())
                        || !Objects.equals(itemDTO.getGoodsHead().getShopCode(), listingDataVIO.getShopCode())) {
                    // 存在，但是 Asin 和 shopCode 不一致 也过滤
                    continue;
                }
                // 不属于VC店铺的
                if (!itemDTO.getGoodsHead().getShopCode().contains("VC")) {
                    continue;
                }
                // 获取属性价格
                List<ListingAmazonAttributeLineV2> listPriceList = Optional.ofNullable(itemDTO).map(ItemDTO::getAmazonAttributeLines).orElse(Collections.emptyList())
                        .stream().filter(f -> Objects.equals(f.getPropNodePath(), "list_price.value")).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(listPriceList)) {
                    continue;
                }
                String newPrice = listPriceList.get(0).getTableValue();
                if (StringUtils.isEmpty(newPrice)) {
                    continue;
                }
                // 开始构造数据的
                VcPriceChangeTodo vcPriceChangeTodo = vcPriceChangeTodoMap.get(itemDTO.getGoodsHead().getId());
                if (vcPriceChangeTodo == null) {
                    VcPriceChangeTodo newVcPriceChangeTodo = new VcPriceChangeTodo();
                    buildVcPrice(newVcPriceChangeTodo,itemDTO,goodsDetailList);
                    newVcPriceChangeTodo.setCurrentPrice(BigDecimal.valueOf(Double.parseDouble(newPrice)));
                    newVcPriceChangeTodo.setHistoryPrice(BigDecimal.ZERO);
                    newVcPriceChangeTodo.setCreateTime(DateUtils.getNowDate());
                    insertVcPriceChangeTodoList.add(newVcPriceChangeTodo);
                } else {
                    // 如果两者相等
                    if (BigDecimal.valueOf(Double.parseDouble(newPrice)).compareTo(vcPriceChangeTodo.getCurrentPrice()) == 0) {
                        continue;
                    }
                    buildVcPrice(vcPriceChangeTodo,itemDTO, goodsDetailList);
                    vcPriceChangeTodo.setHistoryPrice(vcPriceChangeTodo.getCurrentPrice());
                    vcPriceChangeTodo.setCurrentPrice(BigDecimal.valueOf(Double.parseDouble(newPrice)));
                    updateVcPriceChangeTodoList.add(vcPriceChangeTodo);
                }
            }
            // 6、对有变化的价格 进行更新状态或者插入
            if (CollectionUtils.isNotEmpty(insertVcPriceChangeTodoList)) {
                vcPriceChangeTodoService.insertBatchVcPriceChangeTodo(insertVcPriceChangeTodoList);
            }
            if (CollectionUtils.isNotEmpty(updateVcPriceChangeTodoList)) {
                vcPriceChangeTodoService.updateBatchVcPriceChangeTodo(updateVcPriceChangeTodoList);
            }
        } catch (Exception e) {
            log.error("VC 价格变动处理失败：{}", e.getMessage(),e);
        }
    }

    private void buildVcPrice(VcPriceChangeTodo vcPriceChangeTodo, ItemDTO itemDTO, List<GoodsDetailDTO> goodsDetailList) {
        vcPriceChangeTodo.setHeadId(itemDTO.getGoodsHead().getId());
        vcPriceChangeTodo.setShopCode(itemDTO.getGoodsHead().getShopCode());
        vcPriceChangeTodo.setPdmGoodsCode(itemDTO.getGoodsHead().getPdmGoodsCode());
        vcPriceChangeTodo.setPlatformSku(itemDTO.getGoodsHead().getPlatformGoodsCode());
        vcPriceChangeTodo.setAsin(itemDTO.getGoodsHead().getPlatformGoodsId());
        vcPriceChangeTodo.setPublishType(itemDTO.getGoodsHead().getPublishType());
        vcPriceChangeTodo.setCategoryId(itemDTO.getGoodsHead().getCategoryId());
        vcPriceChangeTodo.setUpdateTime(DateUtils.getNowDate());
        GoodsDetailDTO goodsDetailDTO = goodsDetailList.stream().filter(goods -> Objects.equals(goods.getGoodsCode(), itemDTO.getGoodsHead().getPdmGoodsCode())).findFirst().orElse(null);
        if (ObjUtil.isNotEmpty(goodsDetailDTO)) {
            vcPriceChangeTodo.setProductCategoryCode(goodsDetailDTO.getProductCategoryCode());
            vcPriceChangeTodo.setProductCategoryName(goodsDetailDTO.getProductCategoryName());
        }
        vcPriceChangeTodo.setStatus(0);
        String operator = StringUtils.isNotEmpty(itemDTO.getGoodsHead().getCreateBy()) ? itemDTO.getGoodsHead().getCreateBy(): vcPriceChangeTodo.getUpdateBy();
        vcPriceChangeTodo.setOperator(operator);
    }

    private String getSkuKey(String sellerSku,Integer publishType) {
        return sellerSku + "@@" +publishType;
    }

    /**
     *
     * @param summariesBySku
     * @return false 全量同步
     *         true 增量同步
     */
    private Boolean isIncrement(AmazonSummariesDTO summariesBySku,List<GoodsHead> heads) {
        String incrFlag = configService.selectConfigByKey("amazon_listing_pull_increment_flag");
        if (ObjectUtil.equal(incrFlag, "false")){
            return false;
        }
        if(CollUtil.isEmpty(heads)){
            return false;
        }
        if (ObjUtil.isEmpty(summariesBySku)){
            return false;
        }
        AmazonSummariesDTO.DateDTO lastUpdatedDate = summariesBySku.getLastUpdatedDate();
        if (ObjUtil.isEmpty(lastUpdatedDate)) {
            return false;
        }
        String day = lastUpdatedDate.getYear() + "-" + lastUpdatedDate.getMonthValue() + "-" + lastUpdatedDate.getDayOfMonth();
        //与当前日期 相差天数大于7天，返回true
        //更新日期在7天前 不继续同步了
        return DateUtil.between(DateUtil.parse(day, "yyyy-MM-dd"), DateUtil.date(), DateUnit.DAY) > 7;
    }

    private void markSyncSuccess(String shopCode, String asin) {
        try {
            syncFailTodoService.updateSyncFailTodoStatus(shopCode, asin,  PlatformTypeEnum.AM.name(), "2");
        } catch (Exception e) {
            log.error(String.format("标记同步成功异常,shopCode:%s,asin:%s", shopCode, asin), e);
        }
    }

    /**
     * 获取对应店铺的amazon report
     *
     * @param shop
     * @return
     */
    public List<AmazonAllListingPullDTO> getAmazonAllListingsDTOS(Shop shop,List<String> platformGoodsCode) {
        List<AmazonAllListingPullDTO> returnList = new ArrayList<>();
        String shopCode = shop.getShopCode();

        //VC
        if (shopCode.startsWith("VC")) {
            return odsCrlCrlVcCatalogDataBiz.selectOdsCrlCrlVcCatalogDataVo(shop.getShopCode(), platformGoodsCode);
        }

        //SC
        List<AmazonAllListingsReportDTO> allListings = getAmazonAllListingsReportDTOSV2(shop.getShopCode(), platformGoodsCode);


        if (CollectionUtils.isEmpty(allListings)) {
            return returnList;
        }
        for (AmazonAllListingsReportDTO listing : allListings) {
            AmazonAllListingPullDTO pullDTO = new AmazonAllListingPullDTO();
            pullDTO.setAsin(listing.getAsin1());
            pullDTO.setSellerSku(listing.getSellerSku());
            pullDTO.setPublishType(Arrays.asList("AMAZON_NA", "AMAZON_EU", "AMAZON_JP").contains(listing.getFulfillmentChannel()) ? 1 : 0);
            if (listing.getFulfillmentChannel() == null) {
                log.error("获取亚马逊报告异常,店铺:{}, sellerSku:{}", shop.getShopCode(), listing.getSellerSku());
            }
            pullDTO.setStatus(listing.getStatus());
            pullDTO.setQuantity(listing.getQuantity());
            pullDTO.setPrice(listing.getPrice());
            pullDTO.setOpenDate(listing.getOpenDate());
            pullDTO.setCreateTime(listing.getCreateTime());
            returnList.add(pullDTO);
        }
        return returnList;
    }

    /**
     * 获取对应店铺的amazon report (支持分页)
     *
     * @param shop 店铺信息
     * @param pageNum 页码 (从1开始，null表示获取所有数据)
     * @param pageSize 每页大小 (null表示使用默认值)
     * @return
     */
    public List<AmazonAllListingPullDTO> getAmazonAllListingsDTOS(Shop shop, Integer pageNum, Integer pageSize) {
        List<AmazonAllListingPullDTO> returnList = new ArrayList<>();
        String shopCode = shop.getShopCode();

        //VC
        if (shopCode.startsWith("VC")) {
            String vcStartTimeDays = sysConfigService.selectConfigByKey("amazon_vc_listing_start_time_days");
            Date startTime = StrUtil.isNotBlank(vcStartTimeDays) ? DateUtils.addDays(new Date(), -Integer.parseInt(vcStartTimeDays)) : null;
            return odsCrlCrlVcCatalogDataBiz.selectOdsCrlCrlVcCatalogDataVo(shop.getShopCode(), pageNum, pageSize, startTime);
        }

        //SC
        List<AmazonAllListingsReportDTO> allListings = getAmazonAllListingsReportDTOSV2WithPagination(shop.getShopCode(), pageNum, pageSize);
        if (CollectionUtils.isEmpty(allListings)) {
            return returnList;
        }
        for (AmazonAllListingsReportDTO listing : allListings) {
            AmazonAllListingPullDTO pullDTO = new AmazonAllListingPullDTO();
            pullDTO.setAsin(listing.getAsin1());
            pullDTO.setSellerSku(listing.getSellerSku());
            pullDTO.setPublishType(Arrays.asList("AMAZON_NA", "AMAZON_EU", "AMAZON_JP").contains(listing.getFulfillmentChannel()) ? 1 : 0);
            pullDTO.setStatus(listing.getStatus());
            pullDTO.setQuantity(listing.getQuantity());
            pullDTO.setPrice(listing.getPrice());
            pullDTO.setOpenDate(listing.getOpenDate());
            pullDTO.setOpenDateTime(DateUtils.convertTimeZoneToChinaStandardTime(listing.getOpenDate()));
            pullDTO.setCreateTime(listing.getCreateTime());
            returnList.add(pullDTO);
        }
        return returnList;
    }

    /**
     * 根据店铺进行分页同步listing (新的分页实现)
     *
     * @param userId 用户ID
     * @param shop 店铺信息
     * @param version 版本号
     * @param goodsTask 任务信息
     */
    public void pullOnlineListByShopWithPagination(String userId, Shop shop, String version, GoodsTask goodsTask) {
        log.info("开始分页同步listing，店铺信息：{},版本号:{}", shop.getShopCode(), version);

        // 获取分页大小配置
        String pageSizeConfig = sysConfigService.selectConfigByKey("amazon_listing_page_size");
        int pageSize = StrUtil.isNotBlank(pageSizeConfig) ? Integer.parseInt(pageSizeConfig) : 2000;

        // 获取过滤天数配置
        String recentDayConfig = sysConfigService.selectConfigByKey("amazon_recent_day_config");
        int recentDay = StrUtil.isNotBlank(recentDayConfig) ? Integer.parseInt(recentDayConfig) : 15;

        // 是否开启过滤
        String filterConfig = sysConfigService.selectConfigByKey("amazon_listing_filter_config");
        boolean filter = !StrUtil.isNotBlank(filterConfig) || Boolean.parseBoolean(filterConfig);

        int currentPage = 1;
        int totalProcessed = 0;
        boolean hasMoreData = true;

        goodsTask.setTaskStatus(GoodsTaskStatusEnum.IN_PROGRESS.getInfo());
        goodsTaskService.updateGoodsTask(goodsTask);

        while (hasMoreData) {
            try {
                log.info("处理第{}页数据，每页{}条，店铺：{}", currentPage, pageSize, shop.getShopCode());

                // 分页获取数据
                List<AmazonAllListingPullDTO> pageListings = getAmazonAllListingsDTOS(shop, currentPage, pageSize);

                if (CollectionUtils.isEmpty(pageListings)) {
                    log.info("第{}页没有数据，结束分页同步，店铺：{}", currentPage, shop.getShopCode());
                    hasMoreData = false;
                    break;
                }

                // 如果返回的数据少于页大小，说明是最后一页
                if (pageListings.size() < pageSize) {
                    hasMoreData = false;
                }

                if (CollectionUtils.isEmpty(pageListings)) {
                    log.info("第{}页过滤后没有有效数据，继续下一页，店铺：{}", currentPage, shop.getShopCode());
                    currentPage++;
                    continue;
                }

                // 处理当前页数据
                int pageProcessed = processPageListings(userId, shop, version, goodsTask, pageListings, recentDay);
                totalProcessed += pageProcessed;

                log.info("第{}页处理完成，处理{}条数据，累计处理{}条，店铺：{}", currentPage, pageProcessed, totalProcessed, shop.getShopCode());

                currentPage++;
            } catch (Exception e) {
                log.error("分页同步第{}页数据时发生异常，店铺：{}", currentPage, shop.getShopCode(), e);
                // 继续处理下一页，不中断整个流程
                currentPage++;
            }
        }

        // 更新任务状态
        if (ObjUtil.isNotEmpty(goodsTask.getId())) {
            goodsTask.setTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo());
            goodsTask.setTaskNum(totalProcessed);
            goodsTaskService.updateGoodsTask(goodsTask);
        }

        log.info("分页同步listing完成，店铺信息：{},版本号:{},总处理数量:{}", shop.getShopCode(), version, totalProcessed);
    }

    /**
     * 处理单页listing数据
     */
    private int processPageListings(String userId, Shop shop, String version, GoodsTask goodsTask,
                                  List<AmazonAllListingPullDTO> pageListings, int recentDay) {

        List<String> sellerSkuAll = pageListings.stream()
            .map(e -> getSkuKey(e.getSellerSku(), e.getPublishType()))
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sellerSkuAll)) {
            return 0;
        }

        Map<String, List<AmazonAllListingPullDTO>> sellerSkuMap = pageListings.stream()
            .filter(f -> StringUtils.isNotBlank(f.getSellerSku()))
            .filter(f -> ObjUtil.isNotEmpty(f.getPublishType()))
            .collect(Collectors.groupingBy(e -> getSkuKey(e.getSellerSku(), e.getPublishType())));

        // listing对应的本地数据
        Map<String, List<GoodsHead>> onlineMap = selectExistsListing(sellerSkuAll, shop);
        if (CollUtil.isEmpty(sellerSkuAll)) {
            return 0;
        }

        sellerSkuAll = sellerSkuAll.stream().distinct().collect(Collectors.toList());

        // 处理每个SKU的详细逻辑（复用原有的处理逻辑）
        return processSkuList(userId, shop, version, goodsTask, sellerSkuAll, sellerSkuMap, onlineMap);
    }

    private GoodsHead getGoodsHead(Shop shop, List<GoodsHead> equalsHeads, AmazonAllListingPullDTO reportDTO, JSONObject itemInfo, String version, MappingGoods mappingGoods) {
        JSONObject attributes = getAttributes(itemInfo);
        AmazonSummariesDTO summaries = getSummaries(itemInfo);
        String sellerSku = reportDTO.getSellerSku();
        String asin = reportDTO.getAsin();
        Integer publishType = reportDTO.getPublishType();
        GoodsHead goodsHead = new GoodsHead();
        if (CollUtil.isNotEmpty(equalsHeads) && ObjUtil.isNotEmpty(equalsHeads.get(0).getId())) {
            if (CollUtil.isNotEmpty(equalsHeads)) {
                goodsHead = equalsHeads.get(0);
                //平台商品编码一样、asin也一样 证明刊登系统重复了  删除多余的
                for (int i = 1; i < equalsHeads.size(); i++) {
                    goodsHeadService.deleteListingGoodsHeadById(equalsHeads.get(i).getId());
                }
            }
        }else {
            //本地在线没有数据，看看草稿、刊登失败中是否有
            GoodsHead head = new GoodsHead();
            head.setPlatform(PlatformTypeEnum.AM.name());
            head.setShopCode(shop.getShopCode());
            head.setPlatformGoodsCode(sellerSku);
            head.setPublishType(publishType);
            List<GoodsHead> headsList = goodsHeadService.selectListingGoodsHeadList(head);
            if (CollUtil.isNotEmpty(headsList)) {
                goodsHead = headsList.get(0);
                for (int i = 1; i < headsList.size(); i++) {
                    goodsHeadService.deleteListingGoodsHeadById(headsList.get(i).getId());
                }
            }
        }

        goodsHead.setPublishType(publishType);
        goodsHead.setPdmGoodsCode(Objects.isNull(mappingGoods)
                ? ( StrUtil.isBlank(goodsHead.getPdmGoodsCode()) ? "" : goodsHead.getPdmGoodsCode() )
                : mappingGoods.getGoodsCode());
        goodsHead.setPlatformGoodsCode(reportDTO.getSellerSku());
        goodsHead.setPlatformGoodsId(asin);
        goodsHead.setPlatform(PlatformTypeEnum.AM.name());
        goodsHead.setShopCode(shop.getShopCode());
        goodsHead.setSiteCode(shop.getSiteCode());
        AmazonAttributeDTO keyword = getAttributeInfo(attributes, "item_type_keyword");
        AmazonSummariesDTO.BrowseClassificationDTO browseClassification = summaries.getBrowseClassification();
        String productType = getProductType(itemInfo);
        if (StrUtil.isBlank(productType)) {
            productType = summaries.getProductType();
        }

        //1、通过平台返回的数据匹配一个品类id
        List<PlatformCategory> categoryListAll = getPlatformCategories( keyword, browseClassification);


        //2、系统自动匹配一个品类id
        if (CollectionUtils.isEmpty(categoryListAll)){
            categoryListAll = sysAutoGetPlatformCategories(shop, productType, goodsHead.getPdmGoodsCode(), categoryListAll);
            saveFailTodo(shop.getShopCode(), asin, version, ErrorDefine.SYS_AUTO_MAPPING, ErrorDefine.SYS_AUTO_MAPPING.getMessage(), mappingGoods,reportDTO.getSellerSku());
        }else {
            //运营改准确了就删除记录
            syncFailTodoService.deleteOld(shop.getShopCode(), asin, reportDTO.getSellerSku());
        }

        //3、查询一下是否有属性 没有的话系统直接取一个默认的
        PlatformCategory category=getCategory(shop, productType, categoryListAll);
        if (ObjUtil.isEmpty(category)) {
            category = getPlatformCategory(shop, reportDTO, version, mappingGoods, asin);
        }
        if (ObjUtil.isEmpty(category)){
            savePullLogAndTodoLog(shop.getShopCode(), asin, version, ErrorDefine.SMC_CATEGORY_NULL,
                    String.format("平台itemTypeKeyword：%s，平台类目id：%s，链接节点异常", keyword == null ? "" : keyword.getValue(), browseClassification == null ? "" : browseClassification.getClassificationId()));
            return null;
        }

        //4、赋值
        Integer categoryId = Integer.valueOf(String.valueOf(category.getId()));
//        if( CommonUtilsSmc.isNewVersion(shop.getSiteCode(),shop.getShopCode()) ){
            goodsHead.setCategoryId(categoryId);
            goodsHead.setPlatCategoryId(category.getCategoryId());
//        }else {
//            List<ConfigRequiredField> configRequiredFieldList = configRequiredFieldService.selectByCategoryId(categoryId);
//            if (ObjUtil.isNotEmpty(configRequiredFieldList)) {
//                goodsHead.setCategoryId(categoryId);
//                goodsHead.setPlatCategoryId(category.getCategoryId());
//            }else {
//                //赋值失败 补偿一下  可能是Automotive一级节点  重新取个节点
//                category = getPlatformCategory(shop, reportDTO, version, mappingGoods, asin);
//                goodsHead.setCategoryId(Integer.valueOf(String.valueOf(category.getId())));
//                goodsHead.setPlatCategoryId(category.getCategoryId());
//            }
//        }
        if (ObjUtil.isNotEmpty(goodsHead.getId())){
            return goodsHead;
        }
        if (ObjUtil.isEmpty(goodsHead.getCategoryId())) {
            savePullLogAndTodoLog(shop.getShopCode(), asin, version, ErrorDefine.SMC_CATEGORY_NOT_FOUND, String.format("平台itemTypeKeyword：%s，平台类目id：%s，属性未解析到，链接节点异常",
                    keyword == null ? "" : keyword.getValue(), browseClassification == null ? "" : browseClassification.getClassificationId()));
            return null;
        }
        goodsHead.setDelFlag(0);
        goodsHead.setCreateTime(DateUtils.getNowDate());
        goodsHead.setPdmStatus(0);
        goodsHead.setSmcFlag(1);
        goodsHead.setAdaptationStatus(AdaptationStatusEnum.WAIT.getStatus());
        goodsHead.setCondition("New");
        return goodsHead;
    }

    private PlatformCategory getPlatformCategory(Shop shop, AmazonAllListingPullDTO reportDTO, String version, MappingGoods mappingGoods, String asin) {
        String defaultPlatformCategoryId = PlatformSiteEnum.getDefaultPlatformCategoryId(shop.getSiteCode());

        PlatformCategory platformCategory = new PlatformCategory();
        platformCategory.setPlatformCode(PlatformTypeEnum.AM.name());
        platformCategory.setCategoryId(defaultPlatformCategoryId);
        List<PlatformCategory> categoryList = platformCategoryService.selectPlatformCategoryList(platformCategory);
        if (CollUtil.isEmpty(categoryList)){
            return null;
        }
         saveFailTodo(shop.getShopCode(), asin, version, ErrorDefine.SYS_AUTO_MAPPING_CATE, ErrorDefine.SYS_AUTO_MAPPING_CATE.getMessage(), mappingGoods, reportDTO.getSellerSku());
//        log.error("获取平台类目失败，默认类目为空，shopCode：{}，asin：{}，version：{}，mappingGoods：{}，sellerSku：{}", shop.getShopCode(), asin, version, mappingGoods, reportDTO.getSellerSku());
        return categoryList.get(0);
    }


    private PlatformCategory getCategory(Shop shop, String productType, List<PlatformCategory> categoryListAll) {
        if (CollUtil.isEmpty(categoryListAll)){
            return null;
        }
        PlatformCategory platformCategory = categoryListAll.get(0);
        if (StrUtil.isBlank(productType)){
            productType = platformCategory.getProductType();
        }
        if ( StrUtil.isNotEmpty(productType)) {
//        if ( CommonUtilsSmc.isNewVersion(shop.getSiteCode(), shop.getShopCode()) && StrUtil.isNotEmpty(productType)){
            String vcFlag = shop.getShopCode().contains("VC") ? Constants.YesOrNo.YES : Constants.YesOrNo.NO;
            AmCategoryTemplateField fieldQuery = new AmCategoryTemplateField();
            fieldQuery.setProductType(productType);
            fieldQuery.setVcFlag(vcFlag);
            fieldQuery.setSite(shop.getSiteCode());
            List<AmCategoryTemplateField> oldFields = amCategoryTemplateFieldService.selectAmCategoryTemplateFieldList(fieldQuery);
            if (CollUtil.isEmpty(oldFields)) {
                amazonPlatformListingService.syncCategory(productType, shop.getShopCode());
            }
        }
//        }else if (!shop.getShopCode().contains("VC")){
//            //自动解析文件属性
//            pullFileRelatedTaskBiz.doHandlePullFiledDictTaskV2(platformCategory.getCategoryId());
//        }
        return platformCategory;
    }

    private List<PlatformCategory> getPlatformCategories( AmazonAttributeDTO keyword, AmazonSummariesDTO.BrowseClassificationDTO browseClassification) {
        List<PlatformCategory> categoryListAll = new ArrayList<>();

        if (ObjUtil.isNotEmpty(browseClassification)) {
            PlatformCategory platformCategory = new PlatformCategory();
            String amazonCategoryId = browseClassification.getClassificationId();
            platformCategory.setPlatformCode(PlatformTypeEnum.AM.name());
            platformCategory.setCategoryId(amazonCategoryId);
            List<PlatformCategory> categoryList = platformCategoryService.selectPlatformCategoryList(platformCategory);
            categoryListAll.addAll(categoryList);
        }
        if (ObjUtil.isNotEmpty(keyword)) {
            PlatformCategory platformCategory = new PlatformCategory();
            String itemTypeKeyword = keyword.getValue();
            platformCategory.setPlatformCode(PlatformTypeEnum.AM.name());
            platformCategory.setItemTypeKeyword(itemTypeKeyword);
            List<PlatformCategory> categoryList = platformCategoryService.selectPlatformCategoryList(platformCategory);
            categoryListAll.addAll(categoryList);
        }
        return categoryListAll;
    }

    private List<PlatformCategory> sysAutoGetPlatformCategories(Shop shop, String productType, String pdmGoodsCode, List<PlatformCategory> categoryListAll) {
        //通过productType获取品类
        if (CollectionUtils.isNotEmpty(categoryListAll)){
            return categoryListAll;
        }
        List<PlatformCategory> categoryListProductType =new ArrayList<>();
        if ( StrUtil.isNotEmpty(productType) ){
            PlatformCategory platformCategory = new PlatformCategory();
            platformCategory.setPlatformCode(PlatformTypeEnum.AM.name());
            platformCategory.setSite(shop.getSiteCode());
            platformCategory.setProductType(productType);
            categoryListProductType.addAll( platformCategoryService.selectPlatformCategoryList(platformCategory) );
        }

        //没有品类 就通过商品编码随机取一个品类
        List<PlatformCategory> categoryListMapping =new ArrayList<>();
        if (StrUtil.isNotEmpty(pdmGoodsCode)){
            GoodsCategoryMapping goodsCategoryMapping = goodsCategoryMappingService.selectCategoryMatch(shop.getPlatformCode(), shop.getSiteCode(), pdmGoodsCode);
            if (ObjUtil.isNotEmpty(goodsCategoryMapping)){
                String platformCategoryId = goodsCategoryMapping.getPlatformCategoryId();
                PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(platformCategoryId));
                categoryListMapping.add(platformCategory);
            }else {
                GoodsHead head = new GoodsHead();
                head.setPlatform(PlatformTypeEnum.AM.name());
                head.setSiteCode(shop.getSiteCode());
                head.setPdmGoodsCode(pdmGoodsCode);
                List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadList(head);
                if (CollUtil.isNotEmpty(heads)){
                    //获取最多的类目id
                    List<PlatformCategory> platformCategories = platformCategoryService.selectPlatformCategoryListByIds(heads.stream().map(e -> String.valueOf(e.getCategoryId())).toArray(String[]::new));
                    categoryListMapping.addAll(platformCategories);
                }
            }
        }

        //取 categoryListProductType 和 categoryListMapping其的交集 并且取第一个
        if (CollUtil.isNotEmpty(categoryListProductType) && CollUtil.isNotEmpty(categoryListMapping)){
            categoryListAll.addAll(  CollUtil.intersection(categoryListProductType, categoryListMapping) );
        }else if (CollUtil.isNotEmpty(categoryListMapping)){
            categoryListAll.addAll(  categoryListMapping );
        }else if (CollUtil.isNotEmpty(categoryListProductType)){
            categoryListAll.addAll(  categoryListProductType );
        }

        return categoryListAll;
    }


    private Map<String, List<GoodsHead>> selectExistsListing(List<String> sellerSkuAll, Shop shop) {
        // 提取平台商品编码列表（去除发布类型后缀）
        List<String> platformSkuList = sellerSkuAll.stream()
            .map(this::extractPlatformSku)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(platformSkuList)) {
            return new HashMap<>();
        }

        // 根据平台商品编码精确查询指定的链接，而不是查询整个店铺
        List<GoodsHead> headsList = goodsHeadService.listByPlatformAndShopCode(PlatformTypeEnum.AM.name(), shop.getShopCode(), platformSkuList);
        if (CollectionUtils.isEmpty(headsList)) {
            return new HashMap<>();
        }

        //asin不为空证明是线上数据
        List<GoodsHead> onlineList = headsList.stream()
                    .filter(f -> StrUtil.isNotBlank(f.getPlatformGoodsId()))
                    .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(onlineList)) {
            return new HashMap<>();
        }

        return headsList.stream().collect(Collectors.groupingBy(h->getSkuKey(h.getPlatformGoodsCode(), h.getPublishType())));
    }

    /**
     * 从SKU键中提取平台SKU（去除发布类型后缀）
     * 例如：SKU123@@1 -> SKU123
     */
    private String extractPlatformSku(String skuKey) {
        if (StrUtil.isBlank(skuKey)) {
            return skuKey;
        }
        int separatorIndex = skuKey.lastIndexOf("@@");
        if (separatorIndex > 0) {
            return skuKey.substring(0, separatorIndex);
        }
        return skuKey;
    }

    @Override
    public void syncShopLostListingInfo(SyncListingDTO dto) {

    }

    @Override
    public void quickUpdate(ListingQuickEditDTO dto, GoodsHead goodsHead) {
        //如果是刊登中 更新中 下架中的数据 不允许修改
        if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
            throw new RuntimeException("listing状态为刊登中、更新中、下架中、非在售的数据不允许修改");
        }

        if (CommonUtilsSmc.isNewVersion(goodsHead.getSiteCode(), goodsHead.getShopCode())) {
            vcQuickUpdate(dto, goodsHead);
            return;
        }

        if (Objects.equals(dto.getStandardPrice(), goodsHead.getStandardPrice())) {
            //价格相同  证明只需修改库存  库存只改本地数据 不做其它处理
            goodsHead.setStandardPrice(dto.getStandardPrice());
            goodsHead.setStockOnSalesQty(dto.getStockOnSalesQty());
            goodsHead.setUpdateBy(ShiroUtils.getUserId().toString());
            goodsHeadService.updateListingGoodsHead(goodsHead);
            return;
        }

        Integer statusByEdit = PublishStatus.getStatusByEdit(goodsHead.getPublishStatus(), PlatformTypeEnum.AM.name());

        String listPrice = listingAmazonAttributeLineV2Service.getValueByPropNodePath(goodsHead.getId(), "list_price.value");
        String price = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), dto.getStandardPrice(), goodsHead.getPublishType())
                .headId(goodsHead.getId())
                .siteCode(goodsHead.getSiteCode())
                .onlineTime(goodsHead.getOnlineTime())
                .oldPrice(goodsHead.getStandardPrice())
                .isOnline(StrUtil.isNotBlank(goodsHead.getPlatformGoodsId()))
                .listPrice(listPrice)
                .build());
        dto.setStandardPrice(price);

        //修改线上数据
        if (Objects.equals(statusByEdit, PublishStatus.UPDATING.getType())) {
            // 更新文件记录入库
            ProductDocumentRecord productDocumentRecord = new ProductDocumentRecord();
            productDocumentRecord.setFileName("UPDATE_LISTING_PRICE_" + goodsHead.getStandardPrice() + "_TO_" + dto.getStandardPrice());
            productDocumentRecord.setListingIds(JSON.toJSONString(Arrays.asList(goodsHead.getId())));
            productDocumentRecord.setCreateTime(new Date());
            productDocumentRecordService.insertRecord(productDocumentRecord);

            AmazonListingUpdateFeedVO updateFeedVO = new AmazonListingUpdateFeedVO();
            updateFeedVO.setSellerCode(goodsHead.getShopCode());
            updateFeedVO.setSmcRecordId(productDocumentRecord.getId());
            AmazonListingUpdateFeedVO.AmazonUpdateContentVO amazonUpdateContentVO = new AmazonListingUpdateFeedVO.AmazonUpdateContentVO();
            amazonUpdateContentVO.setSellerSku(goodsHead.getPlatformGoodsCode());
            amazonUpdateContentVO.setPrice(dto.getStandardPrice());
            updateFeedVO.setUpdateContent(Arrays.asList(amazonUpdateContentVO));
            updatePriceToApi(updateFeedVO);

            intoUpdateRecord(goodsHead, Lists.newArrayList(ListingModuleType.PRICE.name()),0);
        }

        //修改本地数据
        goodsHead.setStandardPrice(dto.getStandardPrice());
        goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(dto.getStandardPrice())));
        goodsHead.setStockOnSalesQty(dto.getStockOnSalesQty());
        goodsHead.setPublishStatus(statusByEdit);
        if (Objects.equals(statusByEdit, PublishStatus.UPDATING.getType())) {
            goodsHead.setPublishingHandler("处理中");
        }
        goodsHead.setUpdateBy(ShiroUtils.getUserId().toString());
        goodsHeadService.updateListingGoodsHead(goodsHead);
    }

    /**
     * vc快速修改
     *
     * @param dto
     * @param goodsHead
     */
    private void vcQuickUpdate(ListingQuickEditDTO dto, GoodsHead goodsHead) {
        // VC修改list price
        ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2 = listingAmazonAttributeLineV2Service.getAttrByPropNodePath(goodsHead.getId(), "list_price.value");
        if (ObjUtil.isNotEmpty(listingAmazonAttributeLineV2)) {
            String price = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), dto.getStandardPrice(), goodsHead.getPublishType())
                    .headId(goodsHead.getId())
                    .siteCode(goodsHead.getSiteCode())
                    .onlineTime(goodsHead.getOnlineTime())
                    .oldPrice(goodsHead.getStandardPrice())
                    .isOnline(StrUtil.isNotBlank(goodsHead.getPlatformGoodsId()))
                    .listPrice(listingAmazonAttributeLineV2.getTableValue())
                    .build());

            if (Objects.equals(dto.getStandardPrice(), listingAmazonAttributeLineV2.getTableValue())) {
                //价格相同  证明只需修改库存  库存只改本地数据 不做其它处理
                goodsHead.setStockOnSalesQty(dto.getStockOnSalesQty());
                goodsHead.setUpdateBy(ShiroUtils.getUserId().toString());
                goodsHeadService.updateListingGoodsHead(goodsHead);


                listingAmazonAttributeLineV2.setTableValue(dto.getStandardPrice());
                listingAmazonAttributeLineV2Service.updateListingAmazonAttributeLineV2(listingAmazonAttributeLineV2);
                return;
            }

            Integer statusByEdit = PublishStatus.getStatusByEdit(goodsHead.getPublishStatus(), PlatformTypeEnum.AM.name());
            listingAmazonAttributeLineV2.setTableValue(price);
            //修改线上数据
            if (Objects.equals(statusByEdit, PublishStatus.UPDATING.getType())) {
                // 更新文件记录入库
                ProductDocumentRecord productDocumentRecord = new ProductDocumentRecord();
                productDocumentRecord.setFileName("UPDATE_LISTING_PRICE_" + listingAmazonAttributeLineV2.getTableValue() + "_TO_" + listingAmazonAttributeLineV2.getTableValue());
                productDocumentRecord.setListingIds(JSON.toJSONString(Arrays.asList(goodsHead.getId())));
                productDocumentRecord.setCreateTime(new Date());
                productDocumentRecordService.insertRecord(productDocumentRecord);
//
//                AmazonListingUpdateFeedVO updateFeedVO = new AmazonListingUpdateFeedVO();
//                updateFeedVO.setSellerCode(goodsHead.getShopCode());
//                updateFeedVO.setSmcRecordId(productDocumentRecord.getId());
//                AmazonListingUpdateFeedVO.AmazonUpdateContentVO amazonUpdateContentVO = new AmazonListingUpdateFeedVO.AmazonUpdateContentVO();
//                amazonUpdateContentVO.setSellerSku(goodsHead.getPlatformGoodsCode());
//                amazonUpdateContentVO.setPrice(dto.getStandardPrice());
//                updateFeedVO.setUpdateContent(Arrays.asList(amazonUpdateContentVO));
//                updatePriceToApi(updateFeedVO);

                intoUpdateRecord(goodsHead, Lists.newArrayList(ListingModuleType.PRICE.name()),0);
            }

            //修改本地数据
            goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(dto.getStandardPrice())));
            goodsHead.setStockOnSalesQty(dto.getStockOnSalesQty());
            goodsHead.setPublishStatus(statusByEdit);
//            if (Objects.equals(statusByEdit, PublishStatus.UPDATING.getType())) {
//                goodsHead.setPublishingHandler("处理中");
//            }
            //sc需要修改standardPrice
            if (ObjUtil.isNotEmpty(goodsHead.getShopCode()) && !goodsHead.getShopCode().contains("VC")) {
                goodsHead.setStandardPrice(price);
            }
            goodsHead.setUpdateBy(ShiroUtils.getUserId().toString());
            goodsHeadService.updateListingGoodsHead(goodsHead);

            listingAmazonAttributeLineV2Service.updateListingAmazonAttributeLineV2(listingAmazonAttributeLineV2);
        }
    }

    /**
     * 批量修改listing图片
     *
     * @param dto
     */
    @Override
    public void batchUpdatePictures(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE, goodsIds, String.valueOf(userId));
        List<ItemDTO> itemDTOList = new ArrayList<>();
        for (ListingDTO listing : listings) {
            try {
                List<GoodsResource> resourceList = getResourceList(dto, listing);
                GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                for (GoodsResource resource : resourceList) {
                    resource.setGoodsId(goodsHead.getId());
                }
                //goodsResourceList
                if (ObjUtil.isNotEmpty(resourceList.stream().filter(g -> ObjUtil.isNotEmpty(g.getGoodsId())).collect(Collectors.toList()))) {
                    goodsResourceService.deleteListingGoodsResourceByHeadId(goodsHead.getId());
                    goodsResourceService.insertListingGoodsResourceBatch(resourceList);
                }
                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && !PublishStatus.getUnderWayStatus().contains(goodsHead.getPublishStatus())
                        && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Collections.singletonList(ListingModuleType.IMAGE.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE), GoodsTaskSubStatusEnum.NORAML, "");
                }
                brandAdaptImgTodoService.confirmByGoodId(listing.getId(), userId, "2");
                if (CollectionUtils.isNotEmpty(listing.getExsitImgArrs())) {
                    String adapatLogString = listingLogService.compareImagesChanged(listing.getImgArrs(),listing.getExsitImgArrs());
                    listingLogService.insertSuccessListingLog("适配图片变更待办处理成功," + adapatLogString, String.valueOf(userId), listing.getId());
                }
            } catch (Exception e) {
                log.error("批量修改listing图片失败,listingId:{}", listing.getId(), e);
                brandAdaptImgTodoService.confirmByGoodId(listing.getId(), userId, "3");
                listingLogService.insertSuccessListingLog("适配图片变更待办处理失败", String.valueOf(userId), listing.getId());
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        // 检查是否需要将信息同步到其他相同ASIN的链接中
        Boolean syncOtherListings = dto.getSyncOtherListings();
        String syncFields = dto.getSyncFields();
        if (Boolean.TRUE.equals(syncOtherListings) && StringUtils.isNotEmpty(syncFields)) {
            String updateBy = ShiroUtils.getUserId() + "";
            // 开启线程执行
            threadPoolTaskExecutor.execute(() -> {
                Set<Integer> alreadySyncedIds = new HashSet<>(goodsIds);
                sync2OtherListing(syncFields, goodsIds, alreadySyncedIds, updateBy);
            });
        }

        threadPoolForMonitorManager.getThreadPoolExecutor("eidtPoolConfig").execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });
    }

    public void removeOnSaleImg(GoodsHead dbHead) {
        try {
            if (StrUtil.isBlank(dbHead.getPlatformGoodsId()) || dbHead.getShopCode().contains("VC") || StrUtil.isBlank(dbHead.getPlatformGoodsCode())) {
                return;
            }
            List<GoodsResource> newResourceList = goodsResourceService.selectListingGoodsResourceByHeadId(dbHead.getId());
            // 移除主图
            newResourceList = newResourceList.stream().filter(g -> !Objects.equals(g.getIsMain(), 1)).collect(Collectors.toList());
            if(newResourceList.size() >= 8){
                return;
            }

            AjaxResult detailResult = amazonApiHttpRequestBiz.getBackendDetailResult(dbHead.getShopCode(), dbHead.getPublishType(), null, dbHead.getPlatformGoodsCode());
            if (!detailResult.isSuccess()) {
                return;
            }

            JSONObject data = JSONObject.parseObject(JSON.toJSONString(detailResult.get(AjaxResult.DATA_TAG)));
            Integer numberOfResults = data.getInteger("numberOfResults");
            if (numberOfResults == 0) {
                return;
            }
            JSONObject itemInfo = data.getJSONArray("items").getJSONObject(0);

            // 解析得到最新的图
            List<GoodsResource> imagesList = new ArrayList<>();
            getGoodImagesV2(itemInfo, imagesList);

            if (CollUtil.isEmpty(imagesList)) {
                return;
            }
            imagesList = imagesList.stream().filter(g -> !Objects.equals(g.getIsMain(), 1)).collect(Collectors.toList());
            if (CollUtil.isEmpty(imagesList)) {
                return;
            }

            if(imagesList.size() <= newResourceList.size()){
                return;
            }
            imagesList.sort(Comparator.comparing(GoodsResource::getSortNumber));

            int start = newResourceList.size();
            int end = imagesList.size();
            List<AmazonListingJSONFeedVO.Attributes> buildDeleteAttributes = buildDeleteAttributes(imagesList, start, end);
            if (CollUtil.isEmpty(buildDeleteAttributes)) {
                return;
            }
            PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(dbHead.getCategoryId()));
            AjaxResult ajaxResult = amazonApiHttpRequestBiz.updateApi(dbHead.getPlatformGoodsCode(), dbHead.getShopCode(), dbHead.getPublishType() + "", buildDeleteAttributes, platformCategory.getProductType());
            if (ajaxResult.isSuccess()) {
               log.info("headId:{},删除多余的图片成功，删除数量：{}", dbHead.getId(), buildDeleteAttributes.size());
            }
        } catch (Exception e) {
            log.error("删除多余的图片失败", e);
        }
    }

    private void getGoodImagesV2(JSONObject itemInfo, List<GoodsResource> imagesList) {
        JSONObject attributes = getAttributes(itemInfo);
        // other_product_image_locator开头
        JSONObject imageObjList = getAttributeList(attributes, "other_product_image_locator");
        for (Map.Entry<String, Object> entry : imageObjList.entrySet()) {
            String key = entry.getKey();
            // [ {
            //  "marketplace_id" : "ATVPDKIKX0DER",
            //  "media_location" : "https://m.media-amazon.com/images/I/71D5vj7oHpL.jpg"
            //} ]
            String value = JSONArray.parseArray(entry.getValue().toString()).getJSONObject(0).getString("media_location");
            GoodsResource goodsResource = new GoodsResource();
            goodsResource.setResourceUrl(value);
            goodsResource.setResourceName(value.substring(value.lastIndexOf("/") + 1));
            goodsResource.setResourceType("1");
            // other_product_image_locator_7
            goodsResource.setSortNumber(Integer.parseInt(key.substring(key.lastIndexOf("_") + 1)) - 1);
            goodsResource.setDelFlag("0");
            goodsResource.setIsMain(0);
            imagesList.add(goodsResource);
        }
    }

    private JSONObject getAttributeList(JSONObject attributes, String keyPrefix) {
        // attributes有很多个key，找到以keyPrefix开头的key
        JSONObject matchObj = new JSONObject();
        for (Map.Entry<String, Object> entry : attributes.entrySet()) {
            if (entry.getKey().startsWith(keyPrefix)) {
                matchObj.put(entry.getKey(), entry.getValue());
            }
        }
        return matchObj;
    }

    /**
     * 批量修改listing标题
     *
     * @param dto
     */
    @Override
    public void batchUpdateTitle(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_UPDATE_TITLE, goodsIds, String.valueOf(userId));

        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        Map<String, Set<String>> violateWord = new HashMap<>();

        for (ListingDTO listing : listings) {
            try {
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                if (ObjUtil.isEmpty(goodsHead)) {
                    continue;
                }
                goodsHead.setTitle(listing.getTitle());
                violateWordBiz.checkViolateWord(true,null, null, goodsHead);

                GoodsHead updateHead = new GoodsHead();
                updateHead.setId(goodsHead.getId());
                updateHead.setTitle(listing.getTitle());
                if (StringUtils.isNotEmpty(updateHead.getTitle())) {
                    updateHead.setTitle(updateHead.getTitle().replaceAll("“", "\""));
                    updateHead.setTitle(updateHead.getTitle().replaceAll("’", "'"));
                }
                goodsHeadService.updateListingGoodsHead(updateHead);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && !PublishStatus.getUnderWayStatus().contains(goodsHead.getPublishStatus())
                        && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Collections.singletonList(ListingModuleType.TITLE.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改listing标题失败,listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
                if (e instanceof BusinessException) {
                    assembleWord(violateWord, e.getMessage());
                }
            }
        }

        if (ObjUtil.isNotEmpty(violateWord)) {
            assembleWordBefore(violateWord);
        }

        // 检查是否需要将信息同步到其他相同ASIN的链接中
        Boolean syncOtherListings = dto.getSyncOtherListings();
        String syncFields = dto.getSyncFields();
        if (Boolean.TRUE.equals(syncOtherListings) && StringUtils.isNotEmpty(syncFields)) {
            String updateBy = ShiroUtils.getUserId() + "";
            // 开启线程执行
            threadPoolTaskExecutor.execute(() -> {
                Set<Integer> alreadySyncedIds = new HashSet<>(goodsIds);
                sync2OtherListing(syncFields, goodsIds, alreadySyncedIds, updateBy);
            });
        }
        threadPoolForMonitorManager.getThreadPoolExecutor("eidtPoolConfig").execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });
    }

    /**
     * 批量修改listing价格和库存
     *
     * @param dto
     */
    @Override
    public void batchUpdatePriceAndStock(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK, goodsIds, String.valueOf(userId));
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));

        // 封装成List<GoodsHead>
        List<GoodsHead> goodsHeads = new ArrayList<>();
        for (ListingDTO listing : listings) {
            GoodsHead dbGoodsHead = headMap.get(listing.getId());

            GoodsHead goodsHead = new GoodsHead();
            goodsHead.setId(listing.getId());
            goodsHead.setStandardPrice(listing.getStandardPrice());
            goodsHead.setStockOnSalesQty(listing.getStockOnSalesQty());
            goodsHead.setUpdateBy(ShiroUtils.getUserId()+"");
            goodsHead.setShopCode(dbGoodsHead.getShopCode());
            goodsHeads.add(goodsHead);
        }
        Boolean checkResult = dingdingMonitorInfoBiz.monitorAMListingAndSend(goodsHeads, AM_QUICK_UPDATE);
        if (checkResult != null &&!checkResult) {
            throw new BusinessException("[系统拦截] 改动的数据变化较大，请联系管理员核对");
        }

        for (ListingDTO listing : listings) {
            try {
                //head
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (ObjUtil.isNotEmpty(goodsHead.getShopCode()) && goodsHead.getShopCode().contains("VC")) {
                    throw new BusinessException("Amazon vc类型链接暂时不支持修改");
                }
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                if(!Objects.equals(dto.getTag(),"STOCK")){
                    String listPrice = listingAmazonAttributeLineV2Service.getValueByPropNodePath(goodsHead.getId(), "list_price.value");   
                    String price = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), listing.getStandardPrice(), goodsHead.getPublishType())
                            .headId(goodsHead.getId())
                            .siteCode(goodsHead.getSiteCode())
                            .onlineTime(goodsHead.getOnlineTime())
                            .oldPrice(goodsHead.getStandardPrice())
                            .isOnline(StrUtil.isNotBlank(goodsHead.getPlatformGoodsId()))
                            .listPrice(listPrice)
                            .build());
                    goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
                }
                goodsHead.setStockOnSalesQty(ObjectUtils.isEmpty(listing.getStockOnSalesQty()) ? null : listing.getStockOnSalesQty());
                goodsHeadService.updateListingGoodsHead(goodsHead);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && !PublishStatus.getUnderWayStatus().contains(goodsHead.getPublishStatus())
                        && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Arrays.asList(ListingModuleType.INVENTORY.name(), ListingModuleType.PRICE.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改listing价格和库存失败,listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        threadPoolForMonitorManager.getThreadPoolExecutor("eidtPoolConfig").execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });

        //处理待办的状态
        loseCartTodoService.confirmByGoodIds(goodsIds, userId);
        inventoryLowTodoService.confirmByGoodIds(goodsIds, userId);
    }

    @Override
    public void batchUpdateAttribute(BatchListingDTO dto) {
        throw new BusinessException("Amazon暂不支持批量修改属性");
    }

    @Override
    public void batchUpdateAdaptive(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        String ids = dto.getIds();
        boolean monitor = dto.isMonitor();
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        List<Integer> goodsIds = Arrays.stream(ids.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        if (!monitor){
            goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE, goodsIds, String.valueOf(userId));
        }

        for (Integer goodsId : goodsIds) {
            try {
                GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(goodsId);

                //无需适配 适配状态改为无需适配 适配数据不保存
                SaleGoodsDTO query = new SaleGoodsDTO();
                query.setGoodsCode(goodsHead.getPdmGoodsCode());
                Goods pdmGood = goodsService.selectGoodsByGoodCode(query);
                if (ObjUtil.isEmpty(pdmGood)) {
                    throw new BusinessException("该listing对应的pdm商品不存在.");
                }
                if (ObjUtil.equals(pdmGood.getAdaptFlag(), "N")) {
                    GoodsHead updateHead = new GoodsHead();
                    updateHead.setId(goodsHead.getId());
                    updateHead.setAdaptationStatus(AdaptationStatusEnum.NO.getStatus());
                    goodsHeadService.updateListingGoodsHead(updateHead);
                    throw new BusinessException("该商品无需上传适配.");
                }

                if (ObjUtil.isEmpty(goodsHead.getPlatformGoodsId())) {
                    throw new BusinessException("该listing未上传至亚马逊.");
                }
                int i = adsService.countByPnAndAsin(goodsHead.getPlatformGoodsId());
                if (i >= 1) {
                    throw new BusinessException("重复更新适配,原上传任务待处理中.");
                }
                if (monitor){
                    //是否已经有监控对比类型
                    int j  = adsService.countByPnAndAsinCompare(goodsHead.getPlatformGoodsId());
                    if (j >= 1) {
                        throw new BusinessException("重复监控适配,原监控任务待处理中.");
                    }

                    refreshPnCode(userId, goodsHead, pdmGood,null,"2");
                }else {
                    refreshPnCode(userId, goodsHead, pdmGood,null,"1");
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsId), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量处理listing amazon适配失败,listingId:{}", goodsId, e);
                listingLogService.insertErrorListingLog("批量处理listing amazon适配失败", String.valueOf(userId), goodsId, "批量处理listing amazon适配失败:" + e.getMessage());
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsId), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
    }
    public void refreshPnCode(Long userId, GoodsHead goodsHead, Goods pdmGood,String newPnStr,String demandType) {
        Integer goodsId = goodsHead.getId();
        Map<String, String> partMap = new HashMap<>();
        if (StrUtil.isNotEmpty(newPnStr)){
            //这里是指定了pn 数据组指定的
//            String oldPn = listingAmazonAttributeLineService.getPn(goodsId);
            String oldPn = listingAmazonAttributeLineV2Service.getPn(goodsId);
            partMap.put("old", oldPn);
            partMap.put("new", newPnStr);
        }else {
            //这里是更新pn  所以取pn很关键
            partMap.putAll( getPartNumberMap(userId, goodsHead,true) );
        }
        String details = "状态是:" + PublishStatus.getPublishStatusName(goodsHead.getPublishStatus()) + ",由原PN:" + partMap.get("old") + ",更改为新PN:" + partMap.get("new");

        if (Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId())) {

            Shop shop = shopService.selectShopByShopCode(goodsHead.getShopCode());
            Brand brand = brandService.selectBrandByName(goodsHead.getBrandCode());

            //回写至数据组it_demand
            String code = pdmGood.getClassificationCode();
            ItDemand itDemand = ItDemand.builder()
                    .time(dateTimeNow("yyyyMMdd"))
                    .brandEnName(goodsHead.getBrandCode())
                    .sku(goodsHead.getPlatformGoodsCode())
                    .productCode(pdmGood.getProductCode())
                    .pn(partMap.get("new"))
                    .asin(goodsHead.getPlatformGoodsId())
                    .type(code)
                    .demandType(demandType)
                    .country(goodsHead.getSiteCode())
                    .brandAaiaId(ObjUtil.isNotEmpty(brand) ? brand.getBrandAaiaId() : "")
                    .shopCode(goodsHead.getShopCode())
                    .browserName(ObjUtil.isNotEmpty(shop) ? shop.getBrowserName() : "")
                    .currentNumber(1)
                    .build();

            //这个入口是更新适配, 1、数据组更新 可能指定对应pn 也可能是新pn 2、页面点击更新
            if (ObjUtil.equals(demandType,"2")){
                //250321-1  年月日-1 获取这个格式的数据
                String yyMMdd = dateTimeNow("yyMMdd") + "-1";
                itDemand.setUpDataVersion(yyMMdd);
                adsService.saveToAdsItDemandCompare(itDemand);
                details = details + ",适配更新任务待重新监控中.";
            }else {
                adsService.saveToAdsItDemandA(itDemand);
                details = details + ",适配更新任务待处理中.";
            }
        }
        listingLogService.insertSuccessListingLog(details, String.valueOf(userId), goodsHead.getId());
        //记录适配日志
        listingAdapterLogService.insertSuccessListingLog(goodsHead.getPlatform(),Long.valueOf(goodsHead.getId()),goodsHead.getPlatformGoodsId(), details, String.valueOf(userId), null);
    }

    /**
     * 批量修改长描述
     *
     * @param dto
     */

    @Override
    public void batchUpdateDescription(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_UPDATE_DESCRIPTION, goodsIds, String.valueOf(userId));
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        Map<Integer, List<GoodsDescription>> descMap = goodsDescriptionService.selectDescriptionListByGoodsIdList(goodsIds).stream().collect(Collectors.groupingBy(GoodsDescription::getGoodsId));
        for (ListingDTO listing : listings) {
            try {
                //head
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                GoodsDescription goodsDescription = descMap.get(listing.getId()).get(0);

                getGoodsDescription(listing, goodsDescription);

                if (Objects.nonNull(goodsDescription.getDetailDescription())) {
                    goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));
                }
                violateWordBiz.checkViolateWord(true,goodsDescription, null, goodsHead);
                goodsDescriptionService.updateListingGoodsDescription(goodsDescription);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && !PublishStatus.getUnderWayStatus().contains(goodsHead.getPublishStatus())
                        && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Arrays.asList(ListingModuleType.DESCRIPTION.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_DESCRIPTION), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改listing长描述listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_DESCRIPTION), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        // 检查是否需要将信息同步到其他相同ASIN的链接中
        Boolean syncOtherListings = dto.getSyncOtherListings();
        String syncFields = dto.getSyncFields();
        if (Boolean.TRUE.equals(syncOtherListings) && StringUtils.isNotEmpty(syncFields)) {
            String updateBy = ShiroUtils.getUserId() + "";
            // 开启线程执行
            threadPoolTaskExecutor.execute(() -> {
                Set<Integer> alreadySyncedIds = new HashSet<>(goodsIds);
                sync2OtherListing(syncFields, goodsIds, alreadySyncedIds, updateBy);
            });
        }
        threadPoolForMonitorManager.getThreadPoolExecutor("eidtPoolConfig").execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });
    }

    public void fixedPdmPartNumberMapping(GoodsHead goodsHead, String createBy, Map<String, String> partMap) {
        try {
            if (ObjUtil.equals("-1", createBy)) {
                createBy = "1";
            }
            SysUser sysUser = userService.selectUserById(Long.valueOf(createBy));
            CreateMappingDTO createMappingDTO = new CreateMappingDTO();
            createMappingDTO.setGoodsCode(goodsHead.getPdmGoodsCode());
            createMappingDTO.setShopCode(goodsHead.getShopCode());
            createMappingDTO.setPlatformSku(goodsHead.getPlatformGoodsCode());
            createMappingDTO.setAsin(goodsHead.getPlatformGoodsId());
            createMappingDTO.setPrice(goodsHead.getStandardPrice());
            createMappingDTO.setPlatformPn(partMap.get("new"));
            createMappingDTO.setOperators(createBy);
            createMappingDTO.setDeptId(ObjUtil.isEmpty(sysUser)?"":String.valueOf(sysUser.getDeptId()));
            createMappingDTO.setCreateBy(createBy);
            createMappingDTO.setSystemType("SMC");
            createMappingDTO.setBrandCode(goodsHead.getBrandCode());
            if (StrUtil.isNotEmpty(goodsHead.getFnSku())) {
                createMappingDTO.setFnSku(goodsHead.getFnSku());
            }
            createMappingDTO.setPublishType(goodsHead.getPublishType());
            pdmHttpRequestBiz.setCreateMappingDTOPublishStatus(goodsHead, createMappingDTO);
            if (EnvUtils.isProdProfile()) {
                pdmHttpRequestBiz.addPdmMapping(createMappingDTO, goodsHead);
            } else {
                log.info("测试环境回写pdm mapping,{}", JSON.toJSONString(createMappingDTO));
            }
        } catch (Exception e) {
            log.error("回写pdm mapping失败,listingId:{}", goodsHead.getId(), e);
        }
    }

    private Map<String, String> getPartNumberMap(Long userId,  GoodsHead goodsHead,boolean frant) {
        Integer goodsId = goodsHead.getId();
        Map<String, String> returnMap = new HashMap<>();
        returnMap.put("old", null);
        returnMap.put("new", null);
        String pn = listingAmazonAttributeLineV2Service.getPn(goodsId);
        if (ObjUtil.isEmpty(pn)) {
            String platformCode = getPlatformCode(userId, goodsHead,frant,null);
            if (frant){
                returnMap.put("old", platformCode);
            }
            returnMap.put("new", platformCode);
            return returnMap;
        }

        returnMap.put("old", pn);
        String platformCode = getPlatformCode(userId, goodsHead,frant,pn);
        returnMap.put("new", platformCode);
        return returnMap;
    }

    private String getPlatformCode(Long userId, GoodsHead goodsHead,boolean frant,String skuOldPn) {
        if (frant){
            //1、想办法先取到这个asin的前台pn
            List<FitExamineDay> fitExamineDayListByAsins = adapterManageService.getFitExamineDayListByAsins(Lists.newArrayList(goodsHead.getPlatformGoodsId()));
            if (CollUtil.isNotEmpty(fitExamineDayListByAsins) && StrUtil.isNotEmpty(fitExamineDayListByAsins.get(0).getMpn())){
                return fitExamineDayListByAsins.get(0).getMpn();
            }
            //2、取不到前台pn 就取现sku上的pn
            if (StrUtil.isNotEmpty(skuOldPn)){
                return skuOldPn;
            }
        }
        String platformCode = createPlatformCode.getPlatformCodeAync(goodsHead.getPdmGoodsCode(), goodsHead.getSiteCode(),String.valueOf(userId));
        return platformCode;
    }

    @Override
    public Integer move2Draft(List<GoodsHead> heads) {
        if (ObjectUtils.isEmpty(heads)) {
            return 0;
        }
        //amazon只有 刊登失败的数据才能移动到草稿箱
        List<GoodsHead> moveList = heads.stream().filter(e -> PublishStatus.move2DraftStatus(PlatformTypeEnum.AM.name())
                .contains(e.getPublishStatus())).collect(Collectors.toList());

        List<GoodsHead> noMoveList = heads.stream().filter(e -> !PublishStatus.move2DraftStatus(PlatformTypeEnum.AM.name())
                .contains(e.getPublishStatus())).collect(Collectors.toList());
        Integer sum = 0;
        for (GoodsHead head : moveList) {
            listingLogService.insertSuccessListingLog("Listing由[" + PublishStatus.getPublishStatusName(head.getPublishStatus()) + "]移至[草稿]",
                    StringUtils.isBlank(head.getUpdateBy()) ? head.getCreateBy() : head.getUpdateBy(), head.getId());
            head.setPublishStatus(DRAFT.getType());
            sum += goodsHeadService.clearPlatformGoodId(head);

            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.MOVE_DRAFT), GoodsTaskSubStatusEnum.NORAML, "");
        }
        noMoveList.forEach(no -> {
            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(no.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.MOVE_DRAFT), GoodsTaskSubStatusEnum.NORAML, "");
        });
        return sum;
    }

    /**
     * 查找关键词 amazon 标题  属性  描述
     * 此处需要筛选出来的链接是该店铺所有的链接
     *
     * @param userId
     * @param keyWord
     * @param shopCodeList
     */
    @Override
    public void findKeyword(Long userId, String keyWord, List<String> shopCodeList) {
        if (ObjUtil.isEmpty(shopCodeList) || ObjUtil.isEmpty(keyWord)) {
            return;
        }
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setShopCodes(shopCodeList);
        //任务总数
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadList(goodsHead);
        findKeywordPoolConfig.execute(() -> {
            GoodsTask goodsTask = goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.FIND_KEYWORD,
                    goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList()), String.valueOf(userId));

            for (GoodsHead head : goodsHeadList) {
                try {
                    //检测标题
                    if (StringUtils.checkParticipleHandler(head.getTitle().toLowerCase(), keyWord.toLowerCase())) {
                        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.FIND_KEYWORD), GoodsTaskSubStatusEnum.NORAML, "");
                        listingLogService.insertSuccessListingLog("标题中包含关键词:" + keyWord, String.valueOf(userId), head.getId());
                        continue;
                    }
                    //检测属性
                    List<ListingAmazonAttributeLine> attributeLineList = listingAmazonAttributeLineService.selectByGoodsId(goodsHead.getId());
                    if (ObjUtil.isEmpty(attributeLineList)) {
                        continue;
                    }
                    if (attributeLineList.stream().anyMatch(e -> StringUtils.checkParticipleHandler(e.getTableValue().toLowerCase(), keyWord.toLowerCase()))) {
                        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.FIND_KEYWORD), GoodsTaskSubStatusEnum.NORAML, "");
                        listingLogService.insertSuccessListingLog("属性中包含关键词:" + keyWord, String.valueOf(userId), head.getId());
                        continue;
                    }
                    //检测描述
                    GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsHead.getId());
                    if (ObjUtil.isEmpty(goodsDescription)) {
                        continue;
                    }
                    if (StringUtils.checkParticipleHandler(goodsDescription.getDetailDescription().toLowerCase(), keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(goodsDescription.getItemDescription1().toLowerCase(), keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(goodsDescription.getItemDescription2().toLowerCase(), keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(goodsDescription.getItemDescription3().toLowerCase(), keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(goodsDescription.getItemDescription4().toLowerCase(), keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(goodsDescription.getItemDescription5().toLowerCase(), keyWord.toLowerCase())
                    ) {
                        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.FIND_KEYWORD), GoodsTaskSubStatusEnum.NORAML, "");
                        listingLogService.insertSuccessListingLog("描述中包含关键词:" + keyWord, String.valueOf(userId), head.getId());
                    }
                } catch (Exception e) {
                    log.error("查找关键词失败,listingId:{}", head.getId(), e);
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.FIND_KEYWORD), GoodsTaskSubStatusEnum.ERROR, String.valueOf(e.getMessage()));

                }

            }
            //结束任务
            if (ObjUtil.isNotEmpty(goodsTask.getId())) {
                goodsTaskService.updateGoodsTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo(), goodsTask.getId());
            }

        });
    }

    @Override
    public void batchUpdatePrice(BatchListingDTO dto) {

        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        String tag = dto.getTag();
        if (Objects.equals("lostCart",tag)){
           checkExistVC(goodsIds);
        }
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_UPDATE_PRICE, goodsIds, String.valueOf(userId));
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        Map<Integer, ListingPromotion> listingPromotionMap = listingPromotionService.selectListingPromotionByHeadIdList(goodsIds).stream().collect(Collectors.toMap(ListingPromotion::getHeadId, e -> e));
        for (ListingDTO listing : listings) {
            try {
                //head
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                if (ObjUtil.isNotEmpty(goodsHead.getShopCode()) && goodsHead.getShopCode().contains("VC")) {
                    throw new BusinessException("Amazon vc类型链接暂时不支持修改");
                }
                ListingPromotion listingPromotion = listingPromotionMap.get(listing.getId());
                String price = null;
                String listPrice = listingAmazonAttributeLineV2Service.getValueByPropNodePath(goodsHead.getId(), "list_price.value");   
                if (listingPromotion == null) {
                    //无促销数据,直接使用刊登价作为结算价计算   
                    price = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), listing.getStandardPrice(), goodsHead.getPublishType())
                            .headId(goodsHead.getId())
                            .siteCode(goodsHead.getSiteCode())
                            .onlineTime(goodsHead.getOnlineTime())
                            .oldPrice(goodsHead.getStandardPrice())
                            .isOnline(StrUtil.isNotBlank(goodsHead.getPlatformGoodsId()))
                            .listPrice(listPrice)
                            .build());
                } else {
                    //Amazon 计算结算价
                    BigDecimal sellerPrice = commonInfoBiz.getSellerPrice(listing.getStandardPrice(), listingPromotion);
                    //价格校验
                    price = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), sellerPrice.toPlainString(), goodsHead.getPublishType())
                            .headId(goodsHead.getId())
                            .siteCode(goodsHead.getSiteCode())
                            .onlineTime(goodsHead.getOnlineTime())
                            .oldPrice(goodsHead.getStandardPrice())
                            .isOnline(StrUtil.isNotBlank(goodsHead.getPlatformGoodsId()))
                            .listPrice(listPrice)
                            .build());
                }
                goodsHead.setStandardPrice(listing.getStandardPrice());
                goodsHead.setSettlementPrice(Convert.toBigDecimal(price));
                goodsHeadService.updateListingGoodsHead(goodsHead);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && !PublishStatus.getUnderWayStatus().contains(goodsHead.getPublishStatus())
                        && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Arrays.asList(ListingModuleType.INVENTORY.name(), ListingModuleType.PRICE.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    throw e;
                }
                log.error("批量修改listing价格和库存失败,listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        redLinePriceTodoService.updateStatusByHeadIdList(goodsIds, "1");


        threadPoolForMonitorManager.getThreadPoolExecutor("eidtPoolConfig").execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });

        //处理待办的状态
        loseCartTodoService.confirmByGoodIds(goodsIds, userId);
        inventoryLowTodoService.confirmByGoodIds(goodsIds, userId);
    }

    private void checkExistVC(List<Integer> goodsIds) {
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0]));
        List<GoodsHead> vcList = goodsHeadList.stream().filter(goodsHead -> PublishType.VCDF.getType().equals(goodsHead.getPublishType()) || PublishType.VCPO.getType().equals(goodsHead.getPublishType())).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(vcList)){
            throw new BusinessException("批量停售失败,VCPO/VCDF商品不能批量更新价格!");
        }

    }

    @Override
    public void editAttribute(ListingEditDTO listingEditDTO) {
        // 头部基础数据
        GoodsHead dbHead = goodsHeadService.selectListingGoodsHeadById(listingEditDTO.getGoodsHeadId());
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setId(listingEditDTO.getGoodsHeadId());
        String publishStatus1 = listingEditDTO.getPublishStatus();
        //状态流转
        if (StringUtils.isNotEmpty(publishStatus1)) {
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(Integer.valueOf(publishStatus1), PlatformTypeEnum.AM.name()));
        } else {
            Integer publishStatus = dbHead.getPublishStatus();
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(publishStatus, PlatformTypeEnum.AM.name()));
        }
        List<ListingAmazonAttributeLine> attributeTable = listingInfoBiz.getAttributeTable(listingEditDTO);
        violateWordBiz.checkViolateWord(true,null, attributeTable, goodsHead);
        saveAmazonAttributeLine(listingEditDTO, attributeTable);

    }

    @Override
    public void syncTemuListingInfo(List<TemuGoodsHead> temuGoodsHeads, String userId) {

    }

    @Override
    public void batchUpdateVideo(BatchListingDTO dto) {

    }

    @Override
    public void excludeListing(List<Integer> idList, Map<String, Object> extendMap, Long userId) {
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(idList.toArray(new Integer[0]));

        Map<String, List<GoodsHead>> goodsHeadsMap = goodsHeads.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));

        String userIds = configService.selectConfigByKey("vc.inventory.black.operator");

        Boolean vcFlag = ObjectUtil.isNotEmpty(userIds) && Arrays.asList(userIds.split(",")).contains(String.valueOf(userId));


        //不是vc库存黑名单操作人员 不允许添加VC链接库存黑名单
        if (!vcFlag &&
                goodsHeads.stream()
                        .map(GoodsHead::getPublishType)
                        .anyMatch(type -> ObjectUtil.equals(type, PublishType.VCDF.getType())
                                || ObjectUtil.equals(type, PublishType.VCPO.getType()))
        ) {
            throw new BusinessException("暂时不允许添加VC链接到库存黑名单中,请联系管理员!");
        }


        goodsHeadsMap.forEach((shopCode, goodList) -> {
            List<Integer> goodsId = goodList.stream().map(GoodsHead::getId).collect(Collectors.toList());

            Map<Integer, GoodsHead> goodsHeadMap = goodList.stream().collect(Collectors.toMap(GoodsHead::getId, Function.identity()));

            List<InventoryUpdateBlack> inventoryUpdateBlacks = inventoryUpdateBlackService.selectInventoryUpdateBlackListByShopCode(shopCode);
            if (ObjUtil.isNotEmpty(inventoryUpdateBlacks)) {
                //从goodsId移除inventoryUpdateBlackList存在的主键id
                Set<Integer> blackListedGoodIds = inventoryUpdateBlacks.stream()
                        .map(i -> i.getHeadId().intValue())
                        .collect(Collectors.toSet());
                goodsId.removeIf(blackListedGoodIds::contains);
            }

            if (ObjUtil.isNotEmpty(goodsId)) {
                List<InventoryUpdateBlack> insertList = new ArrayList<>();
                for (Integer goodId : goodsId) {
                    GoodsHead goodsHead = goodsHeadMap.get(goodId);
                    InventoryUpdateBlack add = new InventoryUpdateBlack();
                    add.setPlatform(goodsHead.getPlatform());
                    add.setSite(goodsHead.getSiteCode());
                    add.setShopCode(shopCode);
                    add.setHeadId(Long.valueOf(goodId));
                    add.setPlatformGoodsCode(goodsHead.getPlatformGoodsCode());
                    add.setDelFlag(0L);
                    add.setCreateTime(DateUtils.getNowDate());
                    add.setUpdateTime(DateUtils.getNowDate());
                    add.setCreateBy(String.valueOf(userId));
                    add.setUpdateBy(String.valueOf(userId));
                    insertList.add(add);
                }
                Lists.partition(insertList, 100).forEach(part -> inventoryUpdateBlackService.batchInsert(part));
                if (MapUtil.isEmpty(extendMap) || !extendMap.containsKey("stockZero") || Constants.YesOrNo.NO.equals(extendMap.get("stockZero"))) {
                    inventoryExcludeBiz.addListingLog(insertList, String.valueOf(userId));
                }
            }
            if (MapUtil.isNotEmpty(extendMap) && extendMap.containsKey("stockZero") && Constants.YesOrNo.YES.equals(extendMap.get("stockZero"))) {
                // Amazon 链接库存调0
                threadPoolForMonitorManager.getThreadPoolExecutor("eidtPoolConfig").execute(() -> {
                    if (shopCode.contains("VC")) {
                        amazonProductBiz.updateVCZeroStock(goodList, shopCode, userId);
                    } else {
                        for (GoodsHead head : goodList) {
                            amazonProductBiz.updateZeroStock(head, "stockZero");
                        }

                    }


                });
            }
        });
    }

    @Override
    public void delExcludeListing(List<Integer> idList, Long userId) {
        if (ObjUtil.isEmpty(idList)) {
            return;
        }
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(idList.toArray(new Integer[0]));
        if (ObjUtil.isEmpty(goodsHeads)) {
            return;
        }
        Map<String, List<GoodsHead>> goodsHeadsMap = goodsHeads.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));
        goodsHeadsMap.forEach((shopCode, goodList) -> {
            List<Long> headIds = goodList.stream().map(GoodsHead::getId).map(Integer::longValue).collect(Collectors.toList());
            List<InventoryUpdateBlack> inventoryUpdateBlacks = inventoryUpdateBlackService.selectInventoryUpdateBlackListByHeadIds(headIds, shopCode);
            for (InventoryUpdateBlack inventoryUpdateBlack : inventoryUpdateBlacks) {
                inventoryUpdateBlack.setDelFlag(1L);
                inventoryUpdateBlackService.updateInventoryUpdateBlack(inventoryUpdateBlack);
                listingLogService.insertSuccessListingLog("从库存更新黑名单移除", String.valueOf(userId), Math.toIntExact(inventoryUpdateBlack.getHeadId()));
            }
        });
    }

    @Override
    public void listingCopy(ListingCopyDTO listingCopyDTO) {

    }

    @Override
    public List<String> comparativeData(Boolean savaFlag,ItemDTO itemDTO) {
        if (ObjUtil.isEmpty(itemDTO)) {
            return null;
        }
        GoodsHead goodsHead = itemDTO.getGoodsHead();
        if (ObjUtil.isEmpty(goodsHead)) {
            return null;
        }
        //smc新保存的数据不存在
        GoodsHead goodsHeadOld = itemDTO.getItemDTOOld().getGoodsHead();
        if (ObjUtil.isEmpty(goodsHeadOld)) {
            return null;
        }

        itemDTO.setModuleType(new ArrayList<>());
        List<String> comparativeMoudlList = new ArrayList<>();
        comparativeMoudlList.add(ListingModuleType.TITLE.name());
        comparativeMoudlList.add(ListingModuleType.IMAGE.name());
        comparativeMoudlList.add(ListingModuleType.DESCRIPTION.name());
        comparativeMoudlList.add(ListingModuleType.AMAZON_FIVE.name());
        comparativeMoudlList.add(ListingModuleType.ATTRIBUTE.name());
//                comparativeMoudlList.add(ListingModuleType.PRICE.name());
        comparativeMoudlList.add(ListingModuleType.INVENTORY.name());

        for (String mould : comparativeMoudlList) {
            try {
                ListingUpdateModuleResolver impl = handlerListingUpdateModuleComposite.getListingUpdateResolverByModule(PlatformTypeEnum.AM.name() + mould);
                impl.compareData(itemDTO);
            } catch (Exception e) {
                log.error("平台为:{},店铺为:{},头表id:{},webhook对比数据异常", goodsHead.getPlatform(), goodsHead.getShopCode(), goodsHead.getId(), e);
            }
        }
        List<String> updateMoudleList = itemDTO.getModuleType();
        if ( ObjUtil.isEmpty(updateMoudleList) ) {
            return null;
        }

        intoUpdateRecord(goodsHead, updateMoudleList,1);

        return updateMoudleList;
    }

    @Override
    public void batchUpdateShipping(BatchListingDTO dto) {

    }

    @Override
    public void batchUpdatePolicy(BatchListingDTO dto) {

    }

    @Override
    public void batchUpdateDescriptionTemplate(BatchListingDTO dto) {

    }

    public void updatePriceToApi(AmazonListingUpdateFeedVO updateFeedVO) {
        String post = HttpUtils.post(updatePriceUrl, JSON.toJSONString(updateFeedVO));
        log.info("修改价格,param:{},返回结果:{}", updateFeedVO, post);

        if (post.contains("html")) {
            throw new RuntimeException("上传失败,API服务异常,请联系管理员");
        }
        JSONObject jsonObject = JSON.parseObject(post);
        if (Objects.isNull(jsonObject)) {
            throw new RuntimeException("上传失败,API服务异常,请联系管理员.");
        }
        if (Objects.equals(jsonObject.getString("code"), "500")) {
            throw new RuntimeException("上传失败," + jsonObject.getString("msg"));
        }
    }

    @Override
    public void saveBatchTempListingDTO(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        String taskName = listings.get(0).getTaskName();
        boolean save = StrUtil.isEmpty(taskName);
        if(dto.getShopCode().contains("VC")) {
            throw new BusinessException("VC店铺不支持保存为草稿");
        }
        //组装对应的数据
        String siteCode = shopService.selectSiteCodeByShopCode(dto.getShopCode());
        List<ItemDTO> itemDTOList = new ArrayList<>();
        for (ListingDTO listing : listings) {
            GoodsHead goodHead = getGoodHead(dto, siteCode, listing);
            goodHead.setTaskName(taskName);
            goodHead.setDelFlag(3);
            String goodsCode = listing.getGoodsCode();
            if (ObjUtil.isNotEmpty(goodsCode)) {
                //去除KX1ALT00100U1(0)保留KX1ALT00100U1
                listing.setSortPdmGoodsCode(listing.getGoodsCode());
                listing.setGoodsCode(goodsCode.replaceAll("\\(.*\\)", ""));
            }
            if (!save) {
                goodHead.setId(listing.getId());
            }

            GoodsDescription goodsDescription = getGoodsDescription(listing);
            if (Objects.nonNull(goodsDescription.getDetailDescription())) {
                goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));
            }

            listing.setSiteCode(siteCode);
            listing.setCategoryId(goodHead.getCategoryId());

            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(goodHead);
            itemDTO.setGoodsResourceList(getResourceList(dto, listing));
            itemDTO.setGoodDescription(goodsDescription);
            itemDTO.setGoodsSpecification(getGoodsSpecification(listing, userId));
            itemDTO.setGoodsAttributeLineList(getListingAmazonAttributeLinesV2(dto, listing));
            itemDTO.setUserId(userId);
            itemDTOList.add(itemDTO);
        }

        Map<String, Set<String>> violateWord = new HashMap<>();
        List<String> checkPrice = new LinkedList<>();
        for (ItemDTO itemDTO : itemDTOList) {
            try {
                GoodsHead goodsHead = itemDTO.getGoodsHead();
                //校验价格
                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), dto.getPublishType(), goodsHead.getSiteCode(),null,goodsHead.getStandardPrice());
                goodsHead.setStandardPrice(price);
                goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
            } catch (Exception e) {
                checkPrice.add(e.getMessage());
            }
        }

        if (ObjUtil.isNotEmpty(violateWord) || CollectionUtil.isNotEmpty(checkPrice)) {
            assembleWordBefore(violateWord, checkPrice);
        }


        //创建任务

        GoodsTask goodsTask = new GoodsTask();
        if (save) {
            taskName = dto.getShopCode() + ((int) Math.floor(Math.random() * (9999 - 1000 + 1) + 1000)) + System.currentTimeMillis();
            goodsTask.setTaskName(taskName);
            goodsTask.setTaskType(GoodsTaskTypeEnum.BATCH_TEMP_SAVE.getInfo());
            goodsTask.setTaskNum(listings.size());
            goodsTask.setTaskStatus("进行中");
            goodsTask.setSuccessNum(0);
            goodsTask.setPlatform(PlatformTypeEnum.AM.name());
            goodsTask.setCreateBy(String.valueOf(userId));
            goodsTask.setRemark("手动保存");
            goodsTaskService.insertGoodsTask(goodsTask);
        }

        batchSavePool.execute(() -> {
            //保存任务信息
            saveAndUpdateInfo(dto.getShopCode(), itemDTOList, String.valueOf(userId), false);

            //任务详情记录
            if (save) {
                for (ItemDTO itemDTO : itemDTOList) {
                    GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
                    goodsTaskInfo.setTaskId(goodsTask.getId());
                    goodsTaskInfo.setPlatform(PlatformTypeEnum.AM.name());
                    goodsTaskInfo.setCreateBy(String.valueOf(userId));
                    goodsTaskInfo.setPdmGoodsCode(itemDTO.getGoodsHead().getPdmGoodsCode());
                    goodsTaskInfo.setStatus("0");
                    goodsTaskInfo.setListingHeadId(String.valueOf(itemDTO.getGoodsHead().getId()));
                    goodsTaskInfoService.insertGoodsTaskInfo(goodsTaskInfo);
                }
            }
            GoodsTask updateTask = new GoodsTask();
            updateTask.setId(goodsTask.getId());
            updateTask.setTaskStatus("已完成");
            updateTask.setSuccessNum(itemDTOList.size());
            goodsTaskService.updateGoodsTask(updateTask);
        });

    }


    @Override
    public void updateBatchListingDTO(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        dto.setFulfillmentLatency(null);
        String tempFlag = dto.getTempFlag();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_EDIT, goodsIds, String.valueOf(userId));

        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0]))
                .stream()
                .collect(Collectors.toMap(GoodsHead::getId, e -> e));

        Map<Integer, List<GoodsDescription>> descMap = goodsDescriptionService.selectDescriptionListByGoodsIdList(goodsIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsDescription::getGoodsId));

        Map<Integer, List<GoodsSpecification>> specificationMap = goodsSpecificationService.selectSpecificationListByGoodsIds(goodsIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsSpecification::getGoodsId));


        //组装对应的数据
        List<ItemDTO> itemDTOList = new ArrayList<>();
        for (ListingDTO listing : listings) {
            GoodsHead goodsHead = headMap.get(listing.getId());
            GoodsDescription goodsDescription = descMap.get(listing.getId()).get(0);
            GoodsSpecification goodsSpecification = specificationMap.get(listing.getId()).get(0);

            GoodsHead updateHead = new GoodsHead();
            updateHead.setSiteCode(goodsHead.getSiteCode());
            updateHead.setCreateBy(goodsHead.getCreateBy());
            updateHead.setCategoryId(goodsHead.getCategoryId());
            updateHead.setPlatform(goodsHead.getPlatform());
            updateHead.setId(listing.getId());
            updateHead.setStockOnSalesQty(listing.getStockOnSalesQty());
            updateHead.setTitle(listing.getTitle());
            if (StringUtils.isNotEmpty(updateHead.getTitle())) {
                updateHead.setTitle(updateHead.getTitle().replaceAll("“", "\""));
                updateHead.setTitle(updateHead.getTitle().replaceAll("’", "'"));
            }
            //临时保存不改状态
            if (!ObjUtil.equals(tempFlag, "true")) {
                updateHead.setPublishStatus(PublishStatus.getStatusByEdit(goodsHead.getPublishStatus(), PlatformTypeEnum.AM.name()));
            }
            updateHead.setStandardPrice(StrUtil.isBlank(listing.getStandardPrice()) ? "0" : listing.getStandardPrice());

            getGoodsDescription(listing, goodsDescription);
            if (Objects.nonNull(goodsDescription.getDetailDescription())) {
                goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));
            }

            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(updateHead);
            itemDTO.setGoodsResourceList(getResourceList(dto, listing));
            itemDTO.setGoodDescription(goodsDescription);
            itemDTO.setGoodsSpecification(getGoodsSpecification(listing, goodsSpecification));
            itemDTO.setUserId(userId);
            itemDTOList.add(itemDTO);
        }

        Map<String, Set<String>> violateWord = new HashMap<>();
        List<String> checkPrice = new LinkedList<>();
        for (ItemDTO itemDTO : itemDTOList) {
            StringBuilder errorStr = new StringBuilder();
            GoodsHead goodsHead = itemDTO.getGoodsHead();
            try {
                violateWordBiz.checkViolateWord(true,itemDTO.getGoodDescription(), itemDTO.getGoodsAttributeLineList(), itemDTO.getGoodsHead());
            } catch (Exception e) {
                assembleWord(violateWord, e.getMessage());
                errorStr.append(e.getMessage());
            }
            try {
                //校验价格
                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(
                        goodsHead.getId(),
                        headMap.get(goodsHead.getId()).getPdmGoodsCode(),
                        goodsHead.getStandardPrice(),
                        headMap.get(goodsHead.getId()).getPublishType(), goodsHead.getSiteCode(),goodsHead.getOnlineTime(),goodsHead.getStandardPrice());
                goodsHead.setStandardPrice(price);
                goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
            } catch (Exception e) {
                checkPrice.add(e.getMessage());
                errorStr.append(e.getMessage());
            }

            if (StrUtil.isNotEmpty(errorStr)) {
                //有错误消息 该listing则是修改失败
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT), GoodsTaskSubStatusEnum.ERROR, String.valueOf(errorStr));
            }
            if (StrUtil.isBlank(errorStr) && !PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                //该listing不是更新中  并且 没有错误消息  则修改完成
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT), GoodsTaskSubStatusEnum.NORAML, "");
            }
        }

        if (ObjUtil.isNotEmpty(violateWord) || CollectionUtil.isNotEmpty(checkPrice)) {
            assembleWordBefore(violateWord, checkPrice);
        }

        //保存任务信息
        saveAndUpdateInfo(dto.getShopCode(), itemDTOList, String.valueOf(userId), false);
    }

    private GoodsHead buildGoodsHead(AmazonAllListingPullDTO reportDTO, GoodsHead goodsHead, JSONObject frontDetailResult, AmazonSummariesDTO summaries, MappingGoods mappingGoods, Date createdDate) throws ParseException {
        JSONObject attributes = getAttributes(frontDetailResult);
        goodsHead.setTitle(ObjUtil.defaultIfNull(getAttrValue(attributes, "item_name"), summaries.getItemname()));
        goodsHead.setBrandCode(ObjUtil.defaultIfNull(getAttrValue(attributes, "brand"), summaries.getBrand()));

        String sellerSku = goodsHead.getPlatformGoodsCode();
        if (mappingGoods == null) {
            mappingGoods = mappingGoodsService.selectMappingGoodsByPlatformSku(sellerSku, goodsHead.getShopCode());
        }
        if (!Objects.isNull(mappingGoods)) {
//            String fnSku = mappingGoods.getFnSku();
            String userId = mappingGoods.getOperators();
//            goodsHead.setFnSku(fnSku);
            goodsHead.setCreateBy(userId);
            if (ObjUtil.isEmpty(goodsHead.getPdmGoodsCode())) {
                goodsHead.setPdmGoodsCode(mappingGoods.getGoodsCode());
            }
        }

        if (Objects.isNull(reportDTO)) {
            return goodsHead;
        }
//        goodsHead.setPublishType( Objects.equals(reportDTO.getFulfillmentChannel(),"AMAZON_NA") ? 1 : 0 );
        goodsHead.setPublishType(getPublishType(frontDetailResult, reportDTO, goodsHead));
        Integer publishStatus = PublishStatus.getPublishStatus(reportDTO.getStatus());
        // goodsHead.setStockOnSalesQty(StringUtils.isEmpty(reportDTO.getQuantity()) ? new BigDecimal(0) : new BigDecimal(reportDTO.getQuantity()));
        goodsHead.setStockOnSalesQty(getQuantity(frontDetailResult, reportDTO, goodsHead));
        // 将亚马逊给的错误保存起来
        ExceptionUtil.sandbox(() -> {setIssues(frontDetailResult, goodsHead);});
        if(goodsHead.getId() == null) {
            if (goodsHead.getShopCode().contains("VC")) {
                publishStatus = PublishStatus.OFF_SALE.getType();
                // VC使用属性的costPrice
                goodsHead.setStandardPrice(getCostPrice(frontDetailResult, reportDTO));
            }
            // SC链接使用报告的价格
            else {
                goodsHead.setStandardPrice(getB2cPrice(frontDetailResult, reportDTO));
            }
        }else {
            if (goodsHead.getShopCode().contains("VC")) {
                List<ListingLabel> cartList = listingLabelService.selectHeadIdByListingPerformance(Arrays.asList(goodsHead.getId()), "有购物车", null);
                if (CollectionUtil.isNotEmpty(cartList)) {
                    publishStatus = PublishStatus.SALEING.getType();
                } else {
                    publishStatus = PublishStatus.OFF_SALE.getType();
                }
                // VC使用属性的costPrice
                goodsHead.setStandardPrice(getCostPrice(frontDetailResult, reportDTO));
            } else {
                goodsHead.setStandardPrice(getB2cPrice(frontDetailResult, reportDTO));
            }
        }
        goodsHead.setPublishStatus(publishStatus);

        String openDate = reportDTO.getOpenDate();
        if (ObjUtil.isEmpty(goodsHead.getOnlineTime()) && StringUtils.isNotBlank(openDate) && openDate.split(" ").length == 3) {
            Date date = DateUtils.convertTimeZoneToChinaStandardTime(openDate);
            goodsHead.setOnlineTime(ObjUtil.isEmpty(date) ? null : date);
        }
        if (ObjUtil.isEmpty(goodsHead.getOnlineTime()) && ObjUtil.isNotEmpty(createdDate)){
            if (goodsHead.getShopCode().contains("VC")){
                goodsHead.setOnlineTime(createdDate);
            }
        }
        return goodsHead;
    }

    private void setIssues(JSONObject frontDetailResult, GoodsHead goodsHead) {
        if(!frontDetailResult.containsKey("issues")) {
            goodsHead.setHasIssues(false);
            return;
        }
        JSONArray issues = frontDetailResult.getJSONArray("issues");
        for (int i = 0; i < issues.size(); i++) {
            JSONObject issue = issues.getJSONObject(i);
            if (issue.containsKey("severity") && issue.getString("severity").equals("ERROR")) {
                goodsHead.setHasIssues(true);
                goodsHead.setRemarkJSON("issues", issues.toJSONString());
                return;
            }
        }
    }

    private Integer getPublishType(JSONObject frontDetailResult, AmazonAllListingPullDTO reportDTO, GoodsHead goodsHead) {
        boolean isVC = goodsHead.getShopCode().contains("VC");
        // VC链接接口不会返回库存，使用BI报告抓取的库存,FBA也不会返回库存
        if (isVC) {
            return reportDTO.getPublishType();
        }
        try {
            // SC链接接口，先判断最外层的fulfillmentAvailability是否存在
            if (frontDetailResult.containsKey("fulfillmentAvailability")) {
                JSONArray fulfillmentAvailability = frontDetailResult.getJSONArray("fulfillmentAvailability");
                if (fulfillmentAvailability.size() > 0) {
                    JSONObject fulfillmentAvailabilityObj = fulfillmentAvailability.getJSONObject(0);
                    // fulfillmentChannelCode 为 DEFAULT 时，取 quantity
                    if (fulfillmentAvailabilityObj.containsKey("fulfillmentChannelCode")) {
                        String fulfillmentChannelCode = fulfillmentAvailabilityObj.getString("fulfillmentChannelCode");
                        if ("DEFAULT".equals(fulfillmentChannelCode)) {
                            return PublishType.FBM.getType();
                        } else if (Arrays.asList("AMAZON_NA", "AMAZON_EU", "AMAZON_JP").contains(fulfillmentChannelCode)) {
                            return PublishType.FBA.getType();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取刊登类型失败,sku:{}", goodsHead.getPlatformGoodsCode(), e);
        }
        return reportDTO.getPublishType();
    }

    private String getB2cPrice(JSONObject frontDetailResult, AmazonAllListingPullDTO reportDTO) {
        String price = null;
        if (frontDetailResult.containsKey("offers")) {
            try {
                JSONArray offersArray = frontDetailResult.getJSONArray("offers");
                if (CollUtil.isNotEmpty(offersArray)) {
                    // 取offerType为B2C的price  
                for (int i = 0; i < offersArray.size(); i++) {
                    JSONObject offerObj = offersArray.getJSONObject(i);
                    if (offerObj.getString("offerType").equals("B2C")) {
                            JSONObject priceObj = offerObj.getJSONObject("price");
                            price = priceObj.getString("amount");
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取B2C价格失败,sku:{}", reportDTO.getSellerSku(), e);
            }
        }
        if (StrUtil.isNotEmpty(price)) {
            return price;
        }
        // 如果offers中没有B2C的price，从attributes中取
        JSONObject attributes = getAttributes(frontDetailResult);
        if (attributes.containsKey("purchasable_offer")) {
            try {
                JSONArray purchasableOfferArray = attributes.getJSONArray("purchasable_offer");
                if (CollUtil.isNotEmpty(purchasableOfferArray)) {
                    // 找到audience为ALL的our_price
                    for (int i = 0; i < purchasableOfferArray.size(); i++) {
                        JSONObject purchasableOfferObj = purchasableOfferArray.getJSONObject(i);
                        if (purchasableOfferObj.getString("audience").equals("ALL") && purchasableOfferObj.containsKey("our_price")) {
                            JSONObject ourPriceObj = purchasableOfferObj.getJSONArray("our_price").getJSONObject(0);
                            price = ourPriceObj.getJSONArray("schedule").getJSONObject(0).getString("value_with_tax");
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取B2C价格失败,sku:{}", reportDTO.getSellerSku(), e);
            }
        }
        if (StrUtil.isNotEmpty(price)) {
            return price;
        }
        return StringUtils.isEmpty(reportDTO.getPrice()) ? "0" : reportDTO.getPrice();
    }

    private String getCostPrice(JSONObject frontDetailResult, AmazonAllListingPullDTO reportDTO) {
        // 判断是否有procurement,部分sku会存在两个不一样的costPrice
        if (frontDetailResult.containsKey("procurement")) {
            JSONArray procurementArray = frontDetailResult.getJSONArray("procurement");
            if (CollUtil.isNotEmpty(procurementArray)) {
                JSONObject jsonObject = procurementArray.getJSONObject(0);
                if (jsonObject.containsKey("costPrice")) {
                    JSONObject costPriceObj = jsonObject.getJSONObject("costPrice");
                    // amcount 是否大于0
                    if (costPriceObj.containsKey("amount")) {
                        return costPriceObj.getString("amount");
                    }
                }
            }
        }

        JSONObject attributes = getAttributes(frontDetailResult);
        if(attributes.containsKey("cost_price")) {
            JSONArray costPriceArray = attributes.getJSONArray("cost_price");
            JSONObject costPriceObj = costPriceArray.getJSONObject(0);
            if (costPriceObj.containsKey("value")) {
                return costPriceObj.getString("value");
            }
        }
        return StringUtils.isEmpty(reportDTO.getPrice()) ? "0" : reportDTO.getPrice();
    }

    private BigDecimal getQuantity(JSONObject frontDetailResult, AmazonAllListingPullDTO reportDTO, GoodsHead goodsHead) {
        boolean isVC = goodsHead.getShopCode().contains("VC");
        BigDecimal quantity = null;
        // VC链接接口不会返回库存，使用BI报告抓取的库存,FBA也不会返回库存
        if (isVC || goodsHead.getPublishType().equals(PublishType.FBA.getType())) {
            quantity = StringUtils.isEmpty(reportDTO.getQuantity()) ? new BigDecimal(0) : new BigDecimal(reportDTO.getQuantity());
        }
        if (quantity != null) {
            return quantity;
        }
        try {
            // SC链接接口会返回库存，先判断最外层的fulfillmentAvailability是否存在，如果不存在就去attributes中取
            if (frontDetailResult.containsKey("fulfillmentAvailability")) {
                JSONArray fulfillmentAvailability = frontDetailResult.getJSONArray("fulfillmentAvailability");
                if (fulfillmentAvailability.size() > 0) {
                    JSONObject fulfillmentAvailabilityObj = fulfillmentAvailability.getJSONObject(0);
                    // fulfillmentChannelCode 为 DEFAULT 时，取 quantity
                    if (fulfillmentAvailabilityObj.containsKey("quantity") && fulfillmentAvailabilityObj.containsKey("fulfillmentChannelCode")
                            && fulfillmentAvailabilityObj.getString("fulfillmentChannelCode").equals("DEFAULT")) {
                        Object quantityObj = fulfillmentAvailabilityObj.get("quantity");
                        quantity = quantityObj == null ? null : Convert.toBigDecimal(quantityObj);
                    }
                }
            }
            if (quantity == null) {
                // 从attributes中取
                JSONObject attributes = getAttributes(frontDetailResult);
                if (attributes.containsKey("fulfillment_availability")) {
                    JSONArray fulfillmentAvailability = attributes.getJSONArray("fulfillment_availability");
                    if (fulfillmentAvailability.size() > 0) {
                        JSONObject fulfillmentAvailabilityObj = fulfillmentAvailability.getJSONObject(0);
                        // fulfillment_channel_code 为DEFAULT时，取 quantity
                        if (fulfillmentAvailabilityObj.containsKey("quantity") && fulfillmentAvailabilityObj.containsKey("fulfillment_channel_code")
                                && fulfillmentAvailabilityObj.getString("fulfillment_channel_code").equals("DEFAULT")) {
                            Object quantityObj = fulfillmentAvailabilityObj.get("quantity");
                            quantity = quantityObj == null ? null : Convert.toBigDecimal(quantityObj);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取库存失败,sku:{}", goodsHead.getPlatformGoodsCode(), e);
        }
        if (quantity == null) {
            quantity = StringUtils.isEmpty(reportDTO.getQuantity()) ? new BigDecimal(0) : new BigDecimal(reportDTO.getQuantity());
        }
        return quantity;
    }


    public void buildItemDTO(GoodsHead goodsHead, JSONObject itemInfo, AjaxResult result, List<ItemDTO> saveOrUpdateList) {
        JSONObject attributes = getAttributes(itemInfo);
        AmazonSummariesDTO summaries = getSummaries(itemInfo);

        //图片
        List<GoodsResource> imagesList = new ArrayList<>();
        getAllGoodImages(itemInfo, imagesList);
        
        //描述
        GoodsDescription goodDesc = getGoodDesc(attributes, goodsHead);

        //规格
        GoodsSpecification goodsSpecification = getGoodsSpecification(itemInfo, attributes, summaries);

        //旧版属性
        List<ListingAmazonAttributeLine> attributeLines = null;
        try {
            attributeLines = new ArrayList<>();
            getAmAttributeLines(attributes, goodsHead, attributeLines, summaries);

            if (ObjUtil.isNotEmpty(goodsHead.getFnSku())) {
                ListingAmazonAttributeLine attributeLine = new ListingAmazonAttributeLine();
                attributeLine.setPlatform(PlatformTypeEnum.AM.name());
                attributeLine.setCategoryId(goodsHead.getCategoryId());
                attributeLine.setTableValue(goodsHead.getFnSku());
                attributeLine.setTableName(AmazonAttributeEnum.FN_SKU.getInfo());
                attributeLine.setTableType(4);
                attributeLines.add(attributeLine);
            }
        } catch (Exception e) {
            log.error("获取旧版属性失败,sku:{}",goodsHead.getPlatformGoodsCode(), e);
        }

        //新版属性
        List<ListingAmazonAttributeLineV2> amazonAttributeLinesV2 = amazonProductBiz.getAmAttributeLinesV2(attributes, goodsHead);


        ItemDTO itemDTO = new ItemDTO();
        itemDTO.setGoodsHead(goodsHead);
        itemDTO.setGoodsResourceList(imagesList);
        itemDTO.setGoodDescription(goodDesc);
        itemDTO.setGoodsSpecification(goodsSpecification);
        itemDTO.setGoodsAttributeLineList(attributeLines);
        itemDTO.setAmazonAttributeLines(amazonAttributeLinesV2);
        itemDTO.setDeleteOldAttr(true);
        itemDTO.setAjaxResult(result);
        saveOrUpdateList.add(itemDTO);
    }

    // main_product_image_locator other_product_image_locator_1
    private void getAllGoodImages(JSONObject itemInfo, List<GoodsResource> imagesList) {
         // 主图
        getMainProductImage(itemInfo, imagesList);

        // 其他图
        getGoodImagesV2(itemInfo, imagesList);
    }

    private void getMainProductImage(JSONObject itemInfo, List<GoodsResource> imagesList) {
        JSONObject attributes = getAttributes(itemInfo);
        JSONArray mainProductImage = attributes.getJSONArray("main_product_image_locator");
        if (ObjUtil.isNotEmpty(mainProductImage)) {
            JSONObject jsonObject = mainProductImage.getJSONObject(0);
            String imageUrl = jsonObject.getString("media_location");
            if (StrUtil.isNotEmpty(imageUrl)) {
                GoodsResource goodsResource = new GoodsResource();
                goodsResource.setResourceUrl(imageUrl);
                goodsResource.setResourceName(imageUrl.substring(imageUrl.lastIndexOf("/") + 1));
                goodsResource.setResourceType("1");
                // other_product_image_locator_7
                goodsResource.setSortNumber(1);
                goodsResource.setDelFlag("0");
                goodsResource.setIsMain(1);
                imagesList.add(goodsResource);
            }
        }else {
            // 获取主图
            if (itemInfo.containsKey("images")) {
                try {
                    JSONObject jsonObject = itemInfo.getJSONArray("images").getJSONObject(0);
                    if (jsonObject.containsKey("images")) {
                        JSONArray jsonArray = jsonObject.getJSONArray("images");
                        Map<String, List<AmazonAttributeDTO.ImageDTO>> collect = jsonArray.stream().map(json -> JSON.parseObject(JSON.toJSONString(json), AmazonAttributeDTO.ImageDTO.class)).collect(Collectors.groupingBy(AmazonAttributeDTO.ImageDTO::getVariant));
                        for (String variantKey : collect.keySet()) {
                            List<AmazonAttributeDTO.ImageDTO> imageDTOS = collect.get(variantKey);

                            AmazonAttributeDTO.ImageDTO imageDTO = imageDTOS.stream().sorted(Comparator.comparingDouble(e -> -Double.parseDouble(e.getHeight()))).findFirst().orElse(null);
                            //
                            if (Objects.isNull(imageDTO)) {
                                imageDTO = imageDTOS.get(0);
                            }

                            String value = imageDTO.getLink();
                            String variant = imageDTO.getVariant();
                            if (Objects.equals(variant, "MAIN")) {
                                GoodsResource goodsResource = new GoodsResource();
                                goodsResource.setResourceUrl(value);
                                goodsResource.setResourceName(value.substring(value.lastIndexOf("/") + 1));
                                goodsResource.setResourceType("1");
                                goodsResource.setSortNumber(1);
                                goodsResource.setDelFlag("0");
                                goodsResource.setIsMain(1);
                                imagesList.add(goodsResource);
                            }
                        }
                    }
                } catch (Exception ex) {
                   log.error("获取主图失败,itemInfo:{}", itemInfo);
                }
            }
        }
    }

    private String getAttrValue(JSONObject attributes,String key) {
        AmazonAttributeDTO desc = getAttributeInfo(attributes, key);
        if (ObjUtil.isNotEmpty(desc) && StrUtil.isNotEmpty(desc.getValue())) {
            return desc.getValue();
        }
        return null;
    }



    private void getGoodImages(JSONObject itemInfo, List<GoodsResource> imagesList) {
        JSONArray jsonArray = JSON.parseObject(JSON.toJSONString(itemInfo.getJSONArray("images").get(0))).getJSONArray("images");
        if (jsonArray.size() == 0) {
            return;
        }
        Map<String, List<AmazonAttributeDTO.ImageDTO>> collect = jsonArray.stream().map(json -> JSON.parseObject(JSON.toJSONString(json), AmazonAttributeDTO.ImageDTO.class)).collect(Collectors.groupingBy(AmazonAttributeDTO.ImageDTO::getVariant));
        for (String variantKey : collect.keySet()) {
            List<AmazonAttributeDTO.ImageDTO> imageDTOS = collect.get(variantKey);
            //对于imageDTOS 优先取width=1600的图片,如果没有则取width=1200的图片,如果还没有则取width=75的图片
            //根据height排序,取最大的图片
//            AmazonAttributeDTO.ImageDTO imageDTO = imageDTOS.stream().sorted(Comparator.comparing(AmazonAttributeDTO.ImageDTO::getHeight)).findFirst().orElse(null);
            AmazonAttributeDTO.ImageDTO imageDTO = imageDTOS.stream().sorted(Comparator.comparingDouble(e -> -Double.parseDouble(e.getHeight()))).findFirst().orElse(null);
//
//            AmazonAttributeDTO.ImageDTO imageDTO = imageDTOS.stream().filter(image -> Objects.equals(image.getWidth(), "1600.0")).findFirst().orElse(null);
//            if (Objects.isNull(imageDTO)) {
//                imageDTO = imageDTOS.stream().filter(image -> Objects.equals(image.getWidth(), "1200.0")).findFirst().orElse(null);
//            }
//            if (Objects.isNull(imageDTO)) {
//                imageDTO = imageDTOS.stream().filter(image -> Objects.equals(image.getWidth(), "75.0")).findFirst().orElse(null);
//            }
            if (Objects.isNull(imageDTO)) {
                imageDTO = imageDTOS.get(0);
            }

            String value = imageDTO.getLink();
            String variant = imageDTO.getVariant();
            int sortNumber = 0;
            boolean main = Objects.equals(variant, "MAIN") ? true : false;
            if (!main) {
                sortNumber = Integer.parseInt(variant.substring(variant.length() - 1)) - 1;
            }

            GoodsResource goodsResource = new GoodsResource();
            goodsResource.setResourceUrl(value);
            goodsResource.setResourceName(value.substring(value.lastIndexOf("/") + 1));
            goodsResource.setResourceType("1");
            goodsResource.setSortNumber(main ? 1 : sortNumber);
            goodsResource.setDelFlag("0");
            goodsResource.setIsMain(main ? 1 : 0);
            imagesList.add(goodsResource);

        }
    }

    private GoodsDescription getGoodDesc(JSONObject attributes, GoodsHead goodsHead) {
        GoodsDescription goodsDescription = new GoodsDescription();
        List<AmazonAttributeDTO> point = JSON.parseArray(attributes.getString("bullet_point"), AmazonAttributeDTO.class);
        if (!CollectionUtils.isEmpty(point)) {
            goodsDescription.setItemDescription1(point.size() >= 1 ? point.get(0).getValue() : null);
            goodsDescription.setItemDescription2(point.size() >= 2 ? point.get(1).getValue() : null);
            goodsDescription.setItemDescription3(point.size() >= 3 ? point.get(2).getValue() : null);
            goodsDescription.setItemDescription4(point.size() >= 4 ? point.get(3).getValue() : null);
            goodsDescription.setItemDescription5(point.size() >= 5 ? point.get(4).getValue() : null);
        }
        if (goodsHead.getShopCode().contains("VC")) {
            AmazonAttributeDTO desc = getAttributeInfo(attributes, "rtip_product_description");
            goodsDescription.setDetailDescription(Objects.isNull(desc) ? "" : desc.getValue());
        }else {
            AmazonAttributeDTO desc = getAttributeInfo(attributes, "product_description");
            goodsDescription.setDetailDescription(Objects.isNull(desc) ? "" : desc.getValue());
        }
        return goodsDescription;
    }

    private GoodsSpecification getGoodsSpecification(JSONObject itemInfo, JSONObject attributes, AmazonSummariesDTO summaries) {
        GoodsSpecification goodsSpecification = new GoodsSpecification();
        // 从item_package_dimensions中获取setPackageLength
        JSONArray packageDimensionDTO = attributes.getJSONArray("item_package_dimensions");
        if (ObjUtil.isNotEmpty(packageDimensionDTO)) {
            JSONObject jsonObject = packageDimensionDTO.getJSONObject(0);
            JSONObject length = jsonObject.getJSONObject("length");
            JSONObject width = jsonObject.getJSONObject("width");
            JSONObject height = jsonObject.getJSONObject("height");
            goodsSpecification.setPackageLength(Objects.isNull(length) || Objects.isNull(length.get("value")) ? null : length.getBigDecimal("value").setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageLengthUnit(Objects.isNull(length) || Objects.isNull(length.get("unit")) ? null : StringUtils.getUnitSimplified(length.getString("unit")));
            goodsSpecification.setPackageWidth(Objects.isNull(width) || Objects.isNull(width.get("value")) ? null : width.getBigDecimal("value").setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageWidthUnit(Objects.isNull(width) || Objects.isNull(width.get("unit")) ? null : StringUtils.getUnitSimplified(width.getString("unit")));
            goodsSpecification.setPackageHeight(Objects.isNull(height) || Objects.isNull(height.get("value")) ? null : height.getBigDecimal("value").setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageHeightUnit(Objects.isNull(height) || Objects.isNull(height.get("unit")) ? null : StringUtils.getUnitSimplified(height.getString("unit")));
        }

        // 从item_dimensions中获取itemLength
        JSONArray itemDimensionDTO = attributes.getJSONArray("item_dimensions");
        if (ObjUtil.isNotEmpty(itemDimensionDTO)) {
            JSONObject jsonObject = itemDimensionDTO.getJSONObject(0);
            JSONObject length = jsonObject.getJSONObject("length");
            JSONObject width = jsonObject.getJSONObject("width");
            JSONObject height = jsonObject.getJSONObject("height");
            goodsSpecification.setItemLength(Objects.isNull(length) || Objects.isNull(length.get("value")) ? null : length.getBigDecimal("value").setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setItemLengthUnit(Objects.isNull(length) || Objects.isNull(length.get("unit")) ? null : StringUtils.getUnitSimplified(length.getString("unit")));
            goodsSpecification.setItemWidth(Objects.isNull(width) || Objects.isNull(width.get("value")) ? null : width.getBigDecimal("value").setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setItemWidthUnit(Objects.isNull(width) || Objects.isNull(width.get("unit")) ? null : StringUtils.getUnitSimplified(width.getString("unit")));
            goodsSpecification.setItemHeight(Objects.isNull(height) || Objects.isNull(height.get("value")) ? null : height.getBigDecimal("value").setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setItemHeightUnit(Objects.isNull(height) || Objects.isNull(height.get("unit")) ? null : StringUtils.getUnitSimplified(height.getString("unit")));
        }

        // 从item_package_weight中获取setPackageWeight
        JSONArray itemPackageWeightDTO = attributes.getJSONArray("item_package_weight");
        if (ObjUtil.isNotEmpty(itemPackageWeightDTO)) {
            JSONObject jsonObject = itemPackageWeightDTO.getJSONObject(0);
            goodsSpecification.setPackageWeight(Objects.isNull(jsonObject) || Objects.isNull(jsonObject.get("value")) ? null : jsonObject.getBigDecimal("value").setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageWeightUnit(Objects.isNull(jsonObject) || Objects.isNull(jsonObject.get("unit")) ? null : StringUtils.getUnitSimplified(jsonObject.getString("unit")));
        } 
        
        // 从number_of_boxes中获取numberOfBoxes
        JSONArray numberOfBoxesDTO = attributes.getJSONArray("number_of_boxes");
        if (ObjUtil.isNotEmpty(numberOfBoxesDTO)) {
            JSONObject jsonObject = numberOfBoxesDTO.getJSONObject(0);
            goodsSpecification.setNumberOfBoxes(Objects.isNull(jsonObject) || Objects.isNull(jsonObject.get("value")) ? null : jsonObject.getInteger("value"));
        }
        
        goodsSpecification.setIsIrregularity("1");
        return goodsSpecification;
    }

    private void getAmAttributeLines(JSONObject attributes, GoodsHead goodsHead, List<ListingAmazonAttributeLine> attributeLines, AmazonSummariesDTO summaries) {
        if (  CommonUtilsSmc.isNewVersion(goodsHead.getSiteCode(),goodsHead.getShopCode()) ){
            return;
        }
        Integer categoryId = goodsHead.getCategoryId();
        List<ConfigRequiredField> configRequiredFieldList = configRequiredFieldService.selectByCategoryId(categoryId);
        if (CollUtil.isEmpty(configRequiredFieldList)) {
            throw new RuntimeException("通过类目id未找到类目属性，平台类目id:" + goodsHead.getPlatCategoryId() + ",SMC类目id:" + categoryId);
        }

        Map<String, String> lineMap = getLineMap(goodsHead);

        for (ConfigRequiredField configRequiredField : configRequiredFieldList) {
            String attributeCode = configRequiredField.getAttributeCode();

            String value = null;
            try {
                value = getSpecialValue(summaries, attributes, attributeCode);
            } catch (Exception e) {
                log.error("获取特殊属性值异常,asin:{},attributeCode:{}", goodsHead.getPlatformGoodsId(), attributeCode, e);
            }
            //pn码线上不为空 以线上为准
            if (ObjUtil.equals(attributeCode, "part_number") && StrUtil.isNotEmpty(value)) {
                lineMap.put("part_number", value);
            }
            //如果数据库中有值,则取数据库中的值
            if (lineMap.containsKey(attributeCode)) {
                value = lineMap.get(attributeCode);
            }

            if (StringUtils.isBlank(value)) {
                continue;
            }
            if (ObjUtil.equals(attributeCode, "batteries_required") && (ObjUtil.equals(value, "false") || ObjUtil.equals(value, "true"))) {
                lineMap.put("batteries_required", ObjUtil.equals(value, "false") ? "No" : "Yes");
            }
            if (ObjUtil.equals(attributeCode, "contains_liquid_contents") && (ObjUtil.equals(value, "false") || ObjUtil.equals(value, "true"))) {
                lineMap.put("contains_liquid_contents", ObjUtil.equals(value, "false") ? "No" : "Yes");
            }
            if (ObjUtil.equals(attributeCode, "is_assembly_required") && (ObjUtil.equals(value, "false") || ObjUtil.equals(value, "true"))) {
                lineMap.put("is_assembly_required", ObjUtil.equals(value, "false") ? "No" : "Yes");
            }
            ListingAmazonAttributeLine attributeLine = new ListingAmazonAttributeLine();
            attributeLine.setPlatform(PlatformTypeEnum.AM.name());
            attributeLine.setCategoryId(categoryId);
            attributeLine.setTableValue(value);
            attributeLine.setTableName(attributeCode);
            attributeLine.setTableType(Integer.valueOf(configRequiredField.getAttributeType().toString()));
            attributeLines.add(attributeLine);
        }

        //取个list_price价格
//        String listPrice = getSpecialValue(summaries, attributes, "list_price");
//        String standardPrice = goodsHead.getStandardPrice();
//        if (ObjUtil.equals(standardPrice, "0") && StrUtil.isNotEmpty(listPrice)) {
//            goodsHead.setStandardPrice(listPrice);
//        }

    }

    private Map<String, String> getLineMap(GoodsHead goodsHead) {
        Map<String, String> lineMap = new HashMap<>();
        if (ObjUtil.isNotEmpty(goodsHead.getId())) {
            List<ListingAmazonAttributeLine> lines = listingAmazonAttributeLineService.selectByGoodsId(goodsHead.getId());
            if (CollUtil.isNotEmpty(lines)) {
                for (ListingAmazonAttributeLine line : lines) {
                    lineMap.put(line.getTableName(), line.getTableValue());
                }
            }
        }
        return lineMap;
    }

    private String getVal(JSONObject attributes, String attributeCode) {
        AmazonAttributeDTO attributeDTO = getAttributeInfo(attributes, attributeCode);
        if (Objects.isNull(attributeDTO)) {
            return null;
        }
        String value = attributeDTO.getValue();
        if (StringUtils.isBlank(value)) {
            return null;
        }
        //如果是物品数量的属性，去掉小数点
        if (Objects.equals(attributeCode, "number_of_items") && value.contains(".0")) {
            value = value.replaceAll(".0", "");
        }
        if (Objects.equals(attributeCode, "model_year") && value.contains(".0")) {
            value = value.replaceAll(".0", "");
        }
        return value;
    }

    private String getSpecialValue(AmazonSummariesDTO summaries, JSONObject attributes, String attributeCode) {
        if (Objects.equals(attributeCode, "model")) {
            String value = summaries.getModelNumber();
            return value;
        }
        if (Objects.equals(attributeCode, "top_material_type")) {
            //获取attributes中top下的value,value不为空则获取material下的value
            if (ObjUtil.isEmpty(attributes.getJSONArray("top"))) {
                return null;
            }
            Object top = attributes.getJSONArray("top").get(0);
            if (ObjUtil.isEmpty(top)) {
                return null;
            }
            Object material = JSON.parseObject(JSON.toJSONString(top), JSONObject.class).get("material");
            if (ObjUtil.isEmpty(material)) {
                return null;
            }
            List list = JSON.parseObject(JSON.toJSONString(material), List.class);
            if (CollectionUtil.isEmpty(list)) {
                return null;
            }
            AmazonAttributeDTO attributeDTO = JSON.parseObject(JSON.toJSONString(list.get(0)), AmazonAttributeDTO.class);
            if (ObjUtil.isEmpty(attributeDTO)) {
                return null;
            }
            return attributeDTO.getValue();
        }
        if (Objects.equals(attributeCode, "core_material_type")) {
            //获取attributes中top下的value,value不为空则获取material下的value
            JSONArray coreMaterialArr = attributes.getJSONArray("core_material");
            if (ObjUtil.isEmpty(coreMaterialArr)) {
                return null;
            }
            Object coreMaterial = coreMaterialArr.get(0);
            if (ObjUtil.isEmpty(coreMaterial)) {
                return null;
            }
            AmazonAttributeDTO attributeDTO = JSON.parseObject(JSON.toJSONString(coreMaterial), AmazonAttributeDTO.class);
            if (ObjUtil.isEmpty(attributeDTO)) {
                return null;
            }
            return attributeDTO.getValue();
        }
        String val = getVal(attributes, attributeCode);
        return val;
    }

    /**
     * amazon 数据入库
     *
     * @param shopCode
     * @param saveOrUpdateList
     * @param userId
     */
    public void saveAndUpdateInfo(String shopCode, List<ItemDTO> saveOrUpdateList, String userId, boolean isSync) {
        for (ItemDTO itemDTO : saveOrUpdateList) {
            itemDTO.setUserId(Long.valueOf(userId));
            GoodsHead goodsHead = itemDTO.getGoodsHead();
            AjaxResult frontDetailAjaxResult = itemDTO.getAjaxResult();
            try {
                boolean save = Objects.isNull(goodsHead.getId());
                Integer goodsId = goodsHead.getId();
                if (save) {
                    //保存
                    goodsId = amazonPlatformListingService.saveAmazonInfoToDB(null, itemDTO);

                    // 新增日志
                    if (isSync) {
                        ExceptionUtil.sandbox(() -> insertLog(userId, itemDTO, goodsHead));
                        ExceptionUtil.sandbox(() -> insertIssueLabel(goodsHead));
                    }
                } else {
                    // 获取旧的商品信息
                    GoodsHead goodsHeadOld = goodsHeadService.selectListingGoodsHeadById(goodsHead.getId());
                    // 获取旧的List Price
                    ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2 = listingAmazonAttributeLineV2Service.getAttrByPropNodePath(goodsHead.getId(), "list_price.value");
                    // 获取旧的Sale Price
                    ListingAmazonAttributeLineV2 listingAmazonAttributeLineSalePrice = listingAmazonAttributeLineV2Service.getAttrByPropNodePath(goodsHead.getId(), "purchasable_offer.discounted_price.schedule.value_with_tax");

                    //修改
                    amazonPlatformListingService.updateItemToDB(itemDTO, goodsHead);

                    // 更新日志
                    if (isSync) {
                        ExceptionUtil.sandbox(() -> insertUpdateLog(userId, goodsHeadOld, listingAmazonAttributeLineV2,listingAmazonAttributeLineSalePrice, true));
                        ExceptionUtil.sandbox(() -> insertIssueLabel(goodsHead));
                    }
                }
                if (isSync) {
                    listingPullDateService.upsertListingPullDate(Long.valueOf(goodsHead.getId()),PlatformTypeEnum.AM.name());
                    ExceptionUtil.sandbox(() -> monitorListingSnapshotService.saveListingSnapshot(goodsHead));

                    // 保存前台数据到新表
                    amazonApiHttpRequestBiz.saveFrontDetailData(shopCode, Long.valueOf(goodsId), goodsHead.getPlatformGoodsId(), goodsHead.getPlatformGoodsCode(), frontDetailAjaxResult);
                }


            } catch (Exception e) {
                log.error("亚马逊商品同步异常,shopCode:{},asin:{}", shopCode,goodsHead.getPlatformGoodsId(), e);
                String errorMsg = "保存商品失败."+e.getMessage();
                listingLogService.insertErrorListingLog(errorMsg,userId,goodsHead.getId(),errorMsg);
            }
        }
    }

    private void insertIssueLabel(GoodsHead goodsHead) {
        if(goodsHead.getHasIssues() != null && goodsHead.getHasIssues()){
            listingLabelService.deleteListingLabelsByHeadIds(Arrays.asList("链接异常"), Collections.singleton(goodsHead.getId()));

            ListingLabel listingLabel = new ListingLabel();
            listingLabel.setPlatform(PlatformTypeEnum.AM.name());
            listingLabel.setSiteCode(goodsHead.getSiteCode());
            listingLabel.setShopCode(goodsHead.getShopCode());
            listingLabel.setHeadId(goodsHead.getId());
            listingLabel.setLabelType("smc-sync");
            listingLabel.setLabel("链接异常");
            listingLabel.setCreateTime(DateUtils.getNowDate());
            listingLabelService.insertListingLabel(listingLabel);
        }else {
            listingLabelService.deleteListingLabelsByHeadIds(Arrays.asList("链接异常"), Collections.singleton(goodsHead.getId()));
        }
    }

    private void insertUpdateLog(String userId,   GoodsHead goodsHeadOld, ListingAmazonAttributeLineV2 listPrice ,ListingAmazonAttributeLineV2 salePrice, boolean isSync) {
        StringBuilder sb = null;
        if (isSync) {
            sb =  new StringBuilder("Listing同步成功");
        } else {
            sb =  new StringBuilder("更新Listing成功");
        }
//        sb.append(goodsHeadOld.getShopCode().contains("VC") ? "VC" : "SC").append("链接").append("同步成功");
        boolean isInsertLog = false;
        GoodsHead newGoodsHead = goodsHeadService.selectListingGoodsHeadById(goodsHeadOld.getId());
        // ASIN是否有变更
        if(!ObjUtil.equals(newGoodsHead.getPlatformGoodsId(), goodsHeadOld.getPlatformGoodsId())){
            isInsertLog = true;
            sb.append(",ASIN从").append(goodsHeadOld.getPlatformGoodsId()).append("-->").append(newGoodsHead.getPlatformGoodsId());
        }else {
            sb.append(",ASIN:").append(newGoodsHead.getPlatformGoodsId());
        }
        // 商品编码是否有变更
        if(!ObjUtil.equals(newGoodsHead.getPdmGoodsCode(), goodsHeadOld.getPdmGoodsCode())){
            isInsertLog = true;
            sb.append(",商品编码从").append(goodsHeadOld.getPdmGoodsCode()).append("-->").append(newGoodsHead.getPdmGoodsCode());
        }else {
            sb.append(",商品编码:").append(newGoodsHead.getPdmGoodsCode());
        }
        // 平台商品编码是否有变更
        if(!ObjUtil.equals(newGoodsHead.getPlatformGoodsCode(), goodsHeadOld.getPlatformGoodsCode())){
            isInsertLog = true;
            sb.append(",平台商品编码从").append(goodsHeadOld.getPlatformGoodsCode()).append("-->").append(newGoodsHead.getPlatformGoodsCode());
        }else {
            sb.append(",平台商品编码:").append(newGoodsHead.getPlatformGoodsCode());
        }

        // standard price
        boolean canInsertLog = false;
        // 旧的不为空，新的为空，可以插入日志
        if(ObjUtil.isNotEmpty(goodsHeadOld.getStandardPrice()) && ObjUtil.isEmpty(newGoodsHead.getStandardPrice())){
            canInsertLog = true;
        }
        // 旧的为空，新的不为空，可以插入日志
        if(ObjUtil.isEmpty(goodsHeadOld.getStandardPrice()) && ObjUtil.isNotEmpty(newGoodsHead.getStandardPrice())){
            canInsertLog = true;
        }
        // 旧的不为空，新的不为空，判断价格是否一致
        if(ObjUtil.isNotEmpty(goodsHeadOld.getStandardPrice()) && ObjUtil.isNotEmpty(newGoodsHead.getStandardPrice())){
            BigDecimal oldStandardPrice = new BigDecimal(goodsHeadOld.getStandardPrice());
            BigDecimal newStandardPrice = new BigDecimal(newGoodsHead.getStandardPrice());
            if (oldStandardPrice.compareTo(newStandardPrice) != 0) {
                canInsertLog = true;
            }
        }
        if(canInsertLog){
            isInsertLog = true;
            // 当前售价
            if (!goodsHeadOld.getShopCode().contains("VC")) {
                sb.append(",Your Price从").append(goodsHeadOld.getStandardPrice()).append("-->").append(newGoodsHead.getStandardPrice());
            }else {
                sb.append(",Cost Price从").append(goodsHeadOld.getStandardPrice()).append("-->").append(newGoodsHead.getStandardPrice());
            }
        }

        //list price
        String oldListPrice =  listPrice == null ? null : listPrice.getTableValue();
        ListingAmazonAttributeLineV2 newListPriceV2 = listingAmazonAttributeLineV2Service.getAttrByPropNodePath(goodsHeadOld.getId(), "list_price.value");
        String newListPrice = newListPriceV2 == null ? null : newListPriceV2.getTableValue();
        // List Price
        boolean canInsertListPriceLog = false;
        // 旧的不为空，新的为空，可以插入日志
        if(ObjUtil.isNotEmpty(oldListPrice) && ObjUtil.isEmpty(newListPrice)){
            canInsertListPriceLog = true;
        }
        // 旧的为空，新的不为空，可以插入日志
        if(ObjUtil.isEmpty(oldListPrice) && ObjUtil.isNotEmpty(newListPrice)){
            canInsertListPriceLog = true;
        }
        // 旧的不为空，新的不为空，判断价格是否一致
        if(ObjUtil.isNotEmpty(oldListPrice) && ObjUtil.isNotEmpty(newListPrice)){
            BigDecimal oldListPriceBigDecimal = new BigDecimal(oldListPrice);
            BigDecimal newListPriceBigDecimal = new BigDecimal(newListPrice);
            if(oldListPriceBigDecimal.compareTo(newListPriceBigDecimal) != 0){
                canInsertListPriceLog = true;
            }
        }
        if(canInsertListPriceLog){
            isInsertLog = true;
            sb.append(",List Price从").append(oldListPrice).append("-->").append(newListPrice);
        }

        // sale price 只有非VC有
        if (!goodsHeadOld.getShopCode().contains("VC")) {
            String oldSalePrice =  salePrice == null ? null : salePrice.getTableValue();
            ListingAmazonAttributeLineV2 newSalePriceV2 = listingAmazonAttributeLineV2Service.getAttrByPropNodePath(goodsHeadOld.getId(), "purchasable_offer.discounted_price.schedule.value_with_tax");
            String newSalePrice = newSalePriceV2 == null ? null : newSalePriceV2.getTableValue();
            // List Price
            boolean canInsertSalePriceLog = false;
            // 旧的不为空，新的为空，可以插入日志
            if(ObjUtil.isNotEmpty(oldSalePrice) && ObjUtil.isEmpty(newSalePrice)){
                canInsertSalePriceLog = true;
            }
            // 旧的为空，新的不为空，可以插入日志
            if(ObjUtil.isEmpty(oldSalePrice) && ObjUtil.isNotEmpty(newSalePrice)){
                canInsertSalePriceLog = true;
            }
            // 旧的不为空，新的不为空，判断价格是否一致
            if(ObjUtil.isNotEmpty(oldSalePrice) && ObjUtil.isNotEmpty(newSalePrice)){
                BigDecimal oldSalePriceBigDecimal = new BigDecimal(oldSalePrice);
                BigDecimal newSalePriceBigDecimal = new BigDecimal(newSalePrice);
                if(oldSalePriceBigDecimal.compareTo(newSalePriceBigDecimal) != 0){
                    canInsertSalePriceLog = true;
                }
            }
            if(canInsertSalePriceLog){
                isInsertLog = true;
                sb.append(",Sale Price从").append(oldSalePrice).append("-->").append(newSalePrice);
            }
        }

        if(isInsertLog){
            listingLogService.insertSuccessListingLog(sb.toString(),userId,goodsHeadOld.getId());
        }
    }

    private void insertLog(String userId, ItemDTO itemDTO, GoodsHead goodsHead) {
        StringBuilder sb = new StringBuilder();
        sb.append("Listing同步成功").append(",ASIN:").append(goodsHead.getPlatformGoodsId());
        sb.append(",商品编码:").append(goodsHead.getPdmGoodsCode()).append(",平台商品编码:").append(goodsHead.getPlatformGoodsCode());
        if (!goodsHead.getShopCode().contains("VC")) {
            // 当前售价
            sb.append(",Your Price:").append(goodsHead.getStandardPrice());
            // List Price从属性中获取
            itemDTO.getAmazonAttributeLines().stream().filter(attributeLine -> "list_price.value".equals(attributeLine.getPropNodePath())).findFirst().ifPresent(attributeLine -> {
                sb.append(",List Price:").append(attributeLine.getTableValue());
            });
            // Sale Price从属性中获取
            itemDTO.getAmazonAttributeLines().stream().filter(attributeLine -> "purchasable_offer.discounted_price.schedule.value_with_tax".equals(attributeLine.getPropNodePath())).findFirst().ifPresent(attributeLine -> {
                sb.append(",Sale Price:").append(attributeLine.getTableValue());
            });

        }else{
            // Cost Price
            sb.append(",Cost Price:").append(goodsHead.getStandardPrice());
            // List Price从属性中获取
            itemDTO.getAmazonAttributeLines().stream().filter(attributeLine -> "list_price.value".equals(attributeLine.getPropNodePath())).findFirst().ifPresent(attributeLine -> {
                sb.append(",List Price:").append(attributeLine.getTableValue());
            });
        }
        listingLogService.insertSuccessListingLog(sb.toString(),userId,goodsHead.getId());
    }


    /**
     * 更新商品到数据库(从平台拉取下来的)
     *
     * @param itemDTO
     * @param goodsHead
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateItemToDB(ItemDTO itemDTO, GoodsHead goodsHead) {
        Integer goodsId = goodsHead.getId();
        List<GoodsResource> goodsResourceList = itemDTO.getGoodsResourceList();
        GoodsDescription goodsDescription = itemDTO.getGoodDescription();
        GoodsSpecification goodsSpecification = itemDTO.getGoodsSpecification();
        List<ListingAmazonAttributeLine> goodsAttributeLineList = itemDTO.getGoodsAttributeLineList();
        List<ListingAmazonAttributeLineV2> amazonAttributeLinesV2 = itemDTO.getAmazonAttributeLines();

        goodsHeadService.updateListingGoodsHead(goodsHead);

        //图片先删除 后保存
        goodsResourceService.deleteListingGoodsResourceByHeadId(goodsId);
        goodsResourceList.forEach(goodsResource -> {
            goodsResource.setGoodsId(goodsId);
            goodsResource.setCreateBy(goodsHead.getCreateBy());
            goodsResource.setCreateTime(goodsHead.getCreateTime());
            goodsResource.setUpdateBy(goodsHead.getUpdateBy());
            goodsResource.setUpdateTime(DateUtils.getNowDate());
            goodsResourceService.insertListingGoodsResource(goodsResource);
        });

        //描述先查询id 后保存
        GoodsDescription goodsDescriptionOld = goodsDescriptionService.selectDescriptionListByGoodsId(goodsId);
        if (!Objects.isNull(goodsDescriptionOld)) {
            goodsDescription.setId(goodsDescriptionOld.getId());
            goodsDescription.setUpdateBy(goodsHead.getUpdateBy());
            goodsDescription.setUpdateTime(DateUtils.getNowDate());
            goodsDescriptionService.updateListingGoodsDescription(goodsDescription);
        } else {
            goodsDescription.setGoodsId(goodsId);
            goodsDescriptionService.insertListingGoodsDescription(goodsDescription);
        }
        goodsDescription.setGoodsId(goodsId);


        GoodsSpecification goodsSpecificationOld = goodsSpecificationService.selectSpecificationListByGoodsId(goodsId);
        if (!Objects.isNull(goodsSpecificationOld)) {
            goodsSpecification.setId(goodsSpecificationOld.getId());
            goodsSpecification.setUpdateBy(goodsHead.getUpdateBy());
            goodsSpecification.setUpdateTime(DateUtils.getNowDate());
            goodsSpecificationService.updateListingGoodsSpecification(goodsSpecification);
        } else {
            goodsSpecification.setGoodsId(goodsId);
            goodsSpecificationService.insertListingGoodsSpecification(goodsSpecification);
        }

        //属性先删除 后保存
        if (ObjUtil.isNotEmpty(goodsAttributeLineList)) {
            listingAmazonAttributeLineService.deleteListingAmazonAttributeLineByGoodId(goodsId);
            goodsAttributeLineList.forEach(attributeLine -> {
                attributeLine.setGoodsId(goodsId);
                attributeLine.setCreateBy(goodsHead.getCreateBy());
                attributeLine.setCreateTime(goodsHead.getCreateTime());
                attributeLine.setUpdateBy(goodsHead.getUpdateBy());
                attributeLine.setUpdateTime(DateUtils.getNowDate());
                listingAmazonAttributeLineService.insertListingAmazonAttributeLine(attributeLine);
            });
        }

        if (ObjUtil.isNotEmpty(amazonAttributeLinesV2)) {
            if(itemDTO.getDeleteOldAttr() !=null && itemDTO.getDeleteOldAttr()){
                listingAmazonAttributeLineV2Service.deleteListingAmazonAttributeLineV2ByGoodId(goodsId, null, Arrays.asList(0,4));
            }

            amazonAttributeLinesV2.forEach(attributeLine -> {
                attributeLine.setCreateBy(goodsHead.getCreateBy());
                attributeLine.setHeadId(goodsId.longValue());
                attributeLine.setUpdateBy(goodsHead.getUpdateBy());
                attributeLine.setUpdateTime(DateUtils.getNowDate());
                if(Constants.YesOrNo.YES.equals(attributeLine.getNeedDelFlag()) && Objects.nonNull(attributeLine.getId())){
                    listingAmazonAttributeLineV2Service.deleteListingAmazonAttributeLineV2ById(attributeLine.getId());
                    return;
                }

                if (Objects.isNull(attributeLine.getId())) {
                    listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(attributeLine);
                } else {
                    attributeLine.setUpdateTime(new Date());
                    listingAmazonAttributeLineV2Service.updateListingAmazonAttributeLineV2(attributeLine);
                }
            });
            // 统一更新category_id的值，防止v2表的category_id不一致，原因：SMC刊登时时品类A，同步被修改为品类B
            listingAmazonAttributeLineV2Service.updateCategoryIdByGoodsId(goodsId, goodsHead.getCategoryId());
        }

    }

    /**
     * 保存亚马逊商品信息到数据库
     *
     * @param listing
     * @param itemDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer saveAmazonInfoToDB(ListingDTO listing, ItemDTO itemDTO) {
        GoodsHead goodHead = itemDTO.getGoodsHead();
        List<GoodsResource> resourceList = itemDTO.getGoodsResourceList();
        GoodsDescription goodsDescription = itemDTO.getGoodDescription();
        GoodsSpecification goodsSpecification = itemDTO.getGoodsSpecification();
        List<ListingAmazonAttributeLine> attributeLines = itemDTO.getGoodsAttributeLineList();
        List<ListingAmazonAttributeLineV2> amazonAttributeLines = itemDTO.getAmazonAttributeLines();

        // 亚马逊商品基础信息入库
        goodsHeadService.insertListingGoodsHead(goodHead);
        Integer goodsId = goodHead.getId();

        goodsDescription.setGoodsId(goodsId);
        goodsDescriptionService.insertListingGoodsDescription(goodsDescription);

        goodsSpecification.setGoodsId(goodsId);
        goodsSpecificationService.insertListingGoodsSpecification(goodsSpecification);

        // 处理图片数据
        resourceList.forEach(goodsResource -> {
            goodsResource.setGoodsId(goodsId);
            goodsResourceService.insertListingGoodsResource(goodsResource);
        });

        if (CollUtil.isNotEmpty(attributeLines)) {
            attributeLines.forEach(attributeLine -> {
                if (!ObjectUtils.isEmpty(listing) && Objects.equals("part_number", attributeLine.getTableName())) {
                    String platformCode = createPlatformCode.getPlatformCodeAync(listing.getGoodsCode(), listing.getSiteCode(), String.valueOf(itemDTO.getUserId()));
                    attributeLine.setTableValue(platformCode);
                }
                attributeLine.setGoodsId(goodsId);
                attributeLine.setCreateBy(Convert.toStr(itemDTO.getUserId()));
                listingAmazonAttributeLineService.insertListingAmazonAttributeLine(attributeLine);
            });
        }
        if (CollUtil.isNotEmpty(amazonAttributeLines)) {
            amazonAttributeLines.forEach(attributeLine -> {
                attributeLine.setHeadId(goodsId.longValue());
                attributeLine.setCreateBy(Convert.toStr(itemDTO.getUserId()));
                listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(attributeLine);
            });
        }

        return goodsId;
    }

    /**
     * 修改五点描述
     *
     * @param dto
     */
    public void batchUpdateFiveDescription(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS, goodsIds, String.valueOf(userId));
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        Map<Integer, List<GoodsDescription>> descMap = goodsDescriptionService.selectDescriptionListByGoodsIdList(goodsIds).stream().collect(Collectors.groupingBy(GoodsDescription::getGoodsId));
        Map<String, Set<String>> violateWord = new HashMap<>();
        for (ListingDTO listing : listings) {
            try {
                //head
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                GoodsDescription goodsDescription = descMap.get(listing.getId()).get(0);

                getGoodsDescription(listing, goodsDescription);

                if (Objects.nonNull(goodsDescription.getDetailDescription())) {
                    goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));
                }
                goodsHead.setParityBit("description");
                violateWordBiz.checkViolateWord(false,goodsDescription, null, goodsHead);
                goodsDescriptionService.updateListingGoodsDescription(goodsDescription);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && !PublishStatus.getUnderWayStatus().contains(goodsHead.getPublishStatus())
                        && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Arrays.asList(ListingModuleType.AMAZON_FIVE.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改listing五点描述listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
                if (e instanceof BusinessException) {
                    assembleWord(violateWord, e.getMessage());
                }
            }
        }
        if (ObjUtil.isNotEmpty(violateWord)) {
            assembleWordBefore(violateWord);
        }
        // 检查是否需要将信息同步到其他相同ASIN的链接中
        Boolean syncOtherListings = dto.getSyncOtherListings();
        String syncFields = dto.getSyncFields();
        if (Boolean.TRUE.equals(syncOtherListings) && StringUtils.isNotEmpty(syncFields)) {
            String updateBy = ShiroUtils.getUserId() + "";
            // 开启线程执行
            threadPoolTaskExecutor.execute(() -> {
                Set<Integer> alreadySyncedIds = new HashSet<>(goodsIds);
                sync2OtherListing(syncFields, goodsIds, alreadySyncedIds, updateBy);
            });
        }
        threadPoolForMonitorManager.getThreadPoolExecutor("eidtPoolConfig").execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateSku(List<AmazonEditPlatSkuDTO> editPlatSkuDTOS, Long userId) {
        List<Integer> goodsIds = editPlatSkuDTOS.stream().map(AmazonEditPlatSkuDTO::getId).collect(Collectors.toList());
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.stream().toArray(Integer[]::new)).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_UPDATE_PLATFORM_SKU, goodsIds, String.valueOf(userId));

        for (AmazonEditPlatSkuDTO editPlatSkuDTO : editPlatSkuDTOS) {
            GoodsHead goodsHead = headMap.get(editPlatSkuDTO.getId());
            if (ObjUtil.isEmpty(goodsHead)) {
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PLATFORM_SKU), GoodsTaskSubStatusEnum.ERROR, "listing异常");
                continue;
            }
            if (!Objects.equals(goodsHead.getPublishStatus(), DRAFT.getType())) {
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PLATFORM_SKU), GoodsTaskSubStatusEnum.ERROR, "listing状态不为草稿，数据不允许修改");
                throw new BusinessException("listing状态不为草稿，数据不允许修改");
            }
            if (StringUtils.isBlank(editPlatSkuDTO.getPlatformSku()) || StringUtils.isBlank(editPlatSkuDTO.getPlatformPn())) {
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PLATFORM_SKU), GoodsTaskSubStatusEnum.ERROR, "listing平台商品编码为空或PN码为空");
                continue;
            }
            int count = goodsHeadService.countByPlatformGoodsCodeAndShopCodeAndPublishType(editPlatSkuDTO.getPlatformSku(), goodsHead.getShopCode(), goodsHead.getPublishType(), goodsHead.getId());
            if (count > 0) {
                throw new BusinessException("平台商品编码:" + editPlatSkuDTO.getPlatformSku() + ", 类型:" + goodsHead.getPublishType() + "已存在");
            }
            // 判断是否是跟卖
            String followAsin = listingAmazonAttributeLineV2Service.getFollowAsin(goodsHead.getId());
            boolean isFollow = StringUtils.isNotBlank(followAsin);

            StringBuilder sb = new StringBuilder("批量更新平台SKU信息：");
            // 非跟卖的PN才可以更改
            if (!isFollow)  {
                savePnCodeVc(editPlatSkuDTO, goodsHead, userId, sb);
            }

            if (!goodsHead.getPlatformGoodsCode().equals(editPlatSkuDTO.getPlatformSku())) {
                sb.append("平台商品编码从[" + goodsHead.getPlatformGoodsCode() + "]更改为:[" + editPlatSkuDTO.getPlatformSku() + "]");
                listingLogService.insertSuccessListingLog(sb.toString(), String.valueOf(userId), goodsHead.getId());

                GoodsHead updateHead = new GoodsHead();
                updateHead.setId(goodsHead.getId());
                updateHead.setPlatformGoodsCode(editPlatSkuDTO.getPlatformSku());
                goodsHeadService.updateListingGoodsHead(updateHead);
            }else {
                if (sb.length() > "批量更新平台SKU信息：".length()) {
                    listingLogService.insertSuccessListingLog(sb.toString(), String.valueOf(userId), goodsHead.getId());
                }
            }

            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PLATFORM_SKU), GoodsTaskSubStatusEnum.NORAML, "");
        }
    }
    
    private boolean savePnCodeVc(AmazonEditPlatSkuDTO editPlatSkuDTO, GoodsHead goodsHead, Long userId, StringBuilder sb) {
        // 存储PN码
        ListingAmazonAttributeLineV2 listingAmazonAttributeLine = listingAmazonAttributeLineV2Service.getPnObj(goodsHead.getId());
        if (listingAmazonAttributeLine == null) {
            PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
            ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
            attributeLineV2.setPropNodePath("part_number.value");
            attributeLineV2.setTableValue(editPlatSkuDTO.getPlatformPn());
            attributeLineV2.setTableType(0);
            attributeLineV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            attributeLineV2.setCategoryId(goodsHead.getCategoryId());
            attributeLineV2.setProductType(platformCategory.getProductType());
            listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(attributeLineV2);

            String details = "PN从[空]更改为:[" + editPlatSkuDTO.getPlatformPn() + "]";
            sb.append(details).append(",");
            return true;
        }else {
            if (!editPlatSkuDTO.getPlatformPn().equals(listingAmazonAttributeLine.getTableValue())) {
                String details = "PN从[" + listingAmazonAttributeLine.getTableValue() + "]更改为:[" + editPlatSkuDTO.getPlatformPn() + "]";
                sb.append(details).append(",");

                listingAmazonAttributeLine.setTableValue(editPlatSkuDTO.getPlatformPn());
                listingAmazonAttributeLineV2Service.updateListingAmazonAttributeLineV2(listingAmazonAttributeLine);
                return true;
            }
        }
        return false;
    }


    /**
     * 批量停售  库存更新为0，加入库存黑名单
     * @param goodsHeadList
     */
    public void batchStopSold(List<GoodsHead> goodsHeadList, Long userId) {
        //按照店铺分组
        Map<String, List<GoodsHead>> shopMap = goodsHeadList.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));

        shopMap.forEach((shop, onlineList) -> {
            lostCartPoolConfig.execute(()->{
                AmazonBatchUpdateInventoryVO updateFeedVO = buildZeroInventoryData(shop, onlineList);

                updateInventoryToApi(updateFeedVO);
                onlineList.forEach(goodsHead -> {
                    listingLogService.insertSuccessListingLog("批量停售  库存更新为0", goodsHead.getCreateBy(), goodsHead.getId());
                });


                //加入库存黑名单
                this.excludeListing(onlineList.stream().map(GoodsHead::getId).collect(Collectors.toList()), null, userId);
            });

        });

    }

    /**
     * 更新库存到api

     * @param updateFeedVO
     */
    private void updateInventoryToApi(AmazonBatchUpdateInventoryVO updateFeedVO) {
        String post = HttpUtils.post(updateStockUrl, JSON.toJSONString(updateFeedVO));

        log.info("修改库存,param:{},返回结果:{}", updateFeedVO, post);

        if (post.contains("html")) {
            throw new RuntimeException("上传失败,API服务异常,请联系管理员");
        }
        JSONObject jsonObject = JSON.parseObject(post);
        if (Objects.isNull(jsonObject)) {
            throw new RuntimeException("上传失败,API服务异常,请联系管理员.");
        }
        if (Objects.equals(jsonObject.getString("code"), "500")) {
            throw new RuntimeException("上传失败," + jsonObject.getString("msg"));
        }
    }

    /**
     * 构建停售参数
     * @param shop
     * @param onlineList
     * @return
     */
    private AmazonBatchUpdateInventoryVO buildZeroInventoryData(String shop, List<GoodsHead> onlineList) {
        AmazonBatchUpdateInventoryVO updateFeedVO = new AmazonBatchUpdateInventoryVO();
        updateFeedVO.setSellerCode(shop);

        List<GoodsHead> todoList = onlineList.stream().filter(o ->PublishStatus.SALEING.getType().equals(o.getPublishStatus())).collect(Collectors.toList());
        List<Integer> goodsId = todoList.stream().map(GoodsHead::getId).collect(Collectors.toList());
        //批量调整成0
        goodsHeadService.updateZeroInventory(goodsId);

        // 更新文件记录入库
        ProductDocumentRecord productDocumentRecord = new ProductDocumentRecord();
        productDocumentRecord.setFileName("BATCH_STOP_SOLD_"+ shop);
        productDocumentRecord.setListingIds(JSON.toJSONString(Arrays.asList(goodsId)));
        productDocumentRecord.setCreateTime(new Date());
        productDocumentRecordService.insertRecord(productDocumentRecord);


        updateFeedVO.setSmcRecordId(productDocumentRecord.getId());
        updateFeedVO.setAmazonUpdateInventoryVOList(onlineList.stream().map(h -> {
            //构建文件请求参数
            AmazonBatchUpdateInventoryVO.AmazonUpdateInventoryVO amazonUpdateInventoryVO = new AmazonBatchUpdateInventoryVO.AmazonUpdateInventoryVO();
            amazonUpdateInventoryVO.setSellerCode(shop);
            amazonUpdateInventoryVO.setSellerSku(h.getPlatformGoodsCode());
            amazonUpdateInventoryVO.setSku(h.getPdmGoodsCode());
            amazonUpdateInventoryVO.setQuantity(0);
            return amazonUpdateInventoryVO;
        }).filter(o->ObjUtil.isNotEmpty(o)).collect(Collectors.toList()));
        return updateFeedVO;
    }

    public void batchUpdateNode(List<AmazonEditPlatSkuDTO> editPlatSkuDTOS, Long userId) {
        // 判断状态
        List<Integer> goodsIds = editPlatSkuDTOS.stream().map(AmazonEditPlatSkuDTO::getId).collect(Collectors.toList());
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.stream().toArray(Integer[]::new));
        goodsHeads = goodsHeads.stream().filter(g -> PublishStatus.getSaleStatus().contains(g.getPublishStatus()) || PublishStatus.OFF_SALE.getType().equals(g.getPublishStatus())).collect(Collectors.toList());
        if (CollUtil.isEmpty(goodsHeads)) {
            throw new BusinessException("只有在售状态的商品才能修改节点");
        }
        goodsHeads = goodsHeads.stream().filter(g -> g.getPlatform().equals(PlatformTypeEnum.AM.name()) && StrUtil.isNotBlank(g.getPdmGoodsCode())).collect(Collectors.toList());
        if (CollUtil.isEmpty(goodsHeads)) {
            throw new BusinessException("只有AM平台的商品才能修改节点");
        }
        int size = goodsHeads.size();
        long vcSzie = goodsHeads.stream().filter(g -> g.getPublishType().equals(PublishType.VCDF.getType()) || g.getPublishType().equals(PublishType.VCPO.getType())).count();
        if (vcSzie > 0 ) {
            //节点修正上限限制
            BiRpaNodeIncorrectTask select = new BiRpaNodeIncorrectTask();
            select.setCreateTime(DateUtils.getNowDate());
            select.setVcFlag(Constants.YesOrNo.YES);
            List<BiRpaNodeIncorrectTask> biRpaNodeIncorrectTasks = biRpaNodeIncorrectTaskService.selectBiRpaNodeIncorrectTaskList(select);
            if (CollUtil.isNotEmpty(biRpaNodeIncorrectTasks) && biRpaNodeIncorrectTasks.size()>30){
                throw new BusinessException("今日节点修正已达上限,请明日尽早处理.");
            }
            if ( vcSzie+ (ObjUtil.isEmpty(biRpaNodeIncorrectTasks)?0:biRpaNodeIncorrectTasks.size()) > 30){

                throw new BusinessException("今日节点修正已有"+biRpaNodeIncorrectTasks.size()+"个,每日处理数量不能超过30个.");
            }
        }
      


        Map<Integer, GoodsHead> headMap = goodsHeads.stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        // 查询
        List<BiRpaNodeIncorrectTask> unProcessedNodeIncorrectTask = biRpaNodeIncorrectTaskService.getUnProcessedNodeIncorrectTask(goodsIds);
        Map<Long, BiRpaNodeIncorrectTask> nodeMap = unProcessedNodeIncorrectTask.stream().collect(Collectors.toMap(BiRpaNodeIncorrectTask::getHeadId, e -> e));

        //获取商品类别
        List<String> skus = goodsHeads.stream().map(GoodsHead::getPdmGoodsCode).distinct().collect(Collectors.toList());
        List<GoodsDetailDTO> goodsDetail = pdmHttpRequestBiz.getGoodsDetail(skus);
        if (ObjUtil.isEmpty(goodsDetail)) {
            goodsDetail = new ArrayList<>();
        }
        Map<String, GoodsDetailDTO> goodsDetailMap = goodsDetail.stream().collect(Collectors.toMap(GoodsDetailDTO::getGoodsCode, goods -> goods, (k1, k2) -> k1));


        List<BiRpaNodeIncorrectTask> nodeIncorrectTasks = new ArrayList<>();
        List<Long> deleteIds = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        Map<String, String> cateMap = new HashMap<>();
        for (AmazonEditPlatSkuDTO editPlatSkuDTO : editPlatSkuDTOS) {
            GoodsHead goodsHead = headMap.get(editPlatSkuDTO.getId());
            if (ObjUtil.isEmpty(goodsHead)) {
                continue;
            }
            if (StringUtils.isBlank(editPlatSkuDTO.getCategoryId())) {
                continue;
            }

            PlatformCategory oldCategoryInfo = platformCategoryService.selectPlatformCategoryById(goodsHead.getCategoryId().longValue());

            PlatformCategory newCategoryInfo = platformCategoryService.selectPlatformCategoryById(Long.valueOf(editPlatSkuDTO.getCategoryId()));

            String oldCate =oldCategoryInfo.getCategoryName() +"-"+ oldCategoryInfo.getCategoryEnName() + "(" + oldCategoryInfo.getCategoryId() + ")";
            String newCate =newCategoryInfo.getCategoryName() +"-"+ newCategoryInfo.getCategoryEnName() + "(" + newCategoryInfo.getCategoryId() + ")";
            cateMap.put(String.valueOf(goodsHead.getId()), oldCate + "]->[" + newCate);

            BiRpaNodeIncorrectTask nodeIncorrectTask = nodeMap.get(goodsHead.getId().longValue());
            BiRpaNodeIncorrectTask biRpaNodeIncorrectTask = buildBiRpaNodeIncorrectTask(goodsHead, oldCategoryInfo, newCategoryInfo, goodsDetailMap);
            nodeIncorrectTasks.add(biRpaNodeIncorrectTask);

            if(nodeIncorrectTask != null) {
                biRpaNodeIncorrectTask.setTodoId(nodeIncorrectTask.getTodoId());
                // 如果状态为处理中，无法修改节点
                if (nodeIncorrectTask.getStatus().equals(1)) {
                    sb.append("主键ID:").append(goodsHead.getId()).append("正在处理中，无法修改节点").append("\n");
                } else {
                    // 删除未处理的节点任务
                    deleteIds.add(nodeIncorrectTask.getId());
                }
            }
        }
        if (sb.length() > 0) {
            throw new BusinessException(sb.toString());
        }

        if (CollUtil.isNotEmpty(deleteIds)) {
            deleteIds.forEach(id -> {
                biRpaNodeIncorrectTaskService.deleteBiRpaNodeIncorrectTaskById(id);
            });
        }
        if (CollUtil.isNotEmpty(nodeIncorrectTasks)) {
            // 删除未处理的待办
            amazonNodeIncorrectTodoService.deleteProcessingTodoByHeadIds(goodsIds);

            nodeIncorrectTasks.forEach(nodeIncorrectTask -> {
                nodeIncorrectTask.setHandleFlag(Constants.YesOrNo.NO);
                nodeIncorrectTask.setStatus(BiRpaNodeIncorrectTask.StatusEnum.UNPROCESSED.getCode());
                nodeIncorrectTask.setCreateBy(userId+"");
                biRpaNodeIncorrectTaskService.insertBiRpaNodeIncorrectTask(nodeIncorrectTask);
            });

            // 新增处理中的待办
            insertBatchByTask(nodeIncorrectTasks,goodsHeads, userId,cateMap);
        }
    }

    private void insertBatchByTask(List<BiRpaNodeIncorrectTask> nodeIncorrectTasks, List<GoodsHead> goodsHeads, Long userId,Map<String, String> cateMap) {
        Map<Integer, GoodsHead> goodsHeadMap = goodsHeads.stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        nodeIncorrectTasks.forEach(nodeIncorrectTask -> {
            AmazonNodeIncorrectTodo amazonNodeIncorrectTodo = getAmazonNodeIncorrectTodo(nodeIncorrectTask, goodsHeadMap.get(nodeIncorrectTask.getHeadId().intValue()), userId);
            amazonNodeIncorrectTodoService.insertAmazonNodeIncorrectTodo(amazonNodeIncorrectTodo);

            nodeIncorrectTask.setTodoId(amazonNodeIncorrectTodo.getId());
            biRpaNodeIncorrectTaskService.updateBiRpaNodeIncorrectTask(nodeIncorrectTask);

            String cateDetail = "";
            if (ObjUtil.isNotEmpty(cateMap) && cateMap.containsKey(String.valueOf(nodeIncorrectTask.getHeadId()))){
                cateDetail = cateMap.get(String.valueOf(nodeIncorrectTask.getHeadId()));
            }else {
                String oldCate =amazonNodeIncorrectTodo.getOldCategoryName() +"-"+ amazonNodeIncorrectTodo.getOldCategoryName() + "(" + amazonNodeIncorrectTodo.getOldCategoryId() + ")";
                String newCate =amazonNodeIncorrectTodo.getOldCategoryName() +"-"+ amazonNodeIncorrectTodo.getNewCategoryName() + "(" + amazonNodeIncorrectTodo.getNewCategoryId() + ")";
                cateDetail = oldCate + "]->[" + newCate;
            }
            listingLogService.insertSuccessListingLog("批量更新Lisitng平台节点信息，平台节点由["+cateDetail+"]，数据将通过首页-节点异常待办排队处理", userId.toString(), Math.toIntExact(nodeIncorrectTask.getHeadId()));
        });
    }


    private  AmazonNodeIncorrectTodo getAmazonNodeIncorrectTodo(BiRpaNodeIncorrectTask nodeIncorrectTask, GoodsHead goodsHead, Long userId) {
        AmazonNodeIncorrectTodo amazonNodeIncorrectTodo = new AmazonNodeIncorrectTodo();
        amazonNodeIncorrectTodo.setHeadId(goodsHead.getId().longValue());
        amazonNodeIncorrectTodo.setAsin(goodsHead.getPlatformGoodsId());
        amazonNodeIncorrectTodo.setPlatformGoodsCode(goodsHead.getPlatformGoodsCode());
        amazonNodeIncorrectTodo.setShopCode(goodsHead.getShopCode());
        amazonNodeIncorrectTodo.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        amazonNodeIncorrectTodo.setProductCategoryCode(nodeIncorrectTask.getProductCategoryCode());
        amazonNodeIncorrectTodo.setProductCategoryCodeName(nodeIncorrectTask.getProductCategoryCodeName());
        amazonNodeIncorrectTodo.setCreateTime(DateUtils.getNowDate());
        amazonNodeIncorrectTodo.setOldCategoryId(nodeIncorrectTask.getOldSmcCategoryId() == null ? null : Integer.valueOf(nodeIncorrectTask.getOldSmcCategoryId()));
        amazonNodeIncorrectTodo.setOldPlatformCategoryId(nodeIncorrectTask.getOldCategoryId());
        amazonNodeIncorrectTodo.setOldCategoryName(nodeIncorrectTask.getOldCategoryName());
        amazonNodeIncorrectTodo.setOldCategoryIdDetail(nodeIncorrectTask.getOldCategoryIdDetail());
        amazonNodeIncorrectTodo.setNewCategoryId(Integer.valueOf(nodeIncorrectTask.getNewSmcCategoryId()));
        amazonNodeIncorrectTodo.setNewPlatformCategoryId(nodeIncorrectTask.getNewCategoryId());
        amazonNodeIncorrectTodo.setNewCategoryName(nodeIncorrectTask.getNewCategoryName());
        amazonNodeIncorrectTodo.setNewCategoryIdDetail(nodeIncorrectTask.getNewCategoryIdDetail());
        amazonNodeIncorrectTodo.setType("1");
        amazonNodeIncorrectTodo.setOperator(goodsHead.getCreateBy());
        amazonNodeIncorrectTodo.setDelFlag(0);
        amazonNodeIncorrectTodo.setCreateBy(userId + "");
        amazonNodeIncorrectTodo.setStatus(AmazonNodeIncorrectTodo.Status.PROCESSING.getValue());
        amazonNodeIncorrectTodo.setVcFlag(goodsHead.getPublishType().equals(PublishType.VCDF.getType()) || goodsHead.getPublishType().equals(PublishType.VCPO.getType()) ? "Y" : "N");
        return amazonNodeIncorrectTodo;
    }



    private BiRpaNodeIncorrectTask buildBiRpaNodeIncorrectTask(GoodsHead goodsHead, PlatformCategory oldCategoryInfo, PlatformCategory newCategoryInfo, Map<String, GoodsDetailDTO> goodsDetailMap) {
        BiRpaNodeIncorrectTask biRpaNodeIncorrectTask = new BiRpaNodeIncorrectTask();
        biRpaNodeIncorrectTask.setAsin(goodsHead.getPlatformGoodsId());
        biRpaNodeIncorrectTask.setHeadId(goodsHead.getId().longValue());
        biRpaNodeIncorrectTask.setShopCode(goodsHead.getShopCode());
        biRpaNodeIncorrectTask.setPlatformGoodsCode(goodsHead.getPlatformGoodsCode());
        biRpaNodeIncorrectTask.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        biRpaNodeIncorrectTask.setStatus(BiRpaNodeIncorrectTask.StatusEnum.UNPROCESSED.getCode());
        biRpaNodeIncorrectTask.setVcFlag(Arrays.asList( PublishType.VCDF.getType(), PublishType.VCPO.getType()).contains(goodsHead.getPublishType()) ? "Y" : "N");
        biRpaNodeIncorrectTask.setVcCode(buildVcCode(goodsHead, biRpaNodeIncorrectTask.getVcFlag()));
        biRpaNodeIncorrectTask.setOldCategoryId(oldCategoryInfo == null ? null : String.valueOf(oldCategoryInfo.getCategoryId()));
        biRpaNodeIncorrectTask.setOldCategoryName(oldCategoryInfo == null ? null : oldCategoryInfo.getCategoryEnName());
        biRpaNodeIncorrectTask.setOldCategoryIdDetail(oldCategoryInfo == null ? null : oldCategoryInfo.getCategoryEnDetail());
        biRpaNodeIncorrectTask.setBrowsePath(newCategoryInfo.getCategoryIdDetail());
        biRpaNodeIncorrectTask.setNewCategoryId(newCategoryInfo.getCategoryId());
        biRpaNodeIncorrectTask.setNewCategoryName(newCategoryInfo.getCategoryEnName());
        biRpaNodeIncorrectTask.setNewCategoryIdDetail(newCategoryInfo.getCategoryEnDetail());
        biRpaNodeIncorrectTask.setOldSmcCategoryId(oldCategoryInfo == null ? null : oldCategoryInfo.getId()+"");
        biRpaNodeIncorrectTask.setNewSmcCategoryId(String.valueOf(newCategoryInfo.getId()));
        biRpaNodeIncorrectTask.setDelFlag(0);
        biRpaNodeIncorrectTask.setProductCategoryCode(goodsDetailMap.containsKey(goodsHead.getPdmGoodsCode()) ? goodsDetailMap.get(goodsHead.getPdmGoodsCode()).getProductCategoryCode() : "");
        biRpaNodeIncorrectTask.setProductCategoryCodeName(goodsDetailMap.containsKey(goodsHead.getPdmGoodsCode()) ? goodsDetailMap.get(goodsHead.getPdmGoodsCode()).getProductCategoryName() : "");
        biRpaNodeIncorrectTask.setHandleFlag(Constants.YesOrNo.NO);
        return biRpaNodeIncorrectTask;
    }

    private String buildVcCode(GoodsHead goodsHead, String vcFlag) {
        if(Objects.equals(goodsHead.getPublishType(), PublishType.VCDF.getType())) {
            return "WM741";
        }
        if (Objects.equals(goodsHead.getPublishType(), PublishType.VCPO.getType()) ) {
            return "IH75B";
        }
        return "";
    }

    public static final List<String> hiddenProps = Arrays.asList("marketplace_id", "language_tag");

    public void syncCategory(String productType, String shopCode) {
        List<String> siteListByPlatform = PlatformSiteEnum.getSiteListByPlatform(PlatformTypeEnum.AM.name());
        Shop shop = shopService.selectShopByShopCode(shopCode);
        if(ObjUtil.isEmpty(shop)) {
            log.error("店铺不存在,shopCode:{}", shopCode);
            return;
        }
        if(!siteListByPlatform.contains(shop.getSiteCode())) {
            log.error("店铺站点不支持,shopCode:{},siteCode:{}", shopCode, shop.getSiteCode());
            return;
        }

        PlatformCategory query = new PlatformCategory();
        query.setPlatformCode(PlatformTypeEnum.AM.name());
        query.setSite(shop.getSiteCode());
        if(StringUtils.isNotBlank(productType)) {
            query.setProductType(productType);
        }
        List<PlatformCategory> platformCategories = platformCategoryService.selectPlatformCategoryList(query);
        if (CollUtil.isEmpty(platformCategories)) {
            log.error("亚马逊类目为空,productType:{}", productType);
            return;
        }
        // 按productType分组
        Map<String, List<PlatformCategory>> productTypeMap = platformCategories.stream().filter(e -> StringUtils.isNotBlank(e.getProductType())).collect(Collectors.groupingBy(PlatformCategory::getProductType));

        for (Map.Entry<String, List<PlatformCategory>> stringListEntry : productTypeMap.entrySet()) {
            String redisKey = stringListEntry.getKey() + "_" + shopCode;
             // 加锁，防止并发
             RLock lock = redissonClient.getLock(redisKey);
             boolean isLock;
             try {
                isLock = lock.tryLock(10, TimeUnit.SECONDS);
             } catch (Exception e) {
                log.error("获取锁失败，productType:{}", stringListEntry.getKey(), e);
                continue;
             }
             if(!isLock) {
                log.info(String.format("同步亚马逊类目失败,productType:%s,并发", stringListEntry.getKey()));
                continue;
             }
            try { 
                amazonApiHttpRequestBiz.deleteAMattributesResult(shopCode, stringListEntry.getKey());
                amazonApiHttpRequestBiz.deleteAMAttributesResultDetail(shopCode, stringListEntry.getKey(), "schema");
                AjaxResult ajaxResult =  amazonApiHttpRequestBiz.getAMAttributesResultCache(shopCode,  stringListEntry.getKey());
                if (!ajaxResult.isSuccess()) {
                    log.error("同步亚马逊类目失败,productType:{},msg:{}", stringListEntry.getKey(), ajaxResult.get(AjaxResult.MSG_TAG));
                    continue;
                }
                DefinitionsDTO definitionsDTO = JSONUtil.toBean(ajaxResult.get(AjaxResult.DATA_TAG).toString(), DefinitionsDTO.class, false);

                Map<String, Long> propertyCodesTypeMap = getPropertyCodesTypeMap(definitionsDTO, definitionsDTO.getPropertyGroups());
                // 获取schema
                String resource = amazonApiHttpRequestBiz.getAMAttributesResultDetail(shopCode, stringListEntry.getKey(), "schema");
                // 解析json schema
                doParseResource(stringListEntry, resource, propertyCodesTypeMap, shop.getSiteCode(), shopCode);

                amazonProductBiz.getCategoryRequired(stringListEntry.getKey(), shopCode);
            }catch (Exception e) {
                log.error("同步亚马逊类目失败,productType:{}", stringListEntry.getKey(), e);
            } finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        }
    }

    /**
     * 解析json schema
     *
     * @param stringListEntry
     * @param resource
     * @param propertyCodesTypeMap
     * @param siteCode
     * @param shopCode
     */
    public void doParseResource(Map.Entry<String, List<PlatformCategory>> stringListEntry, String resource, Map<String, Long> propertyCodesTypeMap, String siteCode, String shopCode) {
        String vcFlag = shopCode.contains("VC") ? "Y" : "N";
        JSONObject jsonObject = JSON.parseObject(resource);
        HashMap<String, AmazonAttributePropertiesDTO> mainProperties = JSON.parseObject(JSON.toJSONString(jsonObject.get("properties")),HashMap.class);
        amCategoryTemplateFieldService.deleteByProductType(stringListEntry.getKey(), vcFlag, siteCode);
        // 清除旧的字段关系数据
        amCategoryTemplateFieldRelationService.deleteByCondition(stringListEntry.getKey(), vcFlag, siteCode);

        // 数据库已有的字段数据
        AmCategoryTemplateField fieldQuery = new AmCategoryTemplateField();
        fieldQuery.setProductType(stringListEntry.getKey());
        fieldQuery.setVcFlag(vcFlag);
        fieldQuery.setSite(siteCode);
        List<AmCategoryTemplateField> oldFields = amCategoryTemplateFieldService.selectAmCategoryTemplateFieldList(fieldQuery);
        Map<String, AmCategoryTemplateField> oldFieldMap = oldFields.stream().collect(Collectors.toMap(AmCategoryTemplateField::getFieldCode, e -> e));
        List<String> newFieldCodes = new ArrayList<>(mainProperties.keySet());
        List<AmCategoryTemplateField> deleteFields = oldFields.stream().filter(e -> !newFieldCodes.contains(e.getFieldCode())).collect(Collectors.toList());

        // 数据库已有的枚举数据
        AmCategoryTemplateFieldPropEnum fieldPropEnumQuery = new AmCategoryTemplateFieldPropEnum();
        fieldPropEnumQuery.setProductType(stringListEntry.getKey());
        fieldPropEnumQuery.setVcFlag(vcFlag);
        fieldPropEnumQuery.setSite(siteCode);
        List<AmCategoryTemplateFieldPropEnum> oldEnums = amCategoryTemplateFieldPropEnumService.selectAmCategoryTemplateFieldPropEnumList(fieldPropEnumQuery);
        Map<String, List<AmCategoryTemplateFieldPropEnum>> oldEnumMap = oldEnums.stream().collect(Collectors.groupingBy(AmCategoryTemplateFieldPropEnum::getPropNodePath));

        // 遍历主属性
        for (String attributeMainCode : mainProperties.keySet()) {
            AmazonAttributePropertiesDTO amazonAttributePropertiesDTO = JSON.parseObject(JSON.toJSONString(mainProperties.get(attributeMainCode)), AmazonAttributePropertiesDTO.class);

            AmCategoryTemplateField amCategoryTemplateField = buildAmCategoryTemplateField(attributeMainCode, stringListEntry.getKey(),amazonAttributePropertiesDTO , propertyCodesTypeMap, siteCode, vcFlag);
            // 如果是已有的数据，设置id做更新操作
            if (oldFieldMap.containsKey(attributeMainCode)) {
                amCategoryTemplateField.setId(oldFieldMap.get(attributeMainCode).getId());
            }

            AmazonAttributePropertiesDTO.ItemsDTO items = amazonAttributePropertiesDTO.getItems();
            Map<String, Object> properties = items.getProperties();
            List<AmCategoryTemplateFieldProp> allProps = new ArrayList<>(properties.size());
            amCategoryTemplateField.setAllProps(allProps);

            // 获取已有的属性数据
            AmCategoryTemplateFieldProp fieldPropQuery = new AmCategoryTemplateFieldProp();
            fieldPropQuery.setProductType(amCategoryTemplateField.getProductType());
            fieldPropQuery.setFieldCode(amCategoryTemplateField.getFieldCode());
            fieldPropQuery.setVcFlag(vcFlag);
            fieldPropQuery.setSite(siteCode);
            List<AmCategoryTemplateFieldProp> oldProps = amCategoryTemplateFieldPropService.selectAmCategoryTemplateFieldPropList(fieldPropQuery);
            Map<String, AmCategoryTemplateFieldProp> oldPropMap = oldProps.stream().collect(Collectors.toMap(AmCategoryTemplateFieldProp::getPropNodePath, e -> e));

            // 构建模板属性表
            buildAmCategoryTemplateFieldProp(amCategoryTemplateField, amazonAttributePropertiesDTO.getItems().getRequired(), amazonAttributePropertiesDTO.getItems().getProperties(),
                    amCategoryTemplateField.getFieldCode(), amazonAttributePropertiesDTO.getType(), oldPropMap, oldEnumMap, null, vcFlag);

            // 需要删除的属性
            List<AmCategoryTemplateFieldProp> deleteProps = oldPropMap.keySet().stream().filter(e -> !amCategoryTemplateField.getNewPropNodePaths().contains(e)).map(oldPropMap::get).collect(Collectors.toList());
            amCategoryTemplateField.setDeleteProps(deleteProps);

            // 入库
            amCategoryTemplateFieldService.saveField(amCategoryTemplateField);
        }

        amCategoryTemplateFieldService.deleteList(deleteFields);


        // 提取并保存字段关系
        extractAndSaveFieldRelations(jsonObject, stringListEntry.getKey(), siteCode, vcFlag);

        if(Constants.YesOrNo.NO.equals(vcFlag)) {
            stringListEntry.getValue().forEach(e -> {
                pullFileRelatedTaskBiz.saveCategoryToDB(e, new CategoryInfo());
            });
        }
    }


    /**
     * 提取并保存字段关系
     *
     * @param schemaObj JSON Schema对象
     * @param productType 商品类型
     * @param site 站点
     * @param vcFlag VC标志
     */
    private void extractAndSaveFieldRelations(JSONObject schemaObj, String productType, String site, String vcFlag) {
        // 只关注这些特定的字段
        final Set<String> targetFields = new HashSet<>(Arrays.asList(
                "product_category",
                "product_subcategory",
                "item_type_keyword"
        ));

        final Set<String> targetFieldValues = new HashSet<>(Arrays.asList(
                "product_category.value",
                "product_subcategory.value",
                "item_type_keyword.value"
        ));
        // 获取所有枚举值的映射，用于快速查找
        Map<String, Map<String, Long>> enumValueIdMap = buildEnumValueIdMap(productType, site, vcFlag, targetFieldValues);

        // 保存提取的字段关系
        List<AmCategoryTemplateFieldRelation> relationList = new ArrayList<>();

        // 检查是否有allOf节点
        if (schemaObj.containsKey("allOf")) {
            JSONArray allOfArray = schemaObj.getJSONArray("allOf");
            // 处理每个条件
            for (int i = 0; i < allOfArray.size(); i++) {
                JSONObject conditionObj = allOfArray.getJSONObject(i);
                processConditionNode(conditionObj, productType, site, vcFlag, enumValueIdMap, relationList, targetFields);
            }
        }

        // 批量保存字段关系
        if (CollUtil.isNotEmpty(relationList)) {
            amCategoryTemplateFieldRelationService.batchInsert(relationList);
            log.info("成功保存{}个字段关系映射", relationList.size());
        }
    }


    /**
     * 构建枚举值ID映射
     *
     * @param productType 商品类型
     * @param site 站点
     * @param vcFlag VC标志
     * @return 枚举值映射 Map<节点路径, Map<枚举值code, 枚举ID>>
     */
    private Map<String, Map<String, Long>> buildEnumValueIdMap(String productType, String site, String vcFlag, Set<String> targetFields) {
        Map<String, Map<String, Long>> result = new HashMap<>();

        // 查询所有枚举值
        AmCategoryTemplateFieldPropEnum queryParam = new AmCategoryTemplateFieldPropEnum();
        queryParam.setProductType(productType);
        queryParam.setSite(site);
        queryParam.setVcFlag(vcFlag);
        queryParam.setPropNodePaths(new ArrayList<>(targetFields));
        List<AmCategoryTemplateFieldPropEnum> allEnums = amCategoryTemplateFieldPropEnumService.selectAmCategoryTemplateFieldPropEnumList(queryParam);

        // 按节点路径分组
        Map<String, List<AmCategoryTemplateFieldPropEnum>> enumsByPath = allEnums.stream()
                .collect(Collectors.groupingBy(AmCategoryTemplateFieldPropEnum::getPropNodePath));

        // 为每个节点路径创建code到id的映射
        for (Map.Entry<String, List<AmCategoryTemplateFieldPropEnum>> entry : enumsByPath.entrySet()) {
            Map<String, Long> codeToIdMap = entry.getValue().stream()
                    .collect(Collectors.toMap(AmCategoryTemplateFieldPropEnum::getCode, AmCategoryTemplateFieldPropEnum::getId, (id1, id2) -> id1));
            result.put(entry.getKey(), codeToIdMap);
        }

        return result;
    }


    /**
     * 处理条件节点（if-then-else）
     */
    private void processConditionNode(JSONObject node, String productType, String site, String vcFlag,
                                      Map<String, Map<String, Long>> enumValueIdMap, List<AmCategoryTemplateFieldRelation> relationList, Set<String> targetFields) {
        // 处理if-then条件
        if (node.containsKey("if") && node.containsKey("then")) {
            processIfThenCondition(node.get("if"), node.get("then"), productType, site, vcFlag, enumValueIdMap, relationList, targetFields);
        }

        // 递归处理else分支
        if (node.containsKey("else")) {
            processConditionNode(node.getJSONObject("else"), productType, site, vcFlag, enumValueIdMap, relationList, targetFields);
        }

        // 递归处理嵌套的allOf
        if (node.containsKey("allOf")) {
            JSONArray allOfArray = node.getJSONArray("allOf");
            for (int i = 0; i < allOfArray.size(); i++) {
                processConditionNode(allOfArray.getJSONObject(i), productType, site, vcFlag, enumValueIdMap, relationList, targetFields);
            }
        }
    }

    /**
     * 处理if-then条件
     */
    private void processIfThenCondition(Object ifObj, Object thenObj, String productType, String site, String vcFlag,
                                        Map<String, Map<String, Long>> enumValueIdMap, List<AmCategoryTemplateFieldRelation> relationList, Set<String> targetFields) {
        JSONObject ifNode = (JSONObject) ifObj;
        JSONObject thenNode = (JSONObject) thenObj;

        // 跳过没有properties的条件
        if (!ifNode.containsKey("properties") || !thenNode.containsKey("properties")) {
        return;
        }

        JSONObject ifProperties = ifNode.getJSONObject("properties");
        JSONObject thenProperties = thenNode.getJSONObject("properties");

        // 处理父字段属性，只处理目标字段
        for (String parentFieldCode : ifProperties.keySet()) {
        // 跳过非目标字段
        if (!targetFields.contains(parentFieldCode)) {
            continue;
        }

        JSONObject parentFieldObj = ifProperties.getJSONObject(parentFieldCode);

        // 处理父字段枚举值
        List<String> parentValues = extractEnumValues(parentFieldObj);
        if (parentValues.isEmpty()) {
            continue;
        }

        // 对应的父节点路径
        String parentNodePath = parentFieldCode + ".value";

        // 处理子字段属性，只处理目标字段
        for (String childFieldCode : thenProperties.keySet()) {
            // 跳过非目标字段或自身到自身的映射
            if (!targetFields.contains(childFieldCode) || parentFieldCode.equals(childFieldCode)) {
                continue;
            }
            
            JSONObject childFieldObj = thenProperties.getJSONObject(childFieldCode);
            
            // 处理子字段枚举值
            List<String> childValues = extractEnumValues(childFieldObj);
            if (childValues.isEmpty()) {
                continue;
            }
            
            // 对应的子节点路径
            String childNodePath = childFieldCode + ".value";
            
            // 创建父值到子值的映射关系
            createFieldRelations(
                productType, site, vcFlag,
                parentNodePath, parentValues,
                childNodePath, childValues,
                enumValueIdMap, relationList
            );
        }
        }
    }

    /**
     * 从字段对象中提取枚举值
     */
    private List<String> extractEnumValues(JSONObject fieldObj) {
        List<String> values = new ArrayList<>();

        // 处理直接包含enum的情况
        if (fieldObj.containsKey("enum")) {
            JSONArray enumArray = fieldObj.getJSONArray("enum");
            for (int i = 0; i < enumArray.size(); i++) {
                values.add(enumArray.getString(i));
            }
            return values;
        }

        // 处理Amazon特有的contains结构
        if (fieldObj.containsKey("contains") &&
                fieldObj.getJSONObject("contains").containsKey("properties") &&
                fieldObj.getJSONObject("contains").getJSONObject("properties").containsKey("value") &&
                fieldObj.getJSONObject("contains").getJSONObject("properties").getJSONObject("value").containsKey("enum")) {

            JSONArray enumArray = fieldObj.getJSONObject("contains")
                    .getJSONObject("properties")
                    .getJSONObject("value")
                    .getJSONArray("enum");

            for (int i = 0; i < enumArray.size(); i++) {
                values.add(enumArray.getString(i));
            }
            return values;
        }

        // 处理items.properties.value.enum结构
        if (fieldObj.containsKey("items") &&
                fieldObj.getJSONObject("items").containsKey("properties") &&
                fieldObj.getJSONObject("items").getJSONObject("properties").containsKey("value") &&
                fieldObj.getJSONObject("items").getJSONObject("properties").getJSONObject("value").containsKey("enum")) {

            JSONArray enumArray = fieldObj.getJSONObject("items")
                    .getJSONObject("properties")
                    .getJSONObject("value")
                    .getJSONArray("enum");

            for (int i = 0; i < enumArray.size(); i++) {
                values.add(enumArray.getString(i));
            }
            return values;
        }

        return values;
    }


    /**
     * 创建字段关系映射
     */
    private void createFieldRelations(
            String productType, String site, String vcFlag,
            String parentNodePath, List<String> parentValues,
            String childNodePath, List<String> childValues,
            Map<String, Map<String, Long>> enumValueIdMap,
            List<AmCategoryTemplateFieldRelation> relationList) {

        // 获取父节点的枚举值ID映射
        Map<String, Long> parentEnumMap = enumValueIdMap.getOrDefault(parentNodePath, Collections.emptyMap());

        // 获取子节点的枚举值ID映射
        Map<String, Long> childEnumMap = enumValueIdMap.getOrDefault(childNodePath, Collections.emptyMap());

        // 如果没有找到任何枚举值映射，则跳过
        if (parentEnumMap.isEmpty() || childEnumMap.isEmpty()) {
            log.error("未找到节点{}或{}的枚举值映射", parentNodePath, childNodePath);
            return;
        }

        // 为每一对父子枚举值创建关系
        for (String parentValue : parentValues) {
            // 检查父枚举值ID是否存在
            if (!parentEnumMap.containsKey(parentValue)) {
                log.error("未找到父节点{}的枚举值{}的ID", parentNodePath, parentValue);
                continue;
            }

            Long parentEnumId = parentEnumMap.get(parentValue);

            for (String childValue : childValues) {
                // 检查子枚举值ID是否存在
                if (!childEnumMap.containsKey(childValue)) {
                    log.error("未找到子节点{}的枚举值{}的ID", childNodePath, childValue);
                    continue;
                }

                Long childEnumId = childEnumMap.get(childValue);

                // 创建关系对象
                AmCategoryTemplateFieldRelation relation = new AmCategoryTemplateFieldRelation();
                relation.setProductType(productType);
                relation.setParentPropNodePath(parentNodePath);
                relation.setParentEnumId(parentEnumId);
                relation.setChildPropNodePath(childNodePath);
                relation.setChildEnumId(childEnumId);
                relation.setSite(site);
                relation.setVcFlag(vcFlag);
                relation.setCreateBy("-1");
                relation.setDelFlag(0L);
                relation.setCreateTime(new Date());

                relationList.add(relation);
            }
        }
    }


    private void buildAmCategoryTemplateFieldProp(AmCategoryTemplateField amCategoryTemplateField, List<String> required, Map<String, Object> properties , String parentNodePath, String type, Map<String, AmCategoryTemplateFieldProp> oldPropMap,
                                                  Map<String, List<AmCategoryTemplateFieldPropEnum>> oldEnumMap, AmCategoryTemplateFieldProp parentProp, String vcFlag) {
        if (properties == null) {
            return;
        }
        for (Map.Entry<String, Object> entry : properties.entrySet()) {
            String key = entry.getKey();
            JSONObject jsonObject = (JSONObject) entry.getValue();
            AmazonAttributePropertiesDTO value = JSON.parseObject(JSON.toJSONString(entry.getValue()), AmazonAttributePropertiesDTO.class);

            AmCategoryTemplateFieldProp prop = new AmCategoryTemplateFieldProp();
            if(parentProp != null) {
                parentProp.getChildren().add(prop);
            }else {
                amCategoryTemplateField.getAllProps().add(prop);
            }
            if (required.contains(key)) {
                prop.setRequired(Constants.YesOrNo.YES);
            }else {
                prop.setRequired(Constants.YesOrNo.NO);
            }
            prop.setParentPropId(0L);
            prop.setPropCode(key);
            prop.setFieldCode(amCategoryTemplateField.getFieldCode());
            prop.setPropName(value.getTitle());
            prop.setPropDesc(value.getDescription());
            prop.setEditableFlag(value.getEditable() == null ? Constants.YesOrNo.NO : value.getEditable() ? Constants.YesOrNo.YES : Constants.YesOrNo.NO);
            prop.setHiddenFlag(value.getHidden() == null ? Constants.YesOrNo.NO : value.getHidden() ? Constants.YesOrNo.YES : Constants.YesOrNo.NO);
            prop.setStructType(value.getType());
            prop.setProductType(amCategoryTemplateField.getProductType());
            prop.setPropNodePath(parentNodePath + "." + key);
            amCategoryTemplateField.getNewPropNodePaths().add(prop.getPropNodePath());
            prop.setParentStructType(type);
            prop.setMinLength(value.getMinLength());
            prop.setMaxLength(value.getMaxLength());
            prop.setCreateBy(amCategoryTemplateField.getCreateBy());
            prop.setLeafNode(Constants.YesOrNo.YES);
            prop.setVcFlag(vcFlag);
            prop.setSite(amCategoryTemplateField.getSite());

            if (hiddenProps.contains(key)) {
                prop.setHiddenFlag(Constants.YesOrNo.YES);
            }else {
                prop.setHiddenFlag(Constants.YesOrNo.NO);
            }
            if(jsonObject.containsKey("enum")) {
                prop.setEnumFlag(Constants.YesOrNo.YES);
                buildAmCategoryTemplateFieldPropEnum(jsonObject.getJSONArray("enum"), jsonObject.getJSONArray("enumNames"),prop, oldEnumMap);
            }else {
                // 判断是否有anyOf属性，且anyOf的数组对象，能找到一个对象字段有enum
                if(jsonObject.containsKey("anyOf")) {
                    JSONArray anyOf = jsonObject.getJSONArray("anyOf");
                    for (int i = 0; i < anyOf.size(); i++) {
                        JSONObject anyOfObj = anyOf.getJSONObject(i);
                        if (anyOfObj.containsKey("enum")) {
                            buildAmCategoryTemplateFieldPropEnum(anyOfObj.getJSONArray("enum"), anyOfObj.getJSONArray("enumNames"), prop, oldEnumMap);
                            prop.setEnumFlag(Constants.YesOrNo.YES);
                            break;
                        }
                    }
                }else {
                    prop.setEnumFlag(Constants.YesOrNo.NO);
                }
            }
            if (oldPropMap.containsKey(prop.getPropNodePath())) {
                prop.setId(oldPropMap.get(prop.getPropNodePath()).getId());
            }

            if (value.getItems() != null) {
                initChildren(prop);

                buildAmCategoryTemplateFieldProp(amCategoryTemplateField, value.getItems().getRequired(), value.getItems().getProperties(), prop.getPropNodePath(), value.getType(), oldPropMap, oldEnumMap, prop, vcFlag);
            }else if(MapUtil.isNotEmpty(value.getProperties())) {
                initChildren(prop);

                buildAmCategoryTemplateFieldProp(amCategoryTemplateField, value.getRequired(), value.getProperties(), prop.getPropNodePath(), value.getType(), oldPropMap, oldEnumMap, prop, vcFlag);
            }

        }
    }

    private static void initChildren(AmCategoryTemplateFieldProp prop) {
        prop.setLeafNode(Constants.YesOrNo.NO);
        List<AmCategoryTemplateFieldProp> children = prop.getChildren();
        if (children == null) {
            children = new ArrayList<>();
            prop.setChildren(children);
        }
    }

    private void buildAmCategoryTemplateFieldPropEnum(JSONArray anEnum, JSONArray enumNames, AmCategoryTemplateFieldProp prop, Map<String, List<AmCategoryTemplateFieldPropEnum>> oldEnumMap) {
        List<AmCategoryTemplateFieldPropEnum> insertProps = new ArrayList<>(anEnum.size());
        List<AmCategoryTemplateFieldPropEnum> updateProps = new ArrayList<>(anEnum.size());
        List<AmCategoryTemplateFieldPropEnum> oldProps = oldEnumMap.get(prop.getPropNodePath());
        oldProps = oldProps == null ? new ArrayList<>() : oldProps;
        // to map
        Map<String, AmCategoryTemplateFieldPropEnum> oldPropMap = oldProps.stream().collect(Collectors.toMap(AmCategoryTemplateFieldPropEnum::getCode, e -> e));
        List<AmCategoryTemplateFieldPropEnum> deleteProps = oldProps.stream().filter(e -> !anEnum.contains(e.getCode())).collect(Collectors.toList());

        prop.setDeleteEnumList(deleteProps);
        prop.setInsertEnumList(insertProps);
        prop.setUpdateEnumList(updateProps);

        for (int i = 0; i < anEnum.size(); i++) {
            AmCategoryTemplateFieldPropEnum propEnum = new AmCategoryTemplateFieldPropEnum();
            propEnum.setCode(anEnum.getString(i));
            propEnum.setName(enumNames.getString(i));
            propEnum.setProductType(prop.getProductType());
            propEnum.setPropNodePath(prop.getPropNodePath());
            propEnum.setCreateBy(prop.getCreateBy());
            propEnum.setVcFlag(prop.getVcFlag());
            propEnum.setSite(prop.getSite());

            if (oldPropMap.containsKey(anEnum.getString(i))) {
                propEnum.setId(oldPropMap.get(anEnum.getString(i)).getId());
                updateProps.add(propEnum);
            }else {
                insertProps.add(propEnum);
            }
        }
    }

    /**
     * 构建模板字段表
     *
     * @param key
     * @param productType
     * @param value
     * @param propertyCodesTypeMap
     * @param defaultSite
     * @param vcFlag
     * @return
     */
    private AmCategoryTemplateField buildAmCategoryTemplateField(String key, String productType, AmazonAttributePropertiesDTO value, Map<String, Long> propertyCodesTypeMap, String defaultSite, String vcFlag) {
        AmCategoryTemplateField categoryTemplateField = new AmCategoryTemplateField();
        categoryTemplateField.setFieldCode(key);
        categoryTemplateField.setFieldName(value.getTitle());
        categoryTemplateField.setFieldDesc(value.getDescription());
        categoryTemplateField.setFieldType(propertyCodesTypeMap.get(key) == null ? 4 : propertyCodesTypeMap.get(key));
        categoryTemplateField.setMaxItems(value.getMinItems());
        categoryTemplateField.setMaxUniqueItems(value.getMaxUniqueItems());
        categoryTemplateField.setMinItems(value.getMinItems());
        categoryTemplateField.setMinUniqueItems(value.getMinUniqueItems());
        categoryTemplateField.setProductType(productType);
        categoryTemplateField.setSite(defaultSite);
        categoryTemplateField.setStructType(value.getType());
        categoryTemplateField.setVcFlag(vcFlag);
        categoryTemplateField.setCreateBy("-1");
        return categoryTemplateField;
    }

    /**
     * 新版保存
     * @param listingDTO
     * @return
     */
    @Override
    public AjaxResult listingSaveV2(ListingDTO listingDTO) {
        List<String> businessIds = new ArrayList<>();
        Long userId = ShiroUtils.getUserId();
        listingDTO.setNewVersion(Constants.YesOrNo.YES);
        //1、基础数据
        List<GoodsHead> goodsHeadList = goodsInfoBiz.packagingBasis(listingDTO);

        //2、商品图片视频数据
        ArrayList<GoodsResource> goodsResourceList = goodsInfoBiz.getGoodsResourceList(listingDTO);

        //3、商品描述数据
        GoodsDescription goodsDescription = getGoodsDescription(listingDTO);
        goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));

        //4、商品规格数据
        GoodsSpecification goodsSpecification = getGoodsSpecificationNew(listingDTO, userId);

        //获取亚马逊商品三类属性信息
        List<ListingAmazonAttributeLineV2> amazonAttributeLines = listingDTO.getAmazonAttributeLines();
        if (CollectionUtils.isEmpty(amazonAttributeLines)) {
            amazonAttributeLines = new ArrayList<>();
        }else {
            // 移除part_number
            amazonAttributeLines.removeIf(e -> "part_number.value".equals(e.getPropNodePath()));
        }
        ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
        attributeLineV2.setPropNodePath("part_number.value");
        attributeLineV2.setTableName("Manufacturer Part Number");
        String platformCode = createPlatformCode.getPlatformCodeAync(listingDTO.getGoodsCode(), listingDTO.getSiteCode(), String.valueOf(userId));
        attributeLineV2.setTableValue(platformCode);
        attributeLineV2.setTableType(0);
        amazonAttributeLines.add(attributeLineV2);

        List<ListingAmazonAttributeLineV2> attributeLines = goodsInfoBiz.parseAmazonAttributeLines(listingDTO, userId, goodsHeadList.get(0));

        ItemDTO itemDTO = new ItemDTO();
        itemDTO.setGoodsResourceList(goodsResourceList);
        itemDTO.setGoodDescription(goodsDescription);
        itemDTO.setGoodsSpecification(goodsSpecification);
        itemDTO.setAmazonAttributeLines(attributeLines);
        itemDTO.setUserId(userId);
        List<String> goodsIds = new ArrayList<>();

        Map<String,GoodsDetailDTO> goodsDetailMap = new HashMap<>();
        //违禁词检测
        for (GoodsHead goodsHead : goodsHeadList) {
            goodsHead.setAttributeLines(attributeLines);
            amazonProductBiz.canPutListing(goodsHead.getBrandCode(), goodsHead.getPdmGoodsCode(), goodsDetailMap, goodsHead.getShopCode());

            violateWordBiz.checkViolateWord(true,goodsDescription, null, goodsHead);
                  
            String listPrice = attributeLines.stream().filter(e -> "list_price.value".equals(e.getPropNodePath())).findFirst().map(ListingAmazonAttributeLineV2::getTableValue).orElse("");
            commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), goodsHead.getPublishType())
                    .siteCode(goodsHead.getSiteCode())  
                    .listPrice(listPrice).build());
        }

        for (GoodsHead goodsHead : goodsHeadList) {
            try {
                itemDTO.setGoodsHead(goodsHead);
                Integer goodsId = amazonPlatformListingService.saveAmazonInfoToDB(listingDTO, itemDTO);
                goodsIds.add(goodsId.toString());
                businessIds.add(String.valueOf(goodsId));
            } catch (Exception e) {
                log.error("Amazon新建商品编码为：[" + listingDTO.getGoodsCode() + "]listing草稿失败", e);
                throw new RuntimeException(e.getMessage());
            }
        }

        //根据条件将待办更新为处理中
        stockArrivesTodoService.updateStatusToProcessing(listingDTO.getGoodsCode(), Convert.toStr(userId), Convert.toInt(DateUtils.parseDateToStr("yyyyMMdd", new Date())));

        //处理AI文案使用表的id
        if ("1".equals(listingDTO.getUseAI())) {
            try {
                handleAiUseId(listingDTO.getUuid(), goodsHeadList);
            } catch (Exception ex) {
                log.error("Ebay新建商品编码为：[" + listingDTO.getGoodsCode() + "]记录AI文案使用表失败", ex);
            }
        }


        // 处理一键刊登
        if (null != listingDTO.getPublishStatus()
                && PublishStatus.PUBLISHING.getType().toString().equals(listingDTO.getPublishStatus())
                && CollectionUtils.isNotEmpty(goodsIds)) {
            return listingInfoBiz.publishListingByIds(String.join(",", goodsIds), userId);
        }

        return AjaxResult.success(businessIds);
    }

    private String getValue(StandardEvaluationContext context, String nodePath) {
        if (ObjUtil.isEmpty(nodePath)){
            return "";
        }
        if (nodePath.contains("language_tag")){
            return "language_tag";
        }
        if (nodePath.contains( "marketplace_id")){
            return "marketplace_id";
        }
        String value = SpelUtil.parse(nodePath, context);
        if (ObjUtil.equals(value, nodePath)){
            return "";
        }
        return value;
    }



    /**
     *  获取更新的属性列表
     * @param goodsHead
     * @param fields
     * @return
     */
    public List<AmazonListingJSONFeedVO.Attributes> getAmazonUpdateAttributeList(GoodsHead goodsHead,List<AmFieldEnum> fields) {
        List<AmazonListingJSONFeedVO.Attributes> returnList=new ArrayList<>();
        if (ObjUtil.isEmpty(goodsHead) || ObjUtil.isEmpty(fields)){
            return new ArrayList<>();
        }
        //获取品类 的 产品类型
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
        if (platformCategory == null) {
            return new ArrayList<>();
        }
        String vcFlag= goodsHead.getShopCode().startsWith("VC")?Constants.YesOrNo.YES:Constants.YesOrNo.NO;

        //获取属性映射 通过:item_name.value  获取: #{goodsHead.title}
        List<String> codePaths = fields.stream().map(AmFieldEnum::getCodePath).flatMap(List::stream).collect(Collectors.toList());
        List<AmCategoryTemplateSmcMapping> attributeMappings = amCategoryTemplateSmcMappingService.listMapping(null, platformCategory.getSite(), vcFlag, codePaths);
        if (CollUtil.isEmpty(attributeMappings)) {
            return new ArrayList<>();
        }

        //item_name.value  #{goodsHead.title}
        Map<String, List<String>> mappingFieldMap = attributeMappings.stream().collect(Collectors.groupingBy(AmCategoryTemplateSmcMapping::getPropNodePath, Collectors.mapping(AmCategoryTemplateSmcMapping::getMappingField, Collectors.toList())));
        if (ObjUtil.isEmpty(mappingFieldMap)){
            return new ArrayList<>();
        }
        //构建SPEL上下文
        StandardEvaluationContext context = amazonProductBiz.getContext(goodsHead, mappingFieldMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));

        //一次性可更新多个平台属性
        for (AmFieldEnum field : fields) {
            List<Object> valueList = new ArrayList<>();
            // 找到对应的mapping
            List<AmCategoryTemplateSmcMapping> mappingFieldList = attributeMappings.stream().filter(e -> field.getCodePath().contains(e.getPropNodePath())).collect(Collectors.toList());
            if (CollUtil.isEmpty(mappingFieldList)) {
                continue;
            }
            //当一个平台属性对应多个映射字段   如五点
            mappingFieldList = mappingFieldList.stream().sorted(Comparator.comparing(AmCategoryTemplateSmcMapping::getMappingField)).collect(Collectors.toList());
            Map<String, String> mappingFieldMapSingle = new HashMap<>();
            mappingFieldList.forEach((e)->{
                // 没有图片不处理
                if (e.getPropNodePath().contains("image")  && (goodsHead.getNonResource() != null && goodsHead.getNonResource())) {
                    return;
                }
                mappingFieldMapSingle.put(e.getPropNodePath(), e.getMappingField());
                Map<String, Object> attributeMap = getAttributeMap(platformCategory, mappingFieldMapSingle, context, field, goodsHead.getShopCode());
                if (ObjUtil.isEmpty(attributeMap)){
                    return;
                }
                valueList.add(attributeMap);
            });
            if (CollUtil.isEmpty(valueList)){
                continue;
            }
            AmazonListingJSONFeedVO.Attributes attributes = new AmazonListingJSONFeedVO.Attributes();
            attributes.setOp("REPLACE");
            attributes.setPath(field.getUpdatePath());
            attributes.setValue(valueList);
            returnList.add(attributes);
        }
        return returnList;
    }


    /**
     * 获取更新属性
     *
     * @param platformCategory
     * @param mappingFieldMap
     * @param context
     * @param field
     * @param shopCode
     * @return
     */
    private Map<String, Object> getAttributeMap(PlatformCategory platformCategory, Map<String, String> mappingFieldMap, StandardEvaluationContext context, AmFieldEnum field, String shopCode) {
        Map<String, Object> attributeMap = new HashMap<>();
        if (!field.getCodePath().contains(mappingFieldMap.keySet().toArray()[0])){
            return attributeMap;
        }
        for (String codePath : field.getCodePath()) {
            //获取到该一级属性字段 中 包含的所有属性
            AmCategoryTemplateFieldProp fieldPropQuery = new AmCategoryTemplateFieldProp();
            fieldPropQuery.setProductType(platformCategory.getProductType());
            fieldPropQuery.setFieldCode(field.getCode());
            fieldPropQuery.setSite(platformCategory.getSite());
            fieldPropQuery.setVcFlag(shopCode.contains("VC") ? Constants.YesOrNo.YES : Constants.YesOrNo.NO);
            List<AmCategoryTemplateFieldProp> fieldProps = amCategoryTemplateFieldPropService.selectAmCategoryTemplateFieldPropList(fieldPropQuery);
            if (CollUtil.isEmpty(fieldProps)){
                continue;
            }
            List<AmCategoryTemplateFieldProp> attributeChild = fieldProps.stream().filter(prop -> ObjUtil.equals(prop.getParentPropId(), 0L)).collect(Collectors.toList());
            Map<String, Object> attributeMapCurrent = getValue(mappingFieldMap, context, codePath, fieldProps,attributeChild);
            attributeMap.putAll(attributeMapCurrent);
        }
        if (ObjUtil.isNotEmpty(attributeMap)){
            //值有一个是空，则该数据不需要更新 (为空的都是value)
            boolean b = attributeMap.values().stream().anyMatch(ObjectUtil::isEmpty);
            return b ? new HashMap<>() : attributeMap;
        }
        return attributeMap;
    }

    private Map<String, Object> getValue(Map<String, String> mappingFieldMap, StandardEvaluationContext context, String codePath, List<AmCategoryTemplateFieldProp> fieldProps,List<AmCategoryTemplateFieldProp> fieldPropsChild) {
        Map<String, Object> attributeMap = new HashMap<>();
        for (AmCategoryTemplateFieldProp fieldProp : fieldPropsChild) {
            String mappingField = mappingFieldMap.get(fieldProp.getPropNodePath());
            if("none".equals(mappingField)){
                continue;
            }

            String value = getValue(context, ObjUtil.isEmpty(mappingField) ? fieldProp.getPropNodePath() : mappingField );
            if (StrUtil.isBlank(value) && !codePath.contains(fieldProp.getPropNodePath())){
                continue;
            }
            if ( StrUtil.isNotEmpty(value) || ObjUtil.equals(codePath,fieldProp.getPropNodePath()) ) {
                attributeMap.put(fieldProp.getPropCode(), value);
                continue;
            }

            Long id = fieldProp.getId();
            List<AmCategoryTemplateFieldProp> attributeChild = fieldProps.stream().filter(prop -> ObjUtil.equals(prop.getParentPropId(), id)).collect(Collectors.toList());
            Map<String, Object> valueChild = getValue(mappingFieldMap, context, codePath,fieldProps, attributeChild);
            if (ObjUtil.equals(fieldProp.getStructType(),"array")){
                attributeMap.put(fieldProp.getPropCode(),Lists.newArrayList(valueChild) );
            }else if (ObjUtil.equals(fieldProp.getStructType(),"object")){
                attributeMap.put(fieldProp.getPropCode(),valueChild );
            }
        }
        return attributeMap;
    }

    @Override
    public ItemDTO listingEditV2(ListingEditDTO listingEditDTO) {
        // 头部基础数据
        GoodsHead goodsHead = new GoodsHead();
        BeanUtil.copyProperties(listingEditDTO, goodsHead);
        goodsHead.setId(listingEditDTO.getGoodsHeadId());
        String publishStatus1 = listingEditDTO.getPublishStatus();
        GoodsHead dbHead = goodsHeadService.selectListingGoodsHeadById(listingEditDTO.getGoodsHeadId());
        if(dbHead.getDelFlag().equals(2)) {
            throw new BusinessException("该链接已删除");
        }

        //获取亚马逊商品三类属性信息
        ListingAmazonAttributeLineV2 attributeLineV2 = listingAmazonAttributeLineV2Service.getPnObj(listingEditDTO.getGoodsHeadId());
        if (attributeLineV2 != null) {
            if (CollUtil.isEmpty(listingEditDTO.getAmazonAttributeLines())) {
                listingEditDTO.setAmazonAttributeLines(new ArrayList<>());
            }
            listingEditDTO.getAmazonAttributeLines().removeIf(e -> "part_number.value".equals(e.getPropNodePath()));
            listingEditDTO.getAmazonAttributeLines().add(attributeLineV2);
        }
        List<ListingAmazonAttributeLineV2> attributeLines = goodsInfoBiz.parseAmazonAttributeLines(listingEditDTO, ShiroUtils.getUserId(), goodsHead);
        if(CollectionUtils.isEmpty(attributeLines) || attributeLines.size() == 1) {
            throw new BusinessException("属性不能为空");
        }
        Integer publishType = PublishType.getType(listingEditDTO.getPublishTypeName());
        // Cost Price 不能大于 List Price
        String listPrice =  attributeLines.stream().filter(e -> "list_price.value".equals(e.getPropNodePath())).findFirst().map(ListingAmazonAttributeLineV2::getTableValue).orElse("");
        String price = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(dbHead.getPdmGoodsCode(), listingEditDTO.getStandardPrice(), publishType)
                .headId(goodsHead.getId())
                .siteCode(dbHead.getSiteCode())
                .onlineTime(dbHead.getOnlineTime())
                .oldPrice(dbHead.getStandardPrice())
                .isOnline(StrUtil.isNotBlank(dbHead.getPlatformGoodsId()))
                .listPrice(listPrice)
                .build());
        goodsHead.setStandardPrice(price);
        goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
        goodsHead.setPublishingHandler("");
        goodsHead.setCreateBy(dbHead.getCreateBy());
        goodsHead.setShopCode(dbHead.getShopCode());
        goodsHead.setPdmGoodsCode(dbHead.getPdmGoodsCode());

        //状态流转
        if (StringUtils.isNotEmpty(publishStatus1)) {
            //如果是草稿状态则允许修改平台品类
            if (Arrays.asList(PUBLISH_FAIL.getType().toString(), DRAFT.getType().toString()).contains(publishStatus1) && !ObjectUtils.isEmpty(listingEditDTO.getProductCategoryCode())) {
                goodsHead.setCategoryId(Integer.valueOf(listingEditDTO.getProductCategoryCode()));
            }
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(Integer.valueOf(publishStatus1), PlatformTypeEnum.AM.name()));

        } else {
            Integer publishStatus = dbHead.getPublishStatus();
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(publishStatus, PlatformTypeEnum.AM.name()));
        }

        // 商品图片视频数据
        GoodsResource goodsResource = new GoodsResource();
        BeanUtil.copyProperties(listingEditDTO, goodsResource);

        // 商品描述数据
        GoodsDescription goodsDescription = new GoodsDescription();
        BeanUtil.copyProperties(listingEditDTO, goodsDescription);
        goodsDescription.setId(listingEditDTO.getGoodsDescriptionId());
        goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));

        // 商品规格数据
        GoodsSpecification goodsSpecification = new GoodsSpecification();
        BeanUtil.copyProperties(listingEditDTO, goodsSpecification);
        goodsSpecification.setId(listingEditDTO.getGoodsSpecificationId());
        if (StringUtils.isNotEmpty(listingEditDTO.getIsIrregularity())) {
            if (Objects.equals(goodsSpecification.getIsIrregularity(), "是")) {
                goodsSpecification.setIsIrregularity("1");
            } else if (Objects.equals(goodsSpecification.getIsIrregularity(), "否")) {
                goodsSpecification.setIsIrregularity("2");
            }
        }

        ItemDTO itemDTO = new ItemDTO();
        itemDTO.setGoodsHead(goodsHead);
        itemDTO.setGoodsSpecification(goodsSpecification);
        itemDTO.setGoodDescription(goodsDescription);
        itemDTO.setAmazonAttributeLines(attributeLines);
        amazonPlatformListingService.updateAmazonInfoTODB(listingEditDTO, itemDTO);

        //去将待办改为处理中
        TodoStatusEnum todoStatusEnum = ObjUtil.equals(goodsHead.getPublishStatus(), UPDATING.getType()) ? TodoStatusEnum.HANDLE_STATUS : TodoStatusEnum.FINISH_STATUS;
        smcTodoBiz.updateTodoStatusByListingUpdate(goodsHead.getId(), todoStatusEnum);

        // 检查是否需要将信息同步到其他相同ASIN的链接中
        Boolean syncOtherListings = listingEditDTO.getSyncOtherListings();
        String syncFields = listingEditDTO.getSyncFields();
        if (Boolean.TRUE.equals(syncOtherListings) && StringUtils.isNotEmpty(syncFields)) {
            String updateBy = ShiroUtils.getUserId() + "";
            // 开启线程执行
            threadPoolTaskExecutor.execute(() -> {
                Set<Integer> alreadySyncedIds = new HashSet<>();
                alreadySyncedIds.add(goodsHead.getId());
                sync2OtherListing(syncFields, Arrays.asList(goodsHead.getId()), alreadySyncedIds, updateBy);
            });
        }
        return itemDTO;
    }

    public void sync2OtherListing(String syncFields, List<Integer> goodsIds, Set<Integer> alreadySyncedIds, String updateBy) {
        try {
            Map<Integer, ItemDTO> itemDTOMap = new HashMap<>();
            List<String> fields = Arrays.asList(syncFields.split(","));
            boolean allUpdated = fields.size() == 4;

            for (Integer goodsId : goodsIds) {
                GoodsHead currentGoodsHead = goodsHeadService.selectListingGoodsHeadById(goodsId);
                if (StrUtil.isBlank(currentGoodsHead.getPlatformGoodsId()) || StrUtil.isBlank(currentGoodsHead.getCreateBy())){
                    return;
                }

                List<GoodsHead> otherListings = goodsHeadService.selectOtherListingsByAsin(currentGoodsHead.getPlatformGoodsId(), currentGoodsHead.getId().longValue());
                if (CollUtil.isEmpty(otherListings)) {
                    return;
                }
                // 其他链接的创建人和当前链接创建人一致
                otherListings.removeIf(e -> StrUtil.isBlank(e.getCreateBy())  || !e.getCreateBy().equals(currentGoodsHead.getCreateBy()));
                if (CollUtil.isEmpty(otherListings)) {
                    return;
                }

                List<GoodsResource> currentResourceList = goodsResourceService.selectListingGoodsResourceByHeadId(currentGoodsHead.getId());

                GoodsDescription currentGoodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(currentGoodsHead.getId());
                // 将同步字段转换为标题、五点描述、详情描述
                String fieldDesc = syncFields.replaceAll("title", "标题").replaceAll("itemDescription", "五点描述").replaceAll("detailDescription", "详情描述").replaceAll("pictures", "图片");

                // 遍历所有需要同步的链接
                for (GoodsHead otherListing : otherListings) {
                    if (alreadySyncedIds.contains(otherListing.getId())) {
                        continue;
                    }
                    alreadySyncedIds.add(otherListing.getId());
                    if (PublishStatus.getNoUpdateStatus().contains(otherListing.getPublishStatus())) {
                        log.info("当前链接状态不支持更新，链接主键ID：{}，链接状态：{}", otherListing.getId(), otherListing.getPublishStatus());
                        // 加入未更新日志
                        String logContent = "当前链接状态不支持更新";
                        listingLogService.insertErrorListingLog("通过链接主键ID：" + currentGoodsHead.getId() + "手动触发链接更新失败", updateBy, otherListing.getId(), logContent);
                        continue;
                    }

                    // 创建用于同步的对象
                    GoodsHead syncHead = new GoodsHead();
                    syncHead.setId(otherListing.getId());
                    syncHead.setUpdateBy(updateBy);
                    syncHead.setPlatform(otherListing.getPlatform());
                    syncHead.setShopCode(otherListing.getShopCode());
                    syncHead.setCategoryId(otherListing.getCategoryId());
                    syncHead.setPublishStatus(otherListing.getPublishStatus());
                    syncHead.setSiteCode(otherListing.getSiteCode());
                    syncHead.setPlatformGoodsId(otherListing.getPlatformGoodsId());
                    syncHead.setPlatformGoodsCode(otherListing.getPlatformGoodsCode());
                    syncHead.setPdmGoodsCode(otherListing.getPdmGoodsCode());
                    syncHead.setPublishType(otherListing.getPublishType());
                    boolean headUpdated = false;

                    // 商品描述对象
                    GoodsDescription syncDescription = goodsDescriptionService.selectDescriptionListByGoodsId(otherListing.getId());
                    if (syncDescription == null) {
                        continue;
                    }
                    boolean descUpdated = false;

                    boolean picturesUpdated = false;

                    List<GoodsResource> otherResourceList = null;
                
                    // 同步对应的字段
                    for (String field : fields) {
                        if ("title".equals(field)) {
                            // 同步标题
                            syncHead.setTitle(currentGoodsHead.getTitle());
                            ItemDTO itemDTO = new ItemDTO();
                            itemDTO.setGoodsHead(syncHead);
                            itemDTO.setModuleType(Arrays.asList(ListingModuleType.TITLE.name()));
                            if (!allUpdated) {
                                if (!itemDTOMap.containsKey(otherListing.getId())) {
                                    itemDTOMap.put(otherListing.getId(), itemDTO);
                                }else {
                                    itemDTOMap.get(otherListing.getId()).getModuleType().add(ListingModuleType.TITLE.name());
                                }
                            }
                            headUpdated = true;
                            continue;
                        }
                        if ("itemDescription".equals(field)) {
                            // 同步五点描述（全部）
                            syncDescription.setItemDescription1(currentGoodsDescription.getItemDescription1());
                            syncDescription.setItemDescription2(currentGoodsDescription.getItemDescription2());
                            syncDescription.setItemDescription3(currentGoodsDescription.getItemDescription3());
                            syncDescription.setItemDescription4(currentGoodsDescription.getItemDescription4());
                            syncDescription.setItemDescription5(currentGoodsDescription.getItemDescription5());

                            ItemDTO itemDTO = new ItemDTO();
                            itemDTO.setGoodsHead(syncHead);
                            itemDTO.setModuleType(Arrays.asList(ListingModuleType.DESCRIPTION.name()));
                            if (!allUpdated) {
                                if (!itemDTOMap.containsKey(otherListing.getId())) {
                                    itemDTOMap.put(otherListing.getId(), itemDTO);
                                }else {
                                    itemDTOMap.get(otherListing.getId()).getModuleType().add(ListingModuleType.DESCRIPTION.name());
                                }
                            }
                            descUpdated = true;
                            continue;
                        }
                        if ("detailDescription".equals(field)) {
                            // 同步详情描述
                            syncDescription.setDetailDescription(currentGoodsDescription.getDetailDescription());
                            ItemDTO itemDTO = new ItemDTO();
                            itemDTO.setGoodsHead(syncHead);
                            itemDTO.setModuleType(Arrays.asList(ListingModuleType.AMAZON_FIVE.name()));
                            if (!allUpdated) {
                                if (!itemDTOMap.containsKey(otherListing.getId())) {
                                    itemDTOMap.put(otherListing.getId(), itemDTO);
                                }else {
                                    itemDTOMap.get(otherListing.getId()).getModuleType().add(ListingModuleType.AMAZON_FIVE.name());
                                }
                            }
                            descUpdated = true;
                            continue;
                        }
                        if ("pictures".equals(field)) {
                            picturesUpdated = true;
                            otherResourceList = copyResourceList(currentResourceList, syncHead);
                            ItemDTO itemDTO = new ItemDTO();
                            itemDTO.setGoodsHead(syncHead);
                            itemDTO.setModuleType(Arrays.asList(ListingModuleType.IMAGE.name()));
                            if (!allUpdated) {
                                if (!itemDTOMap.containsKey(otherListing.getId())) {
                                    itemDTOMap.put(otherListing.getId(), itemDTO);
                                }else {
                                    itemDTOMap.get(otherListing.getId()).getModuleType().add(ListingModuleType.IMAGE.name());
                                }
                            }
                            continue;
                        }
                    }

                    amazonPlatformListingService.updateDB(headUpdated, syncHead, descUpdated, syncDescription, picturesUpdated, otherResourceList, allUpdated);
                    
                    // 加入日志
                    String oldPublishStatusName = PublishStatus.getPublishStatusName(otherListing.getPublishStatus());

                    String logContent = "跟卖[" + currentGoodsHead.getPlatformGoodsId()+"]，通过链接主键ID：" + currentGoodsHead.getId() + "手动触发链接更新，同步字段：" + fieldDesc;
                    logContent += "，数据由[" + oldPublishStatusName + "]移至[更新中]";
                    listingLogService.insertSuccessListingLog(logContent,  syncHead.getUpdateBy(), otherListing.getId());
                }
            }
            List<ItemDTO> itemDTOList = new ArrayList<>(itemDTOMap.values());
            if (CollUtil.isNotEmpty(itemDTOList)) {
                listingUpdateBuilder.updateApi(itemDTOList);
            }
        } catch (Exception e) {
            log.error("同步信息到其他相同ASIN的链接时出错", e);
            throw e;
        }
    }
    
    private List<GoodsResource> copyResourceList(List<GoodsResource> resourceList, GoodsHead syncHead) {
        if (CollUtil.isEmpty(resourceList)) {
            return new ArrayList<>();
        }
        List<GoodsResource> otherResourceList = new ArrayList<>();
        for (GoodsResource resource : resourceList) {
            GoodsResource otherResource = new GoodsResource();
            otherResource.setGoodsId(syncHead.getId());
            otherResource.setResourceUrl(resource.getResourceUrl());
            otherResource.setResourceName(resource.getResourceName());
            otherResource.setResourceType(resource.getResourceType());
            otherResource.setSourceType(resource.getSourceType());
            otherResource.setSortNumber(resource.getSortNumber());
            otherResource.setDelFlag(resource.getDelFlag());
            otherResource.setIsMain(resource.getIsMain());
            otherResource.setOssKey(resource.getOssKey());
            otherResource.setCreateBy(syncHead.getUpdateBy());
            otherResource.setCreateTime(new Date());
            otherResource.setUpdateBy(syncHead.getUpdateBy());
            otherResource.setUpdateTime(new Date());
            otherResourceList.add(otherResource);
        }
        return otherResourceList;
    }

    @Transactional
    public void updateDB(boolean headUpdated, GoodsHead syncHead, boolean descUpdated, GoodsDescription syncDescription, boolean picturesUpdated, 
    List<GoodsResource> otherResourceList, boolean allUpdated) {
        if (headUpdated) {
            if (allUpdated) {
                syncHead.setPublishStatus(PublishStatus.getStatusByEdit(syncHead.getPublishStatus(), PlatformTypeEnum.AM.name()));
                syncHead.setPublishingHandler("");
            }
            goodsHeadService.updateListingGoodsHead(syncHead);
        }

        // 保存同步的描述
        if (descUpdated) {
            goodsDescriptionService.updateListingGoodsDescription(syncDescription);
        }
        if (picturesUpdated) {
            goodsResourceService.deleteListingGoodsResourceByHeadId(syncHead.getId());
            if (CollUtil.isNotEmpty(otherResourceList)) {
                goodsResourceService.insertListingGoodsResourceBatch(otherResourceList);
            }
        }
    }

    private List<AmazonListingJSONFeedVO.Attributes> buildDeleteAttributes(List<GoodsResource> imagesList, int start, int end) {
        List<AmazonListingJSONFeedVO.Attributes> returnList = new ArrayList<>();
        for (int i = start; i < end; i++) {
            GoodsResource goodsResource = imagesList.get(i);

            AmazonListingJSONFeedVO.Attributes attributes = new AmazonListingJSONFeedVO.Attributes();
            attributes.setOp("DELETE");
            attributes.setPath("/attributes/other_product_image_locator_"  + goodsResource.getSortNumber());
            JSONArray value = new JSONArray();
            JSONObject valueObj = new JSONObject();
            valueObj.put("media_location", goodsResource.getResourceUrl());
            valueObj.put("marketplace_id", "");
            value.add(valueObj);
            attributes.setValue(value);
            returnList.add(attributes);
        }
        return returnList;
    }


    public AmazonListingJSONFeedVO.Attributes getAttributes(BigDecimal stock) {
        Map<String, Object> itemHashMap = new HashMap<>();
        itemHashMap.put("fulfillment_channel_code", "DEFAULT");
        itemHashMap.put("quantity", stock);

        AmazonListingJSONFeedVO.Attributes itemAttributes = new AmazonListingJSONFeedVO.Attributes();
        itemAttributes.setValue(Lists.newArrayList(itemHashMap));
        itemAttributes.setOp("REPLACE");
        itemAttributes.setPath("/attributes/fulfillment_availability");

        return itemAttributes;
    }

    @Override
    public List<String> saveBatchListingDTOV2(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return new ArrayList<>();
        }
        String vcFlag = dto.getShopCode().contains("VC") ? "Y" : "N";
        String siteCode = shopService.selectSiteCodeByShopCode(dto.getShopCode());

        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(dto.getCategoryId()));

        List<ItemDTO> itemDTOList = new ArrayList<>();
        Map<String,GoodsDetailDTO> goodsDetailMap = new HashMap<>();
        
        for (ListingDTO listing : listings) {
            String goodsCode = listing.getGoodsCode();
            if (ObjUtil.isNotEmpty(goodsCode)) {
                //去除KX1ALT00100U1(0)保留KX1ALT00100U1
                listing.setSortPdmGoodsCode(listing.getGoodsCode());
                listing.setGoodsCode(goodsCode.replaceAll("\\(.*\\)", ""));
            }
            if (StrUtil.isEmpty(listing.getTitle())) {
                throw new BusinessException("商品标题不能为空");
            }
            GoodsHead goodHead = getGoodHead(dto, siteCode, listing);
            goodHead.setSmcFlag(3);

            amazonProductBiz.canPutListing(goodHead.getBrandCode(), goodHead.getPdmGoodsCode(), goodsDetailMap, goodHead.getShopCode());

            List<GoodsResource> resourceList = getResourceList(dto, listing);

            if (StrUtil.isBlank(listing.getItemDescription1())) {
                throw new BusinessException("五点描述1不能为空");
            }
            GoodsDescription goodsDescription = getGoodsDescription(listing);
            goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));

            GoodsSpecification goodsSpecification = getGoodsSpecificationNew(listing, userId);

            //获取亚马逊商品三类属性信息
            listing.setSiteCode(siteCode);
            listing.setCategoryId(goodHead.getCategoryId());


            List<ListingAmazonAttributeLineV2> attributeLineV2s = getListingAmazonAttributeLinesV2New(dto, listing, platformCategory, vcFlag);
            addExtendLineV2(attributeLineV2s, listing, goodHead, platformCategory, vcFlag);

            // 移除part_number
            attributeLineV2s.removeIf(e -> "part_number.value".equals(e.getPropNodePath()));
            ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
            attributeLineV2.setPropNodePath("part_number.value");
            attributeLineV2.setTableName("Manufacturer Part Number");
            String platformCode = createPlatformCode.getPlatformCodeAync(goodHead.getPdmGoodsCode(), goodHead.getSiteCode(), String.valueOf(userId));
            attributeLineV2.setTableValue(platformCode);
            attributeLineV2.setTableType(0);
            attributeLineV2.setPdmGoodsCode(listing.getGoodsCode());
            attributeLineV2.setCategoryId(goodHead.getCategoryId());
            attributeLineV2.setProductType(platformCategory.getProductType());
            attributeLineV2.setVcFlag(vcFlag);
            attributeLineV2s.add(attributeLineV2);

            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(goodHead);
            itemDTO.setGoodsResourceList(resourceList);
            itemDTO.setGoodDescription(goodsDescription);
            itemDTO.setGoodsSpecification(goodsSpecification);
            itemDTO.setAmazonAttributeLines(attributeLineV2s);
            itemDTO.setUserId(userId);
            itemDTOList.add(itemDTO);
        }
        Map<String, Set<String>> violateWord = new HashMap<>();
        List<String> checkPrice = new LinkedList<>();
        for (ItemDTO itemDTO : itemDTOList) {
            try {
                GoodsHead goodsHead = itemDTO.getGoodsHead();
                String listPrice = itemDTO.getAmazonAttributeLines().stream().filter(e -> "list_price.value".equals(e.getPropNodePath())).findFirst().map(ListingAmazonAttributeLineV2::getTableValue).orElse("");  
                //校验价格
                String price = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), dto.getPublishType())
                        .siteCode(goodsHead.getSiteCode())
                        .oldPrice(goodsHead.getStandardPrice())
                        .listPrice(listPrice).build());
                goodsHead.setStandardPrice(price);
                goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
            } catch (Exception e) {
                checkPrice.add(e.getMessage());
            }
        }

        if (ObjUtil.isNotEmpty(violateWord) || CollectionUtil.isNotEmpty(checkPrice)) {
            assembleWordBefore(violateWord, checkPrice);
        }
        final String[] taskId = {null};
        List<String> businessIds = itemDTOList.stream().parallel().map(itemDTO -> {
            GoodsHead goodsHead = itemDTO.getGoodsHead();
            try {
                ListingDTO listing = new ListingDTO();
                listing.setGoodsCode(goodsHead.getPdmGoodsCode());
                listing.setSiteCode(siteCode);

                Integer goodsId = amazonPlatformListingService.saveAmazonInfoToDB(listing, itemDTO);
                return String.valueOf(goodsId);
            } catch (Exception e) {
                log.error("批量新建商品编码为：[" + goodsHead.getPdmGoodsCode() + "]listing草稿失败", e);
                throw new RuntimeException(e.getMessage());
            }
        }).collect(Collectors.toList());
        return businessIds;
    }

    @Override
    public List<ItemDTO> batchEditV2(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        dto.setFulfillmentLatency(null);
        String tempFlag = dto.getTempFlag();
        if (CollectionUtils.isEmpty(listings)) {
            return new ArrayList<>();
        }
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_EDIT, goodsIds, String.valueOf(userId));

        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0]))
                .stream()
                .collect(Collectors.toMap(GoodsHead::getId, e -> e));

        Map<Integer, List<GoodsDescription>> descMap = goodsDescriptionService.selectDescriptionListByGoodsIdList(goodsIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsDescription::getGoodsId));

        Map<Integer, List<GoodsSpecification>> specificationMap = goodsSpecificationService.selectSpecificationListByGoodsIds(goodsIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsSpecification::getGoodsId));

        // 对属性进行处理
        List<String> listingAttributeLine = dto.getListingAttributeLine();
        if (CollectionUtils.isEmpty(listingAttributeLine)) {
            throw new BusinessException("属性不能为空");
        }
        StringBuilder canNotUpdateSb = new StringBuilder();

        //组装对应的数据
        List<ItemDTO> itemDTOList = new ArrayList<>();

        List<ItemDTO> needUpdateApiList = new ArrayList<>();
        for (ListingDTO listing : listings) {
            listing.setCategoryId(dto.getCategoryId());
            GoodsHead dbHead = headMap.get(listing.getId());
            if (PublishStatus.getNoUpdateStatus().contains(dbHead.getPublishStatus())) {
                canNotUpdateSb.append("主键ID：" + dbHead.getId()).append("状态不允许修改").append("\n");
                continue;
            }

            String vcFlag = ObjUtil.isNotEmpty(dbHead.getShopCode()) && dbHead.getShopCode().contains("VC") ? Constants.YesOrNo.YES : Constants.YesOrNo.NO;

            PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(dbHead.getCategoryId()));

            GoodsDescription goodsDescription = descMap.get(listing.getId()).get(0);
            GoodsSpecification goodsSpecification = specificationMap.get(listing.getId()).get(0);

            GoodsHead updateHead = new GoodsHead();
            updateHead.setSiteCode(dbHead.getSiteCode());
            updateHead.setCreateBy(dbHead.getCreateBy());
            updateHead.setCategoryId(dbHead.getCategoryId());
            updateHead.setPlatform(dbHead.getPlatform());
            updateHead.setId(listing.getId());
            updateHead.setPlatformGoodsId(dbHead.getPlatformGoodsId());
            updateHead.setStockOnSalesQty(listing.getStockOnSalesQty());
            updateHead.setTitle(listing.getTitle());
            if (StringUtils.isNotEmpty(updateHead.getTitle())) {
                updateHead.setTitle(updateHead.getTitle().replaceAll("“", "\""));
                updateHead.setTitle(updateHead.getTitle().replaceAll("’", "'"));
            }
            //临时保存不改状态
            if (!ObjUtil.equals(tempFlag, "true")) {
                updateHead.setPublishStatus(PublishStatus.getStatusByEdit(dbHead.getPublishStatus(), PlatformTypeEnum.AM.name()));
            }
            updateHead.setStandardPrice(StrUtil.isBlank(listing.getStandardPrice()) ? null : listing.getStandardPrice());

            getGoodsDescription(listing, goodsDescription);
            if (Objects.nonNull(goodsDescription.getDetailDescription())) {
                goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));
            }

            List<ListingAmazonAttributeLineV2> attributeLines = getListingAmazonAttributeLinesV2New(dto, listing, platformCategory, vcFlag);
            addExtendLineV2(attributeLines, listing, dbHead, platformCategory, vcFlag);

            attributeLines.removeIf(e -> Arrays.asList("part_number.value").contains(e.getPropNodePath()));
            ListingAmazonAttributeLineV2 pnObj = listingAmazonAttributeLineV2Service.getPnObj(updateHead.getId());
            if (pnObj != null) {
                pnObj.setId(null);
                attributeLines.add(pnObj);
            }
            List<GoodsResource> newResourcesList = getResourceList(dto, listing);
            List<GoodsResource> dbResourcesList = goodsResourceService.selectListingGoodsResourceByHeadId(updateHead.getId()); 
            if (dbHead.getShopCode().contains("VC") && StrUtil.isNotBlank(dbHead.getPlatformGoodsId()) && newResourcesList.size() < dbResourcesList.size()) {
                // VC店铺不允许删除，接口不支持，可以做替换
                throw new BusinessException("VC店铺不允许删除图片");
            }


            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(updateHead);
            itemDTO.setGoodsResourceList(newResourcesList);
            itemDTO.setGoodDescription(goodsDescription);
            itemDTO.setGoodsSpecification(getGoodsSpecification(listing, goodsSpecification, dbHead));
            itemDTO.setUserId(userId);
            itemDTO.setDeleteOldAttr(true);
            itemDTO.setAmazonAttributeLines(attributeLines);
            itemDTOList.add(itemDTO);

            // 是否需要更新到平台
            if (ObjUtil.isNotEmpty(dbHead.getPlatformGoodsId())) {
                needUpdateApiList.add(itemDTO);
            }
        }
        if (canNotUpdateSb.length() > 0) {
            throw new BusinessException(canNotUpdateSb.toString());
        }

        Map<String, Set<String>> violateWord = new HashMap<>();
        List<String> checkPrice = new LinkedList<>();
        for (ItemDTO itemDTO : itemDTOList) {
            StringBuilder errorStr = new StringBuilder();
            GoodsHead goodsHead = itemDTO.getGoodsHead();
            GoodsHead dbHead = headMap.get(goodsHead.getId());

            try {
                violateWordBiz.checkViolateWord(true,itemDTO.getGoodDescription(), itemDTO.getGoodsAttributeLineList(), itemDTO.getGoodsHead());
            } catch (Exception e) {
                assembleWord(violateWord, e.getMessage());
                errorStr.append(e.getMessage());
            }
            try {
                String listPrice = itemDTO.getAmazonAttributeLines().stream().filter(e -> "list_price.value".equals(e.getPropNodePath())).findFirst().map(ListingAmazonAttributeLineV2::getTableValue).orElse("");  
                //校验价格
                String price = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(headMap.get(goodsHead.getId()).getPdmGoodsCode(), goodsHead.getStandardPrice(), headMap.get(goodsHead.getId()).getPublishType())
                        .headId(goodsHead.getId())
                        .siteCode(dbHead.getSiteCode())
                        .onlineTime(dbHead.getOnlineTime())
                        .oldPrice(dbHead.getStandardPrice())
                        .isOnline(StrUtil.isNotBlank(dbHead.getPlatformGoodsId()))
                        .listPrice(listPrice).build());
                goodsHead.setStandardPrice(price);
                goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
            } catch (Exception e) {
                checkPrice.add(e.getMessage());
                errorStr.append(e.getMessage());
            }

            if (StrUtil.isNotEmpty(errorStr)) {
                //有错误消息 该listing则是修改失败
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT), GoodsTaskSubStatusEnum.ERROR, String.valueOf(errorStr));
            }
            if (StrUtil.isBlank(errorStr) && !PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                //该listing不是更新中  并且 没有错误消息  则修改完成
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT), GoodsTaskSubStatusEnum.NORAML, "");
            }

        }

        if (ObjUtil.isNotEmpty(violateWord) || CollectionUtil.isNotEmpty(checkPrice)) {
            assembleWordBefore(violateWord, checkPrice);
        }

        //保存任务信息
        saveAndUpdateInfo(dto.getShopCode(), itemDTOList, String.valueOf(userId), false);

        return needUpdateApiList;
    }


    public GoodsSpecification getGoodsSpecification(ListingDTO listing,GoodsSpecification goodsSpecification,GoodsHead dbHead) {
        goodsSpecification.setItemLength( ObjUtil.isEmpty(listing.getItemLength()) ? BigDecimal.valueOf(0) : listing.getItemLength().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemWidth( ObjUtil.isEmpty(listing.getItemWidth()) ? BigDecimal.valueOf(0) :  listing.getItemWidth().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemHeight( ObjUtil.isEmpty(listing.getItemHeight()) ? BigDecimal.valueOf(0) :  listing.getItemHeight().setScale(2, RoundingMode.HALF_UP) );
        if (StrUtil.isNotBlank(listing.getItemLengthUnit())) {
            goodsSpecification.setItemLengthUnit(listing.getItemLengthUnit());
            goodsSpecification.setItemWidthUnit(listing.getItemLengthUnit());
            goodsSpecification.setItemHeightUnit(listing.getItemLengthUnit());
        }
        // 未刊登，可以修改包裹尺寸
        if (dbHead == null || StrUtil.isBlank(dbHead.getPlatformGoodsId()) || !dbHead.getSiteCode().contains("VC")) {
            goodsSpecification.setPackageLength( ObjUtil.isEmpty(listing.getPackageLength()) ? BigDecimal.valueOf(0) :listing.getPackageLength().setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageWidth( ObjUtil.isEmpty(listing.getPackageWidth()) ? BigDecimal.valueOf(0) :listing.getPackageWidth().setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageHeight( ObjUtil.isEmpty(listing.getPackageHeight()) ? BigDecimal.valueOf(0) :listing.getPackageHeight().setScale(2, RoundingMode.HALF_UP));
            if (StrUtil.isNotBlank(listing.getPackageLengthUnit())) {
                goodsSpecification.setPackageLengthUnit(listing.getPackageLengthUnit());
                goodsSpecification.setPackageWidthUnit(listing.getPackageLengthUnit());
                goodsSpecification.setPackageHeightUnit(listing.getPackageLengthUnit());
            }
        } else {
            // 不可修改包裹尺寸，设置为null
            goodsSpecification.setPackageLength(null);
            goodsSpecification.setPackageWidth(null);
            goodsSpecification.setPackageHeight(null);
            goodsSpecification.setPackageLengthUnit(null);
            goodsSpecification.setPackageWidthUnit(null);
            goodsSpecification.setPackageHeightUnit(null);
        }
        goodsSpecification.setPackageWeight( ObjUtil.isEmpty(listing.getPackageWeight()) ? BigDecimal.valueOf(0) :listing.getPackageWeight().setScale(2, RoundingMode.HALF_UP));
        goodsSpecification.setPackageWeightUnit(listing.getPackageWeightUnit());
        return goodsSpecification;
    }


    @Override
    public void batchEditStock(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<VcListingInventory> vcInventoryList = parseVcInventory(dto.getStocks());
        Map<String, List<VcListingInventory>> vcInventoryMap = vcInventoryList.stream().collect(Collectors.groupingBy(VcListingInventory::getWarehouseCode));

        List<Integer> goodsIds = vcInventoryList.stream().map(VcListingInventory::getGoodsId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(goodsIds)) {
            return;
        }
        if (goodsIds.size() > 100) {
            throw new BusinessException("批量更新库存数量不能超过100个");
        }
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));

        headMap.forEach((k, goodsHead) -> {
            if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
            }
        });
        // 判断是否已有数据正在处理中
        String keyPrefix = "AMAZON_UPDATE_INVENTORY:MANUAL:";
        List<VcListingInventory> needCacheList = new ArrayList<>();
        for (Map.Entry<String, List<VcListingInventory>> stringListEntry : vcInventoryMap.entrySet()) {
            for (VcListingInventory vcListingInventory : stringListEntry.getValue()) {
                Object cacheMapValue = redisService.getCacheObject(keyPrefix + stringListEntry.getKey() + ":" + vcListingInventory.getGoodsId());
                if (cacheMapValue != null) {
                    throw new BusinessException("商品编码为：" + vcListingInventory.getGoodsId() + "的数据正在库存更新中，请稍后再试");
                }else {
                    needCacheList.add(vcListingInventory);
                }
            }
        }
        if (allEmpty(vcInventoryMap)) {
            return;
        }
        if (CollUtil.isNotEmpty(needCacheList)) {
            needCacheList.forEach(inventory -> {
                redisService.setCacheObject(keyPrefix + inventory.getWarehouseCode() + ":" + inventory.getGoodsId(), "1", 30L, TimeUnit.MINUTES);
            });
        }

        List<AmazonWarehouseMapping> warehouseMappings = amazonWarehouseMappingService.selectAmazonWarehouseMappingListByShopCode("VC1");
        Map<String, String> warehouseCodeMap = warehouseMappings.stream().collect(Collectors.toMap(AmazonWarehouseMapping::getAmWhCode, AmazonWarehouseMapping::getWhCode));

        // 补全
        completeGoodsHead(headMap, vcInventoryMap, warehouseCodeMap);

        //查询启用库存更新的店铺
        ConfigStoreInfo vcShop = storeInfoService.selectConfigStoreInfoByShopCode("VC1");
        if (vcShop == null) {
            throw new BusinessException("未查询到店铺信息");
        }

        // 创建任务
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK, goodsIds, String.valueOf(userId));

        List<String> transactionIds = new ArrayList<>();
        AtomicBoolean isUpdate = new AtomicBoolean(false);
        // 日志内容
        Map<Integer, StringBuilder> goodsLogMap = new HashMap<>();
        // 开始执行任务
        vcInventoryMap.forEach((amWhCode, inventoryList) -> {
            try {
                if (CollectionUtil.isEmpty(inventoryList)) {
                    return;
                }
                inventoryList = inventoryList.stream().filter(e -> e.getAvailableInventory() != null && StrUtil.isNotBlank(e.getAsin())
                        && StrUtil.isNotBlank(e.getWhCode()) && StrUtil.isNotBlank(e.getSku()) && StrUtil.isNotBlank(e.getSellerSku()) &&
                        StrUtil.isNotBlank(e.getShopCode()) && StrUtil.isNotBlank(e.getWarehouseCode())).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(inventoryList)) {
                    return;
                }
                inventoryList.forEach(inventory -> {
                    StringBuilder logContent = goodsLogMap.computeIfAbsent(inventory.getGoodsId(), k -> new StringBuilder());
                    logContent.append("仓库编码：").append(amWhCode).append("，库存更新为：").append(inventory.getAvailableInventory()).append("；");
                });

                baseAmazonProductUpdateV2Task.updateInventoryAmazonVc(vcShop, amWhCode, inventoryList, transactionIds);
                if (CollectionUtil.isNotEmpty(transactionIds)) {
                    String transactionId = transactionIds.get(0);
                    List<Integer> goodsIdList = inventoryList.stream().map(VcListingInventory::getGoodsId).distinct().collect(Collectors.toList());
                    Map<String, Object> goodsIdMap = new HashMap<>();
                    goodsIdMap.put("amWhCode", amWhCode);
                    goodsIdMap.put("goodsIdList", goodsIdList);
                    redisService.setCacheObject("AMAZON_UPDATE_INVENTORY:MANUAL:TRANSACTION:" + transactionId, goodsIdMap, 30L, TimeUnit.MINUTES);
                    isUpdate.set(true);
                }else {
                    inventoryList.forEach(inventory -> {
                        redisService.deleteObject(keyPrefix + amWhCode + ":" + inventory.getGoodsId());
                    });
                }
            }catch (Exception e) {
                log.error("批量更新库存失败", e);
                inventoryList.forEach(inventory -> {
                    redisService.deleteObject(keyPrefix + amWhCode + ":" + inventory.getGoodsId());
                });
            }
        });
        if (!isUpdate.get()) {
            throw new BusinessException("批量更新库存失败");
        }
        // 加入库存更新黑名单
        inventoryExcludeBiz.addListingByHeadIds(vcShop.getPlatform(), vcShop.getSite(), vcShop.getShopCode(), goodsIds, userId);

        goodsLogMap.forEach((goodsId, logContent) -> {
            listingLogService.insertSuccessListingLog(logContent.toString(), userId+"", goodsId);
        });
        // 任务状态回写
        headMap.forEach((k, goodsHead) -> {
            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK), GoodsTaskSubStatusEnum.NORAML, "");
        });

        //处理待办的状态
        loseCartTodoService.confirmByGoodIds(goodsIds, userId);
        inventoryLowTodoService.confirmByGoodIds(goodsIds, userId);
    }

    @Override
    public void batchUpdateAdapterText(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        ListingDTO listing = listings.get(0);
        Integer goodsId = listing.getId();

        //创建任务
        GoodsTask goodsTask = goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.TODO_TEXT_UPDATE, Lists.newArrayList(goodsId), String.valueOf(userId));

        //查询数据
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(goodsId);
        GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsId);
        if (ObjUtil.isEmpty(goodsHead)) {
            throw new BusinessException("listing数据不存在，请重新确认.");
        }
        //整理出需要更新的模块
        List<String> modules = getModules(listing,goodsHead.getTitle(),goodsDescription);
        if (CollectionUtils.isEmpty(modules)){
            throw new BusinessException("文案没有修改,请重新编辑,如需不用处理请联系管理员.");
        }
        if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
            throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
        }
        //更新待办状态
        SmcAdaptTodo update = new SmcAdaptTodo();
        update.setId(listing.getTodoId());
        update.setStatus(5);

        List<ItemDTO> itemDTOList = new ArrayList<>();
        Map<String, Set<String>> violateWord = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        try {
            //记日志  改了啥
            sb.append(modules.contains(ListingModuleType.TITLE.name())?"修改了标题:"+goodsHead.getTitle()+"->"+listing.getTitle()+";":"");
            sb.append(modules.contains(ListingModuleType.DESCRIPTION.name())?"修改了详情描述;":"");
            sb.append(modules.contains(ListingModuleType.AMAZON_FIVE.name())?"修改了五点描述;":"");

            //放标题
            goodsHead.setTitle(listing.getTitle());

            //放五点、长描述
            getGoodsDescription(listing, goodsDescription);
            if (Objects.nonNull(goodsDescription.getDetailDescription())) {
                goodsDescription.setDetailDescription(EscapeUtil.escapeHtml4(goodsDescription.getDetailDescription()));
            }

            //违禁词检测
            violateWordBiz.checkViolateWord(true,goodsDescription, null, goodsHead);

            //更新入库
            if (modules.contains(ListingModuleType.TITLE.name())) {
                GoodsHead updateHead = new GoodsHead();
                updateHead.setId(goodsHead.getId());
                updateHead.setTitle(listing.getTitle());
                updateHead.setTitle(updateHead.getTitle().replaceAll("“", "\"").replaceAll("’", "'"));
                goodsHeadService.updateListingGoodsHead(updateHead);
            }
            if (modules.contains(ListingModuleType.DESCRIPTION.name()) || modules.contains(ListingModuleType.AMAZON_FIVE.name())) {
                goodsDescriptionService.updateListingGoodsDescription(goodsDescription);
            }

            //加入更新后续流程
            if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && !PublishStatus.getUnderWayStatus().contains(goodsHead.getPublishStatus())
                    && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                ItemDTO itemDTO = new ItemDTO();
                itemDTO.setGoodsHead(goodsHead);
                itemDTO.setModuleType( modules );
                itemDTOList.add(itemDTO);
            } else {
                goodsTaskInfoService.updateGoodsStatusTaskInfo(goodsTask.getId(), String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.TODO_TEXT_UPDATE), GoodsTaskSubStatusEnum.NORAML, "");
            }
            //回写日志
            listingLogService.insertSuccessListingLog("适配文案变更待办处理,"+sb, String.valueOf(userId), Integer.valueOf(goodsHead.getId()));
        } catch (Exception e) {
            log.error("适配文案变更待办处理listing文案失败,listingId:{}", listing.getId(), e);
            goodsTaskInfoService.updateGoodsStatusTaskInfo(goodsTask.getId(), String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.TODO_TEXT_UPDATE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            //更新待办状态
            update.setStatus(3);
            //回写日志
            listingLogService.insertErrorListingLog("适配文案变更待办处理,"+sb, String.valueOf(userId), Integer.valueOf(goodsHead.getId()), e.getMessage());
            if (e instanceof BusinessException) {
                assembleWord(violateWord, e.getMessage());
            }
        }
        //更新待办状态
        smcAdaptTodoService.updateSmcAdaptTodo(update);

        if (ObjUtil.isNotEmpty(violateWord)) {
            assembleWordBefore(violateWord);
        }

        if(CollUtil.isNotEmpty(itemDTOList)){
            listingUpdateBuilder.updateApi(itemDTOList);
        }
    }

    @Override
    public void batchUpdatePriceAndStockV2(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        dto.setFulfillmentLatency(null);
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<ItemDTO> needUpdate2Platform = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).distinct().collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.AM.name(), GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK, goodsIds, String.valueOf(userId));

        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        // 封装成List<GoodsHead>
        List<GoodsHead> goodsHeads = new ArrayList<>();

        StringBuilder canNotUpdateSb = new StringBuilder();
        for (ListingDTO listing : listings) {
            listing.setCategoryId(dto.getCategoryId());
            GoodsHead dbHead = headMap.get(listing.getId());
            if (PublishStatus.getNoUpdateStatus().contains(dbHead.getPublishStatus())) {
                if (canNotUpdateSb.length() > 0) {
                    canNotUpdateSb.append("，");
                }
                canNotUpdateSb.append("主键ID：").append(dbHead.getId()).append("状态不允许修改");
                continue;
            }

            String vcFlag = ObjUtil.isNotEmpty(dbHead.getShopCode()) && dbHead.getShopCode().contains("VC") ? Constants.YesOrNo.YES : Constants.YesOrNo.NO;

            PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(dbHead.getCategoryId()));

            GoodsHead updateHead = new GoodsHead();
            updateHead.setSiteCode(dbHead.getSiteCode());
            updateHead.setCreateBy(dbHead.getCreateBy());
            updateHead.setCategoryId(dbHead.getCategoryId());
            updateHead.setPlatform(dbHead.getPlatform());
            updateHead.setShopCode(dbHead.getShopCode());
            updateHead.setId(listing.getId());
            updateHead.setPdmGoodsCode(dbHead.getPdmGoodsCode());
            updateHead.setPlatformGoodsCode(dbHead.getPlatformGoodsCode());
            updateHead.setPlatformGoodsId(dbHead.getPlatformGoodsId());
            updateHead.setPublishType(dbHead.getPublishType());
            if(PublishType.FBM.getType().equals(dbHead.getPublishType())) {
                updateHead.setStockOnSalesQty(listing.getStockOnSalesQty());
            }
            updateHead.setStandardPrice(listing.getStandardPrice());
            updateHead.setUpdateBy(ShiroUtils.getUserId()+"");
//            updateHead.setPublishStatus(PublishStatus.getStatusByEdit(dbHead.getPublishStatus(), PlatformTypeEnum.AM.name()));

            List<ListingAmazonAttributeLineV2> listPriceLines = new ArrayList<>();
            addExtendLineV2(listPriceLines, listing, dbHead, platformCategory, vcFlag);

            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(updateHead);
            itemDTO.setUserId(userId);
            itemDTO.setAmazonAttributeLines(listPriceLines);
            if (dbHead.getShopCode().contains("VC")) {
                itemDTO.setModuleType(Arrays.asList(ListingModuleType.PRICE.name()));
            }else {
                itemDTO.setModuleType(Arrays.asList(ListingModuleType.PRICE.name(), ListingModuleType.INVENTORY.name()));
            }
            itemDTOList.add(itemDTO);

        }
        if (canNotUpdateSb.length() > 0) {
            throw new BusinessException(canNotUpdateSb.toString());
        }

        List<String> checkPrice = new LinkedList<>();
        for (ItemDTO itemDTO : itemDTOList) {
            StringBuilder errorStr = new StringBuilder();
            GoodsHead goodsHead = itemDTO.getGoodsHead();
            try {
                GoodsHead dbHead = headMap.get(goodsHead.getId());
                //校验价格
                String listPrice = itemDTO.getAmazonAttributeLines().stream().filter(e -> "list_price.value".equals(e.getPropNodePath())).findFirst().map(ListingAmazonAttributeLineV2::getTableValue).orElse("");      
                String price = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(headMap.get(goodsHead.getId()).getPdmGoodsCode(), goodsHead.getStandardPrice(), headMap.get(goodsHead.getId()).getPublishType())
                        .headId(goodsHead.getId())
                        .siteCode(dbHead.getSiteCode())
                        .onlineTime(dbHead.getOnlineTime())
                        .oldPrice(dbHead.getStandardPrice())
                        .isOnline(StrUtil.isNotBlank(dbHead.getPlatformGoodsId()))
                        .listPrice(listPrice).build());
                goodsHead.setStandardPrice(price);
                goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
            } catch (Exception e) {
                checkPrice.add(e.getMessage());
                errorStr.append(e.getMessage());
            }

            if (StrUtil.isNotEmpty(errorStr)) {
                //有错误消息 该listing则是修改失败
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK), GoodsTaskSubStatusEnum.ERROR, String.valueOf(errorStr));
            }else {
                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && !PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())
                        && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                    needUpdate2Platform.add(itemDTO);
                }
            }
            if (StrUtil.isBlank(errorStr) && !PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                //该listing不是更新中  并且 没有错误消息  则修改完成
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK), GoodsTaskSubStatusEnum.NORAML, "");
            }
        }

        if (CollectionUtil.isNotEmpty(checkPrice)) {
            assembleWordBefore(null, checkPrice);
        }

        Boolean checkResult = dingdingMonitorInfoBiz.monitorAMListingAndSend(goodsHeads, AM_QUICK_UPDATE);
        if (checkResult != null &&!checkResult) {
            throw new BusinessException("[系统拦截] 改动的数据变化较大，请联系管理员核对");
        }

        //保存任务信息
        updatePriceAndStockInfo(dto.getShopCode(), itemDTOList, String.valueOf(userId));

        //处理待办的状态
        loseCartTodoService.confirmByGoodIds(goodsIds, userId);
        inventoryLowTodoService.confirmByGoodIds(goodsIds, userId);

        if (CollUtil.isNotEmpty(needUpdate2Platform)) {
            threadPoolForMonitorManager.getThreadPoolExecutor("eidtPoolConfig").execute(() -> {
                listingUpdateBuilder.updateApi(needUpdate2Platform);
            });
        }
    }

    public void updatePriceAndStockInfo(String shopCode, List<ItemDTO> itemDTOList, String userId) {
        for (ItemDTO itemDTO : itemDTOList) {
            itemDTO.setUserId(Long.valueOf(userId));
            GoodsHead goodsHead = itemDTO.getGoodsHead();
            try {
                GoodsHead goodsHeadOld = goodsHeadService.selectListingGoodsHeadById(goodsHead.getId());
                // 获取旧的List Price
                ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2 = listingAmazonAttributeLineV2Service.getAttrByPropNodePath(goodsHead.getId(), "list_price.value");
                // 获取旧的Sale Price
                ListingAmazonAttributeLineV2 listingAmazonAttributeLineSalePrice = listingAmazonAttributeLineV2Service.getAttrByPropNodePath(goodsHead.getId(), "purchasable_offer.discounted_price.schedule.value_with_tax");

                //修改
                amazonPlatformListingService.updatePriceAndStockInfoDB(itemDTO, goodsHead);
                
                ExceptionUtil.sandbox(() -> insertUpdateLog(userId, goodsHeadOld, listingAmazonAttributeLineV2,listingAmazonAttributeLineSalePrice, false));
                ExceptionUtil.sandbox(() -> monitorListingSnapshotService.saveListingSnapshot(goodsHead));
            } catch (Exception e) {
                log.error("亚马逊商品同步异常,shopCode:{},asin:{}", shopCode,goodsHead.getPlatformGoodsId(), e);
                String errorMsg = "保存商品失败."+e.getMessage();
                listingLogService.insertErrorListingLog(errorMsg,userId,goodsHead.getId(),errorMsg);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePriceAndStockInfoDB(ItemDTO itemDTO, GoodsHead goodsHead) {
        String vcFlag = ObjUtil.isNotEmpty(goodsHead.getShopCode()) && goodsHead.getShopCode().contains("VC") ? Constants.YesOrNo.YES : Constants.YesOrNo.NO;
        goodsHeadService.updateListingGoodsHead(goodsHead);

        listingAmazonAttributeLineV2Service.deleteListingAmazonAttributeLineV2ByGoodIdAndPathList(goodsHead.getId(), Arrays.asList(LIST_PRICE.getInfoV2(vcFlag), LIST_CURRENCY.getInfoV2(vcFlag)));
        itemDTO.getAmazonAttributeLines().forEach(attributeLine -> {
            attributeLine.setHeadId(goodsHead.getId().longValue());
            listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(attributeLine);
        });
    }


    private List<String> getModules(ListingDTO listing,String title,GoodsDescription goodsDescription) {
        List<String> modules = new ArrayList<>();
        if ( StringUtils.isNotEmpty(listing.getTitle()) && !Objects.equals(listing.getTitle().replaceAll("“", "\"").replaceAll("’", "'"),title) ){
            modules.add(ListingModuleType.TITLE.name());
        }
        if ( StringUtils.isNotEmpty(listing.getItemDescription1()) && !Objects.equals(listing.getItemDescription1(),goodsDescription.getItemDescription1())){
            modules.add(ListingModuleType.AMAZON_FIVE.name());
        }
        if ( StringUtils.isNotEmpty(listing.getItemDescription2()) && !Objects.equals(listing.getItemDescription2(),goodsDescription.getItemDescription2())){
            modules.add(ListingModuleType.AMAZON_FIVE.name());
        }
        if ( StringUtils.isNotEmpty(listing.getItemDescription3()) && !Objects.equals(listing.getItemDescription3(),goodsDescription.getItemDescription3())){
            modules.add(ListingModuleType.AMAZON_FIVE.name());
        }
        if ( StringUtils.isNotEmpty(listing.getItemDescription4()) && !Objects.equals(listing.getItemDescription4(),goodsDescription.getItemDescription4())){
            modules.add(ListingModuleType.AMAZON_FIVE.name());
        }
        if ( StringUtils.isNotEmpty(listing.getItemDescription5()) && !Objects.equals(listing.getItemDescription5(),goodsDescription.getItemDescription5())){
            modules.add(ListingModuleType.AMAZON_FIVE.name());
        }
        if (StringUtils.isNotEmpty(listing.getDetailDescription()) && !Objects.equals(listing.getDetailDescription(),goodsDescription.getDetailDescription())){
            modules.add(ListingModuleType.DESCRIPTION.name());
        }
        if (CollUtil.isNotEmpty(modules)){
            modules=modules.stream().distinct().collect(Collectors.toList());
        }
        return modules;
    }

    private boolean allEmpty(Map<String, List<VcListingInventory>> vcInventoryMap) {
        return vcInventoryMap.values().stream().allMatch(CollectionUtil::isEmpty);
    }

    private void removeNoUpdateInventory(Map<String, List<VcListingInventory>> vcInventoryMap, Map<Integer, List<VcListingInventory>> oldInventoryMap) {
        vcInventoryMap.forEach((goodsCode, inventories) -> {
            inventories.removeIf(inventory -> {
                List<VcListingInventory> oldInventories = oldInventoryMap.get(inventory.getGoodsId());
                if (CollectionUtil.isEmpty(oldInventories)) {
                    return false;
                }
                VcListingInventory oldInventory = oldInventories.stream().filter(e -> e.getWhCode().equals(inventory.getWhCode())).findFirst().orElse(null);
                if (oldInventory == null) {
                    return false;
                }
                return Objects.equals(oldInventory.getAvailableInventory(), inventory.getAvailableInventory());
            });
        });
    }



    private void completeGoodsHead(Map<Integer, GoodsHead> headMap, Map<String, List<VcListingInventory>> inventoryMap, Map<String, String> warehouseCodeMap) {
        inventoryMap.forEach((goodsCode, inventories) -> {
            inventories.forEach(inventory -> {
                GoodsHead goodsHead = headMap.get(inventory.getGoodsId());

                if (goodsHead != null && StrUtil.isNotBlank(goodsHead.getPlatformGoodsId()) && StrUtil.isNotBlank(goodsHead.getPlatformGoodsCode()) && StrUtil.isNotBlank(goodsHead.getPdmGoodsCode())) {
                    inventory.setAsin(goodsHead.getPlatformGoodsId());
                    inventory.setSellerSku(goodsHead.getPlatformGoodsCode());
                    inventory.setWhCode(warehouseCodeMap.get(inventory.getWarehouseCode()));
                    inventory.setShopCode(goodsHead.getShopCode());
                    inventory.setSku(goodsHead.getPdmGoodsCode());
                }
            });
        });
    }


    private List<VcListingInventory> parseVcInventory(List<String> stocks) {
        List<VcListingInventory> vcListingInventories = new ArrayList<>();
        stocks.forEach(stock -> {
            JSONObject jsonObject = JSON.parseObject(stock);


            VcListingInventory vcListingInventory = new VcListingInventory();
            vcListingInventory.setGoodsId(jsonObject.getInteger("headId"));
            vcListingInventory.setAvailableInventory(jsonObject.getInteger("availableInventory"));
            if(vcListingInventory.getAvailableInventory() == null) {
                return;
            }
            vcListingInventory.setWarehouseCode(jsonObject.getString("warehouseCode"));
            vcListingInventory.setWhCode(jsonObject.getString("whCode"));
            vcListingInventories.add(vcListingInventory);
        });
        return vcListingInventories;
    }

    private List<ListingAmazonAttributeLineV2> parseAttributeLine(List<String> listingAttributeLine, List<ListingDTO> listings) {
        List<ListingAmazonAttributeLineV2> attributeLineV2s = new ArrayList<>();
        for (String line : listingAttributeLine) {
            JSONObject jsonObject = JSON.parseObject(line);
            ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
            long goodsId = jsonObject.getLong("id");
            String value = jsonObject.getString("tableValue");
            String propNodePath = jsonObject.getString("propNodePath");
            attributeLineV2.setHeadId(goodsId);
            attributeLineV2.setTableValue(value);
            attributeLineV2.setPropNodePath(propNodePath);
            attributeLineV2s.add(attributeLineV2);
        }
        return attributeLineV2s;
    }

    private void addExtendLineV2(List<ListingAmazonAttributeLineV2> attributeLineV2s, ListingDTO listing, GoodsHead goodHead, PlatformCategory platformCategory, String vcFlag) {
        String listPrice = listing.getListPrice();

        ListingAmazonAttributeLineV2 line = new ListingAmazonAttributeLineV2();
        line.setCategoryId(listing.getCategoryId());
        line.setPdmGoodsCode(listing.getGoodsCode());
        line.setProductType(platformCategory.getProductType());
        line.setTableValue(StrUtil.isBlank(listPrice) ? listPrice : new BigDecimal(StrUtil.trim(listPrice)).setScale(2, RoundingMode.HALF_UP).toString());
        line.setTableType(4);
        line.setPropNodePath(LIST_PRICE.getInfoV2(vcFlag));
        line.setVcFlag(vcFlag);
        attributeLineV2s.add(line);

        ListingAmazonAttributeLineV2 unitLine = new ListingAmazonAttributeLineV2();
        unitLine.setCategoryId(listing.getCategoryId());
        unitLine.setPdmGoodsCode(listing.getGoodsCode());
        unitLine.setProductType(platformCategory.getProductType());
        unitLine.setTableValue("USD");
        unitLine.setTableType(4);
        unitLine.setPropNodePath(LIST_CURRENCY.getInfoV2(vcFlag));
        unitLine.setVcFlag(vcFlag);
        attributeLineV2s.add(unitLine);
    }


    private List<ListingAmazonAttributeLineV2> getListingAmazonAttributeLinesV2New(BatchListingDTO dto, ListingDTO listing, PlatformCategory platformCategory, String vcFlag) {
        List<ListingAmazonAttributeLineV2> amazonAttributeLines = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(dto.getListingAttributeLine())) {
            for (String listingAmazonAttributeLineStr : dto.getListingAttributeLine()) {
                ListingAmazonAttributeLineV2 attributeLine = JSON.parseObject(listingAmazonAttributeLineStr, ListingAmazonAttributeLineV2.class);
                if (ObjectUtils.isEmpty(attributeLine)) {
                    continue;
                }
                if(attributeLine.getHeadId() != null) {
                    if (!Objects.equals(attributeLine.getHeadId().intValue(), listing.getId())) {
                        continue;
                    }
                }else {
                    if (!Objects.equals(attributeLine.getPdmGoodsCode(), listing.getSortPdmGoodsCode())) {
                        continue;
                    }
                }

                //判断是否是平台商品编码类型
                if (isSpecialAttr(attributeLine.getPropNodePath())) {
                    if("ASIN".equalsIgnoreCase(attributeLine.getPropNodePath())) {
                        saveAsin(amazonAttributeLines, attributeLine, listing, platformCategory.getProductType(), vcFlag);
                    }else {
                        handleSpecialV2(amazonAttributeLines, attributeLine, listing, platformCategory.getProductType(), vcFlag);
                    }
                    continue;
                }
                ListingAmazonAttributeLineV2 line = new ListingAmazonAttributeLineV2();
                line.setCategoryId(listing.getCategoryId());
                line.setPdmGoodsCode(listing.getGoodsCode());
                line.setProductType(platformCategory.getProductType());
                line.setTableValue(StrUtil.isBlank(attributeLine.getTableValue()) ? "" : attributeLine.getTableValue().trim());
                line.setTableName(attributeLine.getTableName());
                line.setVcFlag(vcFlag);
                line.setTableType(attributeLine.getTableType() == null ? 0 : attributeLine.getTableType());
                line.setPropNodePath(attributeLine.getPropNodePath());
                amazonAttributeLines.add(line);
            }
        }
        return amazonAttributeLines;
    }

    private void saveAsin(List<ListingAmazonAttributeLineV2> amazonAttributeLines, ListingAmazonAttributeLineV2 attributeLine, ListingDTO listing, String productType, String vcFlag) {
        // 存在就不增加
        if (amazonAttributeLines.stream().anyMatch(e -> e.getPropNodePath().equalsIgnoreCase(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE.getInfoV2(vcFlag)) &&
                e.getTableType().equals(5) && e.getTableValue().equals("ASIN"))) {
            return;
        }
        //类型
        ListingAmazonAttributeLineV2 lineType = new ListingAmazonAttributeLineV2();
        lineType.setCategoryId(listing.getCategoryId());
        lineType.setPropNodePath(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE.getInfoV2(vcFlag));
        lineType.setTableValue("ASIN");
        lineType.setPdmGoodsCode(listing.getGoodsCode());
        lineType.setTableType(5);
        lineType.setProductType(productType);
        lineType.setVcFlag(vcFlag);
        amazonAttributeLines.add(lineType);
        //值
        ListingAmazonAttributeLineV2 lineValue = new ListingAmazonAttributeLineV2();
        lineValue.setCategoryId(listing.getCategoryId());
        lineValue.setPropNodePath(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID.getInfoV2(vcFlag));
        lineValue.setPdmGoodsCode(listing.getGoodsCode());
        lineValue.setTableValue(attributeLine.getTableValue());
        lineValue.setProductType(productType);
        lineValue.setTableType(5);
        lineType.setVcFlag(vcFlag);
        amazonAttributeLines.add(lineValue);
    }

    public boolean isSpecialAttr(String propNodePath) {
        AmazonSpecialAttributeV2Enum[] values = AmazonSpecialAttributeV2Enum.values();
        for (AmazonSpecialAttributeV2Enum value : values) {
            if (value.toString().equalsIgnoreCase(propNodePath)) {
                return true;
            }
        }
        return "ASIN".equalsIgnoreCase(propNodePath);
    }

    private void handleSpecialV2(List<ListingAmazonAttributeLineV2> amazonAttributeLines, ListingAmazonAttributeLineV2 attributeLine, ListingDTO listing, String productType, String vcFlag) {
        //类型
        ListingAmazonAttributeLineV2 lineType = new ListingAmazonAttributeLineV2();
        lineType.setCategoryId(listing.getCategoryId());
        lineType.setPropNodePath(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE.getInfoV2(vcFlag));
        lineType.setTableValue(attributeLine.getPropNodePath().toUpperCase());
        lineType.setPdmGoodsCode(listing.getGoodsCode());
        lineType.setTableType(4);
        lineType.setProductType(productType);
        lineType.setVcFlag(vcFlag);
        amazonAttributeLines.add(lineType);
        //值
        ListingAmazonAttributeLineV2 lineValue = new ListingAmazonAttributeLineV2();
        lineValue.setCategoryId(listing.getCategoryId());
        lineValue.setPropNodePath(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID.getInfoV2(vcFlag));
        lineValue.setPdmGoodsCode(listing.getGoodsCode());
        lineValue.setTableValue(attributeLine.getTableValue());
        lineValue.setProductType(productType);
        lineValue.setTableType(4);
        lineType.setVcFlag(vcFlag);
        amazonAttributeLines.add(lineValue);
    }


    public void callbackHandle(VcDfConfirmTransactionStatus response, VcInventoryRecord vcInventoryRecord) {
        String redisKey = "AMAZON_UPDATE_INVENTORY:MANUAL:TRANSACTION:";

        try {
            if (ObjUtil.isNotEmpty(response)) {
                if (!redisService.exists(redisKey + vcInventoryRecord.getTransactionId())) {
                    return;
                }
                Map<String,Object> cacheObject = redisService.getCacheObject(redisKey + vcInventoryRecord.getTransactionId());
                String amWhCode = (String) cacheObject.get("amWhCode");
                List<Integer> goodsIdList = (List<Integer>) cacheObject.get("goodsIdList");
                if ("Success".equalsIgnoreCase(response.getStatus()) || "Failure".equalsIgnoreCase(response.getStatus())) {
                     goodsIdList.forEach(goodsId -> {
                         redisService.deleteObject("AMAZON_UPDATE_INVENTORY:MANUAL:" + amWhCode + ":" + goodsId);
                     });
                    redisService.deleteObject(redisKey + vcInventoryRecord.getTransactionId());
                    return;
                }
            }
        }catch (Exception ex) {
            log.error("vc库存更新记录表,回调处理异常，请检查！transactionId:{}", vcInventoryRecord.getTransactionId());
        }
    }

}