package com.suncent.smc.web.controller.consumer;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.suncent.rocketmq.rebot.model.TextMessage;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.framework.rocketmq.MQProcessor;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.persistence.common.service.ILogRecordService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreInfoService;
import com.suncent.smc.persistence.inventory.domain.InventoryRecord;
import com.suncent.smc.persistence.inventory.service.IInventoryRecordService;
import com.suncent.smc.persistence.pdm.domain.dto.ThirdpartyFbmDTO;
import com.suncent.smc.persistence.publication.domain.dto.GoodsDetailDTO;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.publication.DingdingMonitorInfoBiz;
import com.suncent.smc.provider.biz.publication.PDMHttpRequestBiz;
import com.suncent.smc.provider.inventory.InventoryUpdateComposite;
import com.suncent.smc.provider.inventory.InventoryUpdateResolver;
import com.suncent.smc.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

/**
 * 库存消费者 负责把库存更新记录到库存表
 */
@Component
@Slf4j
public class InventoryConsumer  implements MQProcessor {

    @Resource
    IInventoryRecordService inventoryRecordService;
    @Resource
    ThirdpartyInventoryBiz inventoryBiz;
    @Resource
    IConfigStoreInfoService storeInfoService;
    @Resource
    RedisService redisService;
    @Resource
    ISysConfigService sysConfigService;
    @Resource
    DingdingMonitorInfoBiz dingdingMonitorInfoBiz;
    @Resource
    ThirdpartyInventoryBiz thirdpartyInventoryBiz;
    @Resource
    PDMHttpRequestBiz pdmHttpRequestBiz;
    @Autowired
    ILogRecordService logRecordService;
    @Autowired
    private ThreadPoolForMonitorManager threadPoolForMonitorManager;

    @Autowired
    private InventoryUpdateComposite inventoryUpdateComposite;

    private final static String TAG = "thirdparty-inv-flow";

    @Override
    public String getTag() {
        return TAG;
    }

    @Override
    public void doProcess(TextMessage message) {
        String key = message.getKey();
        String jsonStr = message.getMsg();

        // 检查库存消费者开关，如果Redis中存在该key，则表示消费者被关闭
        if (redisService.exists(RedisKeyEnum.INVENTORY_CONSUMER_SWITCH.getKey())) {
            log.info("库存消费者已关闭，跳过消息处理，消息key:{}", key);
            return;
        }

        if (checkRepeated(key)) {
            log.error("库存更新,消息重复推送!");
            return;
        }
        try {
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = JSON.parseArray(jsonStr, ThirdpartyFbmDTO.class);
            logRecordService.insertSmcLogRecord(null, null, null, "库存更新-mq消费者推送-message", key, null, jsonStr, 0, thirdpartyFbmDTOList.size());
            if (isEmpty(thirdpartyFbmDTOList)) {
                log.error("库存更新订阅消费者,此次推送sku数量为空,推送消息:{},消息key:{}", jsonStr, key);
                return;
            }
            thirdpartyFbmDTOList.forEach(thirdpartyFbmDTO -> {
                log.info("库存更新订阅消费者,此次推送sku:{},推送仓库:{},推送库存:{},消息key:{}", thirdpartyFbmDTO.getSku(), thirdpartyFbmDTO.getWarehouseCode(), thirdpartyFbmDTO.getNowStock(), key);
            });
            //对库存数据站点映射处理(合并站点数据)
            thirdpartyInventoryBiz.pooledDataBySite(thirdpartyFbmDTOList);

            List<String> sku = thirdpartyFbmDTOList.stream().map(ThirdpartyFbmDTO::getSku)
                    .distinct().collect(Collectors.toList());
            //推送只会有实物库存,此处需要根据实物库存查询对应的共享库存、虚拟套件
            List<GoodsDetailDTO> goodsDetail = pdmHttpRequestBiz.getGoodsDetail(sku);
            List<String> childGoodsCode = goodsDetail.stream().map(GoodsDetailDTO::getChildGoodsCode).flatMap(Collection::stream).collect(Collectors.toList());
            List<String> parentPartList = goodsDetail.stream().map(GoodsDetailDTO::getParentPartList).flatMap(Collection::stream).collect(Collectors.toList());
            sku.addAll(childGoodsCode);
            sku.addAll(parentPartList);
            sku = sku.stream().distinct().collect(Collectors.toList());

            //查询当前sku实时库存(该sku所有仓库库存总和)
            List<ThirdpartyFbmDTO> nowCountList = inventoryBiz.selectStockShareAndPartGoodsCode(sku, null, false);
            //查询当前sku实时库存(该sku所有仓库库存)
            List<ThirdpartyFbmDTO> nowList = inventoryBiz.selectFbmStockByShareAndPartGoodsCode(sku, true);
            if (isEmpty(nowList) || isEmpty(nowCountList)) {
                log.error("库存更新订阅消费者,此次查询sku实时库存为空,推送消息:{},消息key:{}", jsonStr, key);
                return;
            }
            thirdpartyInventoryBiz.pooledDataBySite(nowCountList);
            thirdpartyInventoryBiz.pooledDataBySite(nowList);

            //最终需要更新的sku集合
            List<InventoryRecord> updateRecordList = new ArrayList<>();

            if (intoInventory(thirdpartyFbmDTOList, nowCountList, nowList, updateRecordList, key)) {
                log.error("库存更新订阅消费者,此批次sku,不触发库存更新,推送消息:{},消息key:{}", jsonStr, key);
                return;
            }
            Map<Integer, List<String>> skuMap = updateRecordList.stream()
                    .collect(Collectors.groupingBy(
                            InventoryRecord::getType,
                            Collectors.mapping(InventoryRecord::getSku, Collectors.toList())
                    ));


            //非vc库存数据key -> site +sku
            Map<String, Integer> inventoryMap = updateRecordList.stream().filter(inventoryRecord -> Objects.equals(0, inventoryRecord.getType())).collect(Collectors.toMap
                    (inventoryRecord -> inventoryRecord.getSite() + inventoryRecord.getSku(),
                            InventoryRecord::getNowSum));
            //vc库存数据key ->site +whCode +sku
            thirdpartyInventoryBiz.groupByWarehouseCode(nowList);
            nowList.forEach(fbmNowCount -> inventoryMap.put(fbmNowCount.getWhCountry() + fbmNowCount.getWarehouseCode() + fbmNowCount.getSku(), fbmNowCount.getSellableQty()));

            inventoryMap.forEach((k, v) -> log.info("库存更新订阅消费者,此次更新sku:{},准备更新库存:{},消息key:{}", k, v, key));

            //查询启用库存更新的店铺
            ConfigStoreInfo storeInfo = new ConfigStoreInfo();
            storeInfo.setInventoryUpdate(SMCCommonEnum.ENABLE.getValue());

            List<ConfigStoreInfo> storeInfoList = storeInfoService.selectConfigStoreInfoList(storeInfo);
            if (ObjectUtils.isEmpty(storeInfoList)) {
                return;
            }
            List<Long> recordList = updateRecordList.stream().map(InventoryRecord::getId).collect(Collectors.toList());
            //库存更新表把处理状态改为处理中
            inventoryRecordService.updateInventoryRecordStatus(recordList, SMCCommonEnum.IN_PROCESS.getValue());
            doUpdateInventory(skuMap, inventoryMap, storeInfoList, key);
            //流转库存记录表当前sku处理状态
            inventoryRecordService.updateInventoryRecordStatus(recordList, SMCCommonEnum.PROCESSING_COMPLETE.getValue());
        } catch (Exception e) {
            log.error("inventoryConsumer消费者-接收数据异常,消息key:{}", key, e);
        }
    }

    /**
     * 初始化更新记录数据
     *
     * @param dto
     * @param nowStock
     * @param type
     * @return
     */
    private static InventoryRecord getInventoryRecord(ThirdpartyFbmDTO dto, ThirdpartyFbmDTO nowStock, String type) {
        InventoryRecord inventoryRecord = new InventoryRecord();
        inventoryRecord.setSku(dto.getSku());
        inventoryRecord.setSite(dto.getSite());
        inventoryRecord.setWhCode(Objects.equals(type, "VC") ? dto.getWarehouseCode() : null);
        inventoryRecord.setType(Objects.equals(type, "VC") ? 1 : 0);

        inventoryRecord.setHanderStatus(SMCCommonEnum.TO_BE_PROCESSED.getValue());
        int nowSum = Objects.equals(type, "VC") ? dto.getNowStock() : nowStock.getSellableQty();
        inventoryRecord.setNowSum(nowSum);
        //共享库存sku 记录原库存
        if (Objects.equals(nowStock.getIsInventorySharing(), "Y")) {
            inventoryRecord.setOriginNowStock(nowStock.getOriginNowStock());
        }
        if (Objects.equals(nowStock.getIsPartInventory(), "Y")) {
            //虚拟套件 该字段设置为-1
            inventoryRecord.setOriginNowStock(-1);
        }
        //此处上一次库存取 = 当前总库存 -（该仓库当前库存- 该仓库上一次库存）
        int lastStock = Objects.equals(type, "VC") ? dto.getLastStock() : nowStock.getSellableQty() - (dto.getNowStock() - dto.getLastStock());
        inventoryRecord.setLastNum(lastStock);
        inventoryRecord.setDelFlag(SMCCommonEnum.NORMAL.getValue());
        inventoryRecord.setCreateTime(DateUtils.getNowDate());
        inventoryRecord.setCreateBy("1");
        inventoryRecord.setUpdateTime(DateUtils.getNowDate());
        inventoryRecord.setUpdateBy("1");
        return inventoryRecord;
    }
    /**
     * 检测消息重复推送
     *
     * @param key
     * @return
     */
    public boolean checkRepeated(String key) {
        if (redisService.exists("inventory-consumer-" + key)) {
            return true;
        }
        redisService.setCacheObject("inventory-consumer-" + key, "ture", 7L, TimeUnit.DAYS);
        return false;
    }

    @Override
    public void doProcess(String jsonStr) {
    }

    /**
     * 入库存更新记录表
     *
     * @param thirdpartyFbmDTOList mq推送的库存（仓库变动维度）
     * @param nowCountList         实时库存总和
     * @param nowList              所有仓库库存
     * @param updateRecordList
     * @return
     */
    private boolean intoInventory(List<ThirdpartyFbmDTO> thirdpartyFbmDTOList, List<ThirdpartyFbmDTO> nowCountList, List<ThirdpartyFbmDTO> nowList, List<InventoryRecord> updateRecordList, String key) {

        //入库存更新记录表
        for (ThirdpartyFbmDTO dto : thirdpartyFbmDTOList) {
            if (ObjectUtil.isEmpty(dto)){
                continue;
            }
            //查询当前mq推送库存，在对应站点实时库存
            ThirdpartyFbmDTO nowStock = nowCountList.stream()
                    .filter(n ->ObjectUtil.isNotEmpty(n) &&
                            Objects.equals(n.getSku(), dto.getSku()) &&
                    Objects.equals(n.getWhCountry(), dto.getSite()))
                    .findFirst().orElse(null);
            if (isEmpty(nowStock)) {
                continue;
            }
            //查询当前mq推送库存，在对应站点对应仓库的库存
            ThirdpartyFbmDTO nowDto;
            //共享库存 虚拟库存 没有对应的仓库sku进行匹配
            if (ObjectUtils.isNotEmpty(nowStock.getIsInventorySharing()) || ObjectUtils.isNotEmpty(nowStock.getIsPartInventory())) {
                nowDto = nowList.stream().filter(n -> Objects.equals(n.getSku(), dto.getSku()) &&
                        Objects.equals(n.getWhCountry(), dto.getSite()) &&
                        Objects.equals(n.getWarehouseCode(), dto.getWarehouseCode())).findFirst().orElse(null);
            } else {
                nowDto = nowList.stream().filter(n -> Objects.equals(n.getSku(), dto.getSku()) &&
                        Objects.equals(n.getWhCountry(), dto.getSite()) &&
                        Objects.equals(n.getWarehouseCode(), dto.getWarehouseCode()) &&
                        Objects.equals(n.getWarehouseSku(), dto.getWarehouseSku())).findFirst().orElse(null);
            }

            if (isEmpty(nowDto)) {
                log.error("库存更新订阅消费者接受到消息与实际查询库存不一致,该sku:{},站点:{},仓库：{},仓库sku:{},仓库类型:{},仓库名称:{},仓库编码:{},仓库国家:{}",
                        dto.getSku(), dto.getSite(), dto.getWarehouseCode(), dto.getWarehouseSku(), dto.getWarehouseType(), dto.getWarehouseName(), dto.getWarehouseCode(), dto.getWhCountry());
                continue;
            }
            //非共享库存的sku 对比对应仓库库存, 库存不一致并且单仓库存大于2 该sku则不更新   下次该sku仓库库存有变动 还会再次推送smc  tips:共享库存以及虚拟库存不进行对比
            if (ObjectUtils.isEmpty(nowDto.getIsPartInventory()) && ObjectUtils.isEmpty(nowDto.getIsInventorySharing())
                    && !Objects.equals(nowDto.getSellableQty(), dto.getNowStock()) && nowDto.getSellableQty() > 2) {
                log.error("库存更新订阅消费者库存不一致,sku:{},站点:{},仓库：{},此次消息推送库存为:{},实时查询库存为:{}", dto.getSku(), dto.getSite(), dto.getWarehouseCode(),
                        dto.getNowStock(), ObjectUtils.isEmpty(nowDto) ? 0 : nowDto.getSellableQty());
                continue;
            }

            //根据类型初始化更新记录数据
            groupByType(updateRecordList, key, dto, nowStock, nowDto, "VC");
            groupByType(updateRecordList, key, dto, nowStock, nowDto, "SC");


        }
        if (ObjectUtils.isEmpty(updateRecordList)) {
            return true;
        }
        return false;
    }

    /**
     * 根据类型初始化更新记录数据
     *
     * @param updateRecordList
     * @param key
     * @param dto
     * @param nowStock
     * @param nowDto
     * @param type
     */
    private void groupByType(List<InventoryRecord> updateRecordList, String key, ThirdpartyFbmDTO dto, ThirdpartyFbmDTO nowStock, ThirdpartyFbmDTO nowDto, String type) {
        InventoryRecord inventoryRecord = getInventoryRecord(dto, nowStock, type);
        //判断updateRecordList中是否有该site跟该sku的数据 一次推送 可能含有多条记录去重
        if (updateRecordList.stream().anyMatch(u -> Objects.equals(u.getSku(), inventoryRecord.getSku()) &&
                Objects.equals(u.getSite(), inventoryRecord.getSite()) && Objects.equals(u.getType(), inventoryRecord.getType())
                && Objects.equals(u.getWhCode(), inventoryRecord.getWhCode()))) {
            return;
        }
        InventoryRecord lastUpdateRecord = inventoryRecordService.selectLastInventoryRecordBySku(nowDto.getSku(),
                Objects.equals("VC", type) ? 1 : 0,
                Objects.equals("VC", type) ? dto.getWarehouseCode() : null);

        if (ObjectUtils.isNotEmpty(lastUpdateRecord)) {
            //库存更新梯度拦截
            if (isInventoryGradient(lastUpdateRecord, inventoryRecord)) {
                log.error("库存更新订阅消费者,库存更新梯度拦截成功!sku:{},当前库存:{},上次库存:{},更新时间间隔:{}", inventoryRecord.getSku(), inventoryRecord.getNowSum(), lastUpdateRecord.getNowSum(), DateUtil.between(lastUpdateRecord.getCreateTime(), new Date(), DateUnit.HOUR) + "小时");
                logRecordService.insertSmcLogRecord(null, null, null, "库存更新-库存更新梯度拦截", key, null, JSON.toJSONString(inventoryRecord), 1, 0);
                return;
            }
            //监控单个sku库存与上次库存变化
            singleSkuMonitoring(lastUpdateRecord, inventoryRecord);

            inventoryRecordService.updateInventoryDelFlag(Collections.singletonList(lastUpdateRecord.getId()));
            inventoryRecordService.insertInventoryRecord(inventoryRecord);
            updateRecordList.add(inventoryRecord);
        } else {
            inventoryRecordService.insertInventoryRecord(inventoryRecord);
            updateRecordList.add(inventoryRecord);
        }
    }

    /**
     * 检测此次推送库存记录是否满足梯度更新要求
     *
     * @param lastUpdateRecord
     * @param inventoryRecord
     * @return
     */
    private boolean isInventoryGradient(InventoryRecord lastUpdateRecord, InventoryRecord inventoryRecord) {
        if (ObjectUtils.isEmpty(lastUpdateRecord)) {
            return false;
        }
        if (ObjectUtils.isEmpty(inventoryRecord)) {
            return true;
        }
        int nowSum = inventoryRecord.getNowSum();
        Date lastTime = lastUpdateRecord.getCreateTime();
        long between = DateUtil.between(lastTime, new Date(), DateUnit.HOUR);
        //0-20 实时更新
        if (20 >= nowSum) {
            return false;
        }
        //20-100 2h更新一次
        if (100 >= nowSum) {
            if (between >= 2) {
                return false;
            } else {
                return true;
            }
        }
        //100-200 6h更新一次
        if (200 >= nowSum) {
            if (between >= 6) {
                return false;
            } else {
                return true;
            }
        }
        //大于200 12h更新一次
        if (between >= 12) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * SKU库存监控
     * 若有钉钉消息发送 确认差值需要更新的sku 则需要 调用publication.EbayListingTest#inventoryUpdate()进行更新
     *
     * @param lastUpdateRecord
     * @param nowInventoryRecord
     */
    private void singleSkuMonitoring(InventoryRecord lastUpdateRecord, InventoryRecord nowInventoryRecord) {
        String maxNum = sysConfigService.selectConfigByKey("sku.inventory.max.abs");
        StringBuilder dingdingMsg = new StringBuilder();
        if (StringUtils.isBlank(maxNum)) {
            maxNum = "100";
        }
        //对比同sku 同站点的库存
        if (Objects.equals(nowInventoryRecord.getSku(), lastUpdateRecord.getSku()) && Objects.equals(nowInventoryRecord.getSite(), lastUpdateRecord.getSite()) && ObjectUtil.isEmpty(nowInventoryRecord.getOriginNowStock())) {
            int absQty = Math.abs(nowInventoryRecord.getNowSum() - lastUpdateRecord.getNowSum());
            if (nowInventoryRecord.getNowSum() > 0 && absQty > Integer.parseInt(maxNum)) {
                //发送钉钉警告
                String msg = "库存更新服务监控,站点:" + nowInventoryRecord.getSite() +
                        ",sku:" + nowInventoryRecord.getSku() +
                        "监控日期:" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()) +
                        "本次更新总量库存量：" + nowInventoryRecord.getNowSum() +
                        "上一次更新总库存量：" + lastUpdateRecord.getNowSum() +
                        "总库存存量差异绝对值：" + absQty +
                        " 该sku已从此次库存更新中移除";
                dingdingMsg.append(msg).append("\n");
            }
        }
        //判断是否有需要发送钉钉警告的消息
        if (StringUtils.isNotBlank(dingdingMsg.toString())) {
            dingdingMonitorInfoBiz.monitorMediumRiskAndSend(MonitorEnum.INVENTORY_UPDATE, dingdingMsg.toString());
        }

    }

    /**
     * 库存更新
     * 1.查询店铺所有listing
     * 2.去除排除更新的listing
     * 3.上一次需要更新的listing
     *
     * @param skuMap
     * @param inventoryMap
     * @param storeInfoList
     */

    private void doUpdateInventory(Map<Integer, List<String>> skuMap, Map<String, Integer> inventoryMap, List<ConfigStoreInfo> storeInfoList, String key) {
        for (ConfigStoreInfo shop : storeInfoList) {
            List<String> skuList = shop.getShopCode().contains("VC") ? skuMap.get(1) : skuMap.get(0);
            String scope = sysConfigService.selectConfigByKey("inventory.update.scope");
            if (StringUtils.isBlank(scope)) {
                scope = "local";
            }
            if (ObjectUtils.isEmpty(skuList)) {
                log.error("库存更新服务-库存推送,当前店铺:{}没有可更新的sku", shop.getShopCode());
                return;
            }
            if (PlatformTypeEnum.AM.name().equals(shop.getPlatform()) && redisService.exists(RedisKeyEnum.AM_AUTO_STOCK_UPDATE.getKey())) {
                log.info("AM自动库存更新,当前店铺:{},redis开关为关闭状态,不执行自动库存更新", shop.getShopCode());
                continue;
            }
            if (PlatformTypeEnum.EB.name().equals(shop.getPlatform()) && redisService.exists(RedisKeyEnum.EBAY_AUTO_STOCK_UPDATE.getKey())) {
                log.info("EB自动库存更新,当前店铺:{},redis开关为关闭状态,不执行自动库存更新", shop.getShopCode());
                continue;
            }
            ThreadPoolExecutor poolConfig = threadPoolForMonitorManager.getThreadPoolExecutor("inventory-consumer-pool");
            String finalScope = scope;
            poolConfig.execute(() -> {
                try {
                    //根据刊登类型循环
                    for (PublishType publishType : PublishType.values()) {
                        //获取库存更新工厂执行器
                        InventoryUpdateResolver inventoryUpdateResolver = inventoryUpdateComposite.getInventoryUpdateResolver(shop.getPlatform() + publishType.getType());
                        if (ObjectUtils.isEmpty(inventoryUpdateResolver)) {
                            continue;
                        }
                        //获取链接
                        List listingList = inventoryUpdateResolver.getListingList(skuList, shop.getPlatform(), shop.getSite(), shop.getShopCode());
                        if (ObjectUtils.isEmpty(listingList)) {
                            continue;
                        }
                        //排除掉不需要更新的listing
                        listingList = inventoryUpdateResolver.doExcludeListing(listingList, shop, inventoryMap);
                        if (ObjectUtils.isEmpty(listingList)) {
                            continue;
                        }
                        log.info("库存更新,店铺编码:{},需要更新库存的listing数量:{}", shop.getShopCode(), listingList.size());

                        if (Objects.equals("local", finalScope)) {
                            //更新本地
                            inventoryUpdateResolver.updateLocalListingStock(listingList, shop, inventoryMap);
                        } else {
                            //记录链路日志
                            inventoryUpdateResolver.insertSmcLogRecord(listingList, shop, inventoryMap, key, "库存更新-mq消费者推送-updateInventory");
                            //更新线上
                            inventoryUpdateResolver.updateInventory(listingList, shop.getPlatform(), shop, inventoryMap, "库存更新-mq消费者推送");
                        }

                    }

                } catch (Exception e) {
                    log.error("库存更新失败,店铺编码:{},库存更新异常", shop.getShopCode(), e);
                }
            });

        }
    }


}
