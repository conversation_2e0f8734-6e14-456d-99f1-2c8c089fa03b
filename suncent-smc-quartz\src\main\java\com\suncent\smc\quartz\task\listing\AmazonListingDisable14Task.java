package com.suncent.smc.quartz.task.listing;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.enums.PublishType;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.file.FileUtils;
import com.suncent.smc.oss.IAliOssService;
import com.suncent.smc.persistence.inventory.domain.InventoryUpdateBlack;
import com.suncent.smc.persistence.inventory.service.IInventoryUpdateBlackService;
import com.suncent.smc.persistence.pdm.domain.entity.MappingGoods;
import com.suncent.smc.persistence.pdm.service.IMappingGoodsService;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.provider.dingding.domain.ActionCardMsgDto;
import com.suncent.smc.provider.dingding.service.IDingAsyncSendService;
import com.suncent.smc.quartz.task.listing.dto.ListingDisableExcelDTO;
import com.suncent.smc.system.service.ISysUserService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 1、针对FBM、VCDF链接
 * 2、每月14日系统推送停用状态链接至各自运营
 * 3、每月14-20号，运营提交OA变更的时间
 * 4、每月21日将状态仍为停用的调0
 * 5、原停用状态变更至推新、维稳、风险、清库状态，需移出黑名单并正常管理
 * <p>
 * tip:该任务处理 2
 */
@Slf4j
@Component
public class AmazonListingDisable14Task {
    @Autowired
    private IGoodsHeadService goodsHeadService;
    @Autowired
    private IDingAsyncSendService dingAsyncSendService;

    @Autowired
    private IMappingGoodsService mappingGoodsService;
    @Autowired
    private IAliOssService aliOssService;
    @Autowired
    ISysUserService userService;
    @Autowired
    private IInventoryUpdateBlackService inventoryUpdateBlackService;

    @Value("${aliyun.oss.suncentUrlPrefix}")
    private String aliOssAddress;

    @Value("${aliyun.oss.defaultBucketName}")
    private String bucketName;

    @Value("${sys-file.file-template-path}")
    private String filePath;


    @XxlJob("amazonListingDisable14Task")
    public void execute() {
        try {
            Map<String, List<MappingGoods>> groupByOperator = new HashMap<>();
            Long lastId = 0L;
            int pageSize = 5000;

            while (true) {
                List<MappingGoods> batchList = mappingGoodsService.selectDisabledMappingGoodsPaged(PlatformTypeEnum.AM.name(), lastId, pageSize);
                if (ObjectUtil.isEmpty(batchList)) {
                    break;
                }

                // 按运营分组追加
                for (MappingGoods mg : batchList) {
                    if (mg.getOperators() == null) continue;
                    groupByOperator.computeIfAbsent(mg.getOperators(), k -> new ArrayList<>()).add(mg);
                }

                // 更新 lastId 为当前批次最大ID
                lastId = batchList.stream().mapToLong(MappingGoods::getId).max().orElse(lastId);

                // 提前释放
                batchList.clear();
            }

            if (groupByOperator.isEmpty()) {
                log.info("无FBM、VCDF停用链接，无需推送");
                return;
            }


            for (Map.Entry<String, List<MappingGoods>> entry : groupByOperator.entrySet()) {
                String operator = entry.getKey();
                List<MappingGoods> mappingList = entry.getValue();

                List<String> platformSku = mappingList.stream()
                        .map(MappingGoods::getPlatformSku)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

                List<GoodsHead> listingList = new ArrayList<>();
                if (!platformSku.isEmpty()) {
                    List<GoodsHead> tmp = goodsHeadService.selectListByPlatformGoodsCodes(platformSku);
                    tmp = tmp.stream().filter(head -> Objects.equals(PublishType.FBM.getType(), head.getPublishType())
                            || PublishType.VCDF.getType().equals(head.getPublishType())).collect(Collectors.toList());

                    // 过滤掉已经在黑名单中的链接
                    if (!tmp.isEmpty()) {
                        tmp = filterBlackListedGoods(tmp);
                    }

                    listingList.addAll(tmp);
                }

                if (listingList.isEmpty()) continue;


                SysUser sysUser = null;
                sysUser = userService.selectUserById(ObjectUtil.isNotEmpty(operator) ? Long.parseLong(operator) : 1997L);
                if (ObjectUtil.isEmpty(sysUser)) {
                    sysUser = userService.selectUserById(1997L);
                }

                String fileName = DateUtils.dateTimeNow("yyyyMMdd") + "-" + (ObjectUtil.isNotEmpty(sysUser.getUserName()) ? sysUser.getUserName() : operator) + "-停用链接.xlsx";
                String localFilePath = filePath + fileName;
                String ossUrl = "smc/dingDingFiles/" + DateUtils.getNowDate() + "/" + fileName;

                try {
                    List<ListingDisableExcelDTO> excelData = listingList.stream().map(head -> {
                        ListingDisableExcelDTO dto = new ListingDisableExcelDTO();
                        dto.setGoodsId(String.valueOf(head.getId()));
                        dto.setPlatformGoodsCode(head.getPlatformGoodsCode());
                        dto.setPlatform(head.getPlatform());
                        dto.setShopCode(head.getShopCode());
                        dto.setPdmGoodsCode(head.getPdmGoodsCode());
                        dto.setPlatformGoodsId(head.getPlatformGoodsId());
                        dto.setPlatform(head.getPlatform());
                        return dto;
                    }).collect(Collectors.toList());

                    try (ExcelWriter excelWriter = EasyExcel.write(localFilePath).build()) {
                        WriteSheet writeSheet = EasyExcel.writerSheet(0, "停用链接").head(ListingDisableExcelDTO.class).build();
                        excelWriter.write(excelData, writeSheet);
                    }

                    aliOssService.putObjectByFile(bucketName, ossUrl, localFilePath);

                    ActionCardMsgDto msg = new ActionCardMsgDto();
                    msg.setMessageTitle("FBM/VCDF停用链接提醒");
                    msg.setMessageContent("请注意，您有" + listingList.size() + "条平台状态为停用的链接，8天后IT将把可售库存调整为0。若您认为存在特殊情况，请于7天内提交并通过【OA-经管-产品特殊状态申请】。");
                    msg.setMessageUrl(aliOssAddress + ossUrl);
                    msg.setTargetDingUserId(ObjectUtil.isEmpty(sysUser.getUserCode()) ? "16687664690887479" : sysUser.getUserCode());

                    try {
                        dingAsyncSendService.asyncSend(msg);
                    } catch (Exception e) {
                        log.error("钉钉推送失败, createBy:{}", operator, e);
                    }

                } finally {
                    FileUtils.deleteFile(localFilePath);
                }
            }

            log.info("FBM、VCDF停用链接推送及缓存完成，总运营数:{}", groupByOperator.size());
        } catch (Exception e) {
            log.error("ListingDisable14Task执行异常", e);
        }
    }

    /**
     * 过滤掉已经在黑名单中的商品
     *
     * @param goodsList 商品列表
     * @return 过滤后的商品列表
     */
    private List<GoodsHead> filterBlackListedGoods(List<GoodsHead> goodsList) {
        if (goodsList.isEmpty()) {
            return goodsList;
        }

        try {
            // 按店铺分组处理黑名单过滤
            Map<String, List<GoodsHead>> groupByShop = goodsList.stream()
                    .collect(Collectors.groupingBy(GoodsHead::getSiteCode));

            List<GoodsHead> filteredResult = new ArrayList<>();
            int totalFilteredCount = 0;

            for (Map.Entry<String, List<GoodsHead>> entry : groupByShop.entrySet()) {
                String shopCode = entry.getKey();
                List<GoodsHead> shopGoodsList = entry.getValue();

                // 查询该店铺的黑名单记录
                List<Long> goodsIds = shopGoodsList.stream().map(GoodsHead::getId).map(Long::valueOf).collect(Collectors.toList());
                List<InventoryUpdateBlack> blackList = inventoryUpdateBlackService.selectInventoryUpdateBlackListByHeadIds(goodsIds, shopCode);

                if (blackList.isEmpty()) {
                    filteredResult.addAll(shopGoodsList);
                    continue;
                }

                // 获取黑名单中的商品ID集合
                Set<Long> blackGoodsIdSet = blackList.stream()
                        .map(InventoryUpdateBlack::getHeadId)
                        .collect(Collectors.toSet());

                // 过滤掉黑名单中的商品
                List<GoodsHead> shopFilteredList = shopGoodsList.stream()
                        .filter(goods -> !blackGoodsIdSet.contains(Long.valueOf(goods.getId())))
                        .collect(Collectors.toList());

                int shopFilteredCount = shopGoodsList.size() - shopFilteredList.size();
                if (shopFilteredCount > 0) {
                    log.info("店铺 {} 过滤掉 {} 条已在黑名单中的停用链接", shopCode, shopFilteredCount);
                    totalFilteredCount += shopFilteredCount;
                }

                filteredResult.addAll(shopFilteredList);
            }

            if (totalFilteredCount > 0) {
                log.info("总共过滤掉 {} 条已在黑名单中的停用链接", totalFilteredCount);
            }

            return filteredResult;
        } catch (Exception e) {
            log.error("过滤黑名单商品时发生异常", e);
            return goodsList;
        }
    }
}
