<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <th:block th:include="include :: header('编辑Amazon Listing')"/>
    <th:block th:include="include :: select2-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <link th:href="@{/css/template.widget.css?v=20230424}" rel="stylesheet"/>
    <link th:href="@{/css/fancybox.css}" rel="stylesheet"/>
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: layout-latest-css"/>
    <style>
        .scroll-button-a{
            cursor: pointer;
            position: fixed;
            right: 5px;
            display: inline;
        }

        /*文件上传的样式start*/
        .upload-btn-box{
            position: relative;
        }
        .upload-btn-box a {
            width: 130px;
        }
        .upload-btn-box input {
            width: 150px;
            height: 40px;
            position: absolute;
            opacity: 0;
            left: 0;
            top: 0;
            cursor: pointer;
        }
        .btn-group-sm {
            float: right;
        }
        .master-img{
            border-radius: 5px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            display: inline-block;
        }

        .master-img img{
            margin: 3px;
            width: 146px;
            height: 146px;
            background-color: #ccc;
            object-fit: cover;
        }
        .upimg {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 5px;
            border: dashed #999999;
            background: url(/img/addimg.svg) no-repeat;
            background-position: 33px;
        }

        .upimg input {
            position: absolute;
            width: 100px;
            height: 100px;
            opacity: 0;
        }

        #showui {
            display: flex;
            justify-content: flex-start;
            padding: 0px;
            flex-flow: row wrap;
        }

        #showui li {
            list-style: none;
            position: relative;
            border-radius: 5px;
            /*border: 1px dashed #999;*/
            border: 1px solid #ddd;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        ul#showui:last-child li{
            margin-right: 0px;
        }
        #showui li .showimg {
            margin:3px;
            width: 146px;
            height: 146px;
            background-color: #ccc;
            object-fit: cover;
        }

        .showdiv {
            position: absolute;
            z-index: 9;
            bottom: 0;
            width: 100%;
            padding: 10px;
            display: flex;
            justify-content: space-around;
            background: rgba(0, 0, 0, .6);
            border-radius: 0px 0px 5px 5px;
        }

        .showdiv img {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }


        .oneright {
            opacity: .6;
            cursor: no-drop !important;
        }

        #zoomView {
            position: absolute;
            z-index: 999;
        }
        .small_btn {
            padding: 2px 6px;
        }
        /*文件上传的样式end*/


        .vertical-text {
            writing-mode: vertical-rl; /* 文字竖排，内容从右到左 */
            text-orientation: upright; /* 使文字保持直立 */
            border: 1px solid #000;
            padding: 10px;
        }
        table a {
            color: #337ab7;
            text-decoration: none;
        }
        .select2-selection--multiple.invalid,.select2-selection--single.invalid  {
            background-color: #fbe2e2;
            border-color: #c66161;
            color: #c00;
        }

        .select2-selection__rendered {
            width: 300px;
        }
        .ckeditor-invalid {
            border: 2px solid red !important;
            background-color: #fbe2e2;
            color: #c00;
        }

        /* 前台数据显示样式 - 统一蓝色主题 */
        .front-data-display, .front-data-display-dynamic {
            font-size: 12px;
            margin-bottom: 5px;
            padding: 3px 8px;
            background-color: #e8f4fd;
            border-left: 3px solid #2196F3;
            border-radius: 3px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .front-data-label {
            font-weight: bold;
            color: #666;
            margin-right: 5px;
            user-select: none; /* 防止标签被选中 */
        }

        .front-data-content {
            color: #333;
            margin-left: 5px;
            user-select: text; /* 允许内容被选中 */
            cursor: text;
        }

        /* 前台数据标识 - 不参与文本选择 */
        .front-data-tag {
            color: #999;
            margin-left: 10px;
            font-style: italic;
            font-size: 11px;
            user-select: none; /* 防止标识被选中 */
            pointer-events: none; /* 防止鼠标事件 */
        }

        /* 统一所有前台数据显示样式 */
        .front-title-display,
        .front-brand-display,
        .front-price-display,
        .front-description-display,
        .front-bullet-1-display,
        .front-bullet-2-display,
        .front-bullet-3-display,
        .front-bullet-4-display,
        .front-bullet-5-display,
        .front-spec-display,
        .front-attr-display,
        .front-external-display,
        .front-image-display {
            font-size: 12px;
            margin-bottom: 5px;
            padding: 3px 8px;
            background-color: #e8f4fd;
            border-left: 3px solid #2196F3;
            border-radius: 3px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        /* 确保前台标题不影响输入框和字数统计的布局 */
        .title-warp .front-title-display {
            margin-bottom: 8px;
            margin-top: 0;
        }

        /* 前台标题特殊样式（保持兼容） */
        .front-title-label {
            font-weight: bold;
            color: #666;
            margin-right: 5px;
            user-select: none;
        }

        .front-title-content {
            color: #333;
            margin-left: 5px;
            user-select: text;
            cursor: text;
        }
    </style>
</head>

<body class="gray-bg">
<div class="ui-layout-west" style="display: none">
    <div class="panel panel-info ">
        <div class="panel-heading">
            <h3 class="panel-title">商品信息</h3>
        </div>
        <div class="panel-body form-horizontal">
            <div class="col-sm-12" id="pdmBasicInfo">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>基础信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-12 col-sm-offset-5">
                                <div class="goods-image-box">
                                    <div class="file-loading">
                                        <img class="goods-image" pdm-value="goodsLogo" src=""
                                             style="width: 100px;height: 100px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">产品编码：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text" pdm-value="productCode" need-copy
                                                   readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">产品名称：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text" pdm-value="productName" need-copy
                                                   readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">产品英文名称：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text"
                                                   pdm-value="productEnName" need-copy readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">商品编码：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" need-copy type="text" pdm-value="goodsCode"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">商品名称：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text" need-copy pdm-value="goodsName"
                                                   readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">商品英文名称：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text" need-copy pdm-value="goodsEnName"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">分类：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text"
                                                   pdm-value="classificationName" need-copy readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">大类：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text"
                                                   pdm-value="mainCategoryName" need-copy readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">产品分类：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text"
                                                   pdm-value="productCategoryName" need-copy readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">销售状态：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text" pdm-value="salesStatus"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">商品类型：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text" pdm-value="partType"
                                                   readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">子零件信息：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text" pdm-value="parts"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">开发人员：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text"
                                                   readonly>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">创建时间：</label>
                                        <div class="col-sm-7">
                                            <input name="createTime" class="form-control" type="text"
                                                   pdm-value="createTime"
                                                   readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-12" id="pdmPriceInfo">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>价格信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="container-div">
                            <div class="row">
                                <div class="col-xs-12 select-table table-striped">
                                    <table id="pdm-price-bootstrap-table"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-12" id="pdmStockInfo">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>库存信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="container-div">
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-sm-6">
                                        <label class="col-sm-4 control-label">可售库存：</label>
                                        <div class="col-sm-7">
                                            <input class="form-control" type="text" pdm-value="stockOnSalesQty"
                                                   id="stockOnSalesQty"
                                                   readonly>
                                        </div>
                                    </div>
                                    <!--                                    <div class="col-sm-6">-->
                                    <!--                                        <label class="col-sm-4 control-label">在途库存：</label>-->
                                    <!--                                        <div class="col-sm-7">-->
                                    <!--                                            <input class="form-control" type="text" pdm-value="stockOnWayQty"-->
                                    <!--                                                   readonly>-->
                                    <!--                                        </div>-->
                                    <!--                                    </div>-->
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-sm-5">
                                        <label class="col-sm-4 control-label" style="
    padding-left: 0px;
    padding-right: 0px;
">共享库存信息：</label>
                                    </div>
                                    <div class="col-sm-7">
                                        <label class="col-sm-4 control-label" style="
    padding-left: 0px;
    padding-right: 0px;
">主商品编码：</label>
                                        <div class="col-sm-8">
                                            <input class="form-control" type="text" pdm-value="masterGoodsCode"
                                                   id="masterGoodsCode" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <table id="pdm-stock-bootstrap-table"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-12" id="pdmAdaptInfo">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>适配信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="container-div">
                            <div class="row">
                                <table id="pdm-adapt-index-bootstrap-table"></table>
                            </div>
                        </div>
                        <div class="container-div">
                            <div class="row">
                                <table id="pdm-adapt-bootstrap-table"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-12" id="pdmAttrInfo">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>属性信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="container-div">
                            <div class="row">
                                <table id="pdm-attr-bootstrap-table"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-12" id="pdmSpecInfo">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>规格信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <div class="ibox float-e-margins">
                                        <div class="ibox-title">
                                            <h5>商品尺寸</h5>
                                        </div>
                                        <div class="ibox-content">
                                            <div class="container-div">
                                                <div class="row">
                                                    <div class="form-group">
                                                        <div class="col-sm-6">
                                                            <label class="col-sm-4 control-label">长度：</label>
                                                            <div class="col-sm-7"><input class="form-control"
                                                                                         type="text" pdm-value="length"
                                                                                         readonly>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <label class="col-sm-4 control-label">宽度：</label>
                                                            <div class="col-sm-7"><input class="form-control"
                                                                                         type="text" pdm-value="width"
                                                                                         readonly>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <div class="col-sm-6">
                                                            <label class="col-sm-4 control-label">高度：</label>
                                                            <div class="col-sm-7"><input class="form-control"
                                                                                         type="text" pdm-value="height"
                                                                                         readonly>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <label class="col-sm-4 control-label">单位：</label>
                                                            <div class="col-sm-7"><input class="form-control"
                                                                                         type="text"
                                                                                         pdm-value="lengthUnit"
                                                                                         readonly>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="ibox float-e-margins">
                                        <div class="ibox-title">
                                            <h5>包裹重量</h5>
                                        </div>
                                        <div class="ibox-content">
                                            <div class="container-div">
                                                <div class="row">
                                                    <div class="form-group">
                                                        <div class="col-sm-6">
                                                            <label class="col-sm-4 control-label">重量：</label>
                                                            <div class="col-sm-7"><input class="form-control"
                                                                                         type="text"
                                                                                         pdm-value="grossWeight" default-value="cm"
                                                                                         readonly>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <label class="col-sm-4 control-label">单位：</label>
                                                            <div class="col-sm-7"><input class="form-control"
                                                                                         type="text"
                                                                                         pdm-value="grossWeightUnit" default-value="kg"
                                                                                         readonly>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<div class="ui-layout-center">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">刊登信息</h3>
        </div>
        <div class="panel-body">
<div class="wrapper wrapper-content" style="margin-right: 5%;margin-left: 5%;">
    <div class="col-sm-12" style="margin-bottom: 10px;">
        <div class="col-sm-12">
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="watchPriceCount(listingEditDTO.goodsCode)">
                    <i class="fa fa-plus"></i> 价格测算
                </a>
                <a class="btn btn-success" onclick="watchFitmentVIO(listingEditDTO.goodsCode)"
                   shiro:hasPermission="publication:goods:view">
                    <i class="fa fa-plus"></i> 适配保有量
                </a>
                <a class="btn btn-success" onclick="getCompetingProducts()">
                    <i class="fa fa-plus"></i> 竞品采集
                </a>
                <a class="btn btn-warning" onclick="submitHandler()">
                    <i class="fa fa-download"></i> 保存
                </a>
            </div>
        </div>
    </div>

    <div class="col-sm-12">
        <form id="form-edit-listing" class="form-horizontal" th:object="${listingEditDTO}">
            <input type="hidden" th:field="*{id}"/>
            <input type="hidden" th:field="*{goodsHeadId}"/>
            <input type="hidden" th:field="*{goodsResourceId}"/>
            <input type="hidden" th:field="*{goodsDescriptionId}"/>
            <input type="hidden" th:field="*{goodsSpecificationId}"/>
            <input type="hidden" th:field="*{shopName}"/>
            <input type="hidden" th:field="*{publishTypeName}"/>
            <input type="hidden" th:field="*{categoryId}"/>
            <input type="hidden" id="publishStatus" th:field="*{publishStatus}"/>
            <input type="hidden" id="imgDataArr" name="imgDataArr">
            <input class="form-control" type="hidden" id="masterImgFile" name="masterImgFile">

            <!--基本信息-->
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5 id="scroll-basic-info">基本信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-12">
                                <table class="table table-hover margin bottom">
                                    <div class="form-group">
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label">平台：</label>
                                            <div class="col-sm-9">
                                                <input type="text" id="platform" class="form-control" name="platform"
                                                       readonly
                                                       value="AM">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label">站点：</label>
                                            <div class="col-sm-8">
                                                <input type="text" class="form-control" name="siteCode" id="siteCode"
                                                       th:field="*{siteCode}"
                                                       readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!--店铺-->
                                    <div class="form-group">
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label font-noraml">店铺：</label>
                                            <div class="col-sm-9">
                                                <input type="hidden" id="shopCode" class="form-control"
                                                       name="shopCode" th:value="*{shopCode}" readonly>
                                                <input type="text"  class="form-control"
                                                        th:value="*{shopName}" readonly>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label">商品编码：</label>
                                            <div class="col-sm-9">
                                                <input type="text" class="form-control" th:field="*{goodsCode}" readonly>
                                            </div>
                                        </div>

                                    </div>

                                    <!--商品编码-->
                                    <div class="form-group">
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label">平台商品编码：</label>
                                            <div class="col-sm-9">
                                                <input type="text" class="form-control" th:field="*{platformGoodsCode}"
                                                       readonly>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label">平台销售编码：</label>
                                            <div class="col-sm-9">
                                                <input type="text" class="form-control" th:field="*{platformGoodsId}"
                                                       readonly>
                                            </div>
                                        </div>
                                    </div>
                                    <!--店铺-->
                                    <div class="form-group">
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label">被跟卖ASIN：</label>
                                            <div class="col-sm-9">
                                                <input type="text" name="externalAsin" th:field="*{externalAsin}" readonly
                                                       class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label">平台编码类型：</label>
                                            <div class="col-sm-4">
                                                <select name="externalProductIdType" id="externalProductIdType" th:field="*{externalProductIdType}" class="form-control m-b"
                                                        th:with="type=${@dict.getType('external_product_id_type')}">
                                                    <option th:each="dict : ${type}" th:text="${dict.dictValue}"
                                                            th:value="${dict.dictValue}"  th:unless="${dict.dictValue == 'ASIN'}">
                                                </select>
                                            </div>
                                            <div class="col-sm-4 externalProductId">
                                                <input type="text" th:field="*{externalProductId}"  id="externalProductId" name="externalProductId"  class="form-control" >
                                            </div>
                                        </div>
                                    </div>

                                    <!--商品状况-->
                                    <div class="form-group">
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label">刊登类型：</label>
                                            <div class="col-sm-9">
                                                <input type="text" id="publishType" class="form-control"
                                                       th:name="*{publishType}" th:value="*{publishTypeName}" readonly>
                                            </div>
                                        </div>

                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label">商品状况：</label>
                                            <div class="col-sm-9">
                                                <select name="condition" class="form-control m-b"
                                                        th:with="type=${@dict.getType('amazon_publication_goods_condition')}">
                                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                            th:value="${dict.dictValue}"
                                                            th:field="*{condition}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group sc-product">
                                        <div class="col-sm-6">
                                            <div align="right" class="col-sm-3">
                                                <label class="control-label">FNSKU：</label>
                                            </div>
                                            <div class="col-sm-9">
                                                <input class="form-control" name="fnSku" readonly
                                                       th:value="*{fnSku}" type="input">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <label class="col-sm-3 control-label is-required">品牌：</label>
                                            <div class="col-sm-9" th:if="${noEditFlag}">
                                                <input class="form-control is-required" id="brandName" name="brandName"
                                                       readonly th:field="*{brandCode}"
                                                       type="text">
                                            </div>
                                            <div class="col-sm-9" th:if="${!noEditFlag}">
                                                <select class="form-control m-b" data-none-selected-text="请选择"
                                                        name="brandName" required
                                                        th:field="*{brandCode}">
                                                    <option
                                                            th:each="keyValueEntity:${@cdpBaseConfig.getBrandAllKVList()}"
                                                            th:text="${keyValueEntity.value}"
                                                            th:value="${keyValueEntity.value}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-6">
                                            <div class="col-sm-3" align="right">
                                                <label class="control-label is-required">平台品类：</label>
                                            </div>
                                            <div class="col-sm-9">
                                                <!--                                                <form>-->
                                                <select name="productCategoryCode" class="form-control m-b"
                                                        id="categoryCode"
                                                        data-none-selected-text="请选择" th:field="*{productCategoryCode}">
                                                    <option
                                                        th:each="keyValueEntity:${@smcBaseConfig.getPlatformCategoryByPlatformCode('AM')}"
                                                        th:value="${keyValueEntity.key}"
                                                        th:text="${keyValueEntity.value}"
                                                        th:field="*{productCategoryCode}"></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 vc-product">
                                            <label class="col-sm-3 control-label is-required">品牌：</label>
                                            <div class="col-sm-9" th:if="${noEditFlag}">
                                                <input type="text" id="brandName" name="brandName" class="form-control is-required"  readonly
                                                       th:field="*{brandCode}">
                                            </div>
                                            <div class="col-sm-9" th:if="${!noEditFlag}">
                                                <select name="brandName" class="form-control m-b"
                                                        data-none-selected-text="请选择" th:field="*{brandCode}" required >
                                                    <option
                                                        th:each="keyValueEntity:${@cdpBaseConfig.getBrandAllKVList()}"
                                                        th:value="${keyValueEntity.value}"
                                                        th:text="${keyValueEntity.value}"></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-12 col-sm-offset-1">
                                            <label class="control-label" id="categoryLabel"></label>
                                        </div>
                                        <div class="col-sm-12 col-sm-offset-1">
                                            <label class="control-label" id="categoryLabelEn"></label>
                                        </div>
                                    </div>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--价格库存信息-->
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5 id="scroll-stock-price">价格库存信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-12">
                                <table class="table table-hover margin bottom">
                                    <div class="form-group">
                                        <div class="col-sm-6 vc-product">
                                            <div class="col-sm-3" align="right">
                                                <label class="control-label">Cost Price：</label>
                                            </div>
                                            <div class="col-sm-8">
                                                <input type="text" id="price" name="standardPrice" class="form-control is-required" required
                                                       th:field="*{standardPrice}">
                                            </div>
                                        </div>
                                        <div class="col-sm-6 sc-product">
                                            <label class="col-sm-3 control-label">价格：</label>
                                            <div class="col-sm-8">
                                                <input class="form-control is-required" name="standardPrice"
                                                       required
                                                       th:value="*{standardPrice}" type="text">
                                            </div>
                                        </div>
                                        <div class="col-sm-6 vc-product">
                                            <div class="col-sm-3" align="right">
                                                <label class="control-label">List Price：</label>
                                            </div>
                                            <div class="col-sm-8">
                                                <input type="text" id="listPrice" name="listPrice" class="form-control is-required" required 
                                                       th:field="*{listPrice}">
                                            </div>
                                        </div>
                                        <div class="col-sm-6 sc-product">
                                            <div class="col-sm-3" align="right">
                                                <label class="control-label">处理时间：</label>
                                            </div>
                                            <div class="col-sm-8">
                                                <input type="number" id="fulfillmentLatency" class="form-control" name="fulfillmentLatency"  th:field="*{fulfillmentLatency}" >
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group sc-product">
                                        <div class="col-sm-6">
                                            <div class="col-sm-3" align="right">
                                                <label class="control-label">Sale Price：</label>
                                            </div>
                                            <div class="col-sm-8">
                                                <input type="number" id="salePrice" class="form-control" name="salePrice" th:field="*{salePrice}">
                                            </div>
                                        </div>
                                        <div class="col-sm-6 sc-product">
                                            <div class="col-sm-3" align="right">
                                                <label class="control-label">Sale Date：</label>
                                            </div>
                                            <div class="col-sm-8">
                                                <div class="input-daterange input-group">
                                                    <input type="text" class="input-sm form-control" id="laydate-startTime" name="saleBeginDate" placeholder="yyyy-MM-dd" th:field="*{saleBeginDate}"/>
                                                    <span class="input-group-addon">到</span>
                                                    <input type="text" class="input-sm form-control" id="laydate-endTime"  name="saleEndDate" placeholder="yyyy-MM-dd" th:field="*{saleEndDate}"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group sc-product">
                                        <div class="col-sm-6">
                                            <div class="col-sm-3" align="right">
                                                <label class="control-label">库存：</label>
                                            </div>
                                            <div class="col-sm-8">
                                                <input type="number" class="form-control" th:field="*{stockOnSalesQty}" required>
                                            </div>
                                        </div>
                                    </div>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--标题信息-->
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5 id="scroll-title-info">标题信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                        <div class="btn-group-sm">
                            <a class="btn btn-success" onclick="getGPT('title')">
                                <i class="fa fa-plus"></i> 数据组适配参考
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="form-group title-warp">
                                <label class="col-sm-1 control-label">标题：</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <input class="form-control maxlegthflag"
                                               name="title"
                                               id="title"              required
                                               th:field="*{title}"
                                               reFlag="true"
                                               size="200" />
                                        <span class="input-group-addon count-text">00/00</span>
                                    </div>
                                </div>
                                <div class="col-sm-1">
                                    <a class="btn btn-default btn-sm up-caps-first small_btn"
                                       data-is-up="false"
                                       data-toggle="tooltip"
                                       data-placement="top"
                                       title="首字母大小写转换"
                                       type="button"><i class="iconfont icon-fontsize"></i></a>
                                    <a class="btn btn-default btn-sm up-caps-all small_btn"
                                       data-is-up="false"
                                       data-toggle="tooltip"
                                       data-placement="top"
                                       title="全量大小写转换"
                                       type="button"><i class="iconfont icon-font"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--属性信息-->
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5 id="scroll-attribute-info">属性信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>

                    <!--重要信息-->
<!--                    <div class="ibox-title">-->
<!--                        <h6>重要信息</h6>-->
<!--                    </div>-->
                    <div class="ibox-content">
                        <div class="row">
                            <div class="btn-group-sm" id="toolbar2" role="group">
                                <a class="btn btn-success" onclick="addTable()">
                                    <i class="fa fa-plus"></i> 批量新增
                                </a>
                                <a class="btn btn-danger" onclick="removeItem()">
                                    <i class="fa fa-remove"></i> 删除
                                </a>
                                <a class="btn btn-warning" onclick="doValidField()">
                                    <i class="fa fa-check"></i> 属性内容校验
                                </a>
                                <a class="btn btn-primary" onclick="selectListing()">
                                    <i class="fa fa-copy"></i> 应用已有链接属性
                                </a>
                            </div>

                            <div class="col-xs-12 select-table table-striped">
                                <table id="attr-info-table"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--物流信息-->
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5 id="scroll-logic-info">物流信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-12">

                                <h6>商品尺寸：</h6>
                                <div class="form-group">
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">长度：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="itemLength" class="form-control"
                                                   name="itemLength"  required
                                                   th:value="*{itemLength}" >
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">宽度：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="itemWidth" class="form-control"   required
                                                   name="itemWidth" th:value="*{itemWidth}">
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">高度：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="itemHeight" class="form-control"
                                                   name="itemHeight" required
                                                   th:value="*{itemHeight}" >
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">单位：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="itemLengthUnit"
                                                   class="form-control"    required
                                                   name="itemLengthUnit"
                                                   th:value="*{itemLengthUnit}" >
                                        </div>
                                    </div>
                                </div>

                                <h6>包裹尺寸：</h6>
                                <div class="form-group">
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">长度：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="packageLength" class="form-control"
                                                   name="packageLength"    required
                                                   th:value="*{packageLength}" th:readonly="${noEditFlag}">
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">宽度：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="packageWidth" class="form-control"
                                                   name="packageWidth"              required
                                                   th:value="*{packageWidth}"  th:readonly="${noEditFlag}">
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">高度：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="packageHeight" class="form-control"
                                                   name="packageHeight"                required
                                                   th:value="*{packageHeight}"  th:readonly="${noEditFlag}" >
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">单位：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="packageLengthUnit"
                                                   class="form-control"         required
                                                   name="packageLengthUnit"
                                                   th:value="*{packageLengthUnit}"  th:readonly="${noEditFlag}" >
                                        </div>
                                    </div>
                                </div>

                                <h6>包裹重量：</h6>
                                <div class="form-group">
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">重量：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="packageWeight" class="form-control"
                                                   name="packageWeight"   required
                                                   th:value="*{packageWeight}" >
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="col-sm-4" style="text-align: right;">
                                            <label class="control-label">单位：</label>
                                        </div>
                                        <div class="col-sm-7">
                                            <input id="packageWeightUnit"
                                                   class="form-control"
                                                   name="packageWeightUnit"      required
                                                   th:value="*{packageWeightUnit}">
                                        </div>
                                    </div>
<!--                                    <div class="col-sm-3">-->
<!--                                        <div class="col-sm-4" style="text-align: right;">-->
<!--                                            <label class="control-label">箱子数：</label>-->
<!--                                        </div>-->
<!--                                        <div class="col-sm-7">-->
<!--                                            <input type="number"-->
<!--                                                   class="form-control is-required"-->
<!--                                                   name="numberOfBoxes"-->
<!--                                                   id="numberOfBoxes"-->
<!--                                                   placeholder="请填写箱子个数"-->
<!--                                                   th:value="*{numberOfBoxes}">-->
<!--                                        </div>-->
<!--                                    </div>-->
                                </div>
                                <!--                                <table class="table table-hover margin bottom">-->
                                <!--                                    &lt;!&ndash;商品尺寸&ndash;&gt;-->
                                <!--                                    <div class="form-group">-->
                                <!--                                        <div class="col-sm-10">-->
                                <!--                                            <div class="row">-->
                                <!--                                                <div class="col-sm-12 search-collapse">-->
                                <!--                                                    <p class="select-title">商品尺寸：</p>-->
                                <!--                                                    <div class="select-list">-->
                                <!--                                                        <div class="form-group">-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">长度：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="itemLength" class="form-control"-->
                                <!--                                                                           name="itemLength"-->
                                <!--                                                                           th:value="*{itemLength}" >-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">宽度：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="itemWidth" class="form-control"-->
                                <!--                                                                           name="itemWidth" th:value="*{itemWidth}">-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">高度：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="itemHeight" class="form-control"-->
                                <!--                                                                           name="itemHeight"-->
                                <!--                                                                           th:value="*{itemHeight}" >-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">单位：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="itemLengthUnit"-->
                                <!--                                                                           class="form-control"-->
                                <!--                                                                           name="itemLengthUnit"-->
                                <!--                                                                           th:value="*{itemLengthUnit}" >-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                        </div>-->
                                <!--                                                    </div>-->
                                <!--                                                </div>-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                    &lt;!&ndash;包裹尺寸&ndash;&gt;-->
                                <!--                                    <div class="form-group">-->
                                <!--                                        <div class="col-sm-10">-->
                                <!--                                            <div class="row">-->
                                <!--                                                <div class="col-sm-12 search-collapse">-->
                                <!--                                                    <p class="select-title">包裹尺寸：</p>-->
                                <!--                                                    <div class="select-list">-->
                                <!--                                                        <div class="form-group">-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">长度：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="packageLength" class="form-control"-->
                                <!--                                                                           name="packageLength"-->
                                <!--                                                                           th:value="*{packageLength}" >-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">宽度：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="packageWidth" class="form-control"-->
                                <!--                                                                           name="packageWidth"-->
                                <!--                                                                           th:value="*{packageWidth}">-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">高度：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="packageHeight" class="form-control"-->
                                <!--                                                                           name="packageHeight"-->
                                <!--                                                                           th:value="*{packageHeight}" >-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">单位：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="packageLengthUnit"-->
                                <!--                                                                           class="form-control"-->
                                <!--                                                                           name="packageLengthUnit"-->
                                <!--                                                                           th:value="*{packageLengthUnit}" >-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                        </div>-->
                                <!--                                                    </div>-->
                                <!--                                                </div>-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                    &lt;!&ndash;包裹重量&ndash;&gt;-->
                                <!--                                    <div class="form-group">-->
                                <!--                                        <div class="col-sm-10">-->
                                <!--                                            <div class="row">-->
                                <!--                                                <div class="col-sm-12 search-collapse">-->
                                <!--                                                    <p class="select-title">包裹重量：</p>-->
                                <!--                                                    <div class="select-list">-->
                                <!--                                                        <div class="form-group">-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">重量：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="packageWeight" class="form-control"-->
                                <!--                                                                           name="packageWeight"-->
                                <!--                                                                           th:value="*{packageWeight}" >-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">单位：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input id="packageWeightUnit"-->
                                <!--                                                                           class="form-control"-->
                                <!--                                                                           name="packageWeightUnit"-->
                                <!--                                                                           th:value="*{packageWeightUnit}">-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                        </div>-->
                                <!--                                                    </div>-->
                                <!--                                                </div>-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                    &lt;!&ndash;箱子个数&ndash;&gt;-->
                                <!--                                    <div class="form-group">-->
                                <!--                                        <div class="col-sm-10">-->
                                <!--                                            <div class="row">-->
                                <!--                                                <div class="col-sm-12 search-collapse">-->
                                <!--                                                    <div class="select-list">-->
                                <!--                                                        <div class="form-group">-->
                                <!--                                                            <div class="col-sm-3">-->
                                <!--                                                                <div class="col-sm-5" style="text-align: right;">-->
                                <!--                                                                    <label class="control-label">箱子个数：</label>-->
                                <!--                                                                </div>-->
                                <!--                                                                <div class="col-sm-7">-->
                                <!--                                                                    <input type="number"-->
                                <!--                                                                           class="form-control is-required"-->
                                <!--                                                                           name="numberOfBoxes"-->
                                <!--                                                                           id="numberOfBoxes"-->
                                <!--                                                                           placeholder="请填写箱子个数"-->
                                <!--                                                                           th:value="*{numberOfBoxes}">-->
                                <!--                                                                </div>-->
                                <!--                                                            </div>-->
                                <!--                                                        </div>-->
                                <!--                                                    </div>-->
                                <!--                                                </div>-->
                                <!--                                            </div>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                </table>-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--文案描述信息-->
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5 id="scroll-desc-info">文案描述信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>

                        <div class="btn-group-sm">
                            <a class="btn btn-success" onclick="generateByGPT('am_bullet_point')">
                                <i class="fa"></i> AI
                            </a>
                            <a class="btn btn-success" onclick="getGPT('description')">
                                <i class="fa fa-plus"></i> 数据组适配参考
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-12">
                                <table class="table table-hover margin bottom">
                                    <div class="form-group">
                                        <div class="col-sm-2" align="center">
                                            <label class="control-label">五点描述：</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <textarea type="text" class="form-control is-required maxlegthflag" reFlag="true" name="itemDescription1" id="itemDescription1"
                                                      style="margin-bottom: 3px" required th:text="*{itemDescription1}" size="700" ></textarea>
                                            <textarea type="text" class="form-control maxlegthflag" name="itemDescription2" id="itemDescription2"
                                                      style="margin-bottom: 3px" required th:text="*{itemDescription2}" size="700" ></textarea>
                                            <textarea type="text" class="form-control maxlegthflag" name="itemDescription3" id="itemDescription3"
                                                      style="margin-bottom: 3px" required th:text="*{itemDescription3}" size="700" ></textarea>
                                            <textarea type="text" class="form-control maxlegthflag" name="itemDescription4" id="itemDescription4"
                                                      style="margin-bottom: 3px" required th:text="*{itemDescription4}" size="700" ></textarea>
                                            <textarea type="text" class="form-control maxlegthflag" required name="itemDescription5" id="itemDescription5"
                                                      th:text="*{itemDescription5}" maxlength="700" ></textarea>
                                        </div>
                                        </div>

                                    <div class="form-group">
                                        <div class="ibox-content no-padding" style="margin-left: 0px;">
                                            <div class="ibox float-e-margins">
                                                <div class="ibox-title">
                                                    <h5>产品描述信息</h5>
                                                </div>
                                                <div class="ibox-content" id="eg" style="padding: 0px;">
                                                    <input type="hidden" name="detailDescription" class="form-control">
                                                    <div class="editor maxlegthflag"  size="2000"  id="detailDescription" data-html="${listingEditDTO.detailDescription}"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--图片&视频信息-->
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5 id="scroll-image-info">图片&视频信息</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="container-div">
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">
                                        <h4>多文件上传:最多上传9张</h4>
                                        <h4 style="color: rgba(246,2,24,0.84)">默认第一张图为主图</h4>
                                        <div style='margin-top: 4px; margin-bottom: 4px;'><a class='btn btn-danger' onclick=oneClickClear()>
                                            <i class='fa fa-edit'></i>一键清除</a>
                                        </div>
                                    </label>
                                    <div class="col-sm-10">
                                        <div id="showimg">
                                            <ul id="showui" class="imageSort connectList"></ul>
                                            <div id="showinput"></div>
                                        </div>

                                        <div class="col-sm-2">
                                            <div class="upload-btn-box">
                                                <a class='btn btn-primary'  onclick="imageUploadByheadId()"><i class='fa fa fa-upload'></i>图片选择</a>
                                            </div>
                                        </div>

                                        <div>
                                            <div class="col-sm-2">
                                                <div class="upload-btn-box">
                                                    <a class="btn btn-primary">
                                                        <i class="fa fa fa-upload"></i> 文件上传
                                                    </a>
                                                    <input type="file" class="upgteimg2" multiple="multiple" />
                                                </div>
                                            </div>
                                            <div class="col-sm-2" id="upgteimg3" >
                                                <a class="btn btn-primary" >
                                                    <i class="fa fa fa-upload"></i> 链接上传
                                                </a>
                                            </div>
                                            <div class="col-sm-5" >
                                                <input id="uploadImageUrl" class="form-control" name="uploadImageUrl" >
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="col-sm-12" style="margin-bottom: 10px;">
                <div class="col-sm-12">
                    <div class="btn-group-sm" role="group">
                        <a class="btn btn-warning" onclick="submitHandler()">
                            <i class="fa fa-download"></i> 保存
                        </a>
                        <a class="btn btn-success" onclick="window.close()">
                            <i class="fa fa-save"></i> 关闭
                        </a>
                    </div>
                </div>
            </div>

            <div class="scroll-button-a btn btn-info btn-rounded" style="top: 80px;"><a href="#scroll-basic-info">基本信息</a></div>
            <div class="scroll-button-a btn btn-info btn-rounded" style="top: 120px;"><a href="#scroll-stock-price">价格库存</a></div>
            <div class="scroll-button-a btn btn-info btn-rounded" style="top: 160px;"><a href="#scroll-title-info">标题信息</a></div>
            <div class="scroll-button-a btn btn-info btn-rounded" style="top: 200px;"><a href="#scroll-attribute-info">属性信息</a></div>
            <div class="scroll-button-a btn btn-info btn-rounded" style="top: 240px;"><a href="#scroll-logic-info">物流信息</a></div>
            <div class="scroll-button-a btn btn-info btn-rounded" style="top: 280px;"><a href="#scroll-desc-info">文案描述信息</a></div>
            <div class="scroll-button-a btn btn-info btn-rounded" style="top: 320px;"><a href="#scroll-image-info">图片&视频信息</a></div>

            <div th:if="${listingEditDTO.listingRateLabel}" class="fixed-label" th:name="listingRateLabel" style="position: fixed; top: 20px ; bottom: 10px; right: 10px;">
                <span th:text="${listingEditDTO.listingRateLabel}"></span>
            </div>
            <div th:if="${listingEditDTO.skuRateLabel}" class="fixed-label"  th:name="skuRateLabel"  style="position: fixed; top: 40px ;  bottom: 10px; right: 10px;">
                <span th:text="${listingEditDTO.skuRateLabel}"></span>
            </div>


        </form>
    </div>
</div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: datetimepicker-js" />
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-typeahead-js"/>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: jasny-bootstrap-js"/>
<th:block th:include="include :: jQuery-upload-js"/>
<th:block th:include="include :: loadingoverlay-min-js"/>
<th:block th:include="include :: ckeditor-classic-js"/>
<th:block th:include="include :: layout-latest-js"/>
<script th:src="@{/extend/js/ckeditor/ckeditorCustom.js?v=20241219}"></script>
<script th:src="@{/js/jquery-ui-1.13.2.js}"></script>
<script th:src="@{/js/fancybox.umd.js}"></script>
<script th:src="@{/extend/js/goodsInfo.js?version=20240731}"></script>
<script th:src="@{/extend/js/amazon/singleOperate.js?version=20250605}"></script>
</body>

<script th:inline="javascript">
    // 上传主图
    var masterImgFile;
    //属性
    var goodsInfoList;

    //页面加载执行，图片回显
    var dataArr = [];
    let goodsImage = {
        default: [], //
        amazon: [],
        other: []
    }

    function oneClickClear(){
        $("#showui").empty();
        $("#showinput").empty();
    }

    // 防抖定时器
    var renderAttributeFrontDataTimer = null;
    var renderImagesFrontDataTimer = null;

    // 渲染状态标记，避免重复渲染
    var isRenderingImages = false;
    var imagesRendered = false;

    // 防抖版本的属性前台数据渲染函数
    function renderAttributeFrontDataDebounced() {
        if (renderAttributeFrontDataTimer) {
            clearTimeout(renderAttributeFrontDataTimer);
        }
        renderAttributeFrontDataTimer = setTimeout(function () {
            renderAttributeFrontData();
            renderAttributeFrontDataTimer = null;
        }, 300); // 300ms防抖延迟
    }

    // 重置图片渲染状态（用于强制重新渲染）
    function resetImageRenderState() {
        isRenderingImages = false;
        imagesRendered = false;
    }

    // 防抖版本的图片前台数据渲染函数
    function renderImagesFrontDataDebounced() {
        if (renderImagesFrontDataTimer) {
            clearTimeout(renderImagesFrontDataTimer);
        }
        renderImagesFrontDataTimer = setTimeout(function () {
            renderImagesFrontData();
            renderImagesFrontDataTimer = null;
        }, 300); // 300ms防抖延迟
    }

    // 前台数据处理函数调用标记
    var initFrontDataDisplayCalled = false;

    // 前台数据处理函数
    function initFrontDataDisplay() {
        // 防止重复调用
        if (initFrontDataDisplayCalled) {
            return;
        }
        initFrontDataDisplayCalled = true;

        // 处理映射配置的前台数据
        if (frontMappingData) {
            renderFrontMappingData();

            // 延迟处理属性区域的前台数据映射（等待属性表格加载完成）
            setTimeout(function () {
                renderAttributeFrontDataDebounced();
            }, 2000); // 延迟2秒，确保属性表格已加载
        }

        // 处理图片区域的前台数据显示
        if (frontDetails && frontDetails.length > 0) {
            renderImagesFrontDataDebounced();
        }
    }

    // 获取属性行的当前值（后台数据）
    function getAttributeRowValue($row) {
        // 查找属性值单元格（第5列，索引4）
        const $valueCell = $row.find('td').eq(4);
        if ($valueCell.length === 0) {
            return '';
        }

        // 尝试从input元素获取值
        const $input = $valueCell.find('input[name*="tableValue"]');
        if ($input.length > 0) {
            return $input.val() || '';
        }

        // 尝试从select元素获取值
        const $select = $valueCell.find('select[name*="tableValue"]');
        if ($select.length > 0) {
            return $select.val() || '';
        }

        // 如果都没有找到，返回空字符串
        return '';
    }

    // 专门处理属性区域的前台数据映射（动态匹配版 - 无需枚举配置）
    function renderAttributeFrontData() {
        if (!frontDetails || frontDetails.length === 0) {
            return;
        }

        // 检查属性表格是否已加载
        const $attrTable = $('#attr-info-table');
        if ($attrTable.length === 0) {
            return;
        }

        const $attrRows = $attrTable.find('tbody tr');
        if ($attrRows.length === 0) {
            return;
        }

        // 先清除所有现有的前台数据显示，避免重复
        $attrRows.find('.front-data-display-dynamic').remove();

        // 遍历每个属性行，动态匹配前台数据
        $attrRows.each(function (index, row) {
            const $row = $(row);
            const propNodePath = $row.find('input[name*="propNodePath"]').val();

            if (!propNodePath) {
                return; // 跳过无效行
            }

            // 直接从前台数据中查找匹配的path
            const matchedFrontData = frontDetails.filter(function (detail) {
                return detail.path === propNodePath;
            });

            if (matchedFrontData.length > 0) {
                // 如果匹配到多个前台数据，需要进行value一致性检查
                let finalFrontData = matchedFrontData;

                if (matchedFrontData.length > 1) {
                    // 获取当前属性行的后台值
                    const backendValue = getAttributeRowValue($row);

                    if (backendValue && backendValue.trim() !== '') {
                        // 过滤出与后台值一致的前台数据
                        const matchedByValue = matchedFrontData.filter(function (detail) {
                            return detail.value && detail.value.trim() === backendValue.trim();
                        });

                        if (matchedByValue.length > 0) {
                            finalFrontData = matchedByValue;
                        }
                        // 如果没有value匹配的，保持原有逻辑显示所有匹配的path数据
                    }
                }

                // 为筛选后的前台数据创建显示
                finalFrontData.forEach(function (frontDetail, frontIndex) {
                    if (frontDetail.value && frontDetail.value.trim() !== '') {
                        createAttributeFrontDataDisplayDynamic($row, frontDetail, frontIndex);
                    }
                });
            }
        });
    }

    // 为属性行创建前台数据显示（动态版本）
    function createAttributeFrontDataDisplayDynamic($attrRow, frontDetail, index) {
        // 多重检查避免重复创建
        const $valueCell = $attrRow.find('td').eq(4);
        if ($valueCell.length === 0) {
            return; // 找不到属性值单元格
        }

        // 检查是否已经存在相同path和index的前台数据显示
        const existingDisplay = $valueCell.find(`[data-front-path="${frontDetail.path}"][data-index="${index}"]`);
        if (existingDisplay.length > 0) {
            return; // 避免重复创建
        }

        // 额外检查：如果已经存在相同path的显示（不管index），也跳过
        const existingPathDisplay = $valueCell.find(`[data-front-path="${frontDetail.path}"]`);
        if (existingPathDisplay.length > 0) {
            return; // 避免同一path的重复显示
        }

        // 创建前台数据展示HTML
        const displayHtml = createFrontDataDisplayHtmlDynamic(frontDetail, index);

        // 在属性值输入框/下拉框前面插入前台数据
        $valueCell.prepend(displayHtml);
    }

    // 为属性行创建前台数据显示（原版本，保持兼容）
    function createAttributeFrontDataDisplay($attrRow, mapping, index) {
        // 检查是否已经存在相同的前台数据显示
        const existingDisplay = $attrRow.find(`[data-backend-field="${mapping.backendFieldName}"][data-index="${index}"]`);
        if (existingDisplay.length > 0) {
            return; // 避免重复创建
        }

        // 创建前台数据展示HTML
        const displayHtml = createFrontDataDisplayHtml(mapping, index);

        // 找到属性值单元格（第5列，索引4）
        const $valueCell = $attrRow.find('td').eq(4);
        if ($valueCell.length > 0) {
            // 在属性值输入框/下拉框前面插入前台数据
            $valueCell.prepend(displayHtml);
        }
    }

    // 创建前台数据展示HTML（动态版本）
    function createFrontDataDisplayHtmlDynamic(frontDetail, index) {
        let displayValue = frontDetail.value;

        // 根据数据类型进行格式化
        if (frontDetail.valueType === 'number') {
            const numValue = parseFloat(displayValue);
            if (!isNaN(numValue)) {
                displayValue = numValue.toLocaleString();
            }
        } else if (displayValue && displayValue.startsWith('http')) {
            // URL类型显示为链接
            displayValue = `<a href="${displayValue}" target="_blank" style="color: #337ab7;">${displayValue}</a>`;
        }

        // 生成友好的显示标签
        const displayLabel = generateFriendlyLabel(frontDetail.path, frontDetail.key);

        return `
            <div class="front-data-display-dynamic"
                 data-front-path="${frontDetail.path}"
                 data-index="${index}">
                <span class="front-data-label">${displayLabel}:</span>
                <span class="front-data-content">${displayValue}</span>
                <small class="front-data-tag">(前台数据)</small>
            </div>
        `;
    }

    // 生成友好的显示标签
    function generateFriendlyLabel(path, key) {
        // 根据path生成友好的中文标签
        const labelMapping = {
            'item_name.value': '商品名称',
            'list_price.value': '价格',
            'list_price.unit': '价格单位',
            'manufacturer.value': '制造商',
            'brand.value': '品牌',
            'part_number.value': '零件号',
            'model_name.value': '型号名称',
            'model_number.value': '型号编号',
            'external_product_id.value': '外部编码',
            'external_product_id.type': '编码类型',
            'bullet_point_0.value': '要点1',
            'bullet_point_1.value': '要点2',
            'bullet_point_2.value': '要点3',
            'bullet_point_3.value': '要点4',
            'bullet_point_4.value': '要点5',
            'item_dimensions.length.value': '商品长度',
            'item_dimensions.width.value': '商品宽度',
            'item_dimensions.height.value': '商品高度',
            'item_package_dimensions.length.value': '包装长度',
            'item_package_dimensions.width.value': '包装宽度',
            'item_package_dimensions.height.value': '包装高度',
            'item_package_weight.value': '包装重量'
        };

        // 优先使用映射表
        if (labelMapping[path]) {
            return labelMapping[path];
        }

        // 如果没有映射，尝试从path中提取有意义的部分
        if (path.includes('.value')) {
            const basePath = path.replace('.value', '');
            const pathParts = basePath.split('.');
            const lastPart = pathParts[pathParts.length - 1];

            // 处理常见的属性名
            const commonNames = {
                'item_name': '商品名称',
                'list_price': '价格',
                'manufacturer': '制造商',
                'brand': '品牌',
                'part_number': '零件号',
                'model_name': '型号名称',
                'model_number': '型号编号',
                'color': '颜色',
                'size': '尺寸',
                'material': '材质',
                'weight': '重量',
                'length': '长度',
                'width': '宽度',
                'height': '高度'
            };

            if (commonNames[lastPart]) {
                return commonNames[lastPart];
            }

            // 如果都没有匹配，返回格式化的属性名
            return lastPart.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        // 最后的备选方案，使用key
        return key || '属性';
    }

    // 初始化前台数据双击选择功能
    function initFrontDataSelection() {
        // 为所有前台数据内容添加双击选择事件（包括标题内容）
        $(document).on('dblclick', '.front-data-content, .front-title-content', function (e) {
            e.preventDefault();
            e.stopPropagation();

            // 选择当前元素的文本内容
            const element = this;
            if (window.getSelection) {
                const selection = window.getSelection();
                const range = document.createRange();
                range.selectNodeContents(element);
                selection.removeAllRanges();
                selection.addRange(range);
            } else if (document.selection) {
                // IE兼容
                const range = document.body.createTextRange();
                range.moveToElementText(element);
                range.select();
            }
        });

        // 为整个前台数据容器添加双击事件（备用方案）
        $(document).on('dblclick', '.front-data-display, .front-data-display-dynamic, .front-title-display', function (e) {
            // 如果点击的不是内容区域，则选择内容区域
            if (!$(e.target).hasClass('front-data-content') && !$(e.target).hasClass('front-title-content')) {
                const $content = $(this).find('.front-data-content, .front-title-content');
                if ($content.length > 0) {
                    e.preventDefault();
                    e.stopPropagation();

                    const element = $content[0];
                    if (window.getSelection) {
                        const selection = window.getSelection();
                        const range = document.createRange();
                        range.selectNodeContents(element);
                        selection.removeAllRanges();
                        selection.addRange(range);
                    }
                }
            }
        });
    }

    // 渲染图片区域的前台数据
    function renderImagesFrontData() {
        if (!frontDetails || frontDetails.length === 0) {
            return;
        }

        // 检查是否已经渲染过
        if (imagesRendered) {
            return;
        }

        // 防止重复渲染
        if (isRenderingImages) {
            return;
        }
        isRenderingImages = true;

        // 获取图片相关的前台数据
        const imageData = frontDetails.filter(function (detail) {
            return detail.path && detail.path.startsWith('images_') && detail.key === 'link';
        });

        // 去重处理：解决VC数据异常回退导致的重复问题
        const uniqueImageData = [];
        const seenImages = new Map(); // 使用Map来存储更详细的信息

        imageData.forEach(function (detail) {
            // 基于图片URL去重，因为相同URL的图片应该只显示一次
            const imageUrl = detail.value;
            if (!seenImages.has(imageUrl)) {
                seenImages.set(imageUrl, detail);
                uniqueImageData.push(detail);
            } else {
                // 如果URL相同，选择path更简单的（通常是正常处理的结果）
                const existingDetail = seenImages.get(imageUrl);
                if (detail.path.length < existingDetail.path.length) {
                    // 替换为更简单的path
                    const index = uniqueImageData.findIndex(d => d.value === imageUrl);
                    if (index !== -1) {
                        uniqueImageData[index] = detail;
                        seenImages.set(imageUrl, detail);
                    }
                }
            }
        });

        // 使用去重后的数据
        const finalImageData = uniqueImageData;


        if (finalImageData.length === 0) {
            // 如果没有图片数据，清除现有的前台图片显示区域
            const $existingSection = $('#front-images-section');
            if ($existingSection.length > 0) {
                $existingSection.remove();
            }
            isRenderingImages = false;
            imagesRendered = true; // 标记为已处理，即使没有图片
            return;
        }

        // 按path排序，确保主图在前
        finalImageData.sort(function (a, b) {
            // 提取路径中的索引号进行排序
            const indexA = parseInt(a.path.split('_')[1].split('.')[0]);
            const indexB = parseInt(b.path.split('_')[1].split('.')[0]);
            return indexA - indexB;
        });

        // 获取图片容器
        const $imageContainer = $('#showui');
        if ($imageContainer.length === 0) {
            isRenderingImages = false;
            imagesRendered = true; // 标记为已处理
            return;
        }

        // 在图片容器前添加前台数据显示区域
        let $frontImageSection = $('#front-images-section');
        if ($frontImageSection.length === 0) {
            $frontImageSection = $('<div id="front-images-section" style="margin-bottom: 15px;"></div>');
            $imageContainer.before($frontImageSection);
        }

        // 清空之前的显示，避免重复
        $frontImageSection.empty();

        // 添加标题
        $frontImageSection.append('<h5 style="color: #2196F3; margin-bottom: 10px;"><i class="fa fa-image"></i> 前台图片数据</h5>');

        // 创建图片显示容器
        const $frontImagesContainer = $('<div class="front-images-container" style="display: flex; flex-wrap: wrap; gap: 10px;"></div>');
        $frontImageSection.append($frontImagesContainer);

        // 渲染每张图片
        finalImageData.forEach(function (imageDetail, index) {
            const imageUrl = imageDetail.value;
            if (!imageUrl || !imageUrl.trim()) {
                return;
            }

            // 获取variant信息
            const basePath = imageDetail.path.replace('.link', '');
            const variantDetail = frontDetails.find(function (detail) {
                return detail.path === basePath + '.variant';
            });
            const variant = variantDetail ? variantDetail.value : 'Unknown';

            // 生成图片显示HTML
            const imageHtml = createFrontImageDisplayHtml(imageUrl, variant, index);
            $frontImagesContainer.append(imageHtml);
        });

        // 重置渲染状态标记并设置已渲染标记
        isRenderingImages = false;
        imagesRendered = true;
    }

    // 创建前台图片显示HTML
    function createFrontImageDisplayHtml(imageUrl, variant, index) {
        // 确定图片标签
        let imageLabel = '未知图片';
        if (variant === 'MAIN') {
            imageLabel = '主图';
        } else if (variant.startsWith('PT')) {
            const ptNumber = variant.replace('PT', '');
            imageLabel = '副图' + ptNumber;
        }

        return `
            <div class="front-image-item" style="border: 1px solid #ddd; border-radius: 5px; padding: 8px; background: #f9f9f9; width: 160px;">
                <div class="front-data-display" style="margin-bottom: 8px;">
                    <span class="front-data-label">${imageLabel}:</span>
                    <small class="front-data-tag">(前台数据)</small>
                </div>
                <div style="text-align: center;">
                    <a data-fancybox="front-gallery" href="${imageUrl}" title="${imageLabel}">
                        <img src="${imageUrl}"
                             style="width: 140px; height: 140px; object-fit: cover; border-radius: 3px; cursor: pointer;"
                             alt="${imageLabel}"
                             onerror="this.src='/img/no-image.png'">
                    </a>
                </div>
            </div>
        `;
    }

    // 页面加载完成后初始化前台数据显示
    $(document).ready(function () {
        // 初始化前台数据选择功能
        initFrontDataSelection();
        // 注释掉这里的调用，避免重复初始化
        // initFrontDataDisplay();




    });
    // 渲染选中品类的必填字段数据
    let prefixRequiredField = ctx + "publication/goods/attribute";
    var ads_prefix = ctx + "ads";
    let listingEditDTO = [[${listingEditDTO}]];
    let noEditFlag = [[${noEditFlag}]];
    var refundRateLabelData = [[${@dict.getType('refund_rate_label')}]];
    let pdmGoodsCode = listingEditDTO.goodsCode;
    // 前台数据
    var frontDetails = /*[[${frontDetails}]]*/ null;
    var frontMappingData = /*[[${frontMappingData}]]*/ null;
    let uuid = listingEditDTO.uuid;
    let goodsId = listingEditDTO.goodsHeadId;
    let platform = $('#platform').val();
    let shopCode = $('#shopCode').val();
    let isVc = shopCode.includes('VC');
    var notRequiredData = []
    var showAttrData = []

    // 渲染商品重要信息必填字段
    let categoryId = $('#categoryId').val();


    // 渲染映射配置的前台数据
    function renderFrontMappingData() {
        if (!frontMappingData || typeof frontMappingData !== 'object') {
            return;
        }

        // 遍历每个后台字段的映射数据
        for (const [backendFieldName, mappingList] of Object.entries(frontMappingData)) {
            if (!mappingList || !Array.isArray(mappingList) || mappingList.length === 0) {
                continue;
            }

            // 为每个映射项创建展示元素
            mappingList.forEach(function (mapping, index) {
                createFrontDataDisplay(backendFieldName, mapping, index);
            });
        }
    }

    // 创建前台数据展示元素
    function createFrontDataDisplay(backendFieldName, mapping, index) {
        if (!mapping.frontValue || mapping.frontValue.trim() === '') {
            return;
        }

        // 查找对应的后台字段元素
        const targetFieldName = mapping.targetFieldName || backendFieldName;
        const backendElement = findBackendFieldElement(targetFieldName);
        if (!backendElement || backendElement.length === 0) {
            return;
        }

        // 检查是否已经存在相同的前台数据显示，避免重复
        // 扩大检查范围，确保在整个容器中都不会重复
        const $container = backendElement.closest('.form-group, .input-group, .col-sm-9, .col-sm-10, .col-sm-7');
        const existingDisplay = $container.find(`[data-backend-field="${mapping.backendFieldName}"][data-index="${index}"]`);
        if (existingDisplay.length > 0) {
            return;
        }

        // 创建前台数据展示HTML
        const displayHtml = createFrontDataDisplayHtml(mapping, index);

        // 根据显示位置插入元素
        insertFrontDataDisplay(backendElement, displayHtml, mapping.displayPosition, backendFieldName);
    }

    // 查找后台字段元素
    function findBackendFieldElement(fieldName) {
        // 常见的字段名映射
        const fieldSelectors = {
            'title': '#title',
            'standardPrice': '.sc-product input[name="standardPrice"]',  // SC产品的价格字段
            'listPrice': '.vc-product input[name="listPrice"]',          // VC产品的价格字段
            'description': 'textarea[name="description"]',
            'detailDescription': '#detailDescription',  // 产品描述富文本编辑器
            'itemDescription1': '#itemDescription1',    // 五点描述1
            'itemDescription2': '#itemDescription2',    // 五点描述2
            'itemDescription3': '#itemDescription3',    // 五点描述3
            'itemDescription4': '#itemDescription4',    // 五点描述4
            'itemDescription5': '#itemDescription5',    // 五点描述5
            'brandCode': '.sc-product #brandName, .sc-product input[name="brandCode"], .sc-product input[name="brandName"], .sc-product select[name="brandName"]',
            'brandCode_vc': '.vc-product #brandName, .vc-product input[name="brandCode"], .vc-product input[name="brandName"], .vc-product select[name="brandName"]',  // VC品牌字段只查找VC区域内的
            // VC产品特有字段
            'externalProductIdType': 'select[name="externalProductIdType"]',  // 外部产品ID类型
            'externalProductId': 'input[name="externalProductId"]',          // 外部产品ID值
            // 物流信息字段
            'itemLength': '#itemLength',                // 商品长度
            'itemWidth': '#itemWidth',                  // 商品宽度
            'itemHeight': '#itemHeight',                // 商品高度
            'itemLengthUnit': '#itemLengthUnit',        // 商品尺寸单位
            'packageLength': '#packageLength',          // 包裹长度
            'packageWidth': '#packageWidth',            // 包裹宽度
            'packageHeight': '#packageHeight',          // 包裹高度
            'packageLengthUnit': '#packageLengthUnit',  // 包裹尺寸单位
            'packageWeight': '#packageWeight',          // 包裹重量
            'packageWeightUnit': '#packageWeightUnit',  // 包裹重量单位
            // 属性字段 - 动态表格中的字段，需要特殊处理，字段名需要与propNodePath匹配
            'manufacturer.value': 'attr:manufacturer',
            'model_name.value': 'attr:model_name',
            'model_number.value': 'attr:model_number',
            'part_number.value': 'attr:part_number',
            'fit_type.value': 'attr:fit_type',
            'exterior_finish.value': 'attr:exterior_finish',
            'specific_uses_for_product.value': 'attr:specific_uses_for_product',
            'batteries_required.value': 'attr:batteries_required',
            'is_assembly_required.value': 'attr:is_assembly_required',
            'warranty_description.value': 'attr:warranty_description',
            'auto_part_position.value': 'attr:auto_part_position',
            'number_of_boxes.value': 'attr:number_of_boxes',
            'number_of_items.value': 'attr:number_of_items',
            'item_type_keyword.value': 'attr:item_type_keyword',
            'product_site_launch_date.value': 'attr:product_site_launch_date',
            'mainImage': 'input[name="mainImage"]',
            'stockOnSalesQty': 'input[name="stockOnSalesQty"]'
        };

        const selector = fieldSelectors[fieldName];
        if (selector) {
            // 特殊处理属性字段
            if (selector.startsWith('attr:')) {
                const attrName = selector.substring(5);
                return findAttributeField(attrName);
            }

            // 查找元素
            const elements = $(selector);

            // 对于品牌字段，进行额外的验证确保在正确的产品区域内
            if (fieldName === 'brandCode_vc' && elements.length > 0) {
                // 确保找到的字段确实在VC产品区域内
                const vcElements = elements.filter(function () {
                    return $(this).closest('.vc-product').length > 0;
                });
                return vcElements.length > 0 ? vcElements.first() : null;
            }

            if (fieldName === 'brandCode' && elements.length > 0) {
                // 确保找到的字段确实在SC产品区域内
                const scElements = elements.filter(function () {
                    return $(this).closest('.sc-product').length > 0;
                });
                return scElements.length > 0 ? scElements.first() : null;
            }

            return elements.first();
        } else {
            // 如果没有预定义的选择器，尝试通用匹配
            const genericElement = $(`[name="${fieldName}"]`).first();
            return genericElement.length > 0 ? genericElement : null;
        }
    }

    // 查找属性表格中的字段（改进版：基于标准化路径精确匹配）
    function findAttributeField(attrName) {
        // 在属性表格中查找包含指定属性名的行
        const $attrTable = $('#attr-info-table');
        if ($attrTable.length === 0) {
            return null;
        }

        // 查找属性名匹配的行
        const $rows = $attrTable.find('tbody tr');
        for (let i = 0; i < $rows.length; i++) {
            const $row = $($rows[i]);

            // 优先通过propNodePath精确匹配
            const propNodePath = $row.find('input[name*="propNodePath"]').val();
            if (propNodePath && propNodePath === attrName) {
                // 找到对应的属性值输入框或下拉框
                const $valueInput = $row.find('input[name*="tableValue"], select[name*="tableValue"]');
                if ($valueInput.length > 0) {
                    return $valueInput.first();
                }
            }

            // 备用方案：模糊匹配（保持向后兼容）
            if (propNodePath && (propNodePath.includes(attrName) || propNodePath.startsWith(attrName))) {
                // 找到对应的属性值输入框或下拉框
                const $valueInput = $row.find('input[name*="tableValue"], select[name*="tableValue"]');
                if ($valueInput.length > 0) {
                    return $valueInput.first();
                }
            }
        }

        return null;
    }

    // 创建前台数据展示HTML
    function createFrontDataDisplayHtml(mapping, index) {
        const cssClass = mapping.cssClass || 'front-data-display';
        let displayValue = mapping.frontValue;

        // 处理不同字段类型的显示
        if (mapping.fieldType === 'url' && displayValue.startsWith('http')) {
            displayValue = `<a href="${displayValue}" target="_blank" style="color: #337ab7;">${displayValue}</a>`;
        } else if (mapping.fieldType === 'number') {
            // 数字类型格式化 - 增加验证避免NaN
            const numValue = parseFloat(displayValue);
            if (!isNaN(numValue)) {
                displayValue = numValue.toLocaleString();
            } else {
                console.warn('前台数据数字格式化失败:', {
                    backendField: mapping.backendFieldName,
                    originalValue: displayValue,
                    frontPath: mapping.frontFieldPath,
                    frontKey: mapping.frontFieldKey
                });
                // 保持原始值，不进行格式化
                displayValue = displayValue;
            }
        }

        return `
            <div class="${cssClass}" data-backend-field="${mapping.backendFieldName}" data-index="${index}">
                <span class="front-data-label">${mapping.displayLabel}:</span>
                <span class="front-data-content">${displayValue}</span>
                <small class="front-data-tag">(前台数据)</small>
            </div>
        `;
    }

    // 插入前台数据展示元素
    function insertFrontDataDisplay(backendElement, displayHtml, displayPosition, backendFieldName) {
        const $backendElement = $(backendElement);

        // 特殊处理标题字段，确保不影响字数统计
        if ($backendElement.attr('id') === 'title' || $backendElement.attr('name') === 'title') {
            const $titleContainer = $backendElement.closest('.col-sm-10');
            if ($titleContainer.length > 0) {
                switch (displayPosition) {
                    case 'before_field':
                        // 在input-group前面插入，不影响字数统计
                        $titleContainer.find('.input-group').before(displayHtml);
                        break;
                    case 'after_field':
                        // 在input-group后面插入
                        $titleContainer.find('.input-group').after(displayHtml);
                        break;
                    case 'custom':
                    default:
                        // 默认在input-group前面显示
                        $titleContainer.find('.input-group').before(displayHtml);
                        break;
                }
                return;
            }
        }

        // 特殊处理standardPrice字段，确保显示在SC产品价格区域
        if ($backendElement.attr('name') === 'standardPrice') {
            const $priceContainer = $backendElement.closest('.col-sm-8, .col-sm-9');
            if ($priceContainer.length > 0) {
                switch (displayPosition) {
                    case 'before_field':
                        $priceContainer.prepend(displayHtml);
                        break;
                    case 'after_field':
                        $priceContainer.append(displayHtml);
                        break;
                    case 'custom':
                    default:
                        $priceContainer.prepend(displayHtml);
                        break;
                }
                return;
            }
        }

        // 特殊处理listPrice字段，确保显示在VC产品价格区域
        if ($backendElement.attr('name') === 'listPrice') {
            const $priceContainer = $backendElement.closest('.col-sm-8');
            if ($priceContainer.length > 0) {
                switch (displayPosition) {
                    case 'before_field':
                        $priceContainer.prepend(displayHtml);
                        break;
                    case 'after_field':
                        $priceContainer.append(displayHtml);
                        break;
                    case 'custom':
                    default:
                        $priceContainer.prepend(displayHtml);
                        break;
                }
                return;
            }
        }

        // 特殊处理VC产品的外部产品ID字段
        if ($backendElement.attr('name') === 'externalProductIdType' || $backendElement.attr('name') === 'externalProductId') {
            const $container = $backendElement.closest('.col-sm-4');
            if ($container.length > 0) {
                switch (displayPosition) {
                    case 'before_field':
                        $container.prepend(displayHtml);
                        break;
                    case 'after_field':
                        $container.append(displayHtml);
                        break;
                    case 'custom':
                    default:
                        $container.prepend(displayHtml);
                        break;
                }
                return;
            }
        }

        // 特殊处理brandCode字段，确保显示在正确的品牌区域
        if ($backendElement.attr('id') === 'brandName' ||
            $backendElement.attr('name') === 'brandCode' ||
            $backendElement.attr('name') === 'brandName') {

            // 简化逻辑：如果是VC相关的字段，直接插入到VC产品区域
            if (backendFieldName === 'brandCode_vc' || backendFieldName.includes('_vc')) {
                // 直接查找VC产品区域的品牌容器
                const $vcBrandContainer = $('.vc-product .col-sm-9');
                if ($vcBrandContainer.length > 0) {
                    switch (displayPosition) {
                        case 'before_field':
                            $vcBrandContainer.prepend(displayHtml);
                            break;
                        case 'after_field':
                            $vcBrandContainer.append(displayHtml);
                            break;
                        case 'custom':
                        default:
                            $vcBrandContainer.prepend(displayHtml);
                            break;
                    }
                    return;
                }
            } else {
                // SC字段的处理逻辑
                const $brandContainer = $backendElement.closest('.col-sm-9');
                if ($brandContainer.length > 0) {
                    switch (displayPosition) {
                        case 'before_field':
                            $brandContainer.prepend(displayHtml);
                            break;
                        case 'after_field':
                            $brandContainer.append(displayHtml);
                            break;
                        case 'custom':
                        default:
                            $brandContainer.prepend(displayHtml);
                            break;
                    }
                    return;
                }
            }
        }

        // 特殊处理描述字段
        if ($backendElement.attr('id') && $backendElement.attr('id').startsWith('itemDescription')) {
            // 五点描述字段 - 在textarea上方显示
            const $descContainer = $backendElement.closest('.col-sm-8');
            if ($descContainer.length > 0) {
                // 在对应的textarea前面插入
                $backendElement.before(displayHtml);
                return;
            }
        }

        if ($backendElement.attr('id') === 'detailDescription') {
            // 产品描述字段 - 在富文本编辑器上方显示
            const $detailContainer = $backendElement.closest('.ibox-content');
            if ($detailContainer.length > 0) {
                switch (displayPosition) {
                    case 'before_field':
                        $detailContainer.prepend(displayHtml);
                        break;
                    case 'after_field':
                        $detailContainer.append(displayHtml);
                        break;
                    case 'custom':
                    default:
                        $detailContainer.prepend(displayHtml);
                        break;
                }
                return;
            }
        }

        // 特殊处理物流信息字段
        const logisticsFields = ['itemLength', 'itemWidth', 'itemHeight', 'itemLengthUnit',
            'packageLength', 'packageWidth', 'packageHeight', 'packageLengthUnit',
            'packageWeight', 'packageWeightUnit'];
        if (logisticsFields.includes($backendElement.attr('id'))) {
            // 物流信息字段 - 在输入框上方显示
            const $logisticsContainer = $backendElement.closest('.col-sm-7');
            if ($logisticsContainer.length > 0) {
                switch (displayPosition) {
                    case 'before_field':
                        $logisticsContainer.prepend(displayHtml);
                        break;
                    case 'after_field':
                        $logisticsContainer.append(displayHtml);
                        break;
                    case 'custom':
                    default:
                        $logisticsContainer.prepend(displayHtml);
                        break;
                }
                return;
            }
        }

        // 特殊处理属性字段
        if ($backendElement.attr('name') && $backendElement.attr('name').includes('amazonAttributeLines') && $backendElement.attr('name').includes('tableValue')) {
            // 属性字段 - 在属性值输入框上方显示
            const $attrRow = $backendElement.closest('tr');
            if ($attrRow.length > 0) {
                // 找到属性值单元格（第5列，索引4）
                const $valueCell = $attrRow.find('td').eq(4);
                if ($valueCell.length > 0) {
                    switch (displayPosition) {
                        case 'before_field':
                            // 在输入框/下拉框前面插入
                            $valueCell.prepend(displayHtml);
                            break;
                        case 'after_field':
                            // 在输入框/下拉框后面插入
                            $valueCell.append(displayHtml);
                            break;
                        case 'custom':
                        default:
                            // 默认在输入框/下拉框前面显示
                            $valueCell.prepend(displayHtml);
                            break;
                    }
                    return;
                }
            }
        }

        // 其他字段的通用处理
        // 尝试找到最合适的容器
        let $container = $backendElement.closest('.form-group');
        if ($container.length === 0) {
            $container = $backendElement.closest('.input-group');
        }
        if ($container.length === 0) {
            $container = $backendElement.closest('.col-sm-8, .col-sm-9, .col-sm-10');
        }
        if ($container.length === 0) {
            $container = $backendElement.parent();
        }

        switch (displayPosition) {
            case 'before_field':
                $container.prepend(displayHtml);
                break;
            case 'after_field':
                $container.append(displayHtml);
                break;
            case 'custom':
            default:
                // 默认在字段前面显示
                $container.prepend(displayHtml);
                break;
        }
    }

    function generateByGPT(button) {
        let isTitle = button == 'am_title_button';
        // 弹窗确认
        $.modal.confirm("是否使用GPT生成" + (isTitle ? "标题" : "描述") + "？", function() {
            let data = {};
            if (button == 'am_title_button') {
                data = {
                    "businessId": $("#id").val(),
                    "platform": platform,
                    "originalText": $("#title").val(),
                    "buttonId": button,
                    "actionType": "click"
                }
            } else {
                let bulletPoints = {}
                for (let i = 1; i <= 5; i++) {
                    bulletPoints[i] = $("#itemDescription" + i).val();
                }
                // 值全为空，提示
                if (Object.values(bulletPoints).every(item => item.length === 0)) {
                    $.modal.alertWarning("请先输入描述");
                    return;
                }
                data = {
                    "businessId": $("#id").val(),
                    "platform": platform,
                    "originalText": JSON.stringify(bulletPoints),
                    "buttonId": button
                }
            }
            $.ajax({
                url: ctx + "ai/generate",
                type: "post",
                beforeSend: function() {
                    $.modal.loading("正在生成中，请稍后...");
                },
                data: data,
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        let resData = result.data
                        if (button == 'am_title_button') {
                            $("#title").val(resData.title);
                            // 刷新一下字数
                            $("#title").trigger('input');
                        } else {
                            let bulletPoints = resData.bulletPoints;
                            for (let i = 1; i <= 5; i++) {
                                $("#itemDescription" + i).val(bulletPoints[i]);
                            }
                        }
                        $.modal.msgSuccess(result.msg);
                    } else {
                        $.modal.msgError(result.msg);
                    }
                }
            });
        });
    }

    //新增字段
    function addColumn() {
        table.set('attr-info-table')
        let row = {
            propNodePath: "",
            propRequired: "N",
            showPropNodePath: "",
            propValue: "",
            isAppendAttr: "Y",
            fieldName: "",
            changeAppendAttr: "N",
            isCustom: 'Y'
        }
        let datas = $('#attr-info-table').bootstrapTable('getData');

        datas.push(row);
        sub.editRow()

        $('#attr-info-table').bootstrapTable('load', datas);
        sub.editRow()

        initRequireInfo();
    }


    function initTableSelect() {
        $('.table-select2').select2({
            tags: true
        });
        $('.table-select2').on('change', function() {
            if ($(this).val()) {
                $(this).parent().find('.select2-container').find('.select2-selection').removeClass('invalid');
            } else {
                $(this).parent().find('.select2-container').find('.select2-selection').addClass('invalid');
            }
        });
    }
    var publishStatus = $("#publishStatus").val();

    function attributeInfoTable(){
        $.table.destroy("attr-info-table");
        var options = {
            id: "attr-info-table",
            showSearch: false,
            data: showAttrData,
            toolbar: "toolbar2",
            modalName: "属性信息",
            uniqueId: "showPropNodePath",
            pagination: false,
            showColumns: false,
            showToggle: false,
            queryParams:[],
            columns: [
                {
                    title: '',
                    checkbox: true,
                    formatter: function(value, row, index) {
                        // 如果该行应禁用复选框，返回带有 disabled 属性的复选框
                        if (row.isCustom == 'Y') {
                            return {
                                disabled: false
                            };
                        }
                        return {
                            disabled: true
                        };
                    }
                },
                {
                    field: 'index',
                    align: 'center',
                    width: 50,
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'showPropNodePath',
                    title: '属性名称' + ' ' + productType + '',
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='amazonAttributeLines[%s].id' value='%s'>", index, row.propId);
                        var columnIndexProp = $.common.sprintf("<input type='hidden' name='amazonAttributeLines[%s].propNodePath' value='%s'>", index, row.propNodePath);

                        if(row.isAppendAttr == 'Y' && row.changeAppendAttr == 'N') {
                            var data = [{index: index, type: '', list: notRequiredData}];
                            return $("#appendAttrTpl").tmpl(data).html();
                        }else {
                            return $.common.sprintf("<input type='hidden' name='amazonAttributeLines[%s].showPropNodePath' value='%s'>%s", index, row.showPropNodePath, value)  + columnIndex + columnIndexProp;
                        }
                    }
                },
                {
                    field: 'fieldName',
                    title: '属性释义',
                    formatter: function (value, row, index) {
                        if(row.isAppendAttr == 'Y' && row.changeAppendAttr == 'N') {
                            return '';
                        }
                        let name = "<input readonly class='fieldName' type='hidden' name='amazonAttributeLines[" + index + "].tableName' value='" + value + "'>";
                        name += "<span>" + value + "</span> ";
                        name += "<a href='javascript:void(0)' class='fa fa-info-circle' data-html='true' data-toggle='tooltip' title='" + row.fieldDesc + "' data-original-title='" + row.fieldDesc + "'></a>";
                        return name;
                    }
                },
                {
                    field: 'propValue',
                    title: '属性值',
                    formatter: function (value, row, index) {
                        if(row.isAppendAttr == 'Y' && row.changeAppendAttr == 'N') {
                            return '';
                        }
                        if (value == null || value == undefined) {
                            value = "";
                        }
                        //pn都不能编辑
                        if ( $.common.startWith(row.showPropNodePath,'part_number') ){
                            return $.common.sprintf("<input readonly class='form-control' style='width: 400px;' name='amazonAttributeLines[%s].tableValue' value='" + value + "'>", index, value);
                        }
                        //亚马逊
                        if ($.common.isNotEmpty(row.dictValues)) {
                            // row.dictValues 不包含当前值，使用文本框
                            // if ($.common.isNotEmpty(value) && row.dictValues.filter(item => item.code === value).length === 0) {
                            //     var input =  "<input class='form-control' style='width: 400px;' ";
                            //     if($.common.equals(row.editableFlag,"N") && noEditFlag) {
                            //         input += " readonly";
                            //     }else if (row.propRequired == 'Y' || row.isCustom == 'Y') {
                            //         input += " required";
                            //     }
                            //     let number = ['number','integer'];
                            //     if (number.includes(row.subStructType)) {
                            //         input += " type='number'";
                            //     }else {
                            //         input += " type='text'";
                            //     }
                            //     input += " name='amazonAttributeLines[" + index + "].tableValue' value='" + value + "'>";
                            //     return input;
                            // }
                            let dictValues = row.dictValues;
                            if ($.common.isNotEmpty(value) && row.dictValues.filter(item => item.code === value).length === 0) {
                                dictValues.push({code: value, name: value})
                            }
                            var data = [{index: index, type: value, list: dictValues }];
                            // 拼接 required 和 readonly 属性
                            var tempInput = ''
                            if($.common.equals(row.editableFlag,"N") && noEditFlag && $.common.isNotEmpty(value)) {
                                data[0].readonly = true
                                if ($.common.equals(row.editableFlag,"N") ||  $.common.startWith(row.showPropNodePath,'part_number') ){
                                    tempInput = $.common.sprintf("<input type='hidden' class='form-control' style='width: 400px;' name='amazonAttributeLines[%s].tableValue' value='" + value + "'>", index, value);
                                }
                            }else if (row.propRequired == 'Y' || row.isCustom == 'Y') {
                                data[0].required = true
                            }
                            return $("#goodsInfoTpl").tmpl(data).html() + tempInput
                        } else {
                            var input =  "<input class='form-control' style='width: 400px;' ";
                            if($.common.equals(row.editableFlag,"N") && noEditFlag && $.common.isNotEmpty(value)) {
                                input += " readonly";
                            }else if (row.propRequired == 'Y' || row.isCustom == 'Y') {
                                input += " required";
                            }
                            let number = ['number','integer'];
                            if (number.includes(row.subStructType)) {
                                input += " type='number'";
                            }else {
                                input += " type='text'";
                            }
                            input += " name='amazonAttributeLines[" + index + "].tableValue' value='" + value + "'>";
                            return input;
                        }
                    }
                },
                {
                    align: 'center',
                    title: '操作',
                    width: 100,
                    formatter: function (value, row, index) {
                        if(row.isAppendAttr == 'Y' || row.isCustom == 'Y') {
                            return '<a class="btn btn-danger btn-xs delRow" href="javascript:void(0)" ><i class="fa fa-remove "></i>删除</a>';
                        }
                        let html = '';
                        if (row.parentStructType == 'array' && row.maxUniqueItems > 1 && $.common.equals(row.editableFlag,"Y") && !($.common.startWith(row.showPropNodePath,'part_number'))){
                            html +=  '<a class="btn btn-info btn-xs addRow" href="javascript:void(0)" onclick="addChildRow(' + index + ')"><i class="fa fa-plus"></i>新增</a>';
                        }
                        return html;
                    }
                }
            ]
        };

        $.table.init(options);
        initTableSelect()

    }

    function doValidField() {
        var datas = $("#form-edit-listing").serialize();
        validField(datas, false,false);
    }


    /**
     * 新增子行
     * @param index
     */
    function addChildRow(index) {
        // 找到点击的行
        sub.editRow()
        let datas = $('#attr-info-table').bootstrapTable('getData');

        let currentRow = datas[index];
        // 当前行新增了多少个子行
        let childRows = datas.filter(row => row.propNodePath.startsWith(currentRow.propNodePath));

        // 当前最大的数字
        let max = 0;
        max = Math.max(...childRows.map(row => parseInt(row.showPropNodePath.indexOf("-") > 0 ? row.showPropNodePath.substring(row.showPropNodePath.lastIndexOf('-') + 1) : 0)));
        // 封装新行的数据
        // currentRow
        let newRow = $.extend({}, currentRow);
        newRow.showPropNodePath = currentRow.propNodePath  + '-' +  (max + 1);
        newRow.isCustom = 'Y';
        newRow.propValue= ''
        var lastIndex = -1;
        datas.forEach((item, index) => {
            if (item.propNodePath === currentRow.propNodePath) {
                lastIndex = index;
            }
        });

        if (lastIndex === -1) {
            // 没有子类型，插入到父类型之后
            lastIndex = index + 1
        }
        datas.splice(lastIndex + 1, 0, newRow);
        $('#attr-info-table').bootstrapTable('load', datas);
        initRequireInfo();
    }

    function  initRequireInfo() {
        //页面渲染完成后，遍历所有的select标签
        $(".isRequire").each(function () {
            optinonColor.call(this);
        });
    }


    function removeItem() {
        // 通过元素获取选中的行
        let selected = $('#attr-info-table tbody tr').filter('.selected');
        // 获取数据所有的data-uniqueid属性值
        let selectIds = selected.map((index, element) => $(element).data('uniqueid')).get();

        if (selectIds.length === 0) {
            return;
        }
        sub.editRow();

        $("#attr-info-table").bootstrapTable('remove', { field: 'showPropNodePath', values: selectIds });
        // 移除所有selected
        selected.removeClass('selected');
        // 对数据移除key为0的值
        let datas = $('#attr-info-table').bootstrapTable('getData');
        datas.forEach((item, index) => {
            item[0] = undefined
        });
        $('#attr-info-table').bootstrapTable('load', datas);
    }

    //gpt采集
    function getGPT(buttonId) {
        $.modal.open("数据组适配参考列表", ads_prefix + "/gptList/" + pdmGoodsCode + "/" + buttonId+"/AM", '1000', '800', function (index, layero) {
            $.modal.close(index);
        });
    }

    $("#categoryCode").change(function () {
        categoryId= $('#categoryCode').val();
        getCategoryLabel(categoryId);
        renderAttribute();
    });


    $(document).ready(function() {
        // 检查 listingRateLabel 的值并控制对应的 div
        if (listingEditDTO.listingRateLabel) {
            var listingRateLabelText = "listing退货率: " + $.table.selectDictLabel(refundRateLabelData, listingEditDTO.listingRateLabel);
            $("div[name='listingRateLabel']").show().find('span').html(listingRateLabelText);
        } else {
            $("div[name='listingRateLabel']").hide();
        }

        // 检查 skuRateLabel 的值并控制对应的 div
        if (listingEditDTO.skuRateLabel) {
            var skuRateLabelText = "sku退货率: " + $.table.selectDictLabel(refundRateLabelData, listingEditDTO.skuRateLabel);
            $("div[name='skuRateLabel']").show().find('span').html(skuRateLabelText);
        } else {
            $("div[name='skuRateLabel']").hide();
        }
    });



    function renderAttribute() {
        categoryId= $('#categoryCode').val();
        let shopCode = listingEditDTO.shopCode;
        let listInfoUrl = prefixRequiredField + `/v2/listGoodsHeadAttribute?categoryId=${categoryId}&attributeType=0&platform=${platform}&pdmGoodsCode=${pdmGoodsCode}&goodsId=${goodsId}&shopCode=${shopCode}`;

        let categoryVal = $("#categoryId").val();
        if (categoryVal == null || categoryVal == '') {
            return;
        }
        if (shopCode == null || shopCode == "") {
            return;
        }
        if (goodsCode == null || goodsCode == '') {
            layer.alert("该商品编码为空，PDM数据存在问题，请联系管理员！");
            return;
        }
        let site = $("#siteCode").val();

        var config = {
            url:  listInfoUrl,
            type: 'post',
            dataType: 'json',
            beforeSend: function () {
                $.modal.loading("正在加载中，请稍后...");
            },
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    showAttrData = []
                    notRequiredData = []
                    result.rows.forEach(item => {
                        if (item.propRequired == 'Y' || $.common.isNotEmpty(item.propValue)) {
                            showAttrData.push(item);
                        }
                        if(item.propRequired == 'N') {
                            notRequiredData.push(item);
                        }
                    });

                    attributeInfoTable();

                    // 属性表格渲染完成后，重新触发前台数据显示
                    renderAttributeFrontDataDebounced();
                }
                $.modal.closeLoading();
            }
        };
        $.ajax(config);

    }

    $().ready(function () {
        categoryId= $('#categoryCode').val();
        getCategoryLabel(categoryId);
        renderAttribute()
        
        if(isVc) {
            $(".sc-product").remove();
        } else {
            $(".vc-product").remove();
            document.getElementById('packageLength').removeAttribute('readonly');
            document.getElementById('packageWidth').removeAttribute('readonly');
            document.getElementById('packageHeight').removeAttribute('readonly');
            document.getElementById('packageLengthUnit').removeAttribute('readonly');
        }

        let externalProductId = $('#externalProductId').val();
        let platformGoodsId = $('#platformGoodsId').val();
        // 如果externalProductId有值，而且平台销售编码不为空，不允许编辑
        if (externalProductId && platformGoodsId) {
            $('#externalProductId').attr('readonly', true);

            // externalProductIdType属于select2，调用select2的禁用方法
            $('#externalProductIdType').prop('disabled', true);
        }
        externalChange();
        //自动聚焦校验未通过位置，默认第一个
        $("#form-edit-listing").validate({
            ignore: "",
            focusInvalid: true,
            invalidHandler: function (form, validator) {
                let {errorList} = validator;
                if (errorList && errorList.length) {
                    let element = $(errorList[0].element);
                    // 如果是select2，将焦点定位到真正的select2元素
                    if (element.hasClass('select2-hidden-accessible')) {
                        // $('[name="amazonAttributeLines[0].tableValue"]').select2('focus')
                        element.select2('focus');
                    }else {
                        element.focus();
                    }
                }
            }
        });

        $('#attr-info-table').on('post-body.bs.table', function () {
            // 属性表格渲染完成后，触发前台数据显示
            renderAttributeFrontDataDebounced();

            // append-attr加入监听事件，将枚举值加入到属性值中
            $('.append-attr').on('change', function() {
                // 获取当前下拉框所在的行的父级<tr>
                var $tr = $(this).closest('tr');

                // 获取该<tr>的唯一标识 (uniqueId)，或者获取其当前的行号
                var rowIndex = $tr.index();  // 实时获取当前行的索引（动态索引）

                // 获取下拉框的值
                var selectedValue = $(this).val();

                // 找到当前行所在的索引
                let list = notRequiredData.filter(item => item.propNodePath === selectedValue);
                let selectRow = list[0];
                //
                // // 更新当前行的数据
                let newRow = $.extend({}, selectRow);
                newRow.changeAppendAttr = 'Y'
                newRow.isCustom = 'Y'
                newRow.propValue = ''
                sub.editRow()
                $('#attr-info-table').bootstrapTable('updateRow', {index: rowIndex, row: newRow});
            });

            $('.delRow').on('click', function () {
                var $tr = $(this).closest('tr');
                var rowIndex = $tr.index();  // 实时获取当前行的索引（动态索引）
                table.set('attr-info-table')
                sub.editRow()
                $("#attr-info-table").bootstrapTable('remove', {field: "index", values: [$.table.serialNumber(rowIndex)+'']});
                sub.editRow()
            });
            initTableSelect()

        });

        //箱子个数默认填为1
        $("#numberOfBoxes").val('1');
        // 文本域实时显示文字
        $('.title-warp input').on('input',function(e) {
            let len = $(this).val().length;
            let maxLen = $(this).attr('size') || $(this).attr('maxlength') || 200;
            $(this).next('.count-text').text(`${len}/${maxLen}`)
        })
        $('.title-warp .up-caps-all,.title-warp .up-caps-first').on('click',function() {
            let $textIpt = $(this).parents('.title-warp').find('input');
            let isUp = $(this).data('isUp');
            let text = $textIpt.val() || ''
            let isFirstUp = $(this).hasClass('up-caps-first')
            if(text){
                if(isUp) {
                    text = text.toLowerCase();
                } else {
                    text = isFirstUp ? text.toLowerCase()
                            .split(' ')
                            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                            .join(' ')
                        :text.toUpperCase();

                }
                $textIpt.attr('title',text);
                $textIpt.val(text)
            }
            $(this).data('isUp', !isUp);
        })
        $('.title-warp').each(function() {
            let $input = $(this).find('input');
            let textLen = $input.val().length || 0;
            let textMaxLen = $input.attr('maxlength') || 200;
            $(this).find('.count-text').text(`${textLen}/${textMaxLen}`)
        });


        // 处理图片信息
        // 现有关联图片
        var listingEditDTO = [[${listingEditDTO}]];
        // 商品套图列表
        let goodsImages = [[${goodsImages}]];
        let masterImg = listingEditDTO.goodsResourceList;
        var goodsImageList = listingEditDTO.imgDataArrList;

        if (masterImg.length != 0) {
            let allImage = masterImg.filter(item => { return item.isMain == '1'})
            selectImages(goodsImages,allImage);
        }
        selectImages(goodsImages,goodsImageList);

        mouseOverImages();
        // 渲染富文本
        $(".editor").each(function () {
            initRequiredEditor(this.id);
        });

        Fancybox.bind('[data-fancybox]', {});

        imageLoading("scale-img");

        // 初始化前台数据显示
        initFrontDataDisplay();

        layui.use('laydate', function() {
            var laydate = layui.laydate;

            var startDate = laydate.render({
                elem: '#laydate-startTime',
                // max: $('#laydate-endTime').val(),
                theme: 'molv',
                trigger: 'click',
                done: function (value, date) {
                    // 结束时间大于开始时间
                    if (value !== '') {
                        endDate.config.min.year = date.year;
                        endDate.config.min.month = date.month - 1;
                        endDate.config.min.date = date.date;
                    } else {
                        endDate.config.min.year = '';
                        endDate.config.min.month = '';
                        endDate.config.min.date = '';
                    }
                }
            });

            var endDate = laydate.render({
                elem: '#laydate-endTime',
                // min: $('#laydate-startTime').val(),
                theme: 'molv',
                trigger: 'click',
                done: function (value, date) {
                    // 开始时间小于结束时间
                    if (value !== '') {
                        startDate.config.max.year = date.year;
                        startDate.config.max.month = date.month - 1;
                        startDate.config.max.date = date.date;
                    } else {
                        startDate.config.max.year = '';
                        startDate.config.max.month = '';
                        startDate.config.max.date = '';
                    }
                }
            });
        });
    });
    function deleteRow(tableId,value){
        table.set(tableId)
        sub.editRow()
        $("#" + tableId).bootstrapTable('remove', { field: "index", values: [value] });
        sub.editRow()
    }



    function addTable() {
        let categoryVal = $("#categoryId").val();
        if (categoryVal == null || categoryVal == '') {
            return;
        }
        let shopCode = $("#shopCode").val();
        if (shopCode == null || shopCode == "") {
            return;
        }
        if (goodsCode == null || goodsCode == '') {
            layer.alert("该商品编码为空，PDM数据存在问题，请联系管理员！");
            return;
        }
        let site = $("#siteCode").val();
        var options = {
            title: '新增属性',
            url: ctx + "configuration/category/attribute/json/" + site + "/" + categoryVal + "/" + ($.common.isEmpty(shopCode) ? '0' : shopCode + "/chooseAttr"),
            callBack: chooseAttr
        };
        $.modal.openOptions(options);
    }

    function chooseAttr(index, layero){
        var rows = layero.find("iframe")[0].contentWindow.getSelections();
        if(!rows) {
            return;
        }

        if ($.common.isEmpty(rows) || rows.length == 0) {
            $.modal.close(index);
            return;
        }
        sub.editRow()
        var datas = $('#attr-info-table').bootstrapTable('getData');
        // 加入到表格中
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            var newRow = $.extend({}, row);
            newRow.isCustom = 'Y';
            datas.push(newRow);
        }
        $('#attr-info-table').bootstrapTable('load', datas);
        $.modal.close(index);
        document.getElementById('attr-info-table').scrollIntoView(false)
    }



    function checkForm() {

        $('.table-select2').each(function() {
            if (!$(this).val()) {
                $(this).parent().find('.select2-container').find('.select2-selection').addClass('invalid');
            } else {
                $(this).parent().find('.select2-container').find('.select2-selection').removeClass('invalid');
            }
        });

        return $.validate.form();
    }


    function selectListing() {
        let categoryVal = $("#categoryId").val();
        if (categoryVal == null || categoryVal == '') {
            return;
        }
        let shopCode = $("#shopCode").val();
        if (shopCode == null || shopCode == "") {
            return;
        }
        if (goodsCode == null || goodsCode == '') {
            layer.alert("该商品编码为空，PDM数据存在问题，请联系管理员！");
            return;
        }
        let site = $("#siteCode").val();
        var url = listing_prefix + "/chooseListing" + "/" + site + "/" + categoryVal + "/" + ($.common.isEmpty(shopCode) ? '0' : shopCode);

        var options = {
            title: '选择链接',
            url: url,
            callBack: chooseListing
        };
        $.modal.openOptions(options);
    }

    function chooseListing(index, layero) {
        var rows = layero.find("iframe")[0].contentWindow.getSelections();
        // 只能选择一个
        if (rows.length > 1) {
            $.modal.alertWarning("只能选择一个链接");
            return;
        }
        if ($.common.isEmpty(rows) || rows.length == 0) {
            $.modal.close(index);
            return;
        }

        getTargetListingAttr(rows.id, rows.publishStatus == 0|| rows.publishStatus == 8);
        $.modal.close(index);
    }

    function getTargetListingAttr(goodsId, noEditFlag) {
        let  platformCategoryId = $('#categoryId').val();
        let listInfoUrl = ctx + "publication/goods/attribute" + `/v2/listGoodsHeadAttribute?categoryId=${platformCategoryId}&goodsId=${goodsId}`;
        var config = {
            url:  listInfoUrl,
            type: 'post',
            dataType: 'json',
            beforeSend: function() {
                $.modal.loading("正在加载中，请稍后...");
            },
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    showAttrData = []
                    notRequiredData = []
                    result.rows.forEach(item => {
                        if (item.propRequired == 'Y' || $.common.isNotEmpty(item.propValue)) {
                            showAttrData.push(item);
                        }
                        if(item.propRequired == 'N') {
                            notRequiredData.push(item);
                        }
                    });

                    attributeInfoTable();

                    // 属性表格渲染完成后，重新触发前台数据显示
                    renderAttributeFrontDataDebounced();

                }
                $.modal.closeLoading();
            }
        };
        $.ajax(config);

    }

    var listing_prefix = ctx + "publication/listing";

    /**
     * 价格计算
     * @param goodsCode
     */
    function watchPriceCount(goodsCode) {
        var url = "/publication/task/watchPriceCount/AMAZON/" + goodsCode;
        $.modal.openNoYes("价格计算器", url,'1300', '800');
    }
    /**
     * 适配保有量
     * @param goodsCode
     */
    function watchFitmentVIO(goodsCode) {
        var url = "/ads/watchVIO/" + goodsCode;
        $.modal.open(goodsCode+"-适配保有量", url,'1100', '950');
    }
    //竞品采集按钮
    function getCompetingProducts() {
        $.modal.open("添加属性", listing_prefix + "/getCompetingProducts/amazon/"+$("#goodsCode").val(), '1000', '800', callBack);
    }

    //竞品采集回调函数
    function callBack(index, layero) {
        var option = layero.find("iframe")[0].contentWindow.submitHandlerx();
        let val = option.val;
        if (val.length != 0) {
            $.modal.close(index);
        } else {
            return $.modal.alertError("请选择需要替换的内容！");
        }
        //采集对象字段
        val.forEach((value, index) => {
            if ("title" == value) {
                $("#title").val(option.competitorData.title);
            } else if ("description" == value) {
                $('#detailDescription').val(option.competitorData.description);
                window.detailDescriptionEditor.setData(option.competitorData.description);
            } else if ("price" == value) {
                $("#price").val(option.competitorData.price);
            }  else if("pointDescription" == value) {
                $("#itemDescription1").val(option.competitorData.point1);
                $("#itemDescription2").val(option.competitorData.point2);
                $("#itemDescription3").val(option.competitorData.point3);
                $("#itemDescription4").val(option.competitorData.point4);
                $("#itemDescription5").val(option.competitorData.point5);
            }
        });
    }

    // 提交表单数据
    function submitHandler(index) {
        if (!checkForm()) {
            return;
        }
        if(isVc) {
            let price = $("#price").val();
            let listPrice = $("#listPrice").val();
            if (price && listPrice && parseFloat(price) > parseFloat(listPrice)) {
                $.modal.alertWarning("Cost Price 不能大于 List Price");
                return false;
            }
        }
        let publishStatus = $("#publishStatus").val();
        if ( publishStatus=="1" || publishStatus=="3" || publishStatus=="5" ){
            layer.alert("该商品正在发布中/更新中/下架中，不能修改.");
            return false;
        }
        if ( publishStatus=="2" ){
            $.modal.confirm("该listing将同步更新至平台，是否确认更新！", function() {
                submit();
            });
        }else{
            submit();
        }
        layer.close(index);

    }

    function submit() {
        if (!checkForm()) {
            return;
        }
        //监控长度
        if(!monitorLengthInput())return;


        //获取图片数据
        setImagesToDataArr();
        $("#imgDataArr").val(JSON.stringify(this.dataArr));
        $("#masterImgFile").val(JSON.stringify(window.masterImgFile));
        let desc = window.detailDescriptionEditor.getData();
        if ($.common.isEmpty(desc)) {
            $.modal.alertWarning("请填写产品描述信息");
            return;
        }
        $("input[name='detailDescription']").val(desc);

        $.ajax({
            url: listing_prefix + "/checkDuplicateAsin",
            type: "post",
            contentType: "application/json",
            async: false,
            data: JSON.stringify([goodsId]),
            success: function (result) {
                if (result.code != 0) {
                    $.modal.alertError(result.msg);
                    return;
                }
                if ($.common.isNotEmpty(result.data)) {
                    // 找到了相同ASIN的其他链接
                    let otherListings = result.data;
                    let listingInfo = "";
                    for (let i = 0; i < otherListings.length && i < 5; i++) {
                        listingInfo += "<p>" + (i + 1) + ". 主键ID：" + otherListings[i].id +
                            "，平台商品编码：" + (otherListings[i].platformGoodsCode || '') + "</p>";
                    }

                    if (otherListings.length > 5) {
                        listingInfo += "<p>...等共 " + otherListings.length + " 个链接</p>";
                    }

                    // 弹窗提示是否同步字段
                    layer.confirm("发现同ASIN的跟卖链接，是否要将标题、五点描述和详情描述同步到这些链接中？<br><br>" + listingInfo, {
                        icon: 3,
                        title: "ASIN重复提示",
                        btn: ['是', '否']
                    }, function (index) {
                        // 用户选择"是"，提交表单时需要标记同步属性
                        layer.close(index);
                        var data = $("#form-edit-listing").serialize();
                        let val = $("#externalProductIdType").val();
                        if ($.common.isNotEmpty(val) && $.common.equals(val, 'ASIN')) {
                            data = data + "&externalProductIdType=" + $("#externalProductIdType").val();
                        }
                        // 添加同步标记和需要同步的字段
                        data = data + "&syncOtherListings=true&otherListingIds=" + otherListings.map(item => item.id).join(',');
                        data = data + "&syncFields=title,itemDescription,detailDescription,pictures";
                        validField(data, false, true);
                    }, function (index) {
                        // 用户选择"否"，正常提交表单
                        layer.close(index);
                        var data = $("#form-edit-listing").serialize();
                        let val = $("#externalProductIdType").val();
                        if ($.common.isNotEmpty(val) && $.common.equals(val, 'ASIN')) {
                            data = data + "&externalProductIdType=" + $("#externalProductIdType").val();
                        }
                        validField(data, false, true);
                    });
                } else {
                    // 检查出错，仍然正常提交
                    var data = $("#form-edit-listing").serialize();
                    let val = $("#externalProductIdType").val();
                    if ($.common.isNotEmpty(val) && $.common.equals(val, 'ASIN')) {
                        data = data + "&externalProductIdType=" + $("#externalProductIdType").val();
                    }
                    validField(data, false, true);
                }
            },
            error: function () {
                // 检查出错，仍然正常提交
                var data = $("#form-edit-listing").serialize();
                let val = $("#externalProductIdType").val();
                if ($.common.isNotEmpty(val) && $.common.equals(val, 'ASIN')) {
                    data = data + "&externalProductIdType=" + $("#externalProductIdType").val();
                }
                validField(data, false, true);
            }
        });
    }


    function validField(data, init = false, submit = false) {
        if (!checkForm()) {
            return;
        }
        if($.common.isEmpty(data)) {
            return;
        }
        // valid JSON
        let validJson = {
            url: listing_prefix + "/validateJson",
            type: "post",
            dataType: "json",
            data: data,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍候...");
            },
            success: function (e) {
                if (e.code === 0) {
                    let res = e.data
                    if($.common.isEmpty(res)) {
                        // 提交保存接口
                        if(!init  && submit) {
                            saveData(data);
                        }else{
                            $.modal.closeLoading();
                        }
                        return;
                    }

                    let empty = []
                    let enumErrorSet = []
                    let countErrorSet = []
                    e.data.forEach(item => {
                        if(item.type == 'part-required' || item.type == 'required') {
                            notRequiredData.filter(data => data.propNodePath.includes(item.propNodePath)).forEach(data => {
                                empty.push(data);
                            });
                        }

                        //
                        if (item.type == 'enumError') {
                            let tableData = $('#attr-info-table').bootstrapTable('getData');
                            tableData.filter(data => data.propNodePath == item.propNodePath).forEach(data => {
                                // 如果已经刊登成功，platformGoodsId不为空，且propNodePath为item_type_keyword，则不提示
                                if(item.propNodePath == 'item_type_keyword.value') {
                                    return;
                                }
                                enumErrorSet.push('属性:' + item.propNodePath + "，枚举值范围：" + item.recommendedValue);
                            });
                        }else if (item.type == 'countError') {
                            countErrorSet.push('需提供'+ item.minItems +"组" +  item.propNodePath + "(当前已提供" + item.nowItems + "个)");
                        }
                    })

                    if(empty.length === 0 && enumErrorSet.length === 0 && countErrorSet.length === 0  && !init) {
                        if(submit) {
                            saveData(data);
                        }else {
                            // 校验通过
                            $.modal.alertSuccess("校验通过");
                            $.modal.closeLoading();
                        }

                        return;
                    }
                    let errorInfo = Array.from(empty).map(item => item.showPropNodePath).join(', \n');
                    var errmsg = ""
                    if (empty.length > 0) {
                        errmsg += "以下字段为必填字段： \n" + errorInfo +"<br>";
                    }
                    if (enumErrorSet.length > 0) {
                        errmsg += "\n以下字段值不正确： \n" + enumErrorSet.join(', \n') +"<br>";
                    }
                    if (countErrorSet.length > 0) {
                        errmsg += "\n以下字段个数不正确： \n" + countErrorSet.join(', \n');
                    }
                    if(!init) {
                        let btn = ['确认并添加', '关闭']
                        if ($.common.isEmpty(empty)) {
                            btn = ['确认', '取消']
                        }
                        top.layer.confirm(errmsg, {
                            icon: 3,
                            title: "系统提示",
                            btn: btn
                        }, function (index) {
                            $.modal.close(index);
                            // 将提示的数据加入到table中
                            empty.forEach(item => {
                                sub.editRow();
                                let data = $('#attr-info-table').bootstrapTable('getData');
                                item.propValue = '';
                                item.isCustom = 'Y';
                                data.push(item);
                                $('#attr-info-table').bootstrapTable('load', data);
                            });
                        });
                    }else {
                        // 将提示的数据加入到table中
                        empty.forEach(item => {
                            sub.editRow();
                            let data = $('#attr-info-table').bootstrapTable('getData');
                            item.propValue = '';
                            item.isCustom = 'Y';
                            data.push(item);
                            $('#attr-info-table').bootstrapTable('load', data);
                        });
                    }

                    $.modal.closeLoading();

                } else {
                    $.modal.alertError(e.msg);
                    $.modal.closeLoading();
                }
            }
        }
        $.ajax(validJson);
    }


    function saveData(data) {
        // 提交保存接口
        //获取goodsInfoTab
        var listingPrefix = ctx + "publication/listing";
        $.operate.save(listingPrefix + "/v2/edit",data, function(result) {
            if (result.code === 0) {
                // 关闭当前窗口
                window.close();
            }
        });
    }

    // 平台商品编码改变后
    $("#externalProductIdType").change(function () {
        $("#externalProductId").val("")
        externalChange();

    });
    function externalChange() {
        let external= $("#externalProductIdType").val()
        if (external==null){
            return
        }
        if (external=='GCID'){
            $(".externalProductId").hide()
        }else {
            $(".externalProductId").show()
        }
    }
    $("[data-toggle='tooltip']").tooltip();
    // 渲染商品详情描述
    $("#detailDescription").html(listingEditDTO.detailDescription);

    //折叠页签的功能
    $(".modal").appendTo("body"), $("[data-toggle=popover]").popover(), $(".collapse-link").click(function () {
        var div_ibox = $(this).closest("div.ibox"),
            e = $(this).find("i"),
            i = div_ibox.find("div.ibox-content");
        i.slideToggle(200),
            e.toggleClass("fa-chevron-up").toggleClass("fa-chevron-down"),
            div_ibox.toggleClass("").toggleClass("border-bottom"),
            setTimeout(function () {
                div_ibox.resize();
            }, 50);
    }), $(".close-link").click(function () {
        var div_ibox = $(this).closest("div.ibox");
        div_ibox.remove()
    });
    /**
     * 监听输入框
     */
    function monitorLengthInput() {
        // 获取所有带有 class="maxlegthflag" 的input输入框跟textarea输入框
        var textareas = document.querySelectorAll('.maxlegthflag');
        // 遍历每个输入框 判断输入字符数是否超过了指定的最大字符数,若超过，则给输入框添加红色边框 跳到第一个输入框，禁止表单提交
        for (let i = 0; i < textareas.length-1; i++) {
            let item = textareas[i];
            let  reFlag =item.getAttribute('reFlag');
            let  size =item.getAttribute('size');
            if (reFlag == 'true'){
                if (textareas[i].value.length==0 || textareas[i].value.length >parseInt(size))  {
                    textareas[i].style.borderColor = 'red';
                    textareas[i].focus();
                    // 禁止表单提交
                    return false;
                }
            }else {
                if (textareas[i].value.length >parseInt(size))  {
                    textareas[i].style.borderColor = 'red';
                    textareas[i].focus();
                    // 禁止表单提交
                    return false;
                }
            }
        }

        // sale price 和sale date 两个只填了1个 跳到第一个输入框，禁止表单提交
        let salePrice = $("input[name='salePrice']").val();
        let saleBeginDate = $("input[name='saleBeginDate']").val();
        let saleEndDate = $("input[name='saleEndDate']").val();
        //如果这三个中有一个为空，则跳到为空的输入框 禁止表单提交
        if ($.common.isNotEmpty(salePrice) || $.common.isNotEmpty(saleBeginDate) || $.common.isNotEmpty(saleEndDate)){
            if ($.common.isEmpty(salePrice)) {
                $("input[name='salePrice']").focus();
                $("input[name='salePrice']").css("border-color", "red");
                $("input[name='salePrice']").change(function () {
                    $("input[name='salePrice']").css("border-color", "");
                });
                return false;
            }
            if ($.common.isEmpty(saleBeginDate)) {
                $("input[name='saleBeginDate']").focus();
                $("input[name='saleBeginDate']").css("border-color", "red");
                $("input[name='saleBeginDate']").change(function () {
                    $("input[name='saleBeginDate']").css("border-color", "");
                });
                return false;
            }
            if ($.common.isEmpty(saleEndDate)) {
                $("input[name='saleEndDate']").focus();
                $("input[name='saleEndDate']").css("border-color", "red");
                $("input[name='saleEndDate']").change(function () {
                    $("input[name='saleEndDate']").css("border-color", "");
                });
                return false;
            }
        }
        return  true;
    }
    //
    //
    // document.addEventListener('DOMContentLoaded', function() {
    //     var externalProductIdType = document.getElementById('externalProductIdType');
    //     var externalProductIdInput = document.getElementsByName('externalProductId')[0];
    //
    //     // 初始加载时根据选择框的值设置输入框和选择框的只读状态
    //     toggleReadOnly();
    //
    //     // 监听选择框的变化事件
    //     externalProductIdType.addEventListener('change', function() {
    //         toggleReadOnly();
    //     });
    //
    //     // 函数：根据选择框的值设置输入框和选择框的只读状态
    //     function toggleReadOnly() {
    //         var selectedValue = externalProductIdType.value;
    //         if (selectedValue === 'ASIN') {
    //             externalProductIdType.disabled = true; // 禁用选择框
    //             externalProductIdInput.readOnly = true; // 设置输入框只读
    //         } else {
    //             externalProductIdType.disabled = false; // 启用选择框
    //             externalProductIdInput.readOnly = false; // 设置输入框可编辑
    //         }
    //     }
    // });

</script>
<script th:src="@{/js/jquery.tmpl.js}"></script>
<script id="goodsInfoTpl" type="text/x-jquery-tmpl">
    <div>
       <select name="amazonAttributeLines[${index}].tableValue" class="form-control table-select2" style="width: 400px;"  {{if required}}required {{else readonly}} disabled  {{/if}}>
        <option value="">请选择</option>
         {{each(rowIndex,data) list}}
             <option class='form-control' style='width: 400px;' value="${data.code}"  {{if type===data.code}}selected{{/if}} >${data.name}(${data.code})</option>
         {{/each}}
    </select>
    </div>
</script>
<script id="appendAttrTpl" type="text/x-jquery-tmpl">
    <div>
    <select name="amazonAttributeLines[${index}].tableValue"  class="form-control append-attr table-select2"  >
        <option value="" selected>请选择</option>
         {{each(rowIndex,data) list}}
             <option value="${data.showPropNodePath}" data-type="${data.fieldName}" {{if type===data.showPropNodePath}}selected{{/if}}>${data.showPropNodePath}</option>
         {{/each}}
    </select>
    </div>
</script>
</html>
