package com.suncent.smc.quartz.task.listing.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 标签处理器工厂
 * 管理所有标签处理器的注册和获取
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
@Slf4j
public class LabelProcessorFactory {

    private final Map<String, LabelProcessor> processors = new HashMap<>();
    private final List<LabelProcessor> sortedProcessors = new ArrayList<>();

    @Autowired
    public LabelProcessorFactory(List<LabelProcessor> processorList) {
        // 注册所有处理器
        for (LabelProcessor processor : processorList) {
            processors.put(processor.getLabelType(), processor);
            log.info("注册标签处理器: {} - {}", processor.getLabelType(), processor.getDescription());
        }

        // 按优先级排序
        sortedProcessors.addAll(processors.values());
        sortedProcessors.sort(Comparator.comparingInt(LabelProcessor::getPriority));

        log.info("标签处理器工厂初始化完成，共注册 {} 个处理器", processors.size());
    }

    /**
     * 根据标签类型获取处理器
     *
     * @param labelType 标签类型
     * @return 标签处理器
     * @throws IllegalArgumentException 如果标签类型不支持
     */
    public LabelProcessor getProcessor(String labelType) {
        LabelProcessor processor = processors.get(labelType);
        if (processor == null) {
            throw new IllegalArgumentException("不支持的标签类型: " + labelType +
                    ", 支持的类型: " + getSupportedLabelTypes());
        }
        return processor;
    }

    /**
     * 获取所有支持的标签类型
     *
     * @return 标签类型集合
     */
    public Set<String> getSupportedLabelTypes() {
        return processors.keySet();
    }

    /**
     * 获取所有启用的处理器（按优先级排序）
     *
     * @return 处理器列表
     */
    public List<LabelProcessor> getAllProcessors() {
        return new ArrayList<>(sortedProcessors);
    }


    /**
     * 检查是否支持指定的标签类型
     *
     * @param labelType 标签类型
     * @return true-支持，false-不支持
     */
    public boolean isSupported(String labelType) {
        return processors.containsKey(labelType);
    }

    /**
     * 获取处理器统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalProcessors", processors.size());
        stats.put("supportedTypes", getSupportedLabelTypes());
        return stats;
    }
}
