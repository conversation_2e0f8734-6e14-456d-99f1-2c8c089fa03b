package com.suncent.smc.persistence.ads.service;

import com.suncent.smc.persistence.ads.domain.*;

import java.util.List;
import java.util.Map;

public interface IAdsService {

    /**
     * ASIN数据插入ADS数据库,等待上传适配
     * @param itDemand
     * @return
     */
    Integer saveToAdsItDemandA(ItDemand itDemand);

    /**
     * ASIN数据插入ADS数据库,等待适配重新监控
     * @param itDemand
     * @return
     */
    Integer saveToAdsItDemandCompare(ItDemand itDemand);

    Integer countByPnAndAsin(  String asin);


    Integer countByPnAndAsinCompare( String asin);
    /**
     * 根据ASIN查询适配数据上传状态
     * @param asin
     * @return
     */
    List<Map<String, String>> getStatusByAsin(List<String> asin);


    /**
     * 查询需要重新上传适配的数据
     * @param currentNumber
     * @return
     */
    List<Map<String, Object>> selectAdapterErrorList(int currentNumber);


    /**
     * 新增适配需求 通过copy id的方式  只需要重新指定新的pn
     * @param partNumber
     * @param itDemandId
     * @return
     */
    Integer insertItDemandByCopyId(String partNumber, String itDemandId);

    List<Map<String, String>> selectByStatus(List<String> status, String createTime);
    /**
     * 根据ASIN更新适配数据上传状态
     * @param asinList
     * @return
     */
    int updateItDemandStatusByAsin(List<String> asinList,String status,String errorReason);


    int updateItDemandStatusById(String id,String status,String errorReason);

    /**
     * 根据商品sku查询ebay适配数据
     */
    List<AdsFitmentDataEbay> selectFitmentDataEbayByProductCode(String productCode,String goodsCode);

    /**
     * 更新ebay适配需求表上传状态
     * @param productCodeList
     * @return
     */
    Integer updateEbayFitmentIsUpload(List<String> productCodeList);

    List<AdsEbayDataRequirement> selectEbayDataRequirementByProductCode(String productCode, String goodsCode);
    /**
     * 根据商品产品编码 查询商品保有量数据
     */
    List<AdsFitmentDataVIO> selectFitmentDataVIO (List<String> productCodes);

    /**
     * 根据商品产品编码 查询ads gpt数据
     * @param productCodes
     * @return
     */
    List<AdsFitmentGPTData> selectFitmentGPTData (List<String> productCodes);

    /**
     * 把适配保有量数据转换成导出的格式
     * @param adsEbayDataRequirementList
     * @return
     */
    AdsFitmentCombinationDTO handleAdsDataForExport(List<AdsFitmentDataVIO> adsEbayDataRequirementList,List<String> groupParamList);

    String getDataMaxBatch();

    List<Map<String, String>> getRedLinePriceList(String dataMaxBatch,List<String> goodsCodeList,String platform);

    List<String> getGoodsCodeList(String dataMaxBatch);

    Map<String, Map<String, Object>> getAdaptInfoMap(String productCode, List<String> paramNames,Map<String, Object> globalContext);

    List<AdsListingLabel>getListingLabelList(String shopCode);

    /**
     * 分页查询ADS标签数据
     *
     * @param shopCode 店铺编码
     * @param offset   偏移量
     * @param limit    限制数量
     * @return ADS标签列表
     */
    List<AdsListingLabel> getListingLabelListPaged(String shopCode, int offset, int limit);

    AdsListingLabel getListingLabelByAsin(String shopCode,String asin);

    Map<String, List<Map<String, Object>>> countByProductCodes(List<String> productCodes);

    List<AdsRecordData> selectTodayCartList(String date,String shopCode,List<String> asinList);


    List<Object> handleAdsDataForExportV2(List<AdsFitmentDataVIO> adsList, List<String> columns, String splitStr);

    //获取总数  核心链接
    int countNodeIncorrectByCoreListing(String nowDate);
 

    //获取需要生成待办的数据  在核心链接内
    List<AmzJudgeCategoryInfoVO> listNodeIncorrectByCoreListing(String nowDate);

    void updateCaseIdByNodeCorrect(String platformGoodsId, String platformGoodsCode, String shopCode, String siteCode, String caseId);

    void insertAmzJudgeCategoryMap(AmzJudgeCategoryMapVO amzJudgeCategoryMapVO);

    int countAmzJudgeCategoryMap(String operationClassification, String categoryCode, String newPlatformCategoryId, String newCategoryName);

    void updateAmzJudgeCategoryInfoStatus(String platformGoodsId, String platformGoodsCode, String shopCode, String siteCode);


    /**
     * 查询适配需求列表
     * @return
     */
    List<FitmentDemand> selectFitmentDemandList();

    /**
     * 按类型分页查询适配需求列表
     *
     * @param type   数据类型：1-SKU+ASIN+PN, 2-SKU+ASIN, 3-仅SKU
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 适配需求列表
     */
    List<FitmentDemand> selectFitmentDemandListByType(int type, int offset, int limit);

    /**
     * 批量查询ASIN对应的处理记录数量
     *
     * @param asinList ASIN列表
     * @return ASIN和对应数量的映射
     */
    Map<String, Integer> countByPnAndAsinBatch(List<String> asinList);

    /**
     * 更新适配需求状态
     */
    void updateFitmentDemandStatus(String status,Long id);


    /**
     * 根据asin查询适配结果数据
     * @param asins
     * @return
     */
//    List<FitExamineDay> getFitExamineDayListByAsins(List<String> asins);

    List<Map<String, String>> listDemandByAsins(List<String> notExistsAsin);

    List<AmazonListingChangeMonitorVO> listAmazonListingChangeMonitor(List<String> pdmGoodsCodeList, List<String> asinList, String date);

    List<AdsFitmentDataBi> getByProduct(String productCode);

    List<String> getDataUpdateLinks();

    List<AdsFitmentDataEbayItem> selectFitmentDataEbayItemByProductCode(String productCode);

    List<AdsAmazonListingDataVIO> selectAdsItemFitCompareListBySkuList(List<String> skuList);

    List<AdsAmazonListingDataVIO> selectAdsItemFitCompareListByAsinList(List<String> asinList);


    Integer countCartListByDate(String date, String shopCode, List<String> asinList);

    /**
     * 根据产品编码列表查询ads_fitmentdata_oe表数据
     *
     * @param productCodes 产品编码列表
     * @return OE数据列表
     */
    List<AdsFitmentDataOe> selectFitmentDataOeList(List<String> productCodes);
}