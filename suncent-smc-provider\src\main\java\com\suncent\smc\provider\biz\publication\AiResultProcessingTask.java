package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.suncent.smc.persistence.publication.domain.entity.AiGenerationTask;
import com.suncent.smc.persistence.publication.domain.entity.GoodsTask;
import com.suncent.smc.persistence.publication.service.IAiGenerationTaskService;
import com.suncent.smc.persistence.publication.service.IGoodsTaskService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI结果处理定时任务
 * 扫描已完成但未处理的AI任务，将结果同步到商品标题和描述
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Component
public class AiResultProcessingTask {

    @Autowired
    private IAiGenerationTaskService aiGenerationTaskService;

    @Autowired
    private AiResultSyncBiz aiResultSyncBiz;

    @Autowired
    private AiGenerationTaskBiz aiGenerationTaskBiz;

    /**
     * 定时扫描已完成的AI任务并处理
     * 每30秒执行一次
     */
    @XxlJob("processCompletedAiTasks")
    public void processCompletedAiTasks() {
        try {
            // 查询已完成但未处理的标题任务
            List<AiGenerationTask> taskList = getTaskByStatus(Collections.singletonList(AiGenerationTask.TaskStatus.COMPLETED.getCode()));

            // 过滤出有AI结果的任务
            taskList = taskList.stream()
                    .filter(task -> task.getAiResult() != null && !task.getAiResult().trim().isEmpty() && task.getProcessStatus().equals(0))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(taskList)){
                return; // 没有待处理的任务
            }

            log.info("发现待处理的AI任务，任务: {}", taskList.size());

            // 通过taskId分组
            Map<Integer, List<AiGenerationTask>> taskMap = taskList.stream().collect(Collectors.groupingBy(AiGenerationTask::getTaskId));
            // 处理标题任务
            for (Map.Entry<Integer, List<AiGenerationTask>> entry : taskMap.entrySet()) {
                aiResultSyncBiz.processTasks(entry.getKey(), entry.getValue());
            }
        } catch (Exception e) {
            log.error("定时处理AI任务失败", e);
        }
    }

    /**
     * 定时扫描待处理的任务
     * 每30秒执行一次
     */
    @XxlJob("processAiTasks")
    public void processAiTasks() {
        try {
            // 查询已完成但未处理、失败的任务
            List<AiGenerationTask> allTasks = getTaskByStatus(Arrays.asList(AiGenerationTask.TaskStatus.PENDING.getCode(), AiGenerationTask.TaskStatus.FAILED.getCode()));

            if (CollUtil.isEmpty(allTasks)) {
                return; // 没有待处理的任务
            }

            List<AiGenerationTask> titleTasks = allTasks.stream().filter(e -> e.getTaskType().equals(AiGenerationTask.TaskType.TITLE.getCode())).collect(Collectors.toList());
            List<AiGenerationTask> descTasks = allTasks.stream().filter(e -> e.getTaskType().equals(AiGenerationTask.TaskType.DESCRIPTION.getCode())).collect(Collectors.toList());

            log.info("发现待处理的AI任务，标题任务: {}, 描述任务: {}",titleTasks.size(), descTasks.size());

            // 处理标题任务
            for (AiGenerationTask task : titleTasks) {
                try{
                    aiGenerationTaskBiz.submitAiGenerationTask(task);
                }catch (Exception e){
                    log.error("提交标题任务失败，taskCorrelationId: {}", task.getTaskCorrelationId(), e);
                }
            }

            // 处理描述任务
            for (AiGenerationTask task : descTasks) {
                try{
                    aiGenerationTaskBiz.submitAiGenerationTask(task);
                }catch (Exception e){
                    log.error("提交描述任务失败，taskCorrelationId: {}", task.getTaskCorrelationId(), e);
                }
            }

        } catch (Exception e) {
            log.error("定时处理AI任务失败", e);
        }
    }


    /**
     * 获取已完成但未处理的任务
     *
     * @return 任务列表
     */
    private List<AiGenerationTask> getTaskByStatus(List<String> statusList) {
        AiGenerationTask queryTask = new AiGenerationTask();
        queryTask.setTaskStatusList(statusList);
        queryTask.setProcessStatus(0); // 未处理状态

        PageHelper.startPage(1, 100);

        // 过滤出有AI结果的任务
        return aiGenerationTaskService.selectAiGenerationTaskList(queryTask);
    }

}
