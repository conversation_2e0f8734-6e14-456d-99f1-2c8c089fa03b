package com.suncent.smc.quartz.task.listing.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import com.suncent.smc.persistence.publication.service.IListingLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签同步服务
 * 提供统一的标签同步处理逻辑
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class LabelSyncService {

    @Autowired
    private LabelProcessorFactory processorFactory;

    @Autowired
    private IListingLabelService listingLabelService;

    /**
     * 同步指定店铺的所有标签类型
     *
     * @param shopCode 店铺编码
     */
    public void syncAllLabels(String shopCode) {
        log.info("开始同步店铺 {} 的所有标签", shopCode);
        long startTime = System.currentTimeMillis();

        List<LabelProcessor> processors = processorFactory.getAllProcessors();
        Map<String, Long> processingTimes = new HashMap<>();

        for (LabelProcessor processor : processors) {
            try {
                long processorStartTime = System.currentTimeMillis();
                syncLabels(shopCode, processor.getLabelType());
                long processorEndTime = System.currentTimeMillis();

                long processingTime = processorEndTime - processorStartTime;
                processingTimes.put(processor.getLabelType(), processingTime);

                log.info("店铺 {} 的 {} 标签同步完成，耗时: {}ms",
                        shopCode, processor.getLabelType(), processingTime);

            } catch (Exception e) {
                log.error("店铺 {} 的 {} 标签同步失败", shopCode, processor.getLabelType(), e);
            }
        }

        long totalTime = System.currentTimeMillis() - startTime;
        log.info("店铺 {} 的所有标签同步完成，总耗时: {}ms，详细耗时: {}",
                shopCode, totalTime, processingTimes);
    }

    /**
     * 同步指定类型的标签
     *
     * @param shopCode  店铺编码
     * @param labelType 标签类型
     */
    public void syncLabels(String shopCode, String labelType) {
        if (StrUtil.isBlank(shopCode) || StrUtil.isBlank(labelType)) {
            log.warn("店铺编码或标签类型为空，跳过同步: shopCode={}, labelType={}", shopCode, labelType);
            return;
        }

        LabelProcessor processor = processorFactory.getProcessor(labelType);

        // 简化为统一的分批处理方式
        syncLabelsBatched(shopCode, processor);
    }

    /**
     * 分批同步标签（改进版本 - 真正的分批删除重建）
     *
     * @param shopCode  店铺编码
     * @param processor 标签处理器
     */
    private void syncLabelsBatched(String shopCode, LabelProcessor processor) {
        log.info("开始分批同步店铺 {} 的 {} 标签", shopCode, processor.getLabelType());

        int offset = 0;
        int batchSize = processor.getBatchSize();
        int totalProcessed = 0;

        try {
            // 分批查询并立即处理每批数据
            while (true) {
                List<? extends Object> sourceData = processor.querySourceData(shopCode, offset, batchSize);

                if (CollUtil.isEmpty(sourceData)) {
                    log.debug("店铺 {} 的 {} 标签数据查询完成，offset: {}",
                            shopCode, processor.getLabelType(), offset);
                    break;
                }

                // 处理为标签
                List<ListingLabel> labels = processor.processToLabels(sourceData, shopCode);

                if (CollUtil.isNotEmpty(labels)) {
                    // 立即处理这一批：删除对应的旧标签，插入新标签
                    processBatchLabels(labels, processor.getLabelType(), shopCode);
                    totalProcessed += labels.size();

                    log.debug("店铺 {} 的 {} 标签批次处理完成，本批次: {}条，累计: {}条",
                            shopCode, processor.getLabelType(), labels.size(), totalProcessed);
                }

                offset += batchSize;
            }

            log.info("店铺 {} 的 {} 标签分批同步完成，共处理: {}条",
                    shopCode, processor.getLabelType(), totalProcessed);

        } catch (Exception e) {
            log.error("店铺 {} 的 {} 标签分批同步失败", shopCode, processor.getLabelType(), e);
            throw e;
        }
    }

    /**
     * 处理单批标签：删除对应的旧标签，插入新标签
     *
     * @param labels    标签列表
     * @param labelType 标签类型
     * @param shopCode  店铺编码
     */
    private void processBatchLabels(List<ListingLabel> labels, String labelType, String shopCode) {
        if (CollUtil.isEmpty(labels)) {
            return;
        }

        try {
            // 提取这批标签涉及的headId
            List<Integer> headIds = labels.stream()
                    .map(ListingLabel::getHeadId)
                    .distinct()
                    .collect(Collectors.toList());

            // 删除这些headId对应的旧标签
            if (CollUtil.isNotEmpty(headIds)) {
                int deletedCount = listingLabelService.deleteListingLabelsByHeadIdsAndLabelType(headIds, labelType, shopCode);
                log.debug("删除旧标签: {}条，涉及headId: {}个", deletedCount, headIds.size());
            }

            // 插入新标签
            Lists.partition(labels, 1000).forEach(listingLabelService::insertListingLabelBatch);

        } catch (Exception e) {
            log.error("处理批次标签失败，标签数量: {}, 标签类型: {}, 店铺: {}",
                    labels.size(), labelType, shopCode, e);
            throw e;
        }
    }


}
