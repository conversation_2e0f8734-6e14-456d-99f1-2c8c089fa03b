package com.suncent.smc.quartz.task.listing.am;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.suncent.smc.common.constant.Constants;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.enums.PlatformSiteEnum;
import com.suncent.smc.common.enums.WarehouseExcludeEnum;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.pdm.domain.dto.GetGoodsDetailQueryDTO;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.dto.ThirdpartyFbmDTO;
import com.suncent.smc.persistence.pdm.domain.entity.SuncentWarehouseSkuMapping;
import com.suncent.smc.persistence.pdm.service.IMappingGoodsService;
import com.suncent.smc.persistence.publication.domain.dto.AmazonJSONParamDTO;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.ListingAmazonAttributeLineV2;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingJSONFeedVO;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.persistence.publication.service.IListingAmazonAttributeLineV2Service;
import com.suncent.smc.persistence.publication.service.IListingLogService;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.publication.AmazonApiHttpRequestBiz;
import com.suncent.smc.provider.biz.publication.ListingUpdateBuilder;
import com.suncent.smc.provider.biz.publication.PDMHttpRequestBiz;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.suncent.smc.common.enums.PublishType.VCPO;

@Component
@Slf4j
public class UpdateItemsPerInnerPackTask {

    @Resource
    private IGoodsHeadService goodsHeadService;
    @Resource
    PDMHttpRequestBiz pdmHttpRequestBiz;
    @Resource
    ThirdpartyInventoryBiz inventoryBiz;
    @Resource
    IMappingGoodsService mappingGoodsService;
    @Autowired
    protected ListingUpdateBuilder listingUpdateBuilder;
    @Resource
    private IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;
    @Resource
    private IPlatformCategoryService platformCategoryService;
    @Autowired
    private AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;
    @Autowired
    protected IListingLogService listingLogService;

    /**
     * 刷新VCPO链接的箱规
     */
    @XxlJob("updateItemsPerInnerPack")
    public void updateItemsPerInnerPack() {
        log.info("刷新VCPO链接的箱规");
        // 1、分页获取VCPO链接
        int lastId = 0;
        int allSize = 0;
        String jobParam = XxlJobHelper.getJobParam();
        List<String> platformSkus = Collections.emptyList();
        if (StrUtil.isNotBlank(jobParam)) {
            platformSkus = StrUtil.splitTrim(jobParam, StrUtil.COMMA);
        }
        List<GoodsHead> goodsHeads = goodsHeadService.listNeedUpdateItemsPerInnerPackByLastId(VCPO.getType(), platformSkus, lastId);
        if (CollUtil.isEmpty(goodsHeads)) {
            return;
        }

        allSize = goodsHeads.size();
        lastId = goodsHeads.stream().mapToInt(GoodsHead::getId).max().orElse(0);
        if (lastId == 0) {
            return;
        }
        handle(goodsHeads);

        while (CollUtil.isNotEmpty(goodsHeads)) {
            goodsHeads = goodsHeadService.listNeedUpdateItemsPerInnerPackByLastId(VCPO.getType(), platformSkus, lastId);
            if (CollUtil.isEmpty(goodsHeads)) {
                break;
            }
            lastId = goodsHeads.stream().mapToInt(GoodsHead::getId).max().orElse(0);
            if (lastId == 0) {
                break;
            }
            allSize += goodsHeads.size();
            handle(goodsHeads);
        }
        log.info("刷新VCPO链接的箱规完成，总共处理[{}]条记录", allSize);
    }

    /**
     * 处理
     *
     * @param list
     */
    private void handle(List<GoodsHead> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list = list.stream().filter(goodsHead -> StrUtil.isNotBlank(goodsHead.getPdmGoodsCode()) && StrUtil.isNotBlank(goodsHead.getPlatformGoodsId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) {
            return;
        }
        // 2、按商品编码分组，获取共享SKU
        Map<String, List<GoodsHead>> goodsHeadMap = list.stream().collect(Collectors.groupingBy(GoodsHead::getPdmGoodsCode));
        goodsHeadMap.forEach((goodsCode, goodsHeads) -> {
            if (CollUtil.isEmpty(goodsHeads)) {
                return;
            }
            List<String> currentGoodsCodes = new ArrayList<>();
            currentGoodsCodes.add(goodsCode);

            GetGoodsDetailQueryDTO q1 = new GetGoodsDetailQueryDTO();
            q1.setModels(Lists.newArrayList("GoodsMainList"));
            q1.setGoodsCodes(currentGoodsCodes);
            List<SaleGoodsDTO> goodsDTOList = pdmHttpRequestBiz.listGoodsDetail(q1);
            if (CollUtil.isEmpty(goodsDTOList)) {
                return;
            }

            for (SaleGoodsDTO saleGoodsDTO : goodsDTOList) {
                if (CollUtil.isNotEmpty(saleGoodsDTO.getSubGoodsCodes())) {
                    currentGoodsCodes.addAll(saleGoodsDTO.getSubGoodsCodes());
                }
                if (StrUtil.isNotBlank(saleGoodsDTO.getMainGoodsCode())) {
                    currentGoodsCodes.add(saleGoodsDTO.getMainGoodsCode());
                }
            }

            if (CollUtil.isEmpty(currentGoodsCodes)) {
                return;
            }
            currentGoodsCodes = currentGoodsCodes.stream().distinct().collect(Collectors.toList());

            // 3、获取商品编码对应的库存
            List<ThirdpartyFbmDTO> nowList = inventoryBiz.getVCPOStock(currentGoodsCodes, WarehouseExcludeEnum.getExcludeWarehouseCodeList());
            if (CollUtil.isEmpty(nowList)) {
                return;
            }
            // 只取谷仓、桑椹海外仓和sellable_qty 除以 box_qty > 0的库存
            nowList = nowList.stream().filter(fbmDTO -> Objects.equals(fbmDTO.getWhCountry(), PlatformSiteEnum.AM_US.getSiteCodeType()) && fbmDTO.getBoxQty() > 0
                    && (fbmDTO.getWarehouseType().contains("桑椹海外仓")  || fbmDTO.getWarehouseType().contains("谷仓"))).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nowList)) {
                // 优先级1：有可用库存且总箱数>1，总箱数：可用数量/每箱数量
                List<ThirdpartyFbmDTO> firstList =  nowList.stream().filter(fbmDTO -> fbmDTO.getAvailableQty() > 0 && fbmDTO.getAvailableQty() / fbmDTO.getBoxQty()  > 0).collect(Collectors.toList());
                if (ObjUtil.isNotEmpty(firstList)) {
                    doHandle(goodsHeads, firstList);
                    return;
                }
                // 优先级2：在途库存大于0
                List<ThirdpartyFbmDTO> secondList = nowList.stream().filter(fbmDTO -> fbmDTO.getOnwayQty() > 0).collect(Collectors.toList());
                if (ObjUtil.isNotEmpty(secondList)) {
                    doHandle(goodsHeads, secondList);
                    return;
                }
            }

            List<SuncentWarehouseSkuMapping> skuMappings = mappingGoodsService.listSuncentWarehouseSkuMapping(currentGoodsCodes);
            skuMappings = skuMappings.stream().filter(skuMapping -> skuMapping.getWarehouseType().contains("桑椹海外仓")  || skuMapping.getWarehouseType().contains("谷仓")).collect(Collectors.toList());
            if (CollUtil.isEmpty(skuMappings)) {
                log.info("商品编码[{}]没有箱规信息", goodsCode);
                return;
            }

            // 5、按box_qty排序
            skuMappings = skuMappings.stream().sorted(Comparator.comparingInt(SuncentWarehouseSkuMapping::getBoxQty)).collect(Collectors.toList());

            // 6、判断是否有boxQty>1的箱规，有取>1后的最小箱规，没有取1
            Integer boxQty = skuMappings.stream().map(SuncentWarehouseSkuMapping::getBoxQty).filter(qty -> qty > 1).findFirst().orElse(skuMappings.get(0).getBoxQty());
            if (boxQty == null || boxQty == 0) {
                log.info("商品编码[{}]没有箱规或箱规为0", goodsCode);
                return;
            }

            // 8、批量更新箱规，使用vc json api
            doUpdateItemsPerInnerPack(goodsHeads, boxQty);
        });
    }

    private void doHandle(List<GoodsHead> goodsHeads, List<ThirdpartyFbmDTO> fbmDTOS) {
        fbmDTOS = fbmDTOS.stream().sorted(Comparator.comparingInt(ThirdpartyFbmDTO::getBoxQty)).collect(Collectors.toList());
        // 获取最小的箱规数
        Integer boxQty = fbmDTOS.stream().map(ThirdpartyFbmDTO::getBoxQty).filter(qty -> qty > 1).findFirst().orElse(fbmDTOS.get(0).getBoxQty());

        // 8、批量更新箱规，使用vc json api
        doUpdateItemsPerInnerPack(goodsHeads, boxQty);
    }

    /**
     * 更新箱规
     *
     * @param goodsHeads
     * @param boxQty
     */
    private void doUpdateItemsPerInnerPack(List<GoodsHead> goodsHeads, Integer boxQty) {
        for (GoodsHead goodsHead : goodsHeads) {
            try {
                PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
                String logMsg = "更新商品箱规，从[{}]变更为[{}]";
                boolean insertLog = false;
                // 获取 rtip_items_per_inner_pack.value 属性
                ListingAmazonAttributeLineV2 innerPackLine = listingAmazonAttributeLineV2Service.getAttrByPropNodePath(goodsHead.getId(), "rtip_items_per_inner_pack.value");
                if (ObjUtil.isEmpty(innerPackLine)) {
                    createItemsPerInnerPack(boxQty, goodsHead);
                    logMsg = StrUtil.format(logMsg, "空", boxQty);
                }else {
//                    BigDecimal oldBoxQty = Convert.toBigDecimal(innerPackLine.getTableValue(), BigDecimal.ZERO);
//                    BigDecimal newBoxQty = Convert.toBigDecimal(boxQty, BigDecimal.ZERO);
//                    if (oldBoxQty.compareTo(newBoxQty) == 0) {
//                        log.info("商品ID[{}]箱规从[{}]变更为[{}]，不需要更新", goodsHead.getId(), innerPackLine.getTableValue(), boxQty);
//                        continue;
//                    }
                    logMsg = StrUtil.format(logMsg, innerPackLine.getTableValue(), boxQty);
                    insertLog = !Objects.equals(Integer.parseInt(innerPackLine.getTableValue()), boxQty);
                    innerPackLine.setTableValue(String.valueOf(boxQty));
                    listingAmazonAttributeLineV2Service.updateListingAmazonAttributeLineV2(innerPackLine);
                }
                log.info("商品ID[{}],[{}]", goodsHead.getId(), logMsg);

                AmazonJSONParamDTO amazonJSONParamDTO = new AmazonJSONParamDTO(boxQty+"");
                AmazonListingJSONFeedVO.Attributes attributes = new AmazonListingJSONFeedVO.Attributes();
                attributes.setValue(Lists.newArrayList(amazonJSONParamDTO));
                attributes.setOp("REPLACE");
                attributes.setPath("/attributes/rtip_items_per_inner_pack");
                AjaxResult ajaxResult = amazonApiHttpRequestBiz.updateApi(goodsHead.getPlatformGoodsCode(), goodsHead.getShopCode(), goodsHead.getPublishType() + "", Lists.newArrayList(attributes), platformCategory.getProductType());
                if (ajaxResult.isSuccess() && insertLog) {
                    // 加入日志
                    listingLogService.insertSuccessListingLog(logMsg,  "-1", Integer.valueOf(goodsHead.getId()));
                }
            } catch (Exception e) {
                log.error("更新商品[{}]箱规失败", goodsHead.getId(), e);
            }
        }
    } 

    private ListingAmazonAttributeLineV2 createItemsPerInnerPack(Integer boxQty, GoodsHead goodsHead) {
        PlatformCategory platformCategory =  platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
        ListingAmazonAttributeLineV2 innerPackLine = new ListingAmazonAttributeLineV2();
        innerPackLine.setId(null);
        innerPackLine.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        innerPackLine.setHeadId(Long.valueOf(goodsHead.getId()));
        innerPackLine.setCategoryId(goodsHead.getCategoryId());
        innerPackLine.setTableType(0);
        innerPackLine.setProductType(platformCategory.getProductType());
        innerPackLine.setPropNodePath("rtip_items_per_inner_pack.value");
        innerPackLine.setTableValue(String.valueOf(boxQty));
        innerPackLine.setTableName("rtip_items_per_inner_pack");
        innerPackLine.setCreateBy("-1");
        innerPackLine.setVcFlag(Constants.YesOrNo.YES);
        listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(innerPackLine);
        return innerPackLine;
    }


}
