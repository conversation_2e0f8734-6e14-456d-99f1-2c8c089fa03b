package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.suncent.smc.common.constant.Constants;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.framework.web.service.DictService;
import com.suncent.smc.oss.IAliOssService;
import com.suncent.smc.persistence.ads.domain.AdsFitmentDataEbay;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.cdp.service.IBrandService;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.cdp.service.ISiteService;
import com.suncent.smc.persistence.configuration.category.domain.entity.GoodsCategoryMapping;
import com.suncent.smc.persistence.configuration.category.service.IGoodsCategoryMappingService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreWatermark;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreInfoService;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreWatermarkService;
import com.suncent.smc.persistence.configuration.upc.domain.ConfigUpcPool;
import com.suncent.smc.persistence.configuration.upc.service.IConfigUpcPoolService;
import com.suncent.smc.persistence.ebay.domain.EbayGoodsHeadV2;
import com.suncent.smc.persistence.pdm.domain.dto.GoodsRedPriceDTO;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.entity.Goods;
import com.suncent.smc.persistence.pdm.service.IGoodsService;
import com.suncent.smc.persistence.publication.domain.dto.ItemDTO;
import com.suncent.smc.persistence.publication.domain.dto.ListingDTO;
import com.suncent.smc.persistence.publication.domain.dto.PriceCheckRequest;
import com.suncent.smc.persistence.publication.domain.dto.TitleInfoDTO;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.template.domain.entity.*;
import com.suncent.smc.persistence.template.service.ITemplateShippingLocationLineService;
import com.suncent.smc.persistence.template.service.ITemplateShippingTypeLineService;
import com.suncent.smc.persistence.template.service.ITemplateShopRelationService;
import com.suncent.smc.provider.biz.common.CommonGoodsInfoBiz;
import com.suncent.smc.provider.biz.ebay.EbayListingInfoBiz;
import com.suncent.smc.provider.biz.pdm.CreatePlatformCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品刊登业务类
 *
 * <AUTHOR>
 * @since 2023-01-12 10:42:00
 */
@Slf4j
@Component

public class GoodsInfoBiz extends CommonGoodsInfoBiz {
    @Autowired
    IGoodsHeadService goodsHeadService;
    @Autowired
    IGoodsResourceService goodsResourceService;
    @Autowired
    IGoodsDescriptionService goodsDescriptionService;
    @Autowired
    IGoodsSpecificationService goodsSpecificationService;
    @Autowired
    IAmazonGoodsSafetyService amazonGoodsSafetyService;
    @Autowired
    IAmazonGoodsInfoLineService amazonGoodsInfoLineService;
    @Autowired
    IListingEbayLineService ebayGoodsLineService;
    @Autowired
    IAmazonGoodsDetailService amazonGoodsDetailService;
    @Autowired
    CreatePlatformCode createPlatformCode;
    @Autowired
    IShopService shopService;
    @Autowired
    DictService dictService;
    @Autowired
    IBrandService brandService;
    @Autowired
    ISiteService siteService;
    @Autowired
    IListingEbayValueService ebayValueService;
    @Autowired
    IListingAttributeTempService ebayValueTempService;
    @Autowired
    IListingAdaptiveTempService adaptiveTempService;
    @Autowired
    IListingEbayAdaptiveService adaptiveService;
    @Autowired
    private ITemplateShopRelationService templateShopRelationService;
    @Autowired
    private IListingEbayPolicyService listingEbayPolicyService;
    @Autowired
    private IListingEbayShippingHeadService listingEbayShippingHeadService;
    @Autowired
    private IListingShippingTypeLineService shippingTypeLineService;
    @Autowired
    private IListingShippingLocationLineService shippingLocationLineService;
    @Autowired
    private ITemplateShippingTypeLineService templateShippingTypeLineService;
    @Autowired
    private ITemplateShippingLocationLineService templateShippingLocationLineService;
    @Autowired
    private IConfigStoreInfoService configStoreInfoService;
    @Autowired
    private IListingEbayValueService listingEbayValueService;
    @Autowired
    private IConfigStoreWatermarkService configStoreWatermarkService;

    @Autowired
    private IAliOssService aliyunOssService;


    @Autowired
    private IAdsService adsService;

    @Autowired
    private IGoodsService goodsService;

    @Autowired
    private IConfigUpcPoolService configUpcPoolService;

    @Value("${aliyun.oss.defaultBucketName}")
    public String defaultBucketName;


    @Autowired
    @Lazy
    private ListingInfoBiz listingInfoBiz;
    @Value("${aliyun.oss.urlPrefix}")
    public String urlPrefix;

    @Resource
    private CommonInfoBiz commonInfoBiz;
    @Autowired
    private IPlatformCategoryService platformCategoryService;
    @Autowired
    private IGoodsCategoryMappingService goodsCategoryMappingService;
    @Resource
    private PDMHttpRequestBiz pdmHttpRequestBiz;
    @Autowired
    private IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;
    @Autowired
    private IAmCategoryTemplateSmcMappingService amCategoryTemplateSmcMappingService;

    @Autowired
    private EbayListingInfoBiz ebayListingInfoBiz;

    /**
     * 批量保存编辑 ebay 属性信息
     * @param item
     */
    private void saveEbayAttributeLines(ItemDTO item) {
        //去重map
        Map<String, String> repeatVerMap = new HashMap<>();
        List<ListingAmazonAttributeLine> info = item.getGoodsAttributeLineList();
        if (ObjectUtils.isEmpty(info)) {
            info = new ArrayList<>();
        }
        List<ListingEbayValue> valueList = new ArrayList<>();
        for (ListingAmazonAttributeLine attributeLine : info) {
            if (repeatVerMap.containsKey(attributeLine.getTableName())) {
                continue;
            }
            repeatVerMap.put(attributeLine.getTableName(), null);
            ListingEbayValue line = new ListingEbayValue();
            line.setCreateTime(DateUtils.getNowDate());
            line.setCreateBy(String.valueOf(ObjectUtils.isNotEmpty(item.getUserId()) ? item.getUserId() : ShiroUtils.getUserId()));
            line.setListingLineId(item.getEbayLine().getId());
            line.setName(attributeLine.getTableName());
            line.setValue(attributeLine.getTableValue());

            if (line.getName().equals("UPC")) {
                String upcValue = getUpcValue(item.getGoodsHead().getShopCode(), item.getUserId(), line.getValue());
                line.setValue(upcValue);
            }
            valueList.add(line);
        }
        if (CollUtil.isNotEmpty(valueList)){
            listingEbayValueService.batchInsertListingEbayValue(valueList);
        }
    }


    /**
     * 获取亚马逊商品三类属性信息
     *
     * @param listingDTO
     * @return
     */
    public List<ListingAmazonAttributeLine> getAmazonAttributeLines(ListingDTO listingDTO) {
        //去重map
        Map<String, String> repeatVerMap = new HashMap<>();
        List<ListingAmazonAttributeLine> attributeLines = new ArrayList<>();

        //包含 必填的属性 和 手动新增非必填的属性
        List<ListingAmazonAttributeLine> amazonAttributeLines = getListingAmazonAttributeLines(listingDTO);

        for (ListingAmazonAttributeLine attributeLine : amazonAttributeLines) {
            if (repeatVerMap.containsKey(attributeLine.getTableName())) {
                continue;
            }
            repeatVerMap.put(attributeLine.getTableName(), null);

            ListingAmazonAttributeLine line = new ListingAmazonAttributeLine();
            //line.setGoodsId(listingEditDTO.getGoodsHeadId());
            line.setCreateTime(DateUtils.getNowDate());
            line.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
            line.setPdmGoodsCode(listingDTO.getGoodsCode());
            line.setPlatform(listingDTO.getPlatform());
            line.setCategoryId(listingDTO.getCategoryId());
            line.setTableValue(attributeLine.getTableValue());
            line.setTableName(attributeLine.getTableName());
            line.setTableType(attributeLine.getTableType());
            attributeLines.add(line);
        }
        return attributeLines;
    }

    private ArrayList<ListingAmazonAttributeLine> getListingAmazonAttributeLines(ListingDTO listingDTO) {
        ArrayList<ListingAmazonAttributeLine> amazonAttributeLines = new ArrayList<>();
        List<ListingAmazonAttributeLine> info = listingDTO.getListingAmazonAttributeLinesInfo();
        List<ListingAmazonAttributeLine> detail = listingDTO.getListingAmazonAttributeLinesDetail();
        List<ListingAmazonAttributeLine> safety = listingDTO.getListingAmazonAttributeLinesSafety();

        Integer fulfillmentLatency = listingDTO.getFulfillmentLatency();
        if (ObjectUtils.isNotEmpty(fulfillmentLatency)) {
            ListingAmazonAttributeLine line = new ListingAmazonAttributeLine();
            line.setPlatform(PlatformTypeEnum.AM.name());
            line.setCategoryId(listingDTO.getCategoryId());
            line.setTableName(AmazonAttributeEnum.FULFILLMENT_LATENCY.getInfo());
            line.setTableValue(String.valueOf(fulfillmentLatency));
            line.setTableType(4);
            amazonAttributeLines.add(line);
        }
        //平台商品编码--新增
        String externalProductId = listingDTO.getExternalProductId();
        //平台商品编码类型--新增
        String externalProductIdType = listingDTO.getExternalProductIdType();
        if (ObjectUtils.isNotEmpty(externalProductIdType)) {
            //类型
            ListingAmazonAttributeLine lineType = new ListingAmazonAttributeLine();
            lineType.setPlatform(PlatformTypeEnum.AM.name());
            lineType.setCategoryId(listingDTO.getCategoryId());
            lineType.setTableName(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE.getInfo());
            lineType.setTableValue(externalProductIdType);
            lineType.setTableType(4);
            amazonAttributeLines.add(lineType);
            //值
            ListingAmazonAttributeLine lineValue = new ListingAmazonAttributeLine();
            lineValue.setPlatform(PlatformTypeEnum.AM.name());
            lineValue.setCategoryId(listingDTO.getCategoryId());
            lineValue.setTableName(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID.getInfo());
            lineValue.setTableValue(externalProductId);
            lineValue.setTableType(4);
            amazonAttributeLines.add(lineValue);
        }

        String salePrice = listingDTO.getSalePrice();
        String saleBeginDate = listingDTO.getSaleBeginDate();
        String saleEndDate = listingDTO.getSaleEndDate();
        if (ObjectUtils.isNotEmpty(salePrice) && ObjectUtils.isNotEmpty(saleBeginDate) && ObjectUtils.isNotEmpty(saleEndDate)) {
            ListingAmazonAttributeLine salePriceLine = new ListingAmazonAttributeLine();
            salePriceLine.setPlatform(PlatformTypeEnum.AM.name());
            salePriceLine.setCategoryId(listingDTO.getCategoryId());
            salePriceLine.setTableName(AmazonAttributeEnum.SALE_PRICE.getInfo());
            salePriceLine.setTableValue(salePrice);
            salePriceLine.setTableType(4);
            amazonAttributeLines.add(salePriceLine);

            ListingAmazonAttributeLine saleBeginDateLine = new ListingAmazonAttributeLine();
            saleBeginDateLine.setPlatform(PlatformTypeEnum.AM.name());
            saleBeginDateLine.setCategoryId(listingDTO.getCategoryId());
            saleBeginDateLine.setTableName(AmazonAttributeEnum.SALE_FROM_DATE.getInfo());
            saleBeginDateLine.setTableValue(saleBeginDate);
            saleBeginDateLine.setTableType(4);
            amazonAttributeLines.add(saleBeginDateLine);

            ListingAmazonAttributeLine saleEndDateLine = new ListingAmazonAttributeLine();
            saleEndDateLine.setPlatform(PlatformTypeEnum.AM.name());
            saleEndDateLine.setCategoryId(listingDTO.getCategoryId());
            saleEndDateLine.setTableName(AmazonAttributeEnum.SALE_END_DATE.getInfo());
            saleEndDateLine.setTableValue(saleEndDate);
            saleEndDateLine.setTableType(4);
            amazonAttributeLines.add(saleEndDateLine);
        }

        if (!CollectionUtils.isEmpty(info)) {
            amazonAttributeLines.addAll(info.stream().map(f -> {
                f.setTableType(0);
                return f;
            }).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(detail)) {
            amazonAttributeLines.addAll(detail.stream().map(f -> {
                f.setTableType(1);
                return f;
            }).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(safety)) {
            amazonAttributeLines.addAll(safety.stream().map(f -> {
                f.setTableType(2);
                return f;
            }).collect(Collectors.toList()));
        }
        return amazonAttributeLines;
    }

    public ArrayList<GoodsResource> getGoodsResourceList(ListingDTO listingDTO) {
        ArrayList<GoodsResource> goodsResourceList = new ArrayList<>();
        //获取主图的地址数据
        GoodsResource masterImgUrlResource = new GoodsResource();
        if (ObjectUtils.isNotEmpty(listingDTO.getMasterImgFile())) {
            JSONObject jsonObject = JSONObject.parseObject(listingDTO.getMasterImgFile());
            masterImgUrlResource.setResourceUrl(StringUtils.isNotBlank(jsonObject.getString("urls")) ? jsonObject.getString("urls") : jsonObject.getString("url"))
                    .setResourceName(StringUtils.isNotBlank(jsonObject.getString("originalFilenames")) ? jsonObject.getString("originalFilenames") : jsonObject.getString("originalFilename"))
                    .setSourceType(jsonObject.getInteger("sourceType"))
                    .setResourceType("1").setIsMain(1).setSortNumber(1);

            //给ebay主图加上水印
            // this.addWatermarking(masterImgUrlResource, listingDTO.getPlatform() ,listingDTO.getShopCode(), listingDTO.getProductCategoryCode());
        }

        //获取视频文件的地址数据
        if (ObjectUtils.isNotEmpty(listingDTO.getVideoFile())) {
            GoodsResource videoUrlResource = new GoodsResource();
            videoUrlResource.setResourceUrl(JSONObject.parseObject(listingDTO.getVideoFile()).getString("urls"))
                    .setResourceName(JSONObject.parseObject(listingDTO.getVideoFile()).getString("originalFilenames")).
                    setResourceType("0").setSortNumber(1);
            goodsResourceList.add(videoUrlResource);
        }

        //处理附图12张
        JSONArray imgJsonArray = JSONArray.parseArray(listingDTO.getImgDataArr());
        for (int i = 0; i < imgJsonArray.size(); i++) {
            GoodsResource goodsResource = new GoodsResource();
            goodsResource.setResourceUrl(imgJsonArray.getJSONObject(i).getString("url"));
            goodsResource.setResourceName(imgJsonArray.getJSONObject(i).getString("name"));
            goodsResource.setResourceType("1");
            goodsResource.setIsMain(0);
            goodsResource.setSortNumber(i);
            goodsResource.setSourceType(imgJsonArray.getJSONObject(i).getInteger("sourceType"));
            goodsResourceList.add(goodsResource);
        }
        if ( imgJsonArray.size()==0 && StrUtil.isBlank(masterImgUrlResource.getResourceUrl()) ){
            return goodsResourceList;
        }
        goodsResourceList.add(masterImgUrlResource);

        return goodsResourceList;
    }

    /**
     * 根据店铺code+图片链接 设置水印
     *
     * @param masterImgUrlResource
     * @param platform
     * @param shopCode
     */
    public void addWatermarking(GoodsResource masterImgUrlResource, String platform, String shopCode, String categoryId) {
        File watermarkingFile = null;
        File newFile = null;
        File oldFile = null;
        try {
            if (!Objects.equals(platform, PlatformTypeEnum.EB.name())) {
                return;
            }
            //查询店铺对应的水印图
            ConfigStoreInfo configStoreInfo = new ConfigStoreInfo();
            configStoreInfo.setPlatform(platform);
            configStoreInfo.setShopCode(shopCode);
            List<ConfigStoreInfo> configStoreInfoList = configStoreInfoService.selectConfigStoreInfoList(configStoreInfo);
            if (CollectionUtils.isEmpty(configStoreInfoList)) {
                return;
            }
            ConfigStoreWatermark watermark = new ConfigStoreWatermark();
            watermark.setStoreId(configStoreInfoList.get(0).getId());
            watermark.setStatus("1");
            List<ConfigStoreWatermark> watermarkList = configStoreWatermarkService.selectConfigStoreWatermarkList(watermark);
            if (CollectionUtils.isEmpty(watermarkList)) {
                return;
            }
            //查询类目水印图
            List<ConfigStoreWatermark> categoryWaterMark = watermarkList.stream().filter(w -> categoryId.equals(w.getCategoryId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(categoryWaterMark)) {
                categoryWaterMark = watermarkList.stream().filter(w -> ObjectUtils.isEmpty(w.getCategoryId())).collect(Collectors.toList());
                ;
            }
            //1.下载需要添加水印的主图
            URL oldFileUrl = new URL(masterImgUrlResource.getResourceUrl());
            oldFile = new File("add_old_" + System.currentTimeMillis() + ".jpg");
            FileUtils.copyURLToFile(oldFileUrl, oldFile);
            URL watermarkingFileUrl = new URL(categoryWaterMark.get(0).getImageUrl());
            watermarkingFile = new File("add_watermarking_" + System.currentTimeMillis() + ".jpg");
            FileUtils.copyURLToFile(watermarkingFileUrl, watermarkingFile);
            //3.生成添加完水印图
            newFile = new File("add_new_" + System.currentTimeMillis() + ".jpg");
            ImgUtil.pressImage(
                    oldFile,//输入图片地址
                    newFile,//输出图片地址
                    ImgUtil.read(watermarkingFile), //水印图片
                    0, //x坐标修正值。 默认在中间，偏移量相对于中间偏移
                    0, //y坐标修正值。 默认在中间，偏移量相对于中间偏移
                    0.5f
            );
            String url = "smc/files/" + DateUtils.getDate() + "/" + System.currentTimeMillis() + ".jpg";
            aliyunOssService.putObjectByInputStream(defaultBucketName, url, new FileInputStream(newFile));
            masterImgUrlResource.setResourceUrl(urlPrefix + url);
        } catch (IOException e) {
            log.error("生成水印图片失败");
        } finally {
            if (Objects.nonNull(oldFile) && oldFile.exists()) {
                oldFile.delete();
            }
            if (Objects.nonNull(watermarkingFile) && watermarkingFile.exists()) {
                watermarkingFile.delete();
            }
            if (Objects.nonNull(newFile) && newFile.exists()) {
                newFile.delete();
            }
        }
    }

    /**
     * 1.根据uuid查询适配信息临时表数据插入至正式表中
     * 2.如果临时表没有数据再根据商品编码插入适配信息
     *
     * @param item
     */
    private void insetEbayAdaptiveData(ItemDTO item) {
        GoodsHead goodsHead = item.getGoodsHead();
        String goodsCode = goodsHead.getPdmGoodsCode();
        ListingEbayLine ebayLine = item.getEbayLine();
        Integer ebayLineId = ebayLine.getId();
        GoodsHead updateHead = new GoodsHead();
        updateHead.setId(goodsHead.getId());
        SaleGoodsDTO query = new SaleGoodsDTO();
        query.setGoodsCode(goodsCode);
        Goods pdmGood = goodsService.selectGoodsByGoodCode(query);
        if (ObjectUtils.isNotEmpty(pdmGood)) {
            String adaptFlag = pdmGood.getAdaptFlag();
            if (ObjUtil.equals(adaptFlag, "N")) {
                updateHead.setAdaptationStatus(AdaptationStatusEnum.NO.getStatus());
                goodsHeadService.updateListingGoodsHead(updateHead);
                return;
            }
        }
        //清空适配信息再新增
        adaptiveService.deleteListingEbayAdaptiveByLineId(ebayLineId);
        //插入适配信息表
        int limitSize = 500;
        //批量刊登
        if (ListingBatchOptions.ADD_BATCH.name().equals(item.getOperationFlag())) {
            //根据商品编码插入适配信息 从ads取
            List<AdsFitmentDataEbay> adsFitmentDataEbayList = adsService.selectFitmentDataEbayByProductCode(pdmGood.getProductCode(), pdmGood.getGoodsCode());
            if (CollectionUtils.isEmpty(adsFitmentDataEbayList)) {
                updateHead.setAdaptationStatus(AdaptationStatusEnum.LOST.getStatus());
                goodsHeadService.updateListingGoodsHead(updateHead);
                return;
            }
            List<ListingEbayAdaptive> adaptiveList = adsFitmentDataEbayList.stream().map(f -> {
                ListingEbayAdaptive ebayAdaptive = new ListingEbayAdaptive();
                ebayAdaptive.setEngine(f.getEngine());
                ebayAdaptive.setMake(f.getMake());
                ebayAdaptive.setModel(f.getModel());
                ebayAdaptive.setTrim(f.getTrim());
                ebayAdaptive.setYear(f.getYear());
                ebayAdaptive.setSubmodel(f.getSubmodel());
                ebayAdaptive.setNotes(f.getNotes());
                ebayAdaptive.setPlatform(f.getPlatform());
                ebayAdaptive.setType(f.getType());
                ebayAdaptive.setListingLineId(ebayLineId);
                return ebayAdaptive;
            }).collect(Collectors.toList());

            List<List<ListingEbayAdaptive>> adsList = Lists.partition(adaptiveList, limitSize);
            adsList.forEach(i -> adaptiveService.batchInsertListingEbayAdaptive(i));
        }
        //单个刊登
        if (ListingBatchOptions.ADD.name().equals(item.getOperationFlag())) {
            ListingAdaptiveTemp listingAdaptiveTemp = new ListingAdaptiveTemp();
            listingAdaptiveTemp.setUuid(item.getUuid());
            List<ListingAdaptiveTemp> tempList = adaptiveTempService.selectListingAdaptiveTempList(listingAdaptiveTemp);
            if(ObjectUtils.isEmpty(tempList)){
                updateHead.setAdaptationStatus(AdaptationStatusEnum.LOST.getStatus());
                goodsHeadService.updateListingGoodsHead(updateHead);
                return;
            }
            //从适配临时表取数据
            List<ListingEbayAdaptive> listingEbayAdaptiveList = tempList.stream().map(p -> {
                ListingEbayAdaptive ebayAdaptive = new ListingEbayAdaptive();
                ebayAdaptive.setEngine(p.getAdaptiveEngine());
                ebayAdaptive.setMake(p.getAdaptiveMake());
                ebayAdaptive.setModel(p.getAdaptiveModel());
                ebayAdaptive.setTrim(p.getAdaptiveTrim());
                ebayAdaptive.setYear(p.getAdaptiveYear());
                ebayAdaptive.setSubmodel(p.getAdaptiveSubmodel());
                ebayAdaptive.setNotes(p.getNotes());
                ebayAdaptive.setPlatform(p.getPlatform());
                ebayAdaptive.setType(p.getType());
                ebayAdaptive.setListingLineId(ebayLineId);
                return ebayAdaptive;
            }).collect(Collectors.toList());

            // 分批写入
            List<List<ListingEbayAdaptive>> partitions = Lists.partition(listingEbayAdaptiveList, limitSize);
            partitions.forEach(i -> adaptiveService.batchInsertListingEbayAdaptive(i));
        }
        //处理自动刊登的listing适配
        if (ObjectUtils.isNotEmpty(item.getListingEbayAdaptiveList())) {
            List<ListingEbayAdaptive> listingEbayAdaptiveList = item.getListingEbayAdaptiveList().stream().map(l -> {
                l.setListingLineId(ebayLineId);
                return l;
            }).collect(Collectors.toList());
            // 分批写入
            List<List<ListingEbayAdaptive>> partitions = Lists.partition(listingEbayAdaptiveList, limitSize);
            partitions.forEach(i -> adaptiveService.batchInsertListingEbayAdaptive(i));
        }
        goodsHead.setAdaptationStatus(AdaptationStatusEnum.WAIT.getStatus());
        goodsHeadService.updateListingGoodsHead(goodsHead);
    }



    /**
     * @description: TODO 组装基本数据头表的方法
     * @param: [listingDTO]
     * @return: java.util.List<java.lang.Integer>
     * <AUTHOR>
     * @date: 2023/1/12 18:22
     */
    public List<GoodsHead> packagingBasis(ListingDTO listingDTO) {
        // 店铺数据
        Splitter splitter = Splitter.on(",").omitEmptyStrings();
        List<String> shopCodeList = splitter.splitToList(listingDTO.getShopCode());
        List<GoodsHead> goodsHeadList = new ArrayList<>();

        // 解决店铺和标题信息不匹配的问题
        List<TitleInfoDTO> titleInfo = listingDTO.getTitleInfo();
        Map<String, TitleInfoDTO> titleInfoMap = titleInfo.stream().filter(e -> StrUtil.isNotBlank(e.getShopCode())).collect(Collectors.toMap(TitleInfoDTO::getShopCode, a -> a, (k1, k2) -> k1));
        for (int i = 0; i < shopCodeList.size(); i++) {
            TitleInfoDTO titleInfoDTO = titleInfoMap.containsKey(shopCodeList.get(i)) ? titleInfoMap.get(shopCodeList.get(i)) : titleInfo.get(i);
            // 基础头部数据
            GoodsHead goodsHead = new GoodsHead();
            goodsHead.setAdaptationStatus(AdaptationStatusEnum.WAIT.getStatus());
            BeanUtil.copyProperties(listingDTO, goodsHead);
            //类目
            goodsHead.setCategoryId(StringUtils.isNotEmpty(listingDTO.getProductCategoryCode()) ? Integer.valueOf(listingDTO.getProductCategoryCode()) : listingDTO.getCategoryId());
            //设置刊登状态
            goodsHead.setPublishStatus(0);
            //设置商品编码-同步自PDM系统
            goodsHead.setPdmGoodsCode(listingDTO.getGoodsCode());
            if(Constants.YesOrNo.YES.equals(listingDTO.getNewVersion())) {
                goodsHead.setSmcFlag(3);
            }

            //ebay处理逻辑
            if (PlatformTypeEnum.EB.name().equals(listingDTO.getPlatform())) {

                //设置ebay价格
                //设置店铺的标题信息,ebay标题有最长80个字符长度限制
                goodsHead.setShopCode(shopCodeList.get(i)).
                        setTitle(titleInfoDTO.getTitle());
                goodsHead.setPlatformGoodsCode(listingDTO.getPlatformGoodsCode());
                //设置物品所在地
                goodsHead.setCondition(titleInfoDTO.getCondition());
                //设置描述模板
                goodsHead.setDescriptionId(titleInfoDTO.getDescriptionId());
                //设置视频id
                goodsHead.setVideoId(titleInfoDTO.getVideoId());
                // 生成ebay平台商品编码
                String siteCode = shopService.selectSiteCodeByShopCode(shopCodeList.get(i));
                //价格相关
                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(null,goodsHead.getPdmGoodsCode(), listingDTO.getStandardPrice(), listingDTO.getPublishType(),siteCode,null,goodsHead.getStandardPrice());
                goodsHead.setStandardPrice(price);
                goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
                GoodsRedPriceDTO goodsRedPriceDTO = pdmHttpRequestBiz.getRedLinePriceByGoodsCode(siteCode, listingDTO.getGoodsCode());
                BigDecimal redLinePrice = BigDecimal.valueOf(0);
                if (ObjectUtils.isNotEmpty(goodsRedPriceDTO)) {
                    redLinePrice = BigDecimal.valueOf( ObjUtil.isEmpty(goodsRedPriceDTO.getEbayRedLinePrice()) ? 0 : goodsRedPriceDTO.getEbayRedLinePrice() );
                }
                goodsHead.setRedLinePrice(redLinePrice);
                goodsHead.setSiteCode(siteCode);
                // 设置副标题[x需要判断为null的情况，以及第一个不填只填第二个的情况取值,最好是赋值“”]，用一个list对象进行接收[已解决]
                // 设置店铺的副标题信息
                goodsHead.setShopCode(shopCodeList.get(i)).setSubtitle("".equals(titleInfoDTO.getStoreSecondName()) ? null : titleInfoDTO.getStoreSecondName());
                //设置库存
                //实际库存 大于 店铺配置库存--->取店铺配置库存
                //实际库存 小于 店铺配置库存--->取实际库存
                setStock(goodsHead, shopCodeList.get(i), PlatformTypeEnum.EB.name());
                //设置适配状态
//                goodsHead = listingInfoBiz.checkListingAdaptationBySku(goodsHead);
                String adaptationStatus = listingInfoBiz.checkListingAdaptationBySku(goodsHead.getPdmGoodsCode());
                if (ObjUtil.isNotEmpty(adaptationStatus)){
                    goodsHead.setAdaptationStatus(adaptationStatus);
                }
            }
            //amazon亚马逊处理逻辑
            else {
                goodsHead.setShopCode(shopCodeList.get(i)).
                        setTitle(titleInfoDTO.getTitle());
                // 生成亚马逊平台商品编码
                String siteCode = shopService.selectSiteCodeByShopCode(shopCodeList.get(i));
                //价格相关
                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(null,goodsHead.getPdmGoodsCode(), listingDTO.getStandardPrice(), listingDTO.getPublishType(),siteCode,null,goodsHead.getStandardPrice());
                goodsHead.setStandardPrice(price);
                goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
                GoodsRedPriceDTO goodsRedPriceDTO = pdmHttpRequestBiz.getRedLinePriceByGoodsCode(siteCode, listingDTO.getGoodsCode());
                BigDecimal amazonPrice = commonInfoBiz.getAmazonPrice(listingDTO.getPublishType(), goodsRedPriceDTO);
                goodsHead.setRedLinePrice(amazonPrice);

                goodsHead.setSiteCode(siteCode);
                // 生成亚马逊平台商品编码
                String platformCode = "";
                if (StringUtils.isNotBlank(titleInfoDTO.getPlatformGoodsCode())) {
                    platformCode = titleInfoDTO.getPlatformGoodsCode();
                } else {
                    platformCode = createPlatformCode.getPlatformCode(listingDTO.getGoodsCode(), siteCode);
                }
                //设置平台商品编码
                goodsHead.setPlatformGoodsCode(platformCode);
                //实际库存 大于 店铺配置库存--->取店铺配置库存
                //实际库存 小于 店铺配置库存--->取实际库存
                setStock(goodsHead, shopCodeList.get(i), PlatformTypeEnum.AM.name());
            }
            goodsHead.setStockOnSalesQty(ObjectUtils.isNotEmpty(goodsHead.getStockOnSalesQty()) ? goodsHead.getStockOnSalesQty() : new BigDecimal(0));
            goodsHead.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
            goodsHeadList.add(goodsHead);
        }
        return goodsHeadList;
    }

    private static ListingAmazonAttributeLineV2 getListingAmazonAttributeLineV2(ListingDTO listingDTO, PlatformCategory platformCategory, Long userId, GoodsHead goodsHead, Object value,
                                                                                AmazonAttributeEnum amazonAttributeEnum, int tableType) {
        String vcFlag = listingDTO.getShopCode().contains("VC") ? "Y" : "N";
        ListingAmazonAttributeLineV2 line = new ListingAmazonAttributeLineV2();
        line.setCreateTime(DateUtils.getNowDate());
        line.setCategoryId(platformCategory.getId().intValue());
        line.setPropNodePath(amazonAttributeEnum.getInfoV2(vcFlag));
        line.setProductType(platformCategory.getProductType());
        line.setCreateBy(String.valueOf(userId));
        line.setPdmGoodsCode(listingDTO.getGoodsCode());
        line.setTableValue(String.valueOf(value));
        line.setVcFlag(vcFlag);
        line.setTableName(amazonAttributeEnum.name());
        line.setTableType(tableType);
        line.setHeadId(goodsHead == null || goodsHead.getId() == null ? null : goodsHead.getId().longValue());
        line.setCreateBy(String.valueOf(userId));
        return line;
    }


    /**
     * 保存ebay信息 兼容单个新增和批量新增 以及自动生成
     *
     * @param item
     */
    public void saveEbayData(ItemDTO item) {
        GoodsHead goodsHead = item.getGoodsHead();
        Integer goodsId = goodsHead.getId();
        ListingEbayLine ebayLine = item.getEbayLine();
        GoodsDescription goodsDescription = item.getGoodDescription();
        GoodsSpecification goodsSpecification = item.getGoodsSpecification();
        List<GoodsResource> goodsResourceList = item.getGoodsResourceList();
        //1.插入行表
        ebayLine.setListingHeadId(goodsHead.getId());
        ebayGoodsLineService.insertListingEbayLine(ebayLine);
        //2.插入图片表
        if (ObjectUtils.isNotEmpty(goodsResourceList)) {
            goodsResourceList.forEach(p -> {
                p.setGoodsId(goodsId);
            });
            goodsResourceService.insertListingGoodsResourceBatch(goodsResourceList);
        }
        //3.插入规格表
        goodsSpecification.setGoodsId(goodsId);
        goodsSpecificationService.insertListingGoodsSpecification(goodsSpecification);
        //4.插入适配表 分销不插适配
        if (!Objects.equals(item.getIsDistribution(),SMCCommonEnum.Y.getValue())) {
            insetEbayAdaptiveData(item);
        }
        //5.插入属性表
        saveEbayAttributeLines(item);
        //6.插入描述表
        saveTemplateInfo(item.getUserId(), goodsId, ebayLine.getId(), goodsDescription);

    }


    public void updateEbayData(ItemDTO item) {
        GoodsHead goodsHead = item.getGoodsHead();
        Integer goodsId = goodsHead.getId();
        ListingEbayLine ebayLine = item.getEbayLine();
        Integer lineId = ebayLine.getId();
        GoodsDescription goodsDescription = item.getGoodDescription();
        GoodsSpecification goodsSpecification = item.getGoodsSpecification();
        List<GoodsResource> goodsResourceList = item.getGoodsResourceList();

        //1.插入行表
        ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsId);
        if (ObjectUtils.isNotEmpty(listingEbayLine)) {
            ebayLine.setId(listingEbayLine.getId());
            ebayLine.setListingHeadId(goodsHead.getId());
            ebayGoodsLineService.updateListingEbayLine(ebayLine);

            if (lineId == null) {
                lineId = listingEbayLine.getId();
            }
        } else {
            ebayLine.setListingHeadId(goodsHead.getId());
            ebayGoodsLineService.insertListingEbayLine(ebayLine);
        }

        //2.图片先删除 后保存
        goodsResourceService.deleteListingGoodsResourceByHeadId(goodsId);
        goodsResourceList.forEach(goodsResource -> {
            goodsResource.setGoodsId(goodsId);
            goodsResourceService.insertListingGoodsResource(goodsResource);
        });

        //3.更新规格表
        GoodsSpecification goodsSpecificationOld = goodsSpecificationService.selectSpecificationListByGoodsId(goodsId);
        if (!Objects.isNull(goodsSpecificationOld)) {
            goodsSpecification.setId(goodsSpecificationOld.getId());
            goodsSpecification.setUpdateBy(goodsHead.getUpdateBy());
            goodsSpecificationService.updateListingGoodsSpecification(goodsSpecification);
        } else {
            goodsSpecification.setGoodsId(goodsId);
            goodsSpecificationService.insertListingGoodsSpecification(goodsSpecification);
        }

        //5.属性表 先删除，后插入
        listingEbayValueService.deleteListingEbayValueByListingLineId(lineId);
        saveEbayAttributeLines(item);

        //6.插入描述表
        GoodsDescription goodsDescriptionOld = goodsDescriptionService.selectDescriptionListByGoodsId(goodsId);
        if (!Objects.isNull(goodsDescriptionOld)) {
            goodsDescription.setId(goodsDescriptionOld.getId());
            goodsDescriptionService.updateListingGoodsDescription(goodsDescription);
        } else {
            goodsDescription.setGoodsId(goodsId);
            goodsDescriptionService.insertListingGoodsDescription(goodsDescription);
        }
    }

    /**
     * 保存商品模板数据
     *
     * @param userId
     * @param goodsId          商品头表主键ID
     * @param goodsDescription
     */
    public void saveTemplateInfo(Long userId, Integer goodsId, Integer lineId, GoodsDescription goodsDescription) {
        String shopCode = goodsHeadService.selectShopCodeById(goodsId);

        // 付款退货
        TemplateEbayPolicy policyTemplate = templateShopRelationService.selectTemplatePolicyInfo(shopCode, "policy");
        if (Objects.nonNull(policyTemplate)) {
            ListingEbayPolicy listingEbayPolicy = new ListingEbayPolicy();
            BeanUtil.copyProperties(policyTemplate, listingEbayPolicy, "id");
            listingEbayPolicy.setPaymentPolicy(policyTemplate.getPaymentMethod());
            listingEbayPolicy.setListingLineId(lineId);
            listingEbayPolicyService.insertListingEbayPolicy(listingEbayPolicy);
        }

        saveEbayShipping(ObjUtil.isNotEmpty(userId) ? String.valueOf(userId) : String.valueOf(ShiroUtils.getSysUser().getUserId()), lineId, shopCode);
        // 描述
        goodsDescription.setGoodsId(goodsId);
        if (ObjectUtils.isEmpty(goodsDescription)) {
            TemplateEbayDescription descriptionTemplate = templateShopRelationService.selectTemplateDescriptionInfo(shopCode, "description",null);
            if (Objects.nonNull(descriptionTemplate)) {
                BeanUtil.copyProperties(descriptionTemplate, goodsDescription);
                goodsDescription.setDescriptionId(Math.toIntExact(descriptionTemplate.getId()));
                goodsDescriptionService.insertListingGoodsDescription(goodsDescription);
            }
        } else {
            goodsDescriptionService.insertListingGoodsDescription(goodsDescription);
        }
    }

    /**
     * 保存Ebay物流信息
     * @param userId
     * @param lineId
     * @param shopCode
     */

    public void saveEbayShipping(String userId, Integer lineId, String shopCode) {
        // 物流
        TemplateEbayShippingHead shippingTemplateHead = templateShopRelationService.selectTemplateShippingInfo(shopCode, "shipping");
        if (Objects.nonNull(shippingTemplateHead)) {
            ListingEbayShippingHead listingEbayShippingHead = new ListingEbayShippingHead();
            BeanUtil.copyProperties(shippingTemplateHead, listingEbayShippingHead, "id");
            listingEbayShippingHead.setListingLineId(lineId);
            listingEbayShippingHead.setCreateBy(userId);
            listingEbayShippingHeadService.insertListingEbayShippingHead(listingEbayShippingHead);


            //查询模板表中排查地区的数据
            List<TemplateShippingLocationLine> templateShippingLocationLineByHeadlist = templateShippingLocationLineService.getTemplateShippingLocationLineByHeadId(shippingTemplateHead.getId().longValue());
            if (templateShippingLocationLineByHeadlist.size() > 0) {
                List<ListingShippingLocationLine> listingShippingLocationLines = new ArrayList<>();
                templateShippingLocationLineByHeadlist.stream().forEach(p -> {
                    ListingShippingLocationLine listingShippingLocationLine = new ListingShippingLocationLine();
                    BeanUtil.copyProperties(p, listingShippingLocationLine, "id");
                    listingShippingLocationLine.setShippingHeadId(listingEbayShippingHead.getId().longValue());
                    listingShippingLocationLines.add(listingShippingLocationLine);
                });
                shippingLocationLineService.insertList(listingShippingLocationLines);
            }

            //查询模板表中物流方式的数据
            List<TemplateShippingTypeLine> templateShippingTypeLineByHeadList = templateShippingTypeLineService.getTemplateShippingTypeLineByHeadId(shippingTemplateHead.getId().longValue());
            List<ListingShippingTypeLine> listingShippingTypeLines = new ArrayList<>();
            if (templateShippingTypeLineByHeadList.size() > 0) {
                templateShippingTypeLineByHeadList.stream().forEach(p -> {
                    ListingShippingTypeLine listingShippingTypeLine = new ListingShippingTypeLine();
                    TemplateShippingTypeLine templateShippingTypeLine = p;
                    BeanUtil.copyProperties(templateShippingTypeLine, listingShippingTypeLine, "id");
                    listingShippingTypeLine.setShippingHeadId(listingEbayShippingHead.getId().longValue());
                    listingShippingTypeLines.add(listingShippingTypeLine);
                });
                shippingTypeLineService.insertList(listingShippingTypeLines);
            }
        }
    }

    public String selectGoodsAndCategoryMatch(String platformCode,String productCategoryCode,String site,String pdmGoodsCode) {
        GoodsCategoryMapping goodsCategoryMapping = goodsCategoryMappingService.selectCategoryMatch(platformCode, site, pdmGoodsCode);
        if (ObjUtil.isNotEmpty(goodsCategoryMapping)){
            return goodsCategoryMapping.getPlatformCategoryId();
        }

        if (ObjUtil.isEmpty(productCategoryCode)){
            return null;
        }
        PlatformCategory platformCategory = new PlatformCategory();
        platformCategory.setPdmCode(productCategoryCode);
        platformCategory.setPlatformCode(platformCode);
        List<PlatformCategory> list = platformCategoryService.selectPlatformCategoryList(platformCategory);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return String.valueOf(list.get(0).getId());
    }



    public Map<String, String> getEbayOtherSiteCategoryByUS(String productCategoryCode) {
        Map<String,String> map = new HashMap<>();
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(productCategoryCode));
        if (ObjUtil.isEmpty(platformCategory)){
            return map;
        }
        for (String siteCode : PlatformSiteEnum.getNameList()) {
            PlatformCategory platformCate = platformCategoryService.getPlatformCategoryByCategoryId(siteCode, platformCategory.getCategoryId());
            if ( ObjUtil.isEmpty(platformCate) ){
                continue;
            }
            map.put(siteCode, String.valueOf(platformCate.getId()));
        }
        return map;
    }


    public List<ListingAmazonAttributeLineV2> parseAmazonAttributeLines(ListingDTO listingDTO, Long userId, GoodsHead goodsHead) {
        List<ListingAmazonAttributeLineV2> amazonAttributeLines = listingDTO.getAmazonAttributeLines();
        if (CollectionUtils.isEmpty(amazonAttributeLines)) {
            return new ArrayList<>();
        }
        Integer categoryId = StringUtils.isNotEmpty(listingDTO.getProductCategoryCode()) ? Integer.valueOf(listingDTO.getProductCategoryCode()) : listingDTO.getCategoryId();

        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(categoryId.longValue());
        if (platformCategory == null) {
            throw new BusinessException("商品类目不存在");
        }

        List<ListingAmazonAttributeLineV2> attributeLines = new ArrayList<>(amazonAttributeLines.size());
        for (ListingAmazonAttributeLineV2 attributeLine : amazonAttributeLines) {
            if (StringUtils.isBlank(attributeLine.getTableValue())) {
                continue;
            }
            ListingAmazonAttributeLineV2 line = getListingAmazonAttributeLineV2(listingDTO, attributeLine, platformCategory, userId, goodsHead);
            attributeLines.add(line);
        }

        List<ListingAmazonAttributeLineV2> extendAttributeLines = getAmazonExtendAttributeLines(listingDTO, platformCategory, userId, goodsHead);
        if (CollUtil.isNotEmpty(extendAttributeLines)) {
            attributeLines.addAll(extendAttributeLines);
        }
        return attributeLines;
    }


    private List<ListingAmazonAttributeLineV2> getAmazonExtendAttributeLines(ListingDTO listingDTO, PlatformCategory platformCategory, Long userId, GoodsHead goodsHead) {
        String vcFlag = listingDTO.getShopCode().contains("VC") ? "Y" : "N";
        List<ListingAmazonAttributeLineV2> amazonAttributeLines = new ArrayList<>();

        Integer fulfillmentLatency = listingDTO.getFulfillmentLatency();
        if (ObjectUtils.isNotEmpty(fulfillmentLatency)) {
            ListingAmazonAttributeLineV2 line = getListingExtendAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, fulfillmentLatency, AmazonAttributeEnum.FULFILLMENT_LATENCY);

            amazonAttributeLines.add(line);
        }
        //平台商品编码--新增
        String externalProductId = listingDTO.getExternalProductId();
        //平台商品编码类型--新增
        String externalProductIdType = listingDTO.getExternalProductIdType();
        if (ObjectUtils.isNotEmpty(externalProductIdType) && !"GCID".equals(externalProductIdType) && StringUtils.isNotBlank(externalProductId)) {
            //类型
            ListingAmazonAttributeLineV2 lineType = getListingExtendAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, externalProductIdType.toUpperCase(), AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE);
            amazonAttributeLines.add(lineType);

            //值
            if(StringUtils.isNotBlank(externalProductId)) {
                ListingAmazonAttributeLineV2 lineValue = getListingExtendAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, externalProductId, AmazonAttributeEnum.EXTERNAL_PRODUCT_ID);
                amazonAttributeLines.add(lineValue);
            }
        }
        if(StringUtils.isNotBlank(listingDTO.getExternalAsin())) {
            //类型
            ListingAmazonAttributeLineV2 lineType = getListingAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, "ASIN", AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE,5);
            amazonAttributeLines.add(lineType);

            ListingAmazonAttributeLineV2 lineAsin = getListingAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, listingDTO.getExternalAsin(), AmazonAttributeEnum.EXTERNAL_PRODUCT_ID,5);
            amazonAttributeLines.add(lineAsin);
        }

        String salePrice = listingDTO.getSalePrice();
        String saleBeginDate = listingDTO.getSaleBeginDate();
        String saleEndDate = listingDTO.getSaleEndDate();
        if (ObjectUtils.isNotEmpty(salePrice) && ObjectUtils.isNotEmpty(saleBeginDate) && ObjectUtils.isNotEmpty(saleEndDate)) {
            ListingAmazonAttributeLineV2 salePriceLine = getListingExtendAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, salePrice, AmazonAttributeEnum.SALE_PRICE);
            amazonAttributeLines.add(salePriceLine);

            ListingAmazonAttributeLineV2 saleBeginDateLine = getListingExtendAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, saleBeginDate, AmazonAttributeEnum.SALE_FROM_DATE);
            amazonAttributeLines.add(saleBeginDateLine);

            ListingAmazonAttributeLineV2 saleEndDateLine = getListingExtendAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, saleEndDate, AmazonAttributeEnum.SALE_END_DATE);
            amazonAttributeLines.add(saleEndDateLine);

            //当前时间在saleBeginDateLine 和 saleEndDateLine 之间
            if (DateUtils.isBetween(saleBeginDate, saleEndDate) && ObjUtil.isNotEmpty(goodsHead)) {
                //校验一下sale price不能低于红线价
                Integer publishType = listingDTO.getPublishType();
                if ( ObjUtil.isNotEmpty(listingDTO.getPublishTypeName()) ){
                    publishType = PublishType.getType(listingDTO.getPublishTypeName());
                }
                commonInfoBiz.checkPriceAndReturnSellerPrice(listingDTO.getGoodsHeadId(), listingDTO.getGoodsCode(), salePrice, publishType, listingDTO.getSiteCode(), null, null);
            }
        }

        String listPrice = Objects.equals(vcFlag, "Y") ? listingDTO.getListPrice() : listingDTO.getStandardPrice();
        if (StringUtils.isNotBlank(listPrice)) {
            listPrice = new BigDecimal(listPrice).setScale(2, RoundingMode.HALF_UP).toString();
        }

        ListingAmazonAttributeLineV2 listPriceLine = getListingExtendAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, listPrice, AmazonAttributeEnum.LIST_PRICE);
        amazonAttributeLines.add(listPriceLine);
        // 单位
        ListingAmazonAttributeLineV2 unitLine = getListingAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, "USD", AmazonAttributeEnum.LIST_CURRENCY,4);
        amazonAttributeLines.add(unitLine);

        return amazonAttributeLines;
    }


    private static ListingAmazonAttributeLineV2 getListingExtendAmazonAttributeLineV2(ListingDTO listingDTO, PlatformCategory platformCategory, Long userId,
                                                                                GoodsHead goodsHead, Object value, AmazonAttributeEnum amazonAttributeEnum) {
        ListingAmazonAttributeLineV2 line = getListingAmazonAttributeLineV2(listingDTO, platformCategory, userId, goodsHead, value, amazonAttributeEnum, 4);
        return line;
    }

    public void setStock(GoodsHead goodsHead, String shopCode, String platform) {
        if (ObjUtil.isNotEmpty(shopCode) && shopCode.contains("VC")) {
            return;
        }
        //获取实际库存
        BigDecimal stockOnSalesQty = goodsHead.getStockOnSalesQty();
        //获取店铺配置库存
        ConfigStoreInfo configStoreInfo = new ConfigStoreInfo();
        configStoreInfo.setShopCode(shopCode);
        configStoreInfo.setPlatform(platform);
        List<ConfigStoreInfo> storeInfos = configStoreInfoService.selectConfigStoreInfoList(configStoreInfo);
        if (ObjectUtils.isEmpty(storeInfos)) {
            return;
        }
        ConfigStoreInfo storeInfo = storeInfos.get(0);
        if (ObjectUtils.isEmpty(storeInfo.getQuantity())) {
            return;
        }
        //实际库存 大于 店铺配置库存--->取店铺配置库存
        //实际库存 小于 店铺配置库存--->取实际库存
        if (stockOnSalesQty.compareTo(BigDecimal.valueOf(storeInfo.getQuantity())) > 0) {
            goodsHead.setStockOnSalesQty(BigDecimal.valueOf(storeInfo.getQuantity()));
        }

    }

    private static ListingAmazonAttributeLineV2 getListingAmazonAttributeLineV2(ListingDTO listingDTO, ListingAmazonAttributeLineV2 attributeLine, PlatformCategory platformCategory, Long userId, GoodsHead goodsHead) {
        String vcFlag = listingDTO.getShopCode().contains("VC") ? "Y" : "N";
        ListingAmazonAttributeLineV2 line = new ListingAmazonAttributeLineV2();
        line.setCreateTime(DateUtils.getNowDate());
        line.setCategoryId(platformCategory.getId().intValue());
        line.setPropNodePath(attributeLine.getPropNodePath());
        line.setProductType(platformCategory.getProductType());
        line.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
        line.setPdmGoodsCode(listingDTO.getGoodsCode());
        line.setTableValue(attributeLine.getTableValue());
        line.setVcFlag(vcFlag);
        line.setTableName(attributeLine.getTableName());
        line.setTableType(attributeLine.getTableType() == null ? 0 : attributeLine.getTableType());
        line.setHeadId(goodsHead == null || goodsHead.getId() == null ? null : goodsHead.getId().longValue());
        line.setCreateBy(String.valueOf(userId));
        return line;
    }
}