package com.suncent.smc.provider.biz.configuration;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ebay.soap.eBLBaseComponents.*;
import com.suncent.smc.common.enums.PlatformSiteEnum;
import com.suncent.smc.common.enums.PublishStatus;
import com.suncent.smc.common.enums.PublishType;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.persistence.api.domain.EbayCompatibilityProperties;
import com.suncent.smc.persistence.api.domain.EbayShippingExcludeLocation;
import com.suncent.smc.persistence.api.service.IEbayShippingExcludeLocationService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.ebay.domain.*;
import com.suncent.smc.persistence.ebay.service.*;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.ListingEbayValue;
import com.suncent.smc.persistence.template.domain.entity.RenderTemplateDescriptionToHtml;
import com.suncent.smc.persistence.template.domain.entity.TemplateEbayPolicy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * V2版本 组装ebay数据Biz
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BuildEbayItemV2Biz extends BuildEbayItemBiz {
    @Resource
    protected IEbayGoodsDescriptionV2Service ebayGoodsDescriptionV2Service;
    @Resource
    protected IEbayListingAdaptiveV2Service ebayListingAdaptiveV2Service;
    @Resource
    protected IEbayListingDetailV2Service ebayListingDetailV2Service;
    @Resource
    protected IEbayListingGoodsHeadV2Service ebayListingGoodsHeadV2Service;
    @Resource
    protected IEbayListingGoodsResourceV2Service ebayListingGoodsResource;
    @Resource
    protected IEbayListingLocationV2Service ebayLocationV2Service;
    @Resource
    protected IEbayListingLogV2Service ebayListingLogV2Service;
    @Resource
    protected IEbayListingPolicyV2Service ebayListingPolicyV2Service;
    @Resource
    protected IEbayListingShippingV2Service ebayListingShippingV2Service;
    @Resource
    protected IEbayListingSpecAttributeV2Service ebayListingSpecAttributeV2Service;
    @Resource
    protected IEbayShippingExcludeLocationService ebayShippingExcludeLocationService;
    @Resource
    protected IEbayListingSpecsItemV2Service ebayListingSpecsItemV2Service;
    @Resource
    protected IEbayListingValueV2Service ebayValueV2Service;
    /**
     * 封装配置属性对象的方法
     *
     * @param ebayListingValueV2s
     * @return
     */
    public static NameValueListArrayType getNameValueListArrayTypeV2(List<EbayListingValueV2> ebayListingValueV2s) {
        NameValueListArrayType nameValueListArrayType = new NameValueListArrayType();
        NameValueListType[] nameValueListTypes = new NameValueListType[ebayListingValueV2s.size()];
        for (int i = 0; i < ebayListingValueV2s.size(); i++) {
            //判断属性中是否有, 并且属性值长度大于65字符 按照,分割
            if (ObjectUtils.isEmpty(ebayListingValueV2s.get(i)) || ObjectUtils.isEmpty(ebayListingValueV2s.get(i).getValue())) {
                continue;
            }
            // 不区分大小写包含
            if (!getEbayOnlyOneValue().contains(ebayListingValueV2s.get(i).getName().toUpperCase()) && ebayListingValueV2s.get(i).getValue().length() >= 65 && ebayListingValueV2s.get(i).getValue().contains(",")) {

                String[] split = ebayListingValueV2s.get(i).getValue().split(",");
                // 对分割后的每个值进行长度检查，如果超过65字符则从后往前找空格再次分割
                List<String> processedSplit = new ArrayList<>();
                for (String splitValue : split) {
                    String trimmedValue = splitValue.trim();
                    if (trimmedValue.length() > 65) {
                        // 从后往前找倒数第一个空格
                        int lastSpaceIndex = trimmedValue.lastIndexOf(' ');
                        if (lastSpaceIndex > 0) {
                            // 在空格处分割
                            processedSplit.add(trimmedValue.substring(0, lastSpaceIndex).trim());
                            processedSplit.add(trimmedValue.substring(lastSpaceIndex + 1).trim());
                        } else {
                            // 没有空格，直接添加
                            processedSplit.add(trimmedValue);
                        }
                    } else {
                        processedSplit.add(trimmedValue);
                    }
                }

                NameValueListType nameValueListType = new NameValueListType();
                nameValueListType.setName(ebayListingValueV2s.get(i).getName());
                if (processedSplit.size() > 26) {
                    List<String> valueList = new ArrayList<>();
                    StringBuilder value = new StringBuilder(ObjUtil.isNotEmpty(processedSplit.get(0)) ? processedSplit.get(0).trim() : "");
                    for (int j = 1; j < processedSplit.size(); j++) {
                        String currentValue = processedSplit.get(j);
                        if (value.length() + currentValue.length() <= 60) {
                            value.append(" ").append(currentValue);
                            continue;
                        }
                        valueList.add(value.toString());
                        value = new StringBuilder(ObjUtil.isNotEmpty(currentValue) ? currentValue.trim() : "");
                    }
                    nameValueListType.setValue(valueList.toArray(new String[valueList.size()]));
                } else {
                    nameValueListType.setValue(processedSplit.toArray(new String[processedSplit.size()]));
                }
                nameValueListTypes[i] = nameValueListType;
                continue;
            }
            NameValueListType nameValueListType1 = new NameValueListType();
            nameValueListType1.setName(ebayListingValueV2s.get(i).getName());
            nameValueListType1.setValue(new String[]{ebayListingValueV2s.get(i).getValue()});
            nameValueListTypes[i] = nameValueListType1;
        }
        nameValueListArrayType.setNameValueList(nameValueListTypes);
        return nameValueListArrayType;
    }

    /**
     * 重写v2版本组装数据
     *
     * @param listingHeadId
     * @param type
     * @return
     * @throws Exception
     */
    @Override
    public ItemType buildEbayItem(Integer listingHeadId, String type) throws Exception {
        ItemType itemType = new ItemType();
        //头数据
        EbayGoodsHeadV2 goodsHead = ebayListingGoodsHeadV2Service.selectEbayListingGoodsHeadV2ById(Long.valueOf(listingHeadId));
        Long goodsHeadId = goodsHead.getId();
        //规格项
        List<EbayListingSpecsItemV2> ebayListingSpecsItemV2s = ebayListingSpecsItemV2Service.selectEbayListingSpecsItemV2ListByHeadId(goodsHeadId);
        for (EbayListingSpecsItemV2 specItem : ebayListingSpecsItemV2s) {
            //校验价格 ebayV2
            commonInfoBiz.checkPriceAndReturnSellerPrice(null,specItem.getPdmGoodsCode(), specItem.getPrice(), goodsHead.getPublishType(), goodsHead.getSiteCode(), goodsHead.getOnlineTime(), specItem.getPrice());
        }
        //规格属性
        List<EbayListingSpecAttributeV2> ebayListingSpecAttributeV2s = ebayListingSpecAttributeV2Service.selectEbayListingAttributeV2ListByHeadId(goodsHeadId);
        //描述信息
        EbayGoodsDescriptionV2 ebayGoodsDescriptionV2 = ebayGoodsDescriptionV2Service.selectDescriptionListByGoodsId(goodsHeadId);
        //拓展信息
        EbayListingDetailV2 ebayListingDetailV2 = ebayListingDetailV2Service.selectEbayListingDetailV2HeadId(goodsHeadId);
        //物流信息
        List<EbayListingShippingV2> ebayListingShippingV2s = ebayListingShippingV2Service.selectEbayListingShippingV2ByHeadId(goodsHeadId);
        //排除地区
        List<EbayListingLocationV2> ebayListingLocationV2s = ebayLocationV2Service.selectEbayListingShippingV2ByHeadId(goodsHeadId);
        //属性
        List<EbayListingValueV2> ebayListingValueV2s = ebayValueV2Service.selectEbayListingValueV2ByHeadId(goodsHeadId);
        //适配信息
        List<EbayListingAdaptiveV2> ebayListingAdaptiveV2s = getListingEbayAdaptiveV2(goodsHead);
        //付款政策信息
        EbayListingPolicyV2 ebayListingPolicyV2 = ebayListingPolicyV2Service.selectEbayListingPolicyV2ByHeadId(goodsHeadId);
        //图片信息
        List<EbayListingGoodsResourceV2> ebayListingGoodsResourceV2s = ebayListingGoodsResource.selectEbayListingGoodsResourceV2ByHeadId(Collections.singletonList(goodsHeadId), null);
        //ebay排除地区
        List<EbayShippingExcludeLocation> ebayShippingExcludeLocationList =
                ebayShippingExcludeLocationService.getEbayShippingExcludeListByRegionAndSiteCode(null, goodsHead.getSiteCode());
        //取反拿到排除地区
        ebayListingLocationV2s = getEbayShippingExcludeLocationList(ebayListingLocationV2s, ebayShippingExcludeLocationList, Long.valueOf(listingHeadId));
        //校验本地数据是否完整
        String errorLog = verifyListingV2(type, goodsHead, ebayListingSpecsItemV2s, ebayListingSpecAttributeV2s, ebayGoodsDescriptionV2, ebayListingDetailV2,
                ebayListingShippingV2s, ebayListingLocationV2s, ebayListingValueV2s, ebayListingAdaptiveV2s, ebayListingPolicyV2, ebayListingGoodsResourceV2s);
        if (ObjectUtils.isNotEmpty(errorLog)) {
            throw new BusinessException(errorLog);
        }
        //图片EPS上传换链
        imageHandleBiz.replaceEbayResourcesUrl(ebayListingGoodsResourceV2s, goodsHead.getShopCode());

        //根据头表组装数据
        setItemByHead(itemType, goodsHead);
        //根据拓展表组装数据
        setItemByDetail(itemType, goodsHead, ebayListingDetailV2, ebayListingSpecsItemV2s);
        //根据属性组装数据
        setItemByValue(itemType, goodsHead, ebayListingValueV2s);
        //根据图片组装数据
        setItemByResources(itemType, ebayListingGoodsResourceV2s);
        //根据适配组装数据
        setItemByAdaptive(itemType, goodsHead, ebayListingAdaptiveV2s);
        //根据描述组装数据
        setItemDescription(itemType, goodsHead, ebayListingGoodsResourceV2s, ebayListingValueV2s, ebayGoodsDescriptionV2);
        //根据物流组装数据
        setItemByShipping(itemType, CurrencyCodeType.fromValue(ebayListingDetailV2.getCurrency()), ebayListingShippingV2s, ebayListingLocationV2s);
        //根据付款组装数据
        setItemByPayment(itemType, goodsHead, ebayListingPolicyV2);
        //根据多属性信息组装数据
        setItemBySpec(itemType, goodsHead, ebayListingSpecsItemV2s, ebayListingGoodsResourceV2s, ebayListingSpecAttributeV2s);


        return itemType;
    }

    /**
     * 根据数据库存的相反的数据, 取反
     *
     * @param ebayListingLocationV2s
     * @param ebayShippingExcludeLocationList
     * @param goodsId
     * @return
     */
    private List<EbayListingLocationV2> getEbayShippingExcludeLocationList(List<EbayListingLocationV2> ebayListingLocationV2s, List<EbayShippingExcludeLocation> ebayShippingExcludeLocationList, Long goodsId) {
        List<String> locationList = ebayListingLocationV2s.stream().map(EbayListingLocationV2::getLocation).distinct().collect(Collectors.toList());
        List<EbayListingLocationV2> dbLocationLineSet = new ArrayList<>();
        ebayShippingExcludeLocationList.stream()
                .map(location -> {
                    if (ObjUtil.isNotEmpty(locationList) && locationList.contains(location.getLocation())) {
                        return new EbayListingLocationV2();
                    }
                    EbayListingLocationV2 locationLine = new EbayListingLocationV2();
                    locationLine.setHeadId(goodsId);
                    locationLine.setRegion(location.getRegion());
                    locationLine.setLocation(location.getLocation());
                    return locationLine;
                })
                .forEach(dbLocationLineSet::add);
        return dbLocationLineSet;
    }

    /**
     * 校验数据是否完整
     *
     * @param type
     * @param goodsHead
     * @param ebayListingSpecsItemV2s
     * @param ebayListingSpecAttributeV2s
     * @param ebayGoodsDescriptionV2
     * @param ebayListingDetailV2
     * @param ebayListingShippingV2s
     * @param ebayListingLocationV2s
     * @param ebayListingValueV2s
     * @param ebayListingAdaptiveV2s
     * @param ebayListingPolicyV2
     * @param ebayListingGoodsResourceV2s
     * @return
     */
    private String verifyListingV2(String type, EbayGoodsHeadV2 goodsHead, List<EbayListingSpecsItemV2> ebayListingSpecsItemV2s, List<EbayListingSpecAttributeV2> ebayListingSpecAttributeV2s,
                                   EbayGoodsDescriptionV2 ebayGoodsDescriptionV2, EbayListingDetailV2 ebayListingDetailV2, List<EbayListingShippingV2> ebayListingShippingV2s,
                                   List<EbayListingLocationV2> ebayListingLocationV2s, List<EbayListingValueV2> ebayListingValueV2s, List<EbayListingAdaptiveV2> ebayListingAdaptiveV2s,
                                   EbayListingPolicyV2 ebayListingPolicyV2, List<EbayListingGoodsResourceV2> ebayListingGoodsResourceV2s) {


        StringBuilder errorLog = new StringBuilder();
        if (ObjectUtils.isEmpty(ebayGoodsDescriptionV2) || (ObjectUtils.isEmpty(ebayGoodsDescriptionV2.getDetailDescription()) && ObjectUtils.isEmpty(ebayGoodsDescriptionV2.getShortDescription()))) {
            errorLog.append("商品描述信息、");
        }
        if (ObjectUtils.isEmpty(ebayListingSpecsItemV2s)) {
            errorLog.append("ebay规格信息、");
        }
        if (PublishType.POLYTROPIC.getType().equals((goodsHead.getPublishType())) && ObjectUtils.isEmpty(ebayListingSpecAttributeV2s)) {
            errorLog.append("ebay规格属性信息、");
        }
        if (ObjectUtils.isEmpty(ebayListingDetailV2)) {
            errorLog.append("ebay拓展信息、");
        }
        if (ObjectUtils.isEmpty(ebayListingShippingV2s)) {
            errorLog.append("ebay物流信息、");
        }
        if (ObjectUtils.isEmpty(ebayListingLocationV2s)) {
            errorLog.append("ebay排除地区、");
        }
        if (ObjectUtils.isEmpty(ebayListingValueV2s)) {
            errorLog.append("ebay属性、");
        }
        if (ObjectUtils.isEmpty(ebayListingAdaptiveV2s)) {
            errorLog.append("ebay适配信息、");
        }
        if (ObjectUtils.isEmpty(ebayListingPolicyV2)) {
            errorLog.append("ebay付款政策信息、");
        }
        if (ObjectUtils.isEmpty(ebayListingGoodsResourceV2s)) {
            errorLog.append("ebay图片信息、");
        }
        if (errorLog.length() == 0) {
            return null;
        }
        if (!StringUtils.isNotBlank(type) || !type.equals("inspect")) {
            //记录操作日志
            ebayListingLogV2Service.insertErrorListingLog("ebay商品送检组装数据失败", StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy(), goodsHead.getId(), errorLog + "数据不完整,请检查数据,请联系管理员编辑再次刊登");

            goodsHead.setPublishStatus(String.valueOf(PublishStatus.getStatusByApi(Integer.valueOf(goodsHead.getPublishStatus()), false)));
            ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(goodsHead);
        }
        return errorLog + "数据不完整,请检查数据,请联系管理员编辑再次刊登";


    }

    private static NameValueListArrayType getVariationSpecifics(EbayListingSpecsItemV2 ebayListingSpecsItemV2, List<EbayListingSpecAttributeV2> ebayListingSpecAttributeV2s) {
        NameValueListArrayType variationSpecifics = new NameValueListArrayType();
        String specAttributeIds = ebayListingSpecsItemV2.getSpecAttributeIds();
        List<Long> specAttributeIdList = JSON.parseArray(specAttributeIds, Long.class);
        if (ObjUtil.isEmpty(specAttributeIdList)) {
            return null;
        }
        List<NameValueListType> nameValueListTypes = new ArrayList<>();
        List<EbayListingSpecAttributeV2> specAttributeV2s = ebayListingSpecAttributeV2s.stream().filter(e -> specAttributeIdList.contains(e.getId())).collect(Collectors.toList());
        for (EbayListingSpecAttributeV2 specAttributeV2 : specAttributeV2s) {
            NameValueListType nameValueListType = new NameValueListType();
            nameValueListType.setName(specAttributeV2.getSpecificName());
            nameValueListType.setValue(new String[]{specAttributeV2.getSpecificValue()});
            nameValueListTypes.add(nameValueListType);
        }
        variationSpecifics.setNameValueList(nameValueListTypes.toArray(new NameValueListType[nameValueListTypes.size()]));
        return variationSpecifics;
    }

    private void setItemBySpec(ItemType itemType, EbayGoodsHeadV2 goodsHead, List<EbayListingSpecsItemV2> ebayListingSpecsItemV2s, List<EbayListingGoodsResourceV2> ebayListingGoodsResourceV2s, List<EbayListingSpecAttributeV2> ebayListingSpecAttributeV2s) {
        if (!PublishType.POLYTROPIC.getType().equals((goodsHead.getPublishType()))) {
            return;
        }
        if (CollectionUtils.isEmpty(ebayListingSpecsItemV2s)) {
            return;
        }
        VariationsType variationsType = new VariationsType();
        //多属性价格、库存等信息
        variationsType.setVariation(getVariation(goodsHead, ebayListingSpecsItemV2s, ebayListingSpecAttributeV2s));
        //多属性规格
        variationsType.setVariationSpecificsSet(getVariationSpecificsSetType(ebayListingSpecAttributeV2s));
        //多属性规格图片
        variationsType.setPictures(getVariationPictures(ebayListingSpecAttributeV2s, ebayListingGoodsResourceV2s));

        itemType.setVariations(variationsType);

    }

    public static ProductListingDetailsType getProductListingDetailsTypeV2(EbayGoodsHeadV2 goodsHead, List<EbayListingValueV2> ebayListingValueV2s) {
        ProductListingDetailsType productListingDetailsType = new ProductListingDetailsType();
        productListingDetailsType.setIncludeeBayProductDetails(true);
        String upc = ebayListingValueV2s.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("UPC")).findFirst().orElse(new EbayListingValueV2()).getValue();
        String ean = ebayListingValueV2s.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("EAN")).findFirst().orElse(new EbayListingValueV2()).getValue();
        String brand = ebayListingValueV2s.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("Brand") || listingEbayValue.getName().equals("Hersteller"))
                .findFirst().orElse(new EbayListingValueV2()).getValue();
        String mpn = ebayListingValueV2s.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("Manufacturer Part Number") ||
                listingEbayValue.getName().contains("MPN") || listingEbayValue.getName().contains("Herstellernummer")).findFirst().orElse(new EbayListingValueV2()).getValue();
        //德站
        if (Objects.equals(goodsHead.getSiteCode(), PlatformSiteEnum.DE.name())) {
            if (StringUtils.isEmpty(ean)) {
                return null;
            }
            productListingDetailsType.setEAN(ean);
            BrandMPNType brandMPNType = new BrandMPNType();
            brandMPNType.setBrand(brand);
            brandMPNType.setMPN(mpn);
            productListingDetailsType.setBrandMPN(brandMPNType);
            return productListingDetailsType;
        }
        //美站
        if (Objects.equals(goodsHead.getSiteCode(), PlatformSiteEnum.US.name())) {
            if (StringUtils.isEmpty(upc) || StringUtils.isEmpty(brand) || StringUtils.isEmpty(mpn)) {
                return null;
            }
            productListingDetailsType.setUPC(upc);
            BrandMPNType brandMPNType = new BrandMPNType();
            brandMPNType.setBrand(brand);
            brandMPNType.setMPN(mpn);
            productListingDetailsType.setBrandMPN(brandMPNType);
        }
        return productListingDetailsType;
    }

    /**
     * 获取多属性规格图片
     *
     * @param ebayListingSpecAttributeV2s
     * @param ebayListingGoodsResourceV2s
     * @return
     */
    private PicturesType[] getVariationPictures(List<EbayListingSpecAttributeV2> ebayListingSpecAttributeV2s, List<EbayListingGoodsResourceV2> ebayListingGoodsResourceV2s) {
        if (ObjUtil.isEmpty(ebayListingSpecAttributeV2s) || ObjUtil.isEmpty(ebayListingGoodsResourceV2s)) {
            return null;
        }
        //按照排序分组
        List<PicturesType> pictures = new ArrayList<>();
        Map<Integer, List<EbayListingSpecAttributeV2>> groupMap = ebayListingSpecAttributeV2s.stream()
                .collect(Collectors.groupingBy(EbayListingSpecAttributeV2::getIndexFlag, TreeMap::new, Collectors.toList()));
        groupMap.forEach((index, groupSpecAttr) -> {
            String specificName = groupSpecAttr.stream().map(EbayListingSpecAttributeV2::getSpecificName).findFirst().orElse(null);
            //取对应规格的图片
            List<EbayListingGoodsResourceV2> groupResource = ebayListingGoodsResourceV2s.stream()
                    .filter(resource -> groupSpecAttr.stream().map(EbayListingSpecAttributeV2::getId).collect(Collectors.toList())
                            .contains(resource.getSpecAttributeId()))
                    .collect(Collectors.toList());
            if (ObjUtil.isEmpty(groupResource)) {
                return;
            }
            PicturesType picturesType = new PicturesType();
            picturesType.setVariationSpecificName(specificName);
            picturesType.setVariationSpecificPictureSet(getVariationSpecificPictureSet(groupSpecAttr, groupResource));
            pictures.add(picturesType);

        });
        return pictures.toArray(new PicturesType[groupMap.size()]);
    }

    /**
     * 获取variationSpecificPictureSet
     *
     * @param groupSpecAttr
     * @param groupResource
     * @return
     */
    private VariationSpecificPictureSetType[] getVariationSpecificPictureSet(List<EbayListingSpecAttributeV2> groupSpecAttr, List<EbayListingGoodsResourceV2> groupResource) {
        if (ObjUtil.isEmpty(groupSpecAttr) || ObjUtil.isEmpty(groupResource)) {
            return null;
        }
        List<VariationSpecificPictureSetType> variationSpecificPictureSetTypes = new ArrayList<>();
        for (EbayListingSpecAttributeV2 specAttributeV2 : groupSpecAttr) {
            String[] pictureUrl = groupResource.stream().filter(resource -> resource.getSpecAttributeId().equals(specAttributeV2.getId()))
                    .map(EbayListingGoodsResourceV2::getResourceUrl).collect(Collectors.toList()).toArray(new String[]{});
            if (ObjUtil.isEmpty(pictureUrl)) {
                continue;
            }
            VariationSpecificPictureSetType variationSpecificPictureSetType = new VariationSpecificPictureSetType();
            variationSpecificPictureSetType.setVariationSpecificValue(specAttributeV2.getSpecificValue());
            variationSpecificPictureSetType.setPictureURL(pictureUrl);
            variationSpecificPictureSetTypes.add(variationSpecificPictureSetType);
        }
        return variationSpecificPictureSetTypes.toArray(new VariationSpecificPictureSetType[groupSpecAttr.size()]);
    }

    private NameValueListArrayType getVariationSpecificsSetType(List<EbayListingSpecAttributeV2> ebayListingSpecAttributeV2s) {
        if (ObjUtil.isEmpty(ebayListingSpecAttributeV2s)) {
            return null;
        }
        NameValueListArrayType nameValueListArrayType = new NameValueListArrayType();
        List<NameValueListType> nameValueList = new ArrayList<>();
        //按照排序分组
        Map<Integer, List<EbayListingSpecAttributeV2>> groupMap = ebayListingSpecAttributeV2s.stream()
                .collect(Collectors.groupingBy(EbayListingSpecAttributeV2::getIndexFlag, TreeMap::new, Collectors.toList()));

        groupMap.forEach((index, value) -> {
            String specificName = value.stream().map(EbayListingSpecAttributeV2::getSpecificName).findFirst().orElse(null);
            NameValueListType nameValueListType = new NameValueListType();
            nameValueListType.setName(specificName);
            nameValueListType.setValue(value.stream().map(EbayListingSpecAttributeV2::getSpecificValue).toArray(String[]::new));
            nameValueList.add(nameValueListType);
        });
        nameValueListArrayType.setNameValueList(nameValueList.toArray(new NameValueListType[nameValueList.size()]));
        return nameValueListArrayType;
    }

    /**
     * 规格的价格库存等信息
     *
     * @param goodsHead
     * @param ebayListingSpecsItemV2s
     * @return
     */
    public VariationType[] getVariation(EbayGoodsHeadV2 goodsHead, List<EbayListingSpecsItemV2> ebayListingSpecsItemV2s, List<EbayListingSpecAttributeV2> ebayListingSpecAttributeV2s) {
        if (ObjUtil.isEmpty(goodsHead) || ObjUtil.isEmpty(ebayListingSpecsItemV2s)) {
            return null;
        }
        List<VariationType> variations = new ArrayList<>();
        for (EbayListingSpecsItemV2 ebayListingSpecsItemV2 : ebayListingSpecsItemV2s) {
            VariationType variationType = new VariationType();
            variationType.setSKU(ebayListingSpecsItemV2.getPdmGoodsCode());
            variationType.setStartPrice(getAmountType(Double.parseDouble(ebayListingSpecsItemV2.getPrice()), CurrencyCodeType.fromValue(ebayListingSpecsItemV2.getCurrency())));
            variationType.setQuantity(getQuantity(goodsHead.getPublishType(), BigDecimal.valueOf(Double.parseDouble(ebayListingSpecsItemV2.getStock()))));
            //处理规格详情
            if (ObjUtil.isNotEmpty(ebayListingSpecAttributeV2s)) {
                variationType.setVariationSpecifics(getVariationSpecifics(ebayListingSpecsItemV2, ebayListingSpecAttributeV2s));
            }
            //处理规格UPC
            if (ObjUtil.isNotEmpty(ebayListingSpecsItemV2.getUpc())) {
                VariationProductListingDetailsType variationProductListingDetailsType = new VariationProductListingDetailsType();
                variationProductListingDetailsType.setUPC(ebayListingSpecsItemV2.getUpc());
                variationType.setVariationProductListingDetails(variationProductListingDetailsType);
            }
            variations.add(variationType);
        }
        return variations.toArray(new VariationType[ebayListingSpecsItemV2s.size()]);
    }

    /**
     * 获取退货政策描述
     *
     * @param shopCode
     * @param ebayListingPolicyV2
     * @return
     */
    private String getReturnsDescription(String shopCode, EbayListingPolicyV2 ebayListingPolicyV2) {
        if (ObjUtil.isEmpty(ebayListingPolicyV2.getReturnsDescription())) {
            TemplateEbayPolicy templateEbayPolicy = templateShopRelationService.selectTemplatePolicyInfo(shopCode, "policy");
            if (ObjectUtils.isEmpty(templateEbayPolicy) || ObjectUtils.isEmpty(templateEbayPolicy.getReturnsDescription())) {
                return null;
            }
            ebayListingPolicyV2.setReturnsDescription(templateEbayPolicy.getReturnsDescription());
            ebayListingPolicyV2Service.updateEbayListingPolicyV2(ebayListingPolicyV2);
        }
        return ebayListingPolicyV2.getReturnsDescription();
    }

    public void setItemByPayment(ItemType itemType, EbayGoodsHeadV2 goodsHead, EbayListingPolicyV2 ebayListingPolicyV2) {
        ReturnPolicyType returnPolicyType = new ReturnPolicyType();
        //指定买家退货的时间量 domestic_return_day
        //适用值为或。设置为 时，此选项表示卖家允许退回商品。如果不接受退货，请为商品指定退货不接受 domestic_return_flag
        returnPolicyType.setReturnsAcceptedOption("1".equals(ebayListingPolicyV2.getDomesticReturnFlag()) ? "ReturnsAccepted" : "ReturnsNotAccepted");
        if ("1".equals(ebayListingPolicyV2.getDomesticReturnFlag())) {
            returnPolicyType.setShippingCostPaidByOption(ebayListingPolicyV2.getDomesticReturnBearer());
            returnPolicyType.setReturnsWithinOption("Days_" + ebayListingPolicyV2.getDomesticReturnDay().replaceAll("\\D", ""));
            returnPolicyType.setReturnsWithin(ebayListingPolicyV2.getDomesticReturnDay() + " Days");
        }
        //指定卖家支付运费还是买家支付运费          domestic_return_bearer
        returnPolicyType.setInternationalReturnsAcceptedOption("1".equals(ebayListingPolicyV2.getInternationalReturnFlag()) ? "ReturnsAccepted" : "ReturnsNotAccepted");
        if ("1".equals(ebayListingPolicyV2.getInternationalReturnFlag())) {
            returnPolicyType.setShippingCostPaidBy(ebayListingPolicyV2.getInternationalReturnBearer());
            returnPolicyType.setInternationalReturnsWithinOption("Days_" + ebayListingPolicyV2.getInternationalReturnDay().replaceAll("\\D", ""));
            returnPolicyType.setInternationalShippingCostPaidByOption(ebayListingPolicyV2.getInternationalReturnBearer());
        }
        returnPolicyType.setDescription(getReturnsDescription(goodsHead.getShopCode(), ebayListingPolicyV2));
        itemType.setReturnPolicy(returnPolicyType);
    }

    public void setItemByShipping(ItemType itemType, CurrencyCodeType currencyCode, List<EbayListingShippingV2> ebayListingShippingV2s, List<EbayListingLocationV2> ebayListingLocationV2s) {
        ShippingDetailsType shippingDetailsType = new ShippingDetailsType();
        List<ShippingServiceOptionsType> shippingServiceOptionsType = new ArrayList<>();
        Stream.iterate(1, i -> i + 1).limit(ebayListingShippingV2s.size()).forEach(index -> {
            EbayListingShippingV2 p = ebayListingShippingV2s.get(index - 1);
            shippingServiceOptionsType.add(getShippingServiceOptionsType(p.getShippingService(), getAmountType(Double.valueOf(String.valueOf(ObjectUtils.isNotEmpty(p.getShippingCost()) ? p.getShippingCost() : BigDecimal.ONE)), currencyCode), index, Boolean.FALSE, Integer.valueOf(p.getShippingTimeMin()), Integer.valueOf(p.getShippingTimeMax()), "1".equals(p.getFreeShippingFlag()) ? Boolean.TRUE : Boolean.FALSE, currencyCode));
        });


        ShippingServiceOptionsType[] shippingServiceOptionsTypes = new ShippingServiceOptionsType[shippingServiceOptionsType.size()];
        Stream.iterate(1, i -> i + 1).limit(shippingServiceOptionsType.size()).forEach(index -> {
            shippingServiceOptionsTypes[index - 1] = shippingServiceOptionsType.get(index - 1);
        });

        List<String> excldeLocations = new ArrayList<>();
        ebayListingLocationV2s.stream().forEach(lo -> {
            excldeLocations.add(lo.getLocation());
        });
        List<String> arrayList = new ArrayList(new HashSet(excldeLocations));
        String[] excludeShipToLocation = arrayList.toArray(new String[arrayList.size()]);
//        String[] excludeShipToLocation = {"Alaska/Hawaii", "APO/FPO", "US Protectorates", "Africa", "Asia", "Central America and Caribbean", "Europe", "Middle East", "North America", "Oceania", "Southeast Asia", "South America", "PO Box"};

        //配置物流服务方式
        shippingDetailsType.setShippingServiceOptions(shippingServiceOptionsTypes);
        //物流方式   sc_smc_listing_shipping_type_line    shipping_type 暂时写死
        shippingDetailsType.setShippingType(ShippingTypeCodeType.FLAT);
        //排除地区国家
        shippingDetailsType.setExcludeShipToLocation(excludeShipToLocation);
        // 刊登时用不到
//        SalesTaxType salesTaxType = new SalesTaxType();
//        AmountType amountType3 = getAmountType(0.0d, null);
//        salesTaxType.setShippingIncludedInTax(Boolean.FALSE);
//        salesTaxType.setSalesTaxAmount(amountType3);
//        shippingDetailsType.setSalesTax(salesTaxType);
//        // 输入时，这是为国内配送服务提供的运费折扣的 ID（其中运费折扣类型为“固定运费折扣”或“计算运费折扣”类型）。
//        shippingDetailsType.setShippingDiscountProfileID("0");
//        //输入时，这是为国际运输服务提供的运输折扣的 ID（其中运输折扣类型为“固定运输折扣”或“计算运输折扣”类型）。
//        shippingDetailsType.setInternationalShippingDiscountProfileID("0");
        //新增时不用此字段
//        shippingDetailsType.setSellerExcludeShipToLocationsPreference(Boolean.TRUE);

        itemType.setShippingDetails(shippingDetailsType);
    }

    private List<ListingEbayValue> ebayValueConverter(List<EbayListingValueV2> ebayListingValueV2s) {
        if (ObjUtil.isEmpty(ebayListingValueV2s)) {
            return null;
        }
        if (CollUtil.isNotEmpty(ebayListingValueV2s)) {
            return ebayListingValueV2s.stream().map(listingEbayValue -> {
                ListingEbayValue l = new ListingEbayValue();
                l.setName(listingEbayValue.getName());
                l.setValue(listingEbayValue.getValue());
                return l;
            }).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 组装属性数据
     *
     * @param itemType
     * @param goodsHead
     * @param ebayListingValueV2s
     */
    private void setItemByValue(ItemType itemType, EbayGoodsHeadV2 goodsHead, List<EbayListingValueV2> ebayListingValueV2s) {
        //物品属性
        itemType.setItemSpecifics(getNameValueListArrayTypeV2(ebayListingValueV2s));
        //特定的产品列表详情  品牌 以及upc mpn等
        itemType.setProductListingDetails(getProductListingDetailsTypeV2(goodsHead, ebayListingValueV2s));
    }

    /**
     * 根据描述组装数据
     *
     * @param itemType
     * @param goodsHead
     * @param ebayListingGoodsResourceV2s
     * @param ebayListingValueV2s
     * @param ebayGoodsDescriptionV2
     */
    public void setItemDescription(ItemType itemType, EbayGoodsHeadV2 goodsHead, List<EbayListingGoodsResourceV2> ebayListingGoodsResourceV2s, List<EbayListingValueV2> ebayListingValueV2s, EbayGoodsDescriptionV2 ebayGoodsDescriptionV2) {
        if (ObjectUtils.isEmpty(ebayGoodsDescriptionV2)) {
            return;
        }
        //对描述中的图片进行http换链成https处理
        ebayGoodsDescriptionV2.setDetailDescription(commonInfoBiz.replaceHttpToHttps(ebayGoodsDescriptionV2.getDetailDescription()));
        ebayGoodsDescriptionV2.setShortDescription(commonInfoBiz.replaceHttpToHttps(ebayGoodsDescriptionV2.getShortDescription()));
        ebayGoodsDescriptionV2Service.updateListingGoodsDescription(ebayGoodsDescriptionV2);
        if (ObjectUtils.isEmpty(ebayGoodsDescriptionV2.getDescriptionId())) {
            itemType.setDescription(ebayGoodsDescriptionV2.getDetailDescription());
            return;
        }
        //找到对应的模板
        RenderTemplateDescriptionToHtml toHtmlDTO = new RenderTemplateDescriptionToHtml();
        toHtmlDTO.setTemplateId(String.valueOf(ebayGoodsDescriptionV2.getDescriptionId()));
        toHtmlDTO.setImageSrcArr(CollUtil.isNotEmpty(ebayListingGoodsResourceV2s) ? ebayListingGoodsResourceV2s.stream().map(EbayListingGoodsResourceV2::getResourceUrl).collect(Collectors.toList()) : null);
        toHtmlDTO.setOnlineDesc(ebayGoodsDescriptionV2.getShortDescription());
        //
        toHtmlDTO.setAttributeJsonArr(ebayValueConverter(ebayListingValueV2s));
        toHtmlDTO.setTitle(goodsHead.getTitle());
        String html = templateEbayDescriptionBiz.renderTemplateDescriptionToHtml(toHtmlDTO);
        itemType.setDescription(commonInfoBiz.replaceHttpToHttps(html));

    }

    /**
     * 组装适配数据
     *
     * @param itemType
     * @param goodsHead
     * @param ebayListingAdaptiveV2s
     */
    private void setItemByAdaptive(ItemType itemType, EbayGoodsHeadV2 goodsHead, List<EbayListingAdaptiveV2> ebayListingAdaptiveV2s) {
        CategoryType primaryCategory = itemType.getPrimaryCategory();
        if (ObjUtil.isEmpty(primaryCategory)) {
            throw new BusinessException("主键ID:" + goodsHead.getId() + "平台类目:" + goodsHead.getCategoryId() + "信息不存在");
        }
        //适配车型属性数据
        EbayCompatibilityProperties ebayCompatibilityProperties = retryable.retryableEps(() -> getEbayCompatibilityProperties(goodsHead.getShopCode(), goodsHead.getSiteCode(), primaryCategory.getCategoryID()));
        if (ObjectUtils.isNotEmpty(ebayCompatibilityProperties)) {
            itemType.setItemCompatibilityList(getItemCompatibilityListTypeV2(false, ebayCompatibilityProperties, ebayListingAdaptiveV2s));
        } else {
            itemType.setItemCompatibilityList(getItemCompatibilityListTypeV2(false, getDefaultBySite(goodsHead.getSiteCode()), ebayListingAdaptiveV2s));
        }
    }

    /**
     * 封装适配车型对象的方法V2
     *
     * @param isReplaceAll                是否全部覆盖
     * @param ebayCompatibilityProperties
     * @param ebayListingAdaptiveV2s
     * @return
     */
    public ItemCompatibilityListType getItemCompatibilityListTypeV2(boolean isReplaceAll, EbayCompatibilityProperties ebayCompatibilityProperties, List<EbayListingAdaptiveV2> ebayListingAdaptiveV2s) {
        ItemCompatibilityListType itemCompatibilityListType = new ItemCompatibilityListType();
        List<ItemCompatibilityType> itemCompatibilityTypeList = new ArrayList<ItemCompatibilityType>();
        if (CollectionUtils.isEmpty(ebayListingAdaptiveV2s)) {
            if (isReplaceAll) {
                itemCompatibilityListType.setReplaceAll(true);
            }
            return itemCompatibilityListType;
        }
        itemCompatibilityListType.setReplaceAll(true);
        for (EbayListingAdaptiveV2 e : ebayListingAdaptiveV2s) {
            ItemCompatibilityType itemCompatibilityType = new ItemCompatibilityType();
            ArrayList<NameValueListType> typeArrayList = new ArrayList<>();
            NameValueListType nameValueListType0 = setNameValueListType("Notes", new String[]{e.getNotes()});
            typeArrayList.add(nameValueListType0);
            //从api获取该类目适配属性
            if (ObjectUtils.isNotEmpty(ebayCompatibilityProperties) && ObjectUtils.isNotEmpty(ebayCompatibilityProperties.getCompatibilityProperties())) {
                for (EbayCompatibilityProperties.CompatibilityPropertiesBean compatibilityProperty : ebayCompatibilityProperties.getCompatibilityProperties()) {
                    if (compatibilityProperty.getName().equals("Year")) {
                        NameValueListType nameValueListType1 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getYear()});
                        typeArrayList.add(nameValueListType1);
                    }
                    if (compatibilityProperty.getName().equals("Make")) {
                        NameValueListType nameValueListType2 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getMake()});
                        typeArrayList.add(nameValueListType2);
                    }
                    if (compatibilityProperty.getName().equals("Model")) {
                        NameValueListType nameValueListType3 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getModel()});
                        typeArrayList.add(nameValueListType3);
                    }
//                    if (compatibilityProperty.getName().equals("Submodel")) {
//                        NameValueListType nameValueListType4 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getSubmodel()});
//                        typeArrayList.add(nameValueListType4);
//                    }
                    if (compatibilityProperty.getName().equals("Engine")) {
                        NameValueListType nameValueListType5 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getEngine()});
                        typeArrayList.add(nameValueListType5);
                    }
                    if (compatibilityProperty.getName().equals("Trim")) {
                        NameValueListType nameValueListType6 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getTrim()});
                        typeArrayList.add(nameValueListType6);
                    }
                    if (compatibilityProperty.getName().equals("Platform")) {
                        NameValueListType nameValueListType6 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getPlatform()});
                        typeArrayList.add(nameValueListType6);
                    }
                    if (compatibilityProperty.getName().equals("Type")) {
                        NameValueListType nameValueListType6 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getType()});
                        typeArrayList.add(nameValueListType6);
                    }

                }
            }

            NameValueListType[] nameValueListTypes = typeArrayList.toArray(new NameValueListType[typeArrayList.size()]);
            itemCompatibilityType.setNameValueList(nameValueListTypes);
            itemCompatibilityTypeList.add(itemCompatibilityType);
        }
        ItemCompatibilityType[] itemCompatibilityTypearr = itemCompatibilityTypeList.toArray(new ItemCompatibilityType[itemCompatibilityTypeList.size()]);
        itemCompatibilityListType.setCompatibility(itemCompatibilityTypearr);
        return itemCompatibilityListType;
    }

    /**
     * 查询ebay适配信息
     *
     * @param goodsHead
     * @return
     */
    private List<EbayListingAdaptiveV2> getListingEbayAdaptiveV2(EbayGoodsHeadV2 goodsHead) {
        if (ObjectUtils.isEmpty(goodsHead) || ObjectUtils.isEmpty(goodsHead.getId())) {
            return new ArrayList<>();
        }
        Object cacheObject = redisService.getCacheObject("ebay-adaptive-sku");
        if (ObjectUtils.isNotEmpty(cacheObject)) {
            List<String> skus = Arrays.asList(String.valueOf(cacheObject).split(","));
            if (skus.contains(goodsHead.getPdmGoodsCode())) {
                //如果是指定的sku 则先修改适配信息,再从适配表取适配信息
                ebayListingAdaptiveV2Service.updateAdapterByAds(goodsHead);
            }
        }
        List<EbayListingAdaptiveV2> ebayAdaptiveList = ebayListingAdaptiveV2Service.selectListByEbayHeadId(goodsHead.getId());

        return ebayAdaptiveList;
    }

    /**
     * @param itemType
     * @param ebayListingGoodsResourceV2s
     */
    public void setItemByResources(ItemType itemType, List<EbayListingGoodsResourceV2> ebayListingGoodsResourceV2s) {
        PictureDetailsType pictureDetailsType = new PictureDetailsType();
        List<String> pictureUrlList = new LinkedList<>();
        Map<String, List<EbayListingGoodsResourceV2>> resource = ebayListingGoodsResourceV2s.stream().filter(p -> ObjectUtils.isNotEmpty(p.getResourceType())).collect(Collectors.groupingBy(EbayListingGoodsResourceV2::getResourceType));
        if (!resource.isEmpty()) {
            //视频数据
            List<EbayListingGoodsResourceV2> videoResource = resource.get("0");
            //图片数据
            List<EbayListingGoodsResourceV2> imgResource = resource.get("1");
            if (ObjectUtils.isNotEmpty(imgResource) && !imgResource.isEmpty()) {

                //主图
                List<String> mainPictureUrl = imgResource.stream().filter(f -> Objects.equals(f.getIsMain(), 1)).map(EbayListingGoodsResourceV2::getResourceUrl).collect(Collectors.toList());
                if (ObjectUtils.isNotEmpty(mainPictureUrl) && !mainPictureUrl.isEmpty()) {
                    pictureUrlList.add(mainPictureUrl.get(0));
                }

                //副图
                List<String> secondaryDrawingUrl = imgResource.stream().filter(f -> Objects.equals(f.getIsMain(), 0)).map(EbayListingGoodsResourceV2::getResourceUrl).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(secondaryDrawingUrl)) {
                    pictureUrlList.addAll(secondaryDrawingUrl);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(pictureUrlList) && pictureUrlList.size() > 24) {
            pictureUrlList = pictureUrlList.subList(0, 24);
        }
        if (CollectionUtils.isNotEmpty(pictureUrlList)) {
            pictureDetailsType.setPictureURL(pictureUrlList.toArray(new String[0]));
        }
        pictureDetailsType.setGalleryType(GalleryTypeCodeType.GALLERY);
        pictureDetailsType.setPhotoDisplay(PhotoDisplayCodeType.PICTURE_PACK);
        pictureDetailsType.setPictureSource(PictureSourceCodeType.EPS);
        itemType.setPictureDetails(pictureDetailsType);
    }

    /**
     * 根据拓展表组装数据
     *
     * @param itemType
     * @param ebayListingDetailV2
     * @param ebayListingSpecsItemV2List
     */
    private void setItemByDetail(ItemType itemType, EbayGoodsHeadV2 goodsHead, EbayListingDetailV2 ebayListingDetailV2, List<EbayListingSpecsItemV2> ebayListingSpecsItemV2List) {
        //国家
        itemType.setCountry(StrUtil.isBlank(ebayListingDetailV2.getCountry()) ? CountryCodeType.US : CountryCodeType.fromValue(ebayListingDetailV2.getCountry()));
        //地区
        itemType.setLocation(ebayListingDetailV2.getLocation());
        if (ObjectUtils.isNotEmpty(ebayListingDetailV2.getFirstShopCategory()) && ebayListingDetailV2.getFirstShopCategory().matches("[0-9]+")) {
            //店铺类目信息
            StorefrontType storefrontType = new StorefrontType();
            storefrontType.setStoreCategoryID(StrUtil.isNotEmpty(ebayListingDetailV2.getFirstShopCategory()) ? Long.parseLong(ebayListingDetailV2.getFirstShopCategory()) : 0);
            storefrontType.setStoreCategory2ID(StrUtil.isNotEmpty(ebayListingDetailV2.getSecondShopCategory()) ? Long.parseLong(ebayListingDetailV2.getSecondShopCategory()) : 0);
            itemType.setStorefront(storefrontType);
        }
        //是否开启议价
        itemType.setBestOfferDetails(getBestOfferDetailsType("1".equals((goodsHead.getPublishType().equals(PublishType.CHINESE.getType())) ? "0" : ebayListingDetailV2.getBestOfferFlag()), 0));
        //立即购买价格              -取值逻辑 - 取根据刊登类型为拍卖时填写
        itemType.setBuyItNowPrice(getBuyItNowPrice(goodsHead.getPublishType(), ebayListingDetailV2.getOnePrice(), ebayListingDetailV2.getCurrency()));
        //最低售价-收费
        itemType.setReservePrice(getReservePrice(goodsHead.getPublishType(), ebayListingDetailV2.getReservePrice(), ebayListingDetailV2.getCurrency()));
        //起拍价      取ebayListingSpecsItemV2List
        Optional<Boolean> optional = ebayListingSpecsItemV2List.stream().map(e -> Objects.equals(e.getSpecAttributeIds(), "[0]")).findFirst();
        if (optional.isPresent() && optional.get()) {
            String price = ObjUtil.isNotEmpty(goodsHead.getStandardPrice()) ? goodsHead.getStandardPrice() : ebayListingSpecsItemV2List.get(0).getPrice();
            itemType.setStartPrice(getAmountType(Double.parseDouble(price), CurrencyCodeType.fromValue(ebayListingDetailV2.getCurrency())));
        }
        //橱窗视频
        itemType.setVideoDetails(getVideoDetails(ebayListingDetailV2.getVideoId()));
        //处理时间
        itemType.setDispatchTimeMax(Integer.valueOf(ebayListingDetailV2.getHandlingTime()));
        //币种
        itemType.setCurrency(CurrencyCodeType.fromValue(ebayListingDetailV2.getCurrency()));


    }

    /**
     * 根据头表数据组装ebayItem对象数据
     *
     * @param itemType
     * @param goodsHead
     */
    private void setItemByHead(ItemType itemType, EbayGoodsHeadV2 goodsHead) {
        GoodsHead head = new GoodsHead();
        head.setSiteCode(goodsHead.getSiteCode());
        head.setCategoryId(Math.toIntExact(goodsHead.getCategoryId()));

        //item id
        itemType.setItemID(goodsHead.getPlatformGoodsId());
        //站点
        itemType.setSite(getSite(goodsHead.getSiteCode(), String.valueOf(goodsHead.getCategoryId())));
        //销售方式
        itemType.setListingType(getListingType(goodsHead.getPublishType()));
        //专用电话号码表-  -取值逻辑-  默认false
        itemType.setPrivateListing(false);
        //标题-           -取值逻辑-  头表中-title
        itemType.setTitle(goodsHead.getTitle());
        //副标题【收费】   -取值逻辑-  头表中-subtitle
        itemType.setSubTitle(goodsHead.getSubtitle());
        //SKU-            -取值逻辑-    头表中-platform_goods_code
        itemType.setSKU(goodsHead.getPlatformGoodsCode());
        //数字标识符-     -取值逻辑-
        itemType.setConditionID(1000);
        //VAT税率
        itemType.setVATDetails(getVATDetails(goodsHead.getPlatform(), goodsHead.getSiteCode(), goodsHead.getShopCode()));
        //刊登天数
        itemType.setListingDuration(getListingDurationV2(goodsHead));
        //平台类目信息
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(goodsHead.getCategoryId());
        if (ObjUtil.isEmpty(platformCategory)) {
            throw new BusinessException("主键ID:" + goodsHead.getId() + "平台类目:" + goodsHead.getCategoryId() + "信息不存在");
        }
        itemType.setPrimaryCategory(getCategoryType(platformCategory.getCategoryId(), platformCategory.getCategoryName()));

    }


    /**
     * 映射刊登日期
     *
     * @param goodsHead
     * @return
     */
    public String getListingDurationV2(EbayGoodsHeadV2 goodsHead) {
        if (PublishType.FIXED.getType().equals((goodsHead.getPublishType()))) {
            return "GTC";
        }
        if (PublishType.POLYTROPIC.getType().equals((goodsHead.getPublishType()))) {
            return "GTC";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("Days_");
        if (PublishType.CHINESE.getType().equals((goodsHead.getPublishType())) && ObjectUtils.isNotEmpty(goodsHead.getSellDay())) {
            sb.append(goodsHead.getSellDay());
        }
        return sb.toString();
    }


}
