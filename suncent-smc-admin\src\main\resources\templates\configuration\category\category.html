<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('品类配置列表')" />
    <th:block th:include="include :: select2-css"/>
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>平台：</label>
                                <select name="platform" id="platform" class="form-control" onchange="changePlatForm(this)">
                                    <option value="" >--请选择--</option>
                                    <option value="AM" selected>AM</option>
                                    <option value="EB">EB</option>
                                    <option value="TEMU">TEMU</option>
                                </select>
                            </li>
                            <li>
                                <label>站点：</label>
                                <select name="site"
                                        id="site"
                                        data-placeholder="请选择站点"
                                        data-tags="true"
                                        class="form-control" onchange="changeSite(this)">
                                    <option
                                        th:each="keyValueEntity:${@siteService.selectSiteAllList()}"
                                        th:value="${keyValueEntity.key}"
                                        th:text="${keyValueEntity.value}"></option>
                                </select>
                            </li>
                            <li>
                                <label>英文名：</label>
                                <input type="text" name="categoryEnName"/>
                            </li>
                            <li>
                                <label>中文名：</label>
                                <input type="text" name="categoryName"/>
                            </li>
                            <li>
                                <label>品类：</label>
                                <select name="platformCategoryId" id="platformCategoryId" class="form-control"></select>
                            </li>
                            <li>
                                <label>品类编码：</label>
                                <input name="categoryId" type="text"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addFull()" shiro:hasPermission="configuration:category:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editFull()" shiro:hasPermission="configuration:category:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="configuration:category:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="configuration:category:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
     <th:block th:include="include :: select2-js"/>
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('configuration:category:edit')}]];
        var removeFlag = [[${@permission.hasPermi('configuration:category:remove')}]];
        var platformDatas = [[${@dict.getType('publication_goods_platform')}]];
        var prefix = ctx + "configuration/category";

        $(function() {

            getPlatformCategory('AM');

            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "品类配置",
                pageList: [10, 25, 50,100],
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'platform',
                    title: '平台',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(platformDatas, value);
                    }
                },
                {
                    field: 'site',
                    title: '站点'
                },
                {
                    field: 'categoryName',
                    title: '品类名',
                    formatter: function(value, row, index) {
                        var html = '';
                        if ($.common.isNotEmpty(value)){
                            html+= '<div>'+value+'</div>';
                        }
                        if ($.common.isNotEmpty(row.categoryEnName)){
                            html+= '<div>'+row.categoryEnName+'</div>';
                        }
                        return html;
                    }
                },
                {
                    field: 'categoryDetail',
                    title: '品类线',
                    formatter: function(value, row, index) {
                        var html = '';
                        if ($.common.isNotEmpty(value)){
                            html+= '<div>'+value+'</div>';
                        }
                        if ($.common.isNotEmpty(row.categoryEnDetail)){
                            html+= '<div>'+row.categoryEnDetail+'</div>';
                        }
                        return html;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if (row.platform == 'AM' && row.site == 'US') {
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="editJSONFull(\'' + row.id + '\', \'VC\')"><i class="fa fa-edit"></i>VC编辑</a> ');
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="editJSONFull(\'' + row.id + '\', \'SC\')"><i class="fa fa-edit"></i>SC编辑</a> ');
                            // actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editFull(\'' + row.id + '\')"><i class="fa fa-edit"></i>SC编辑</a> ');
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        } else {
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editFull(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });


        /**
         * 编辑品类配置 JSON
         * @param id
         * @param type
         */
        function editJSONFull(id, type) {
            var updateUrl = prefix + "/edit/json/" + id + "/" + type;
            var title = type === 'VC' ? "修改品类配置 (VC)" : "修改品类配置 (SC)";
            $.modal.openFull(title, updateUrl);
        }


        function changePlatForm(e){
            var val = $(e).val();
            getPlatformCategory(val);
        }

        function changeSite(e){
            let val = $("#platform").val();
            getPlatformCategory(val);
        }

        function getPlatformCategory(val){
            var config = {
                // url: "/configuration/category/getCategoryInfoByPlatform",
                url: "/configuration/platformCategory/getPlatformCategoryByPlatform",
                type: "post",
                dataType: "json",
                data: {
                    platform: val,
                    site: $("#site").val()
                },
                success: function (result) {
                    var html = "<option value=''>--请选择--</option>";
                    $(result).each(function (index, row) {
                        let categoryName = row.categoryName==null?"":row.categoryName;
                        html+= "<option value='"+row.id+"'>"+row.platformCode+"-"+categoryName+"["+row.categoryEnName+"]</option>";
                    })
                    $("#platformCategoryId").html(html);
                }
            };
            $.ajax(config);
        }

        function reset(){
            $.form.reset();
            $("#platform").val('').trigger('change');
            $("#site").val('').trigger('change');
            $("#platformCategoryId").val('').trigger('change');
        }

    </script>
</body>
</html>