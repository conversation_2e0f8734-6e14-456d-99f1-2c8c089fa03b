<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('BD促销综合日志')" />
    <style>
        .log-header {
            background: #f8f8f9;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .log-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .log-title {
            color: #1ab394;
            font-weight: bold;
            margin: 0;
        }
        .log-meta {
            color: #676a6c;
            font-size: 12px;
        }
        .log-tabs {
            margin-bottom: 20px;
        }
        .log-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        .status-normal { background: #1ab394; color: white; }
        .status-error { background: #ed5565; color: white; }
        .event-type {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        .event-create { background: #1ab394; }
        .event-update { background: #f8ac59; }
        .event-delete { background: #ed5565; }
        .event-status { background: #23c6c8; }
        .event-asin { background: #9b59b6; }
        .json-viewer {
            background: #f8f8f9;
            border: 1px solid #e7eaec;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <!-- 日志头部信息 -->
        <div class="log-header">
            <div class="log-info">
                <div>
                    <h4 class="log-title" th:text="${record.promotionName}">促销名称</h4>
                    <div class="log-meta">
                        <span>促销ID：<span th:text="${record.promotionId}">-</span></span> |
                        <span>站点：<span th:text="${record.site}">-</span></span> |
                        <span>状态：<span th:text="${record.status}">-</span></span> |
                        <span>创建时间：<span th:text="${#dates.format(record.createTime, 'yyyy-MM-dd HH:mm:ss')}">-</span></span>
                    </div>
                </div>
                <div>
                    <button class="btn btn-primary btn-sm" onclick="refreshAllLogs()">
                        <i class="fa fa-refresh"></i> 刷新
                    </button>
                </div>
            </div>
        </div>

        <!-- 日志类型切换 -->
        <div class="log-tabs">
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" class="active">
                    <a href="#operation-logs" aria-controls="operation-logs" role="tab" data-toggle="tab">
                        <i class="fa fa-list"></i> 操作日志
                    </a>
                </li>
                <!-- 暂时屏蔽其他功能，第一版只保留操作日志 -->
                <!--
                <li role="presentation">
                    <a href="#event-logs" aria-controls="event-logs" role="tab" data-toggle="tab">
                        <i class="fa fa-bolt"></i> 事件日志
                    </a>
                </li>
                <li role="presentation">
                    <a href="#asin-events" aria-controls="asin-events" role="tab" data-toggle="tab">
                        <i class="fa fa-cubes"></i> ASIN事件
                    </a>
                </li>
                <li role="presentation">
                    <a href="#integrated-view" aria-controls="integrated-view" role="tab" data-toggle="tab">
                        <i class="fa fa-timeline"></i> 综合视图
                    </a>
                </li>
                -->
            </ul>
        </div>

        <div class="tab-content">
            <!-- 操作日志 -->
            <div role="tabpanel" class="tab-pane active" id="operation-logs">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>操作日志</h5>
                        <div class="ibox-tools">
                            <span class="label label-info" id="operation-count">共 0 条</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <table id="operation-table" class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th data-field="operTime">操作时间</th>
                                    <th data-field="details">操作详情</th>
                                    <th data-field="operName">操作人</th>
                                    <th data-field="status">状态</th>
                                    <th data-field="errorMsg">错误信息</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 暂时屏蔽其他功能，第一版只保留操作日志 -->
            <!--
            <!-- 事件日志 -->
            <div role="tabpanel" class="tab-pane" id="event-logs">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>事件日志</h5>
                        <div class="ibox-tools">
                            <span class="label label-info" id="event-count">共 0 条</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <table id="event-table" class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th data-field="eventTime">事件时间</th>
                                    <th data-field="eventType">事件类型</th>
                                    <th data-field="operatorId">操作人</th>
                                    <th data-field="beforeData">变更前</th>
                                    <th data-field="afterData">变更后</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>

            <!-- ASIN事件 -->
            <div role="tabpanel" class="tab-pane" id="asin-events">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>ASIN事件</h5>
                        <div class="ibox-tools">
                            <span class="label label-info" id="asin-event-count">共 0 条</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <table id="asin-event-table" class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th data-field="eventTime">事件时间</th>
                                    <th data-field="eventType">事件类型</th>
                                    <th data-field="platformGoodsId">ASIN</th>
                                    <th data-field="operatorId">操作人</th>
                                    <th data-field="beforeData">变更前</th>
                                    <th data-field="afterData">变更后</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 综合视图 -->
            <div role="tabpanel" class="tab-pane" id="integrated-view">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>综合时间线</h5>
                        <div class="ibox-tools">
                            <span class="label label-info" id="integrated-count">共 0 条</span>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div id="integrated-timeline">
                            <!-- 动态填充综合时间线 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "promotion/record";
        var recordId = /*[[${record.id}]]*/ 0;
        var allLogs = [];
        var userDatas = [[${@sysBaseConfig.getSysUserNameAllKVList()}]];

        $(function() {
            // 第一版只初始化操作日志表格
            initOperationTable();

            // 暂时屏蔽其他功能
            // initEventTable();
            // initAsinEventTable();

            // 加载操作日志数据
            loadOperationLogs();

            // 暂时屏蔽标签页切换监听
            /*
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var target = $(e.target).attr("href");
                if (target === '#integrated-view') {
                    renderIntegratedTimeline();
                }
            });
            */
        });

        // 初始化操作日志表格
        function initOperationTable() {
            var options = {
                url: prefix + "/logs/list/" + recordId,
                method: 'post',
                sidePagination: "client",
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                search: false,
                pagination: true,
                pageSize: 20,
                height: 400,
                columns: [{
                    field: 'operTime',
                    title: '操作时间',
                    width: '20%',
                    formatter: function(value, row, index) {
                        return formatDateTime(value);
                    }
                }, {
                    field: 'details',
                    title: '操作详情',
                    width: '35%'
                }, {
                    field: 'operName',
                    title: '操作人',
                    width: '15%',
                    formatter: function (value, row, index) {
                        var label = '';
                        $(userDatas).each(function(i,site){
                            if(site.key == value){
                                label = site.value;
                                return false;
                            }
                        });
                        if (label == '')
                            return value;
                        else
                            return label;
                    }
                }, {
                    field: 'status',
                    title: '状态',
                    width: '10%',
                    formatter: function(value, row, index) {
                        if (value === 0) {
                            return '<span class="log-status status-normal">正常</span>';
                        } else {
                            return '<span class="log-status status-error">异常</span>';
                        }
                    }
                }, {
                    field: 'errorMsg',
                    title: '错误信息',
                    width: '20%',
                    formatter: function(value, row, index) {
                        if (value && value.trim() !== '') {
                            return '<span class="text-danger">' + value + '</span>';
                        }
                        return '-';
                    }
                }],
                onLoadSuccess: function(data) {
                    $('#operation-count').text('共 ' + (data.total || 0) + ' 条');
                }
            };
            $('#operation-table').bootstrapTable(options);
        }

        // 暂时屏蔽其他表格初始化，第一版只保留操作日志
        /*
        // 初始化事件日志表格
        function initEventTable() {
            var options = {
                data: [],
                sidePagination: "client",
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                search: false,
                pagination: true,
                pageSize: 20,
                height: 400,
                columns: [{
                    field: 'eventTime',
                    title: '事件时间',
                    width: '20%',
                    formatter: function(value, row, index) {
                        return formatDateTime(value);
                    }
                }, {
                    field: 'eventType',
                    title: '事件类型',
                    width: '15%',
                    formatter: function(value, row, index) {
                        return formatEventType(value);
                    }
                }, {
                    field: 'operatorId',
                    title: '操作人',
                    width: '15%'
                }, {
                    field: 'beforeData',
                    title: '变更前',
                    width: '25%',
                    formatter: function(value, row, index) {
                        return formatJsonData(value);
                    }
                }, {
                    field: 'afterData',
                    title: '变更后',
                    width: '25%',
                    formatter: function(value, row, index) {
                        return formatJsonData(value);
                    }
                }]
            };
            $('#event-table').bootstrapTable(options);
        }

        // 初始化ASIN事件表格
        function initAsinEventTable() {
            var options = {
                data: [],
                sidePagination: "client",
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                search: false,
                pagination: true,
                pageSize: 20,
                height: 400,
                columns: [{
                    field: 'eventTime',
                    title: '事件时间',
                    width: '18%',
                    formatter: function(value, row, index) {
                        return formatDateTime(value);
                    }
                }, {
                    field: 'eventType',
                    title: '事件类型',
                    width: '12%',
                    formatter: function(value, row, index) {
                        return formatEventType(value);
                    }
                }, {
                    field: 'platformGoodsId',
                    title: 'ASIN',
                    width: '12%'
                }, {
                    field: 'operatorId',
                    title: '操作人',
                    width: '12%'
                }, {
                    field: 'beforeData',
                    title: '变更前',
                    width: '23%',
                    formatter: function(value, row, index) {
                        return formatJsonData(value);
                    }
                }, {
                    field: 'afterData',
                    title: '变更后',
                    width: '23%',
                    formatter: function(value, row, index) {
                        return formatJsonData(value);
                    }
                }]
            };
            $('#asin-event-table').bootstrapTable(options);
        }
        */

        // 加载操作日志数据（第一版只保留操作日志）
        function loadOperationLogs() {
            // 加载操作日志
            $('#operation-table').bootstrapTable('refresh');
        }

        // 暂时保留原函数名以兼容其他调用
        function loadAllLogs() {
            loadOperationLogs();
        }

        // 暂时屏蔽其他功能，第一版只保留操作日志
        /*
        // 加载事件日志
        function loadEventLogs() {
            // 这里应该调用实际的API
            var mockEventData = [
                {
                    eventTime: new Date(),
                    eventType: 'CREATE',
                    operatorId: 'admin',
                    beforeData: null,
                    afterData: '{"promotionName":"春季促销","status":"DRAFT"}'
                }
            ];

            $('#event-table').bootstrapTable('load', mockEventData);
            $('#event-count').text('共 ' + mockEventData.length + ' 条');
        }

        // 加载ASIN事件
        function loadAsinEvents() {
            // 这里应该调用实际的API
            var mockAsinData = [
                {
                    eventTime: new Date(),
                    eventType: 'ASIN_ADD',
                    platformGoodsId: 'B07XAMPLE1',
                    operatorId: 'admin',
                    beforeData: null,
                    afterData: '{"referencePrice":29.99,"dealPrice":19.99}'
                }
            ];

            $('#asin-event-table').bootstrapTable('load', mockAsinData);
            $('#asin-event-count').text('共 ' + mockAsinData.length + ' 条');
        }

        // 渲染综合时间线
        function renderIntegratedTimeline() {
            var container = $('#integrated-timeline');
            container.html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> 正在加载综合时间线...</div>');

            // 这里应该合并所有日志数据并按时间排序
            setTimeout(function() {
                container.html('<div class="text-center text-muted" style="padding: 50px;"><i class="fa fa-timeline" style="font-size: 48px;"></i><h4>综合时间线</h4><p>显示所有操作和事件的时间线视图</p></div>');
            }, 1000);
        }
        */

        // 暂时屏蔽事件相关的格式化函数，第一版只保留操作日志
        /*
        // 格式化事件类型
        function formatEventType(eventType) {
            var typeMap = {
                'CREATE': '<span class="event-type event-create">创建</span>',
                'UPDATE': '<span class="event-type event-update">更新</span>',
                'DELETE': '<span class="event-type event-delete">删除</span>',
                'STATUS_CHANGE': '<span class="event-type event-status">状态变更</span>',
                'ASIN_ADD': '<span class="event-type event-asin">ASIN添加</span>',
                'ASIN_UPDATE': '<span class="event-type event-asin">ASIN更新</span>',
                'ASIN_REMOVE': '<span class="event-type event-asin">ASIN移除</span>'
            };
            return typeMap[eventType] || eventType;
        }

        // 格式化JSON数据
        function formatJsonData(jsonStr) {
            if (!jsonStr || jsonStr.trim() === '') {
                return '-';
            }

            try {
                var obj = JSON.parse(jsonStr);
                var formatted = JSON.stringify(obj, null, 2);
                return '<div class="json-viewer">' + formatted + '</div>';
            } catch (e) {
                return '<span class="text-muted">' + jsonStr + '</span>';
            }
        }
        */

        // 格式化日期时间
        function formatDateTime(dateTime) {
            if (!dateTime) return '-';
            var date = new Date(dateTime);
            return date.getFullYear() + '-' + 
                   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(date.getDate()).padStart(2, '0') + ' ' + 
                   String(date.getHours()).padStart(2, '0') + ':' + 
                   String(date.getMinutes()).padStart(2, '0') + ':' + 
                   String(date.getSeconds()).padStart(2, '0');
        }

        // 刷新操作日志（第一版只保留操作日志）
        function refreshAllLogs() {
            loadOperationLogs();
            $.modal.alertSuccess("操作日志已刷新");
        }

        // 导出操作日志（第一版只保留操作日志）
        function exportAllLogs() {
            window.open(prefix + "/logs/export/" + recordId, "_blank");
        }
    </script>
</body>
</html>
