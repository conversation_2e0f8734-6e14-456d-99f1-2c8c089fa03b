package com.suncent.smc.provider.biz.configuration;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ebay.soap.eBLBaseComponents.*;
import com.google.common.collect.Lists;
import com.suncent.smc.common.config.SpringTaskRetry;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.domain.UrlReplaceEntity;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.Utils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.persistence.api.domain.EbayCompatibilityProperties;
import com.suncent.smc.persistence.common.domain.LogRecord;
import com.suncent.smc.persistence.common.service.ILogRecordService;
import com.suncent.smc.persistence.configuration.category.domain.entity.CategoryInfo;
import com.suncent.smc.persistence.configuration.category.service.ICategoryInfoService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreInfoService;
import com.suncent.smc.persistence.publication.domain.dto.ItemBackUpDTO;
import com.suncent.smc.persistence.publication.domain.dto.ItemDTO;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.template.domain.entity.*;
import com.suncent.smc.persistence.template.service.ITemplateEbayDescriptionService;
import com.suncent.smc.persistence.template.service.ITemplateShopRelationService;
import com.suncent.smc.persistence.template.service.impl.TemplateShippingLocationLineServiceImpl;
import com.suncent.smc.persistence.template.service.impl.TemplateShippingTypeLineServiceImpl;
import com.suncent.smc.provider.biz.image.ImageHandleBiz;
import com.suncent.smc.provider.biz.publication.CommonInfoBiz;
import com.suncent.smc.provider.biz.publication.ListingUpdateBuilder;
import com.suncent.smc.provider.biz.publication.domain.EbayVideo;
import com.suncent.smc.provider.biz.publication.domain.EbayVideoInfo;
import com.suncent.smc.provider.biz.template.TemplateEbayDescriptionBiz;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 用smc数据构建成ebay 所需要的对象
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BuildEbayItemBiz {
    @Resource
    protected IGoodsHeadService listingGoodsHeadService;
    @Autowired
    protected IGoodsDescriptionService goodsDescriptionService;
    @Resource
    protected IListingEbayLineService ebayGoodsLineService;
    @Resource
    protected IListingEbayShippingHeadService ebayShippingHeadService;
    @Resource
    protected IListingEbayAdaptiveService adaptiveService;
    @Resource
    protected IListingEbayPolicyService ebayPolicyService;
    @Autowired
    protected IGoodsResourceService goodsResourceService;
    @Resource
    protected IListingLogService listingLogService;
    @Resource
    protected IListingShippingTypeLineService shippingTypeLineService;
    @Resource
    protected IListingShippingLocationLineService shippingLocationLineService;
    @Resource
    protected IListingEbayValueService ebayValueService;
    @Autowired
    protected IPlatformCategoryService platformCategoryService;
    protected static String GET_COMPATIBILITY_PROPERTIES_URL;
    @Value("${api.ebay-getCompatibilityProperties-url}")
    protected String getCompatibilityPropertiesUrl;
    @Autowired
    protected ImageHandleBiz imageHandleBiz;

    @Resource
    protected ITemplateEbayDescriptionService templateEbayDescriptionService;
    @Resource
    protected ICategoryInfoService categoryInfoService;
    @Resource
    protected CommonInfoBiz commonInfoBiz;
    @Autowired
    protected ITemplateShopRelationService templateShopRelationService;
    @Resource
    protected ILogRecordService logRecordService;
    @Resource
    protected RedisService redisService;
    @Autowired
    @Lazy
    protected ListingUpdateBuilder listingUpdateBuilder;
    @Autowired
    protected IConfigStoreInfoService configStoreInfoService;
    @Autowired
    @Lazy
    protected TemplateEbayDescriptionBiz templateEbayDescriptionBiz;
    @Resource
    protected SpringTaskRetry retryable;
    @Autowired
    protected TemplateShippingLocationLineServiceImpl templateShippingLocationLineServiceImpl;
    @Autowired
    IListingEbayVideoService ebayVideoService;


    @Value("${api.ebay-createVideo-url}")
    protected String createVideo;
    @Value("${api.ebay-uploadVideo-url}")
    protected String uploadVideo;
    @Value("${api.ebay-getVideo-url}")
    protected String getVideo;
    @Autowired
    protected TemplateShippingTypeLineServiceImpl templateShippingTypeLineServiceImpl;

    /**
     * 调用ebay接口获取适配车型属性
     * @param shopCode
     * @param siteCode
     * @param platformCategoryId
     * @return
     */
    public static EbayCompatibilityProperties getEbayCompatibilityProperties(String shopCode, String siteCode, String platformCategoryId) {
        UrlReplaceEntity urlReplaceEntity = new UrlReplaceEntity();
        urlReplaceEntity.setUrl(GET_COMPATIBILITY_PROPERTIES_URL);
        urlReplaceEntity.setAccountCode(shopCode);
        urlReplaceEntity.setCategoryTreeId(Objects.requireNonNull(PlatformSiteEnum.getEnumByName(siteCode)).getCategoryTreeId());
        urlReplaceEntity.setCategoryId(platformCategoryId);
        String get = HttpUtils.get(Utils.replaceUrl(urlReplaceEntity));
        AjaxResult result = JSONObject.parseObject(get, AjaxResult.class);
        return JSONObject.parseObject(JSON.toJSONString(result.get(AjaxResult.DATA_TAG)), EbayCompatibilityProperties.class);
    }

    /**
     * 根据smc的数据构建ebay的item对象
     */
    public ItemType buildEbayItem(Integer listingHeadId, String type) throws Exception {
        ItemType itemType = new ItemType();
        //头数据
        GoodsHead goodsHead = listingGoodsHeadService.selectListingGoodsHeadById(listingHeadId);
        //校验价格
        commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), goodsHead.getPublishType(),goodsHead.getSiteCode(),goodsHead.getOnlineTime(),goodsHead.getStandardPrice());

        Integer goodsHeadId = goodsHead.getId();
        //描述信息
        GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsHeadId);
        //行信息
        ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHeadId);
        if (ObjectUtils.isEmpty(listingEbayLine)) {
            return null;
        }
        Integer ebayLineId = listingEbayLine.getId();
        //物流信息
        ListingEbayShippingHead listingEbayShippingHead = ebayShippingHeadService.selectListingEbayShippingHeadByLineId(ebayLineId);
        if (ObjectUtils.isEmpty(listingEbayShippingHead)){
            //兼容物流信息缺失的情况，取模板中的数据
                //补偿
                ShippingDataResult shippingDataResult = compensationShippingData(ebayLineId, goodsHead,listingEbayShippingHead);
                //赋值
                listingEbayShippingHead = shippingDataResult.getListingEbayShippingHead();
        }
        List<ListingShippingTypeLine> listingShippingTypeLines = shippingTypeLineService.selectListingShippingTypeLineByHeadId(listingEbayShippingHead.getId());
        List<ListingShippingLocationLine> listingShippingLocationLines = shippingLocationLineService.selectByShippingHeadId(listingEbayShippingHead.getId());
        //属性
        List<ListingEbayValue> listingEbayValues = ebayValueService.selectListingEbayValueByLineId(ebayLineId);
        if (CollUtil.isNotEmpty(listingEbayValues)) {
            listingEbayValues = listingEbayValues.stream().filter(f -> StrUtil.isNotEmpty(f.getName()) && StrUtil.isNotEmpty(f.getValue())).collect(Collectors.toList());
        }
        //适配信息
        List<ListingEbayAdaptive> ebayAdaptiveList = getListingEbayAdaptive(ebayLineId, goodsHead);
        //付款方式信息
        ListingEbayPolicy listingEbayPolicy = ebayPolicyService.selectListingEbayPolicyByLineId(ebayLineId);
        //图片信息
        List<GoodsResource> goodsResourcesList = goodsResourceService.selectListingGoodsResourceByHeadId(goodsHeadId);
        //图片EPS上传换链
        imageHandleBiz.replaceResourcesUrl(goodsResourcesList, goodsHead.getShopCode());
        //视频信息
        ListingEbayVideo listingEbayVideo = ebayVideoService.selectListingEbayVideoByGoodsId(goodsHead.getId());

        //补偿物流&&付款数据
        CompensationData result = getCompensationData(listingEbayShippingHead, listingShippingTypeLines, listingShippingLocationLines, ebayLineId, goodsHead, listingEbayPolicy);

        //校验本地数据是否完整
        String errorLog = verifyListing(type, goodsHead, goodsDescription, listingEbayLine, result.listingEbayPolicy, goodsResourcesList, result.listingEbayShippingHead);
        if (ObjectUtils.isNotEmpty(errorLog)) {
            throw new BusinessException(errorLog);
        }

        //处理listing更新
        if (Objects.equals(type, "update")) {
            //对比数据
            ItemType data = comparativeData(goodsHead, listingEbayLine, result.listingEbayPolicy, goodsResourcesList, result.listingEbayShippingHead, result.listingShippingTypeLines, result.listingShippingLocationLines, listingEbayValues, goodsDescription,listingEbayVideo);
            if (ObjectUtils.isNotEmpty(data)) {
                return data;
            }
        }
        //9、开始组装数据基本数据
        ItemType itemType1 = setItemTypeBase(itemType, goodsHead, goodsDescription, listingEbayLine, result.listingEbayShippingHead, goodsResourcesList, listingEbayValues,listingEbayVideo);
        //10、开始组装数据基本对象数据
        ItemType itemType2 = setItemTypeObj(itemType1, goodsHead, listingEbayLine, result.getListingEbayPolicy(), goodsResourcesList, result.getListingShippingTypeLines(), result.getListingShippingLocationLines(), listingEbayValues, ebayAdaptiveList);
        return itemType2;
    }

    private CompensationData getCompensationData(ListingEbayShippingHead listingEbayShippingHead, List<ListingShippingTypeLine> listingShippingTypeLines, List<ListingShippingLocationLine> listingShippingLocationLines, Integer ebayLineId, GoodsHead goodsHead, ListingEbayPolicy listingEbayPolicy) {
        //兼容物流信息缺失的情况，取模板中的数据
        if (ObjUtil.isEmpty(listingEbayShippingHead)|| ObjUtil.isEmpty(listingShippingTypeLines)||ObjUtil.isEmpty(listingShippingLocationLines)){
            //补偿
            ShippingDataResult shippingDataResult = compensationShippingData(ebayLineId, goodsHead,listingEbayShippingHead);
            //赋值
            listingEbayShippingHead = shippingDataResult.getListingEbayShippingHead();
            listingShippingTypeLines = shippingDataResult.getListingShippingTypeLines();
            listingShippingLocationLines = shippingDataResult.getListingShippingLocationLines();
        }
        //兼容付款信息缺失的情况,取模板中的数据
        if (ObjUtil.isEmpty(listingEbayPolicy)){
            //补偿
            ListingEbayPolicy policyDataResult = compensationPolicyData(ebayLineId, goodsHead);
            //赋值
            listingEbayPolicy = policyDataResult;
        }
        CompensationData result = new CompensationData(listingEbayShippingHead, listingShippingTypeLines, listingShippingLocationLines, listingEbayPolicy);
        return result;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class CompensationData {
        public  ListingEbayShippingHead listingEbayShippingHead;
        public  List<ListingShippingTypeLine> listingShippingTypeLines;
        public  List<ListingShippingLocationLine> listingShippingLocationLines;
        public  ListingEbayPolicy listingEbayPolicy;

    }

    /**
     * 补偿付款信息
     * @param ebayLineId
     * @param goodsHead
     * @return
     */
    public ListingEbayPolicy compensationPolicyData(Integer ebayLineId, GoodsHead goodsHead) {
        String shopCode = goodsHead.getShopCode();
        // 付款退货
        TemplateEbayPolicy policyTemplate = templateShopRelationService.selectTemplatePolicyInfo(shopCode, "policy");
        if (ObjUtil.isEmpty(policyTemplate)) {
            return null;
        }
        ListingEbayPolicy listingEbayPolicy = new ListingEbayPolicy();
        BeanUtil.copyProperties(policyTemplate, listingEbayPolicy, "id");
        listingEbayPolicy.setPaymentPolicy(policyTemplate.getPaymentMethod());
        listingEbayPolicy.setListingLineId(ebayLineId);
        ebayPolicyService.insertListingEbayPolicy(listingEbayPolicy);
        return listingEbayPolicy;
    }

    /**
     * 使用模板的数据进行补偿物流信息
     * @param lineId 线ID
     * @param goodsHead 商品头信息
     * @return 包含处理结果的对象
     */
    public ShippingDataResult compensationShippingData(Integer lineId, GoodsHead goodsHead,ListingEbayShippingHead oldListingEbayShippingHead) {
        String shopCode = goodsHead.getShopCode();
        String userId = ObjUtil.isEmpty(goodsHead.getCreateBy()) ? goodsHead.getUpdateBy() : goodsHead.getCreateBy();

        // 获取模板物流头信息
        TemplateEbayShippingHead shippingTemplateHead = templateShopRelationService.selectTemplateShippingInfo(shopCode, "shipping");
        if (ObjectUtil.isEmpty(shippingTemplateHead)) {
            throw new BusinessException("未找到模板物流信息.");
        }
        // 复制模板数据到新的物流头信息
        ListingEbayShippingHead listingEbayShippingHead = new ListingEbayShippingHead();
        BeanUtil.copyProperties(shippingTemplateHead, listingEbayShippingHead, "id");
        listingEbayShippingHead.setListingLineId(lineId);
        listingEbayShippingHead.setCreateBy(userId);
        ebayShippingHeadService.insertListingEbayShippingHead(listingEbayShippingHead);

        if (ObjectUtil.isNotEmpty(oldListingEbayShippingHead)) {
            ebayShippingHeadService.deleteListingEbayShippingHeadById(oldListingEbayShippingHead.getId());
        }

        // 处理物流位置数据
        List<ListingShippingLocationLine> listingShippingLocationLines = processShippingLocationLines(shippingTemplateHead,listingEbayShippingHead.getId());
        shippingLocationLineService.insertList(listingShippingLocationLines);

        // 处理物流方式数据
        List<ListingShippingTypeLine> listingShippingTypeLines = processShippingTypeLines(shippingTemplateHead, listingEbayShippingHead.getId());
        shippingTypeLineService.insertList(listingShippingTypeLines);

        // 返回包含所有处理结果的对象
        return new ShippingDataResult(listingEbayShippingHead, listingShippingLocationLines, listingShippingTypeLines);
    }

    /**
     * 处理物流位置行数据
     * @param shippingTemplateHead 物流模板头信息
     * @return 物流位置行列表
     */
    private List<ListingShippingLocationLine> processShippingLocationLines(TemplateEbayShippingHead shippingTemplateHead,Integer newShippingHeadId) {
        List<TemplateShippingLocationLine> templateShippingLocationLines = templateShippingLocationLineServiceImpl.getTemplateShippingLocationLineByHeadId(shippingTemplateHead.getId().longValue());
        return templateShippingLocationLines.stream()
                .map(p -> {
                    ListingShippingLocationLine listingShippingLocationLine = new ListingShippingLocationLine();
                    BeanUtil.copyProperties(p, listingShippingLocationLine, "id");
                    listingShippingLocationLine.setShippingHeadId(Long.valueOf(newShippingHeadId));
                    return listingShippingLocationLine;
                })
                .collect(Collectors.toList());
    }

    /**
     * 处理物流方式行数据
     * @param shippingTemplateHead 物流模板头信息
     * @return 物流方式行列表
     */
    private List<ListingShippingTypeLine> processShippingTypeLines(TemplateEbayShippingHead shippingTemplateHead,Integer newShippingHeadId) {
        List<TemplateShippingTypeLine> templateShippingTypeLines = templateShippingTypeLineServiceImpl.getTemplateShippingTypeLineByHeadId(shippingTemplateHead.getId());
        return templateShippingTypeLines.stream()
                .map(p -> {
                    ListingShippingTypeLine listingShippingTypeLine = new ListingShippingTypeLine();
                    BeanUtil.copyProperties(p, listingShippingTypeLine, "id");
                    listingShippingTypeLine.setShippingHeadId(Long.valueOf(newShippingHeadId));
                    return listingShippingTypeLine;
                })
                .collect(Collectors.toList());
    }

    /**
     * 物流结果数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class ShippingDataResult {
        private ListingEbayShippingHead listingEbayShippingHead;
        private List<ListingShippingLocationLine> listingShippingLocationLines;
        private List<ListingShippingTypeLine> listingShippingTypeLines;

    }




    /**
     * 对比数据->针对单个详情编辑更新的数据做对比
     *
     * @param goodsHead
     * @param listingEbayLine
     * @param listingEbayPolicy
     * @param goodsResourcesList
     * @param listingEbayShippingHead
     * @param listingShippingTypeLines
     * @param listingShippingLocationLines
     * @param listingEbayValues
     * @param goodsDescription
     * @return
     */
    private ItemType comparativeData(GoodsHead goodsHead, ListingEbayLine listingEbayLine, ListingEbayPolicy listingEbayPolicy, List<GoodsResource> goodsResourcesList, ListingEbayShippingHead listingEbayShippingHead, List<ListingShippingTypeLine> listingShippingTypeLines, List<ListingShippingLocationLine> listingShippingLocationLines, List<ListingEbayValue> listingEbayValues,  GoodsDescription goodsDescription, ListingEbayVideo listingEbayVideo) {
        try {
            //拿到创建时间为最近6h的数据
            LogRecord headBackup = logRecordService.selectGoodsHeadBackupByGoodsIds(Lists.newArrayList(String.valueOf(goodsHead.getId())), OperTypeEnum.SINGLE_EDIT.name())
                    .stream()
                    .filter(f -> f.getCreateTime().getTime() > System.currentTimeMillis() - 6 * 60 * 60 * 1000)
                    //拿到最近的一条数据
                    .max(Comparator.comparing(LogRecord::getCreateTime)).orElse(null);

            List<String> moduleType = new ArrayList<>();
            List<String> excludeFields = new ArrayList<>();
            excludeFields.add("id");
            excludeFields.add("createTime");
            excludeFields.add("updateTime");
            excludeFields.add("createBy");
            excludeFields.add("updateBy");
            excludeFields.add("params");
            excludeFields.add("remark");

            if (ObjectUtils.isEmpty(headBackup)) {
                return null;
            }
            ItemBackUpDTO itemBackUpDTO = JSON.parseObject(headBackup.getParam(), ItemBackUpDTO.class);
            if (ObjectUtils.isEmpty(itemBackUpDTO)) {
                return null;
            }
            GoodsHead oldHead = itemBackUpDTO.getGoodsHead();
            //更新失败的状态不做对比 直接全量更新
            if (Objects.equals(oldHead.getPublishStatus(), PublishStatus.UPDATING_FAIL.getType())) {
                return null;
            }
            //对比标题
            if (!Objects.equals(goodsHead.getTitle(), oldHead.getTitle())) {
                moduleType.add(ListingModuleType.TITLE.name());
            }
            //对比价格
            if (!Objects.equals(goodsHead.getStandardPrice(), oldHead.getStandardPrice())) {
                moduleType.add(ListingModuleType.PRICE.name());
            }
            //对比库存
            if (!Objects.equals(goodsHead.getStockOnSalesQty(), oldHead.getStockOnSalesQty())) {
                moduleType.add(ListingModuleType.INVENTORY.name());
            }
            //对比品牌
            if (!Objects.equals(goodsHead.getBrandCode(), oldHead.getBrandCode())) {
                moduleType.add(ListingModuleType.BRAND.name());
            }
            //对比平台商品编码
            if (!Objects.equals(goodsHead.getPlatformGoodsCode(), oldHead.getPlatformGoodsCode())) {
                moduleType.add(ListingModuleType.PLATFORM_PRODUCT_CODE.name());
            }
            //对比行信息
            if (!EqualsBuilder.reflectionEquals(listingEbayLine, itemBackUpDTO.getEbayLine(),excludeFields)) {
                moduleType.add(ListingModuleType.EBAY_LINE.name());
            }
            //对比付款退款信息
            if (!EqualsBuilder.reflectionEquals(listingEbayPolicy, itemBackUpDTO.getEbayPolicy(),excludeFields)) {
                moduleType.add(ListingModuleType.EBAY_POLICY.name());
            }
            //对比视频
            if (!EqualsBuilder.reflectionEquals(listingEbayVideo, itemBackUpDTO.getEbayVideo(),excludeFields)) {
                moduleType.add(ListingModuleType.VIDEO.name());
            }
            //对比图片
            if (goodsResourcesList.size() != itemBackUpDTO.getGoodsResourceList().size()) {
                moduleType.add(ListingModuleType.IMAGE.name());
            }else {
                for (int i = 0; i < itemBackUpDTO.getGoodsResourceList().size(); i++) {
                    if (!EqualsBuilder.reflectionEquals(goodsResourcesList.get(i), itemBackUpDTO.getGoodsResourceList().get(i),excludeFields)) {
                        moduleType.add(ListingModuleType.IMAGE.name());
                        moduleType.add(ListingModuleType.DESCRIPTION.name());
                        break;
                    }
                }
            }
            //对比物流信息
            if (!EqualsBuilder.reflectionEquals(listingEbayShippingHead, itemBackUpDTO.getEbayShippingHead(),excludeFields)) {
                moduleType.add(ListingModuleType.EBAY_SHIPPING.name());
            }
            if (listingShippingTypeLines.size() != itemBackUpDTO.getEbayShippingTypeLines().size() || listingShippingLocationLines.size() != itemBackUpDTO.getEbayShippingLocationLines().size()) {
                moduleType.add(ListingModuleType.EBAY_SHIPPING.name());
            }else {
                for (int i = 0; i < itemBackUpDTO.getEbayShippingTypeLines().size(); i++) {
                    if (!EqualsBuilder.reflectionEquals(listingShippingTypeLines.get(i), itemBackUpDTO.getEbayShippingTypeLines().get(i),excludeFields)) {
                        if (!moduleType.contains(ListingModuleType.EBAY_SHIPPING.name())) {
                            moduleType.add(ListingModuleType.EBAY_SHIPPING.name());
                        }
                        break;
                    }
                }
                for (int i = 0; i < itemBackUpDTO.getEbayShippingLocationLines().size(); i++) {
                    if (!EqualsBuilder.reflectionEquals(listingShippingLocationLines.get(i), itemBackUpDTO.getEbayShippingLocationLines().get(i),excludeFields)) {
                        if (!moduleType.contains(ListingModuleType.EBAY_SHIPPING.name())) {
                            moduleType.add(ListingModuleType.EBAY_SHIPPING.name());
                        }
                        break;
                    }
                }
            }

            //对比属性
            if (listingEbayValues.size() != itemBackUpDTO.getEbayValues().size()) {
                moduleType.add(ListingModuleType.ATTRIBUTE.name());
                moduleType.add(ListingModuleType.DESCRIPTION.name());
            }else {
                for (int i = 0; i < itemBackUpDTO.getEbayValues().size(); i++) {
                    if (!EqualsBuilder.reflectionEquals(listingEbayValues.get(i), itemBackUpDTO.getEbayValues().get(i),excludeFields)) {
                        moduleType.add(ListingModuleType.ATTRIBUTE.name());
                        moduleType.add(ListingModuleType.DESCRIPTION.name());
                        break;
                    }
                }
            }
            //对比描述
            if (!EqualsBuilder.reflectionEquals(goodsDescription, itemBackUpDTO.getGoodDescription(),excludeFields)) {
                if (!moduleType.contains(ListingModuleType.DESCRIPTION.name())) {
                    moduleType.add(ListingModuleType.DESCRIPTION.name());
                }
            }
            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(goodsHead);
            itemDTO.setModuleType(moduleType);

            List<ItemDTO> itemDTOList = listingUpdateBuilder.buildItem(Collections.singletonList(itemDTO),false);
            if (ObjectUtils.isEmpty(itemDTOList)||ObjectUtils.isEmpty(itemDTOList.get(0))||ObjectUtils.isEmpty(itemDTOList.get(0).getEbayItem())) {
                return null;
            }

            return  itemDTOList.get(0).getEbayItem();
        } catch (Exception e) {
            log.error("Ebay 更新listing,对比数据异常", e);
            return null;
        }
    }

    /**
     * 查询ebay适配信息
     *
     * @param ebayLineId
     * @param goodsHead
     * @return
     */
    private List<ListingEbayAdaptive> getListingEbayAdaptive(Integer ebayLineId, GoodsHead goodsHead) {
        Object cacheObject = redisService.getCacheObject("ebay-adaptive-sku");
        if (ObjectUtils.isNotEmpty(cacheObject)){
            List<String> skus = Arrays.asList(String.valueOf(cacheObject).split(","));
            if (skus.contains(goodsHead.getPdmGoodsCode())) {
                //如果是指定的sku 则先修改适配信息,再从适配表取适配信息
                adaptiveService.updateAdapterByAds(ebayLineId, goodsHead);
            }
        }
        List<ListingEbayAdaptive> ebayAdaptiveList = adaptiveService.selectListByEbayLineId(ebayLineId);

        return ebayAdaptiveList;
    }

    /**
     * 校验ebay本地刊登数据是否完整
     *
     * @param type
     * @param goodsHead
     * @param goodsDescription
     * @param listingEbayLine
     * @param listingEbayPolicy
     * @param goodsResourcesList
     * @param listingEbayShippingHead
     */
    private String verifyListing(String type, GoodsHead goodsHead, GoodsDescription goodsDescription, ListingEbayLine listingEbayLine, ListingEbayPolicy listingEbayPolicy, List<GoodsResource> goodsResourcesList, ListingEbayShippingHead listingEbayShippingHead) {
        StringBuilder errorLog = new StringBuilder();
        if (ObjectUtils.isEmpty(goodsDescription)||(ObjectUtils.isEmpty(goodsDescription.getDetailDescription())&&ObjectUtils.isEmpty(goodsDescription.getShortDescription()))) {
            errorLog.append("商品描述信息、");
        }
        if (ObjectUtils.isEmpty(listingEbayLine)) {
            errorLog.append("ebay行信息、");
        }
        if (ObjectUtils.isEmpty(listingEbayPolicy)) {
            errorLog.append("ebay付款方式信息、");
        }
        if (CollectionUtils.isEmpty(goodsResourcesList)) {
            errorLog.append("ebay图片资源信息、");
        }
        if (ObjectUtils.isEmpty(listingEbayShippingHead)) {
            errorLog.append("ebay物流信息、");
        }
        if (errorLog.length() == 0) {
            return null;
        }
        if (!StringUtils.isNotBlank(type) || !type.equals("inspect")) {
            //记录操作日志
            listingLogService.insertErrorListingLog("ebay商品送检组装数据失败", StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy(), goodsHead.getId(), errorLog + "数据不完整,请检查数据,请联系管理员编辑再次刊登");

            goodsHead.setPublishStatus(PublishStatus.getStatusByApi(goodsHead.getPublishStatus(), false));
            listingGoodsHeadService.updateListingGoodsHead(goodsHead);
        }
        return errorLog + "数据不完整,请检查数据,请联系管理员编辑再次刊登";
    }

    /**
     * 封装适配车型对象的方法
     *
     * @param isReplaceAll                是否全部覆盖
     * @param ebayCompatibilityProperties
     * @param ebayAdaptiveList
     * @return
     */

    public static ItemCompatibilityListType getItemCompatibilityListType(boolean isReplaceAll, EbayCompatibilityProperties ebayCompatibilityProperties, List<ListingEbayAdaptive> ebayAdaptiveList) {
        ItemCompatibilityListType itemCompatibilityListType = new ItemCompatibilityListType();
        List<ItemCompatibilityType> itemCompatibilityTypeList = new ArrayList<ItemCompatibilityType>();
        if (CollectionUtils.isEmpty(ebayAdaptiveList)) {
            if (isReplaceAll){
                itemCompatibilityListType.setReplaceAll(true);
            }
            return itemCompatibilityListType;
        }
        itemCompatibilityListType.setReplaceAll(true);
        for (ListingEbayAdaptive e : ebayAdaptiveList) {
            ItemCompatibilityType itemCompatibilityType = new ItemCompatibilityType();
            ArrayList<NameValueListType> typeArrayList = new ArrayList<>();
            NameValueListType nameValueListType0 = setNameValueListType("Notes", new String[]{e.getNotes()});
            typeArrayList.add(nameValueListType0);
            //从api获取该类目适配属性
            if (ObjectUtils.isNotEmpty(ebayCompatibilityProperties) && ObjectUtils.isNotEmpty(ebayCompatibilityProperties.getCompatibilityProperties())) {
                for (EbayCompatibilityProperties.CompatibilityPropertiesBean compatibilityProperty : ebayCompatibilityProperties.getCompatibilityProperties()) {
                    if (compatibilityProperty.getName().equals("Year")) {
                        NameValueListType nameValueListType1 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getYear()});
                        typeArrayList.add(nameValueListType1);
                    }
                    if (compatibilityProperty.getName().equals("Make")) {
                        NameValueListType nameValueListType2 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getMake()});
                        typeArrayList.add(nameValueListType2);
                    }
                    if (compatibilityProperty.getName().equals("Model")) {
                        NameValueListType nameValueListType3 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getModel()});
                        typeArrayList.add(nameValueListType3);
                    }
//                    if (compatibilityProperty.getName().equals("Submodel")) {
//                        NameValueListType nameValueListType4 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getSubmodel()});
//                        typeArrayList.add(nameValueListType4);
//                    }
                    if (compatibilityProperty.getName().equals("Engine")) {
                        NameValueListType nameValueListType5 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getEngine()});
                        typeArrayList.add(nameValueListType5);
                    }
                    if (compatibilityProperty.getName().equals("Trim")) {
                        NameValueListType nameValueListType6 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getTrim()});
                        typeArrayList.add(nameValueListType6);
                    }
                    if (compatibilityProperty.getName().equals("Platform")) {
                        NameValueListType nameValueListType6 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getPlatform()});
                        typeArrayList.add(nameValueListType6);
                    }
                    if (compatibilityProperty.getName().equals("Type")) {
                        NameValueListType nameValueListType6 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getType()});
                        typeArrayList.add(nameValueListType6);
                    }

                }
            }

            NameValueListType[] nameValueListTypes = typeArrayList.toArray(new NameValueListType[typeArrayList.size()]);
            itemCompatibilityType.setNameValueList(nameValueListTypes);
            itemCompatibilityTypeList.add(itemCompatibilityType);
        }
        ItemCompatibilityType[] itemCompatibilityTypearr = itemCompatibilityTypeList.toArray(new ItemCompatibilityType[itemCompatibilityTypeList.size()]);
        itemCompatibilityListType.setCompatibility(itemCompatibilityTypearr);
        return itemCompatibilityListType;
    }

    /**
     * 获取ebay只能输入一个值德属性名
     *
     * @return
     */

    public static List<String> getEbayOnlyOneValue() {
        List<String> list = new ArrayList<>();
        list.add("TYPE");
        list.add("CALIFORNIA PROP 65 WARNING");
        return list;
    }

    /**
     * 封装配置属性对象的方法
     *
     * @param listingEbayValues
     * @return
     */
    public static NameValueListArrayType getNameValueListArrayType(List<ListingEbayValue> listingEbayValues) {
        NameValueListArrayType nameValueListArrayType = new NameValueListArrayType();
        NameValueListType[] nameValueListTypes = new NameValueListType[listingEbayValues.size()];
        for (int i = 0; i < listingEbayValues.size(); i++) {
            //判断属性中是否有, 并且属性值长度大于65字符 按照,分割
            if (ObjectUtils.isEmpty(listingEbayValues.get(i)) || ObjectUtils.isEmpty(listingEbayValues.get(i).getValue())) {
                continue;
            }
            // 不区分大小写包含
            if (!getEbayOnlyOneValue().contains(listingEbayValues.get(i).getName().toUpperCase()) && listingEbayValues.get(i).getValue().length() >= 65 && listingEbayValues.get(i).getValue().contains(",")) {

                String[] split = listingEbayValues.get(i).getValue().split(",");
                // 对分割后的每个值进行长度检查，如果超过65字符则从后往前找空格再次分割
                List<String> processedSplit = new ArrayList<>();
                for (String splitValue : split) {
                    String trimmedValue = splitValue.trim();
                    if (trimmedValue.length() > 65) {
                        // 从后往前找倒数第一个空格
                        int lastSpaceIndex = trimmedValue.lastIndexOf(' ');
                        if (lastSpaceIndex > 0) {
                            // 在空格处分割
                            processedSplit.add(trimmedValue.substring(0, lastSpaceIndex).trim());
                            processedSplit.add(trimmedValue.substring(lastSpaceIndex + 1).trim());
                        } else {
                            // 没有空格，直接添加
                            processedSplit.add(trimmedValue);
                        }
                    } else {
                        processedSplit.add(trimmedValue);
                    }
                }

                NameValueListType nameValueListType = new NameValueListType();
                nameValueListType.setName(listingEbayValues.get(i).getName());
                if (processedSplit.size() > 26) {
                    List<String> valueList = new ArrayList();
                    StringBuilder value = new StringBuilder(ObjUtil.isNotEmpty(processedSplit.get(0)) ? processedSplit.get(0).trim() : "");
                    for (int j = 1; j < processedSplit.size(); j++) {
                        String currentValue = processedSplit.get(j);
                        if (value.length() + currentValue.length() <= 60) {
                            value.append(" ").append(currentValue);
                            continue;
                        }
                        valueList.add(value.toString());
                        value = new StringBuilder(ObjUtil.isNotEmpty(currentValue)?currentValue.trim():"");
                    }
                    nameValueListType.setValue(valueList.toArray(new String[valueList.size()]));
                }else {
                    nameValueListType.setValue(processedSplit.toArray(new String[processedSplit.size()]));
                }
                nameValueListTypes[i] = nameValueListType;
                continue;
            }
            NameValueListType nameValueListType1 = new NameValueListType();
            nameValueListType1.setName(listingEbayValues.get(i).getName());
            nameValueListType1.setValue(new String[]{listingEbayValues.get(i).getValue()});
            nameValueListTypes[i] = nameValueListType1;
        }
        nameValueListArrayType.setNameValueList(nameValueListTypes);
        return nameValueListArrayType;
    }

    @PostConstruct
    protected void init() {
        GET_COMPATIBILITY_PROPERTIES_URL = getCompatibilityPropertiesUrl;
    }

    public ItemType setItemTypeBase(ItemType itemType, GoodsHead goodsHead, GoodsDescription goodsDescription, ListingEbayLine listingEbayLine, ListingEbayShippingHead listingEbayShippingHead, List<GoodsResource> goodsResourcesList, List<ListingEbayValue> listingEbayValues, ListingEbayVideo listingEbayVideo) {

        itemType.setItemID(goodsHead.getPlatformGoodsId());

        //国家-           -取值逻辑-  自动生成根据location
        itemType.setCountry(StrUtil.isBlank(listingEbayLine.getCountry()) ? CountryCodeType.US : CountryCodeType.fromValue(listingEbayLine.getCountry()));

        //币种-           -取值逻辑-  自动生成根据country
        itemType.setCurrency(StrUtil.isBlank(listingEbayLine.getCurrency()) ? CurrencyCodeType.USD : CurrencyCodeType.fromValue(listingEbayLine.getCurrency()));

        //地区-           -取值逻辑-  sc_smc_listing_ebay_line行表中的-location
        itemType.setLocation(listingEbayLine.getLocation());

        //邮政编码-        -取值逻辑-   自动生成根据location
//        itemType.setPostalCode(listingEbayLine.getPostcode());//邮政编码好像不是必填项

        //描述-           -取值逻辑-sc_smc_listing_goods_description   若有descriptionID需要根据模板id组装描述信息，若无则取detail_description
        itemType.setDescription(buildDescription(goodsResourcesList, goodsDescription, listingEbayValues,goodsHead.getTitle()));

        //活动天数-        -取值逻辑-   sc_smc_listing_ebay_line行表中的-sell_day
        itemType.setListingDuration(getListingDuration(goodsHead, listingEbayLine));

        //销售方式-       -取值逻辑-    sc_smc_listing_goods_head头表-publish_type【暂时固定为固定售卖类型】
        itemType.setListingType(getListingType(goodsHead.getPublishType()));

        //专用电话号码表-  -取值逻辑-  默认false
        itemType.setPrivateListing(false);

        //库存数量-       -取值逻辑-    头表中-stock_on_sales_qty
        itemType.setQuantity(getQuantity(goodsHead.getPublishType(), goodsHead.getStockOnSalesQty()));

        //站点-           -取值逻辑-  默认ebay摩托
        itemType.setSite(getSite(goodsHead.getSiteCode(), String.valueOf(goodsHead.getCategoryId())));

        //副标题【收费】   -取值逻辑-  头表中-subtitle
        itemType.setSubTitle(goodsHead.getSubtitle());


        //标题-           -取值逻辑-  头表中-title
        itemType.setTitle(goodsHead.getTitle());

        //SKU-            -取值逻辑-    头表中-platform_goods_code
        itemType.setSKU(goodsHead.getPlatformGoodsCode());

        //最大调度时间-    -取值逻辑-     sc_smc_listing_ebay_shipping_head   -  handling_time
        itemType.setDispatchTimeMax(Integer.valueOf(listingEbayShippingHead.getHandlingTime()));

        //数字标识符-     -取值逻辑-
        itemType.setConditionID(1000);

        //VAT税率
        itemType.setVATDetails(getVATDetails(goodsHead.getPlatform(), goodsHead.getSiteCode(), goodsHead.getShopCode()));

        //橱窗视频
        if (ObjUtil.isNotEmpty(listingEbayVideo)) {
            itemType.setVideoDetails(getVideoDetails(listingEbayVideo.getVideoId()));
        }


        //车辆列表的描述性自由文本标题【收费】-   -取值逻辑-
//        itemType.setSellerProvidedTitle("2017 GMC Yukon Denali 5***4 Miles Mineral Metallic");

        return itemType;
    }

    public VideoDetailsType getVideoDetails(String videoID) {
        if (ObjUtil.isEmpty(videoID)) {
            return null;
        }

        VideoDetailsType videoDetailsType = new VideoDetailsType();
        videoDetailsType.setVideoID(videoID);
        return videoDetailsType;
    }

    /**
     * 获取VAT税率 --增值税并不适用于所有国家，包括美国
     * @param platform
     * @param site
     * @param shopCode
     * @return
     */
    public VATDetailsType getVATDetails(String platform, String site, String shopCode) {
        ConfigStoreInfo info = new ConfigStoreInfo();
        info.setPlatform(platform);
        info.setSite(site);
        info.setShopCode(shopCode);
        List<ConfigStoreInfo> infoList = configStoreInfoService.selectConfigStoreInfoList(info);
        if (ObjectUtils.isEmpty(infoList) ||ObjectUtils.isEmpty(infoList.get(0).getVatRate())) {
            return null;
        }
        VATDetailsType vatDetailsType = new VATDetailsType();
        float vatRate = Float.valueOf(infoList.get(0).getVatRate());
        vatDetailsType.setVATPercent(vatRate);
        return vatDetailsType;
    }


    /**
     * 根据平台获取默认的适配车型属性
     *
     * @param siteCode
     * @return
     */
    public static EbayCompatibilityProperties getDefaultBySite(String siteCode) {
        if (Objects.equals(siteCode, "US")) {
            EbayCompatibilityProperties properties = new EbayCompatibilityProperties();
            EbayCompatibilityProperties.CompatibilityPropertiesBean b1 = new EbayCompatibilityProperties.CompatibilityPropertiesBean("Year", "Year");
            EbayCompatibilityProperties.CompatibilityPropertiesBean b2 = new EbayCompatibilityProperties.CompatibilityPropertiesBean("Make", "Make");
            EbayCompatibilityProperties.CompatibilityPropertiesBean b3 = new EbayCompatibilityProperties.CompatibilityPropertiesBean("Model", "Model");
            EbayCompatibilityProperties.CompatibilityPropertiesBean b4 = new EbayCompatibilityProperties.CompatibilityPropertiesBean("Trim", "Trim");
            EbayCompatibilityProperties.CompatibilityPropertiesBean b5 = new EbayCompatibilityProperties.CompatibilityPropertiesBean("Engine", "Engine");
            List<EbayCompatibilityProperties.CompatibilityPropertiesBean> list = new ArrayList<>();
            list.add(b1);
            list.add(b2);
            list.add(b3);
            list.add(b4);
            list.add(b5);
            properties.setCompatibilityProperties(list);
            return properties;
        }
        return null;
    }

    /**
     * 产品列表详情指定
     *
     * @param listingEbayValues <ProductListingDetails>
     *                          <UPC>997750800267</UPC>
     *                          <BrandMPN>
     *                          <Brand>KAC</Brand>
     *                          <MPN>LNM800267</MPN>
     *                          </BrandMPN>
     *                          <IncludeeBayProductDetails>true</IncludeeBayProductDetails>
     *                          </ProductListingDetails>
     * @return
     */
    public ProductListingDetailsType getProductListingDetailsType(GoodsHead head,List<ListingEbayValue> listingEbayValues) {
        ProductListingDetailsType productListingDetailsType = new ProductListingDetailsType();
        productListingDetailsType.setIncludeeBayProductDetails(true);
        String upc = listingEbayValues.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("UPC")).findFirst().orElse(new ListingEbayValue()).getValue();
        String ean = listingEbayValues.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("EAN")).findFirst().orElse(new ListingEbayValue()).getValue();
        String brand = listingEbayValues.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("Brand")||listingEbayValue.getName().equals("Hersteller"))
                .findFirst().orElse(new ListingEbayValue()).getValue();
        String mpn = listingEbayValues.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("Manufacturer Part Number") ||
                listingEbayValue.getName().contains("MPN") || listingEbayValue.getName().contains("Herstellernummer")).findFirst().orElse(new ListingEbayValue()).getValue();
        //德站
        if (Objects.equals(head.getSiteCode(),PlatformSiteEnum.DE.name())){
            if (StringUtils.isEmpty(ean)) {
                return null;
            }
            productListingDetailsType.setEAN(ean);
            BrandMPNType brandMPNType = new BrandMPNType();
            brandMPNType.setBrand(brand);
            brandMPNType.setMPN(mpn);
            productListingDetailsType.setBrandMPN(brandMPNType);
            return productListingDetailsType;
        }
        //美站
        if (Objects.equals(head.getSiteCode(),PlatformSiteEnum.US.name())){
            if (StringUtils.isEmpty(upc) || StringUtils.isEmpty(brand) || StringUtils.isEmpty(mpn)) {
                return null;
            }
            productListingDetailsType.setUPC(upc);
            BrandMPNType brandMPNType = new BrandMPNType();
            brandMPNType.setBrand(brand);
            brandMPNType.setMPN(mpn);
            productListingDetailsType.setBrandMPN(brandMPNType);
        }
        return productListingDetailsType;
    }

    /**
     * /**
     * 组装描述
     *
     * @param goodsDescription
     */
    public String buildDescription(List<GoodsResource> goodsResourcesList, GoodsDescription goodsDescription, List<ListingEbayValue> listingEbayValues,String title) {
        if (ObjectUtils.isEmpty(goodsDescription)) {
            return null;
        }
        //对描述中的图片进行http换链成https处理
        goodsDescription.setDetailDescription(commonInfoBiz.replaceHttpToHttps(goodsDescription.getDetailDescription()));
        goodsDescription.setShortDescription(commonInfoBiz.replaceHttpToHttps(goodsDescription.getShortDescription()));
        goodsDescriptionService.updateListingGoodsDescription(goodsDescription);

        if (ObjectUtils.isEmpty(goodsDescription.getDescriptionId())) {
            return goodsDescription.getDetailDescription();
        }
        //找到对应的模板
        TemplateEbayDescription ebayDescription = templateEbayDescriptionService.selectTemplateEbayDescriptionById(Long.valueOf(goodsDescription.getDescriptionId()));

        //新模板处理逻辑
//        if (Objects.equals(ebayDescription.getIsNew(), "1")) {
        RenderTemplateDescriptionToHtml toHtmlDTO = new RenderTemplateDescriptionToHtml();
        toHtmlDTO.setTemplateId(String.valueOf(goodsDescription.getDescriptionId()));
        toHtmlDTO.setImageSrcArr(CollUtil.isNotEmpty(goodsResourcesList) ? goodsResourcesList.stream().map(GoodsResource::getResourceUrl).collect(Collectors.toList()) : null);
        toHtmlDTO.setOnlineDesc(goodsDescription.getShortDescription());
        toHtmlDTO.setAttributeJsonArr(listingEbayValues);
        toHtmlDTO.setTitle( title );
        String html = templateEbayDescriptionBiz.renderTemplateDescriptionToHtml(toHtmlDTO);
        return commonInfoBiz.replaceHttpToHttps(html);
//        }


        //处理模板的html
//        List<TemplateWidgetDTO> widgetList = JSON.parseArray(ebayDescription.getWidgetConfig(), TemplateWidgetDTO.class);
//        List<TemplateWidget> templateWidgets = templateWidgetService.selectTemplateWidgetByIds(widgetList.stream().map(TemplateWidgetDTO::getId).collect(Collectors.toList()));
//        StringBuilder html = new StringBuilder();
//        StringBuilder mainImgHtml = new StringBuilder();
//        StringBuilder imgHtml = new StringBuilder();
//        //根据sort排序，小的在前面
//        templateWidgets.stream().sorted(Comparator.comparingInt(TemplateWidget::getSort)).forEach(templateWidget -> {
//            widgetList.stream().forEach(templateWidgetDTO -> {
//                if (templateWidget.getId() == (templateWidgetDTO.getId())) {
//                    //处理图片
//                    if (templateWidget.getType().equals("image")) {
//                        //拿到listing的对应图片数据
//                        mainImgHtml.append("<div class=\"relative flex mt-2 image-box widget justify-center\" id=\"template-images\">");
//                        for (GoodsResource goodsResource : goodsResourcesList) {
//                            if (ObjectUtils.isNotEmpty(goodsResource.getResourceUrl()) && goodsResource.getIsMain() == 1) {
//                                mainImgHtml.append("<div class=\"overflow-hidden rounded master\">\n" + "  <img  class=\"object-cover\" src=\"" + goodsResource.getResourceUrl() + "\" >\n" + "  </div>");
//
//                            }
//                            imgHtml.append("<div class=\"overflow-hidden rounded cursor-pointer img-item\">\n" + "  <img class=\"object-cover thumbnail\" src=\"" + goodsResource.getResourceUrl() + "\">\n" + "  <div class=\"absolute top-0 left-0 overflow-hidden rounded img-preview\">\n" + "  <img  class=\"object-cover\" src=\"" + goodsResource.getResourceUrl() + "\" >\n" + "  </div> \n" + "  </div>");
//
//
//                        }
//                        mainImgHtml.append("<div class=\"overflow-y-auto slave\">\n" + "  <div class=\"flex flex-row flex-wrap justify-start gap-2 px-4\">\n" + imgHtml + "  </div>\n" + "</div>" + "</div>");
//
//                        html.append(mainImgHtml);
//                    }
//                    //描述文案
//                    else if (templateWidget.getType().equals("descriptor")) {
//                        templateWidgetDTO.getConfigs().get(0).setDefaultValue(goodsDescription.getShortDescription());
//                        templateWidget.setRole("{configs:" + JSON.toJSONString(templateWidgetDTO.getConfigs()) + "}");
//                        html.append(templateWidgetService.renderWidgetHtml(templateWidget));
//                    } else {
//                        templateWidget.setRole("{configs:" + JSON.toJSONString(templateWidgetDTO.getConfigs()) + "}");
//                        html.append(templateWidgetService.renderWidgetHtml(templateWidget));
//                    }
//
//                }
//            });
//        });
//
//        //对应模板的配置
//        String config = ebayDescription.getConfig();
//        TemplateWidgetConfigDTO configObj = JSON.parseObject(config, TemplateWidgetConfigDTO.class);
//        String resp = templateEngine.process("template/widget/build", new Context());
//        String divstyle = "<div style=" + configObj.toStyle() + ">" + resp + html + "</div>";
//        return divstyle;


    }


    /**
     * 映射刊登日期
     *
     * @param listingEbayLine
     * @return
     */
    public String getListingDuration(GoodsHead goodsHead, ListingEbayLine listingEbayLine) {
        if (PublishType.FIXED.getType().equals((goodsHead.getPublishType()))) {
            return "GTC";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("Days_");
        if (PublishType.CHINESE.getType().equals((goodsHead.getPublishType())) && ObjectUtils.isNotEmpty(listingEbayLine.getSellDay())) {
            sb.append(listingEbayLine.getSellDay());
        }
        return sb.toString();
    }

    public ItemType setItemTypeObj(ItemType itemType, GoodsHead goodsHead, ListingEbayLine listingEbayLine, ListingEbayPolicy listingEbayPolicy, List<GoodsResource> goodsResourcesList, List<ListingShippingTypeLine> listingShippingTypeLines, List<ListingShippingLocationLine> listingShippingLocationLines, List<ListingEbayValue> listingEbayValues, List<ListingEbayAdaptive> ebayAdaptiveList) {

        //立即购买价格              -取值逻辑 - 取根据刊登类型为拍卖时填写
        itemType.setBuyItNowPrice(getBuyItNowPrice(goodsHead.getPublishType(), listingEbayLine.getOnePrice(), listingEbayLine.getCurrency()));

        //最低售价-收费
        itemType.setReservePrice(getReservePrice(goodsHead.getPublishType(), listingEbayLine.getReservePrice(), listingEbayLine.getCurrency()));
        //起拍价                   -取值逻辑 -固定、拍卖 均取头表中的sc_smc_listing_goods_head   -standard_price
        itemType.setStartPrice(getAmountType(Double.parseDouble(goodsHead.getStandardPrice()), CurrencyCodeType.fromValue(listingEbayLine.getCurrency())));

        //支付方式                  -取值逻辑 - 取sc_smc_template_ebay_policy   - payment_method
//        HashMap<String, BuyerPaymentMethodCodeType> buyerPaymentHashMapEnum = new HashMap<>();
//        buyerPaymentHashMapEnum.put(listingEbayPolicy.getPaymentPolicy(), BuyerPaymentMethodCodeType.fromValue(listingEbayPolicy.getPaymentPolicy()));
//        BuyerPaymentMethodCodeType buyerPaymentMethodCodeType = buyerPaymentHashMapEnum.get(listingEbayPolicy.getPaymentPolicy());
//        itemType.setPaymentMethods(new BuyerPaymentMethodCodeType[]{BuyerPaymentMethodCodeType.fromValue(listingEbayPolicy.getPaymentPolicy())});

        //商品类目信息                -取值逻辑 - 取sc_smc_platform_category category_id
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
        itemType.setPrimaryCategory(getCategoryType(platformCategory.getCategoryId(), platformCategory.getCategoryName()));
        //判断listingEbayLine.getFirstShopCategory()是纯数字还是纯字母
        if (ObjectUtils.isNotEmpty(listingEbayLine.getFirstShopCategory()) && listingEbayLine.getFirstShopCategory().matches("[0-9]+")) {
            //店铺类目信息
            StorefrontType storefrontType = new StorefrontType();
            storefrontType.setStoreCategoryID(StrUtil.isNotEmpty(listingEbayLine.getFirstShopCategory()) ? Long.parseLong(listingEbayLine.getFirstShopCategory()) : 0);
            storefrontType.setStoreCategory2ID(StrUtil.isNotEmpty(listingEbayLine.getSecondShopCategory()) ? Long.parseLong(listingEbayLine.getSecondShopCategory()) : 0);
//        storefrontType.setStoreURL("https://stores.ebay.com.hk/headlightsassembly");
            itemType.setStorefront(storefrontType);
        }

        //运费对象                  -取值逻辑 - 取sc_smc_listing_ebay_shipping_head表中关联的数据
        itemType.setShippingDetails(getShippingDetailsType(listingShippingTypeLines, listingShippingLocationLines, CurrencyCodeType.fromValue(listingEbayLine.getCurrency())));

        //最佳报价详情-是否开启议价   -取值逻辑 - 取sc_smc_listing_ebay_line-best_offer_flag
        itemType.setBestOfferDetails(getBestOfferDetailsType("1".equals((goodsHead.getPublishType().equals(PublishType.CHINESE.getType())) ? "0" : listingEbayLine.getBestOfferFlag()), 0));

        //照片信息对象               -取值逻辑 - 取资源表中sc_smc_listing_goods_resource的字段
        itemType.setPictureDetails(getPictureDetailsType(goodsResourcesList));

        //适配车型属性数据           -取值逻辑 -  取适配信息表中的数据sc_smc_listing_ebay_adaptive
        EbayCompatibilityProperties ebayCompatibilityProperties = retryable.retryableEps(() -> getEbayCompatibilityProperties(goodsHead.getShopCode(), goodsHead.getSiteCode(), platformCategory.getCategoryId()));
        if (ObjectUtils.isNotEmpty(ebayCompatibilityProperties)) {
            itemType.setItemCompatibilityList(getItemCompatibilityListType(false, ebayCompatibilityProperties, ebayAdaptiveList));
        } else {
            itemType.setItemCompatibilityList(getItemCompatibilityListType(false, getDefaultBySite(goodsHead.getSiteCode()), ebayAdaptiveList));
        }

        //物品属性               -取值逻辑 -  取属性表中的数据sc_smc_listing_ebay_value
        itemType.setItemSpecifics(getNameValueListArrayType(listingEbayValues));
        //特定的产品列表详情  品牌 以及upc mpn等
        itemType.setProductListingDetails(getProductListingDetailsType(goodsHead,listingEbayValues));

        //退货政策                  -取值逻辑 -  取付款&退货表中数据sc_smc_listing_ebay_policy
        itemType.setReturnPolicy(getReturnPolicyType(goodsHead, listingEbayPolicy));
        //付款政策
        if (Objects.equals(listingEbayLine.getCountry(), "US")) {
//            BuyerPaymentMethodCodeType[] nameValueListTypes = {BuyerPaymentMethodCodeType.PERSONAL_CHECK};
//            itemType.setPaymentMethods(nameValueListTypes);
        }
//        PaymentDetailsType paymentDetailsType = new PaymentDetailsType();
//        paymentDetailsType.setDepositAmount(getAmountType(111d,CurrencyCodeType.USD));
//        itemType.setPaymentDetails(paymentDetailsType);


        //卖方配置                  -取值逻辑 -  取付款&退货表中数据sc_smc_listing_ebay_policy
//        itemType.setSellerProfiles(getSellerProfilesType());

        return itemType;
    }

    /**
     * 获取立即购买价格
     *
     * @param publishType
     * @param onePrice
     * @param currency
     * @return
     */
    public AmountType getBuyItNowPrice(Integer publishType, String onePrice, String currency) {
        // 只有拍卖才设置
        if (publishType.equals(PublishType.CHINESE.getType()) || ObjectUtil.isEmpty(onePrice)) {
            return null;
        }
        // 将价格转换为 AmountType 对象
        double priceValue = Double.parseDouble(onePrice);
        CurrencyCodeType currencyValue = CurrencyCodeType.fromValue(currency);
        return getAmountType(priceValue, currencyValue);
    }

    /**
     * 映射站点
     *
     * @param site
     * @param categoryId
     * @return
     */
    public SiteCodeType getSite(String site, String categoryId) {
        if (!Objects.equals(site, PlatformSiteEnum.US.getName())) {
            PlatformSiteEnum enumByName = PlatformSiteEnum.getEnumByName(site);
            if (enumByName == null) {
                throw new BusinessException("站点选择错误,请重新选择.");
            }
            return SiteCodeType.fromValue(enumByName.getSiteCodeType());
        }
        CategoryInfo category = new CategoryInfo();
        category.setPlatformCategoryId(categoryId);
        category.setIsDistribution(SMCCommonEnum.SUCCESS.getValue());
        List<CategoryInfo> categoryInfoList = categoryInfoService.selectCategoryInfoList(category);
        if (ObjectUtils.isEmpty(categoryInfoList)) {
            return SiteCodeType.E_BAY_MOTORS;
        }
        if (ObjectUtils.equals(categoryInfoList.get(0).getIsDistribution(), 0)) {
            return SiteCodeType.E_BAY_MOTORS;
        }
        if (ObjectUtils.equals(categoryInfoList.get(0).getIsDistribution(), 1)) {
            return SiteCodeType.US;
        }
        return null;
    }

    /**
     * 封装金额对象的方法
     *
     * @param value
     * @param currencyCodeType
     * @return
     */

    public static AmountType getAmountType(double value, CurrencyCodeType currencyCodeType) {
        AmountType amountType = new AmountType();
        amountType.setValue(value);
        if (ObjectUtil.isNotEmpty(currencyCodeType)) {
            amountType.setCurrencyID(currencyCodeType);
        }
        return amountType;
    }

    /**
     * 封装类别对象的方法
     *
     * @param categoryID
     * @param categoryName
     * @return
     */
    public static CategoryType getCategoryType(String categoryID, String categoryName) {
        CategoryType categoryType = new CategoryType();
        categoryType.setCategoryID(categoryID);
        categoryType.setCategoryName(categoryName);
        return categoryType;
    }

    /**
     * 封装运输对象的方法
     *
     * @param listingEbayShippingHead
     * @param listingShippingLocationLines
     * @return
     */
    public static ShippingDetailsType getShippingDetailsType(List<ListingShippingTypeLine> listingEbayShippingHead, List<ListingShippingLocationLine> listingShippingLocationLines, CurrencyCodeType currencyCode) {
        ShippingDetailsType shippingDetailsType = new ShippingDetailsType();
        List<ShippingServiceOptionsType> shippingServiceOptionsType = new ArrayList<>();
        Stream.iterate(1, i -> i + 1).limit(listingEbayShippingHead.size()).forEach(index -> {
            ListingShippingTypeLine p = listingEbayShippingHead.get(index - 1);
            shippingServiceOptionsType.add(getShippingServiceOptionsType(p.getShippingService(), getAmountType(Double.valueOf(String.valueOf(ObjectUtils.isNotEmpty(p.getShippingCost()) ? p.getShippingCost() : BigDecimal.ONE)), currencyCode), index, Boolean.FALSE, Integer.valueOf(p.getShippingTimeMin()), Integer.valueOf(p.getShippingTimeMax()), "1".equals(p.getFreeShippingFlag()) ? Boolean.TRUE : Boolean.FALSE, currencyCode));
        });
//        ShippingServiceOptionsType us_standardSppedPAK1 = getShippingServiceOptionsType("ShippingMethodStandard", getAmountType(0.0d, CurrencyCodeType.USD), 1, Boolean.FALSE, 1, 5, Boolean.FALSE);
//        ShippingServiceOptionsType us_standardSppedPAK2 = getShippingServiceOptionsType("UPSGround", getAmountType(0.0d, CurrencyCodeType.USD), 2, Boolean.FALSE, 1, 5, Boolean.FALSE);

//        shippingServiceOptionsTypes = ArrayUtil.newArray(ShippingServiceOptionsType.class, shippingServiceOptionsType.size());

        ShippingServiceOptionsType[] shippingServiceOptionsTypes = new ShippingServiceOptionsType[shippingServiceOptionsType.size()];
        Stream.iterate(1, i -> i + 1).limit(shippingServiceOptionsType.size()).forEach(index -> {
            shippingServiceOptionsTypes[index - 1] = shippingServiceOptionsType.get(index - 1);
        });

        List<String> excldeLocations = new ArrayList<>();
        listingShippingLocationLines.stream().forEach(lo -> {
            excldeLocations.add(lo.getLocation());
        });
        List<String> arrayList = new ArrayList(new HashSet(excldeLocations));
        String[] excludeShipToLocation = arrayList.toArray(new String[arrayList.size()]);
//        String[] excludeShipToLocation = {"Alaska/Hawaii", "APO/FPO", "US Protectorates", "Africa", "Asia", "Central America and Caribbean", "Europe", "Middle East", "North America", "Oceania", "Southeast Asia", "South America", "PO Box"};

        //配置物流服务方式
        shippingDetailsType.setShippingServiceOptions(shippingServiceOptionsTypes);
        //物流方式   sc_smc_listing_shipping_type_line    shipping_type 暂时写死
        shippingDetailsType.setShippingType(ShippingTypeCodeType.FLAT);
        //排除地区国家
        shippingDetailsType.setExcludeShipToLocation(excludeShipToLocation);
        // 刊登时用不到
//        SalesTaxType salesTaxType = new SalesTaxType();
//        AmountType amountType3 = getAmountType(0.0d, null);
//        salesTaxType.setShippingIncludedInTax(Boolean.FALSE);
//        salesTaxType.setSalesTaxAmount(amountType3);
//        shippingDetailsType.setSalesTax(salesTaxType);
//        // 输入时，这是为国内配送服务提供的运费折扣的 ID（其中运费折扣类型为“固定运费折扣”或“计算运费折扣”类型）。
//        shippingDetailsType.setShippingDiscountProfileID("0");
//        //输入时，这是为国际运输服务提供的运输折扣的 ID（其中运输折扣类型为“固定运输折扣”或“计算运输折扣”类型）。
//        shippingDetailsType.setInternationalShippingDiscountProfileID("0");
        //新增时不用此字段
//        shippingDetailsType.setSellerExcludeShipToLocationsPreference(Boolean.TRUE);
        return shippingDetailsType;
    }

    /**
     * 封装运输对象中的【运输服务】对象选项方法
     *
     * @param shippingService
     * @param shippingServiceCost
     * @param shippingServicePriority
     * @param expeditedService
     * @param shippingTimeMin
     * @param shippingTimeMax
     * @param freeShipping
     * @return
     */
    public static ShippingServiceOptionsType getShippingServiceOptionsType(String shippingService, AmountType shippingServiceCost, Integer shippingServicePriority, Boolean expeditedService, Integer shippingTimeMin, Integer shippingTimeMax, Boolean freeShipping, CurrencyCodeType currencyCode) {
        ShippingServiceOptionsType shippingServiceOptionsType = new ShippingServiceOptionsType();
        //此枚举值表示卖方提供的特定国内运输服务选项，用于将项目运送到与该项目位于同一国家的买方
        shippingServiceOptionsType.setShippingService(shippingService);
        //费用
        shippingServiceOptionsType.setShippingServiceCost(shippingServiceCost);
        //控制订单(相对于其他运输服务)，其中对应的ShippingService将出现在“查看项目”和“结付”页中。
        shippingServiceOptionsType.setShippingServicePriority(shippingServicePriority);
        //是否加急订单，默认false
        shippingServiceOptionsType.setExpeditedService(expeditedService);
        //
        shippingServiceOptionsType.setShippingTimeMin(shippingTimeMin);
        //
        shippingServiceOptionsType.setShippingTimeMax(shippingTimeMax);
        //是否免费
        shippingServiceOptionsType.setFreeShipping(freeShipping);

        shippingServiceOptionsType.setShippingServiceCost(getAmountType(0d, currencyCode));
        shippingServiceOptionsType.setShippingServiceAdditionalCost(getAmountType(0d, currencyCode));
        return shippingServiceOptionsType;
    }

    /**
     * 封装是否开启议价对象的方法
     *
     * @param bestOfferEnabled
     * @param count
     * @return
     */
    public static BestOfferDetailsType getBestOfferDetailsType(Boolean bestOfferEnabled, Integer count) {
        BestOfferDetailsType bestOfferDetailsType = new BestOfferDetailsType();
        bestOfferDetailsType.setBestOfferEnabled(bestOfferEnabled);
        bestOfferDetailsType.setBestOfferCount(count);
        return bestOfferDetailsType;
    }

    /**
     * 封装配置图片对象的方法
     *
     * @param goodsResourcesList
     * @return
     */
    public static PictureDetailsType getPictureDetailsType(List<GoodsResource> goodsResourcesList) {
        PictureDetailsType pictureDetailsType = new PictureDetailsType();
        List<String> pictureUrlList = new LinkedList<>();
        Map<String, List<GoodsResource>> resource = goodsResourcesList.stream().filter(p -> ObjectUtils.isNotEmpty(p.getResourceType())).collect(Collectors.groupingBy(GoodsResource::getResourceType));
        if (resource.size() != 0) {
            //视频数据
            List<GoodsResource> videoResource = resource.get("0");
            //图片数据
            List<GoodsResource> imgResource = resource.get("1");
            if (ObjectUtils.isNotEmpty(imgResource) && imgResource.size() != 0) {

                //主图
                List<String> mainPictureUrl = imgResource.stream().filter(f -> Objects.equals(f.getIsMain(), 1)).map(GoodsResource::getResourceUrl).collect(Collectors.toList());
                if (ObjectUtils.isNotEmpty(mainPictureUrl) && mainPictureUrl.size() != 0) {
                    pictureUrlList.add(mainPictureUrl.get(0));
                }

                //副图
                List<String> secondaryDrawingUrl = imgResource.stream().filter(f -> Objects.equals(f.getIsMain(), 0)).map(GoodsResource::getResourceUrl).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(secondaryDrawingUrl)) {
                    pictureUrlList.addAll(secondaryDrawingUrl);
                }
            }
        }


        if (CollectionUtils.isNotEmpty(pictureUrlList) && pictureUrlList.size() > 24) {
            pictureUrlList = pictureUrlList.subList(0, 24);
        }
        if (CollectionUtils.isNotEmpty(pictureUrlList)) {
            pictureDetailsType.setPictureURL(pictureUrlList.stream().toArray(String[]::new));
        }
        pictureDetailsType.setGalleryType(GalleryTypeCodeType.GALLERY);
        pictureDetailsType.setPhotoDisplay(PhotoDisplayCodeType.PICTURE_PACK);
        pictureDetailsType.setPictureSource(PictureSourceCodeType.EPS);
        return pictureDetailsType;
    }

    /**
     * 设置最低售价 --会收费
     * @param publishType
     * @param reservePrice
     * @return
     */
    public AmountType getReservePrice(Integer publishType, String reservePrice, String currency) {
        if (!Objects.equals(publishType, PublishType.CHINESE.getType()) || ObjectUtils.isEmpty(reservePrice)) {
            return null;
        }
        AmountType amountType = new AmountType();
        amountType.setValue(Double.parseDouble(reservePrice));
        amountType.setCurrencyID(CurrencyCodeType.fromValue(currency));
        return amountType;
    }

    /**
     * 映射ebay刊登类型
     *
     * @param publishType
     * @return
     */
    public ListingTypeCodeType getListingType(Integer publishType) {
        //2为多变体 3为固定 4为拍卖   	https://developer.ebay.com/Devzone/XML/docs/Reference/eBay/types/ListingTypeCodeType.html
        switch (publishType) {
            case (2):
                return ListingTypeCodeType.FIXED_PRICE_ITEM;
            case (4):
                return ListingTypeCodeType.CHINESE;
            case (3):
            default:
                return ListingTypeCodeType.FIXED_PRICE_ITEM;
        }
    }

    /**
     * 映射库存数量
     *
     * @param publishType
     * @param stockOnSalesQty
     * @return
     */
    public Integer getQuantity(Integer publishType, BigDecimal stockOnSalesQty) {
        if (PublishType.FIXED.getType().equals(publishType) || PublishType.POLYTROPIC.getType().equals(publishType)) {
            return ObjectUtils.isNotEmpty(stockOnSalesQty) ? stockOnSalesQty.intValue() : 0;
        }
        if (PublishType.CHINESE.getType().equals(publishType)) {
            return 1;
        }
        return 0;
    }

    /**
     * 封装退货政策的方法
     *
     * @param goodsHead
     * @param listingEbayPolicy
     * @return
     */
    public ReturnPolicyType getReturnPolicyType(GoodsHead goodsHead, ListingEbayPolicy listingEbayPolicy) {
        ReturnPolicyType returnPolicyType = new ReturnPolicyType();
        //指定买家退货的时间量 domestic_return_day
        //适用值为或。设置为 时，此选项表示卖家允许退回商品。如果不接受退货，请为商品指定退货不接受 domestic_return_flag
        returnPolicyType.setReturnsAcceptedOption("1".equals(listingEbayPolicy.getDomesticReturnFlag()) ? "ReturnsAccepted" : "ReturnsNotAccepted");
        if ("1".equals(listingEbayPolicy.getDomesticReturnFlag())) {
            returnPolicyType.setShippingCostPaidByOption(listingEbayPolicy.getDomesticReturnBearer());
            returnPolicyType.setReturnsWithinOption("Days_" + listingEbayPolicy.getDomesticReturnDay().replaceAll("\\D", ""));
            returnPolicyType.setReturnsWithin(listingEbayPolicy.getDomesticReturnDay() + " Days");
        }
        //指定卖家支付运费还是买家支付运费          domestic_return_bearer
        returnPolicyType.setInternationalReturnsAcceptedOption("1".equals(listingEbayPolicy.getInternationalReturnFlag()) ? "ReturnsAccepted" : "ReturnsNotAccepted");
        if ("1".equals(listingEbayPolicy.getInternationalReturnFlag())) {
            returnPolicyType.setShippingCostPaidBy(listingEbayPolicy.getInternationalReturnBearer());
            returnPolicyType.setInternationalReturnsWithinOption("Days_" + listingEbayPolicy.getInternationalReturnDay().replaceAll("\\D", ""));
            returnPolicyType.setInternationalShippingCostPaidByOption(listingEbayPolicy.getInternationalReturnBearer());
        }

        if (ObjUtil.isEmpty(listingEbayPolicy.getReturnsDescription())) {
            TemplateEbayPolicy templateEbayPolicy = templateShopRelationService.selectTemplatePolicyInfo(goodsHead.getShopCode(), "policy");
            if (ObjectUtils.isEmpty(templateEbayPolicy) || ObjectUtils.isEmpty(templateEbayPolicy.getReturnsDescription())) {
                return returnPolicyType;
            }
            listingEbayPolicy.setReturnsDescription(templateEbayPolicy.getReturnsDescription());
            ebayPolicyService.updateListingEbayPolicy(listingEbayPolicy);
        }
        returnPolicyType.setDescription(listingEbayPolicy.getReturnsDescription());
        return returnPolicyType;
    }

    /**
     * 封装适配属性对象中组装对象的方法
     *
     * @param name
     * @param value
     * @return
     */
    public static NameValueListType setNameValueListType(String name, String[] value) {
        NameValueListType nameValueListType = new NameValueListType();
        nameValueListType.setName(name);
        nameValueListType.setValue(value);
        return nameValueListType;
    }

    /**
     * @description:ebay刊登暂时不需要的字段
     */
    public ItemType setItemTypeOther(ItemType itemType) {
        itemType.setApplicationData("");
        itemType.setAutoPay(false);
        itemType.setBuyerProtection(BuyerProtectionCodeType.ITEM_INELIGIBLE);
        itemType.setCategoryMappingAllowed(false);
        itemType.setCharity(new CharityType());
        itemType.setCrossPromotion(new CrossPromotionsType());
        itemType.setDescriptionReviseMode(DescriptionReviseModeCodeType.REPLACE);
        itemType.setDistance(new DistanceType());
        itemType.setHitCounter(HitCounterCodeType.NO_HIT_COUNTER);
        itemType.setItemID("");
        itemType.setListingDetails(new ListingDetailsType());
        itemType.setListingDesigner(new ListingDesignerType());
//        itemType.setListingEnhancement(new ListingEnhancementsCodeType[]());
        itemType.setListingEnhancement(0, ListingEnhancementsCodeType.BORDER);
        itemType.setLotSize(0);
        itemType.setPartnerCode("");
        itemType.setPartnerName("");
        itemType.setPaymentDetails(new PaymentDetailsType());
        itemType.setPayPalEmailAddress("");
        itemType.setPrivateNotes("");
        itemType.setRegionID("");
        itemType.setRelistLink(false);
        itemType.setReviseStatus(new ReviseStatusType());
//        itemType.setScheduleTime(new Calendar());
        itemType.setSecondaryCategory(new CategoryType());
        itemType.setFreeAddedCategory(new CategoryType());
        itemType.setSeller(new UserType());
        itemType.setSellingStatus(new SellingStatusType());
        itemType.setUUID("");
        itemType.setSellerVacationNote("");
        itemType.setWatchCount(0L);
        itemType.setHitCount(0L);
        itemType.setDisableBuyerRequirements(false);
        itemType.setLocationDefaulted(false);
        itemType.setUseTaxTable(false);
        itemType.setGetItFast(false);
        itemType.setBuyerResponsibleForShipping(false);
        itemType.setLimitedWarrantyEligible(false);
        itemType.setEBayNotes("");
        itemType.setQuestionCount(0L);
        itemType.setRelisted(false);
        itemType.setQuantityAvailable(0);
        itemType.setSearchDetails(new SearchDetailsType());
        itemType.setBestOfferEnabled(false);
        itemType.setLocalListing(false);
        itemType.setSellerContactDetails(new AddressType());
        itemType.setTotalQuestionCount(0L);
        itemType.setProxyItem(false);
        itemType.setExtendedSellerContactDetails(new ExtendedContactDetailsType());
        itemType.setLeadCount(0);
        itemType.setNewLeadCount(0);
        itemType.setGroupCategoryID("");
        itemType.setClassifiedAdPayPerLeadFee(new AmountType());
        itemType.setBidGroupItem(false);
        itemType.setApplyBuyerProtection(new BuyerProtectionDetailsType());
        itemType.setListingSubtype2(ListingSubtypeCodeType.CLASSIFIED_AD);
        itemType.setMechanicalCheckAccepted(false);
        itemType.setUpdateSellerInfo(false);
        itemType.setUpdateReturnPolicy(false);
        itemType.setItemPolicyViolation(new ItemPolicyViolationType());
//        itemType.setCrossBorderTrade(new String[]());
        itemType.setCrossBorderTrade(0, "");
        itemType.setBusinessSellerDetails(new BusinessSellerDetailsType());
        itemType.setBuyerGuaranteePrice(new AmountType());
        itemType.setBuyerRequirementDetails(new BuyerRequirementDetailsType());
//        itemType.setPaymentAllowedSite(new SiteCodeType[]());
        itemType.setPaymentAllowedSite(0, SiteCodeType.US);
        itemType.setInventoryTrackingMethod(InventoryTrackingMethodCodeType.ITEM_ID);
        itemType.setIntegratedMerchantCreditCardEnabled(false);
        itemType.setVariations(new VariationsType());
        itemType.setItemCompatibilityCount(0);
        itemType.setConditionDescription("");
        itemType.setConditionDisplayName("");
        itemType.setTaxCategory("");
        itemType.setQuantityAvailableHint(QuantityAvailableHintCodeType.LIMITED);
        itemType.setQuantityThreshold(0);
        itemType.setDiscountPriceInfo(new DiscountPriceInfoType());
        itemType.setVINLink("");
        itemType.setVRM("");
        itemType.setVRMLink("");
        itemType.setQuantityInfo(new QuantityInfoType());
        itemType.setShippingServiceCostOverrideList(new ShippingServiceCostOverrideListType());
        itemType.setShippingOverride(new ShippingOverrideType());
        itemType.setShippingPackageDetails(new ShipPackageDetailsType());
        itemType.setTopRatedListing(false);
        itemType.setQuantityRestrictionPerBuyer(new QuantityRestrictionPerBuyerInfoType());
        itemType.setFloorPrice(new AmountType());
        itemType.setCeilingPrice(new AmountType());
        itemType.setIsIntermediatedShippingEligible(false);
        itemType.setUnitInfo(new UnitInfoType());
        itemType.setRelistParentID(0L);
        itemType.setConditionDefinition("");
        itemType.setHideFromSearch(false);
        itemType.setReasonHideFromSearch(ReasonHideFromSearchCodeType.DUPLICATE_LISTING);
        itemType.setIncludeRecommendations(false);
        itemType.setPickupInStoreDetails(new PickupInStoreDetailsType());
        itemType.setSiteId(0);
        itemType.setIgnoreQuantity(false);
        itemType.setAvailableForPickupDropOff(false);
        itemType.setEligibleForPickupDropOff(false);
        itemType.setLiveAuction(false);
        itemType.setDigitalGoodInfo(new DigitalGoodInfoType());
        itemType.setEBayPlus(false);
        itemType.setEBayPlusEligible(false);
        itemType.setEMailDeliveryAvailable(false);
        itemType.setIsSecureDescription(false);
//        itemType.setAny(new Object[]());
        itemType.setAny(0, new Object());

        //第二次筛选出来的额外数据-------------------小黑屋-------start
        //车辆识别号--取值逻辑-
        itemType.setVIN("1214521580");
        //卖方排除船舶到地点优先
        itemType.setShipToLocations(new String[]{"US"});

        //第二次筛选出来的额外数据-------------------小黑屋-------end
        return itemType;
    }
    /**
     * 创建视频
     */
    public String createVideo(EbayVideo ebayVideo){
        String videoId ;
        try {
            String result = HttpUtils.post(createVideo, JSON.toJSONString(ebayVideo));
            if (ObjUtil.isEmpty(result)){
                return null;
            }
            AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
            if (ajaxResult.isSuccess()){
                videoId = (String) ajaxResult.get(AjaxResult.DATA_TAG);
            }else {
                log.error("ebay创建视频失败,参数:{},返回结果:{}",JSON.toJSONString(ebayVideo),result);
                return null;
            }
        } catch (Exception e) {
            log.error("ebay创建视频失败,参数:{}",JSON.toJSONString(ebayVideo),e);
            return null;
        }
        return videoId;
    }

    /**
     * 上传视频
     */
    public void uploadVideo(EbayVideo ebayVideo) {
        try {
            String result = HttpUtils.post(uploadVideo, JSON.toJSONString(ebayVideo));
            if (ObjUtil.isEmpty(result)){
                return ;
            }
            AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
            if (!ajaxResult.isSuccess()){
                log.error("ebay上传视频失败,参数:{},返回结果:{}",JSON.toJSONString(ebayVideo),result);
            }
        } catch (Exception e) {
            log.error("ebay创建视频失败,参数:{}",JSON.toJSONString(ebayVideo),e);
        }
    }

    /**
     * 获取视频信息
     */
    public EbayVideoInfo getVideo(EbayVideo ebayVideo){
        try {
            String result = HttpUtils.post(getVideo, JSON.toJSONString(ebayVideo));
            if (ObjUtil.isEmpty(result)){
                return  null;
            }
            AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
            EbayVideoInfo info = JSONObject.parseObject((String) ajaxResult.get(AjaxResult.DATA_TAG), EbayVideoInfo.class);
            if (ObjUtil.isEmpty(info)){
                log.error("ebay获取视频失败,参数:{},返回结果:{}",JSON.toJSONString(ebayVideo),result);
                return null;
            }
            return info;
        }catch (Exception e){
            log.error("ebay获取视频失败,参数:{}",JSON.toJSONString(ebayVideo),e);
            return null;
        }
    }

}
