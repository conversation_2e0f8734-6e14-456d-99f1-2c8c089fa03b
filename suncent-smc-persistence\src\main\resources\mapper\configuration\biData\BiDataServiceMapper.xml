<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suncent.smc.persistence.bi.mapper.BiDataServiceMapper">
  

<select id="getCompanyListingDetail" resultType="java.util.Map">
    SELECT
        SUBSTR(SYSDATE(),1,10)      as period_id_d,
        a.platform_sale_code,
        sum(a.sales_volume_30)      AS sales_volume_30,
        sum(a.order_quantity_30)  AS order_quantity_30,
        sum(a.sales_volume_60)      AS sales_volume_60,
        sum(a.order_quantity_60)  AS order_quantity_60,
        sum(a.sales_volume_90)      AS sales_volume_90,
        sum(a.order_quantity_90)  AS order_quantity_90,
        sum(a.sales_volume_270)      AS sales_volume_270,
        sum(a.order_quantity_270)  AS order_quantity_270
    from (
             SELECT
                 platform_sale_code,
                 sum(quantity) as sales_volume_30,
                 count(plat_order_id) as order_quantity_30,
                 ''as sales_volume_60,
                 ''as order_quantity_60,
                 ''as sales_volume_90,
                 ''as order_quantity_90,
                 '' as sales_volume_270,
                 '' as order_quantity_270
             FROM
                 dwi_sales_detail
             WHERE type = 'Order'
               AND platform_sale_code is not null
               AND  (period_id_d BETWEEN SUBSTR(subdate(SYSDATE(), interval 1 month),1,10)and SUBSTR(SYSDATE(),1,10))
             GROUP BY platform_sale_code

             union all

             SELECT
                 platform_sale_code,
                 ''as sales_volume_30,
                 ''as order_quantity_30,
                 sum(quantity) as sales_volume_60,
                 count(plat_order_id) as order_quantity_60,
                 ''as sales_volume_90,
                 ''as order_quantity_90,
                 '' as sales_volume_270,
                 '' as order_quantity_270
             FROM
                 dwi_sales_detail
             WHERE type = 'Order'
               AND platform_sale_code is not null
               AND  (period_id_d BETWEEN SUBSTR(subdate(SYSDATE(), interval 2 month),1,10)and SUBSTR(SYSDATE(),1,10))
             GROUP BY platform_sale_code

             union all

             SELECT
                 platform_sale_code,
                 ''as sales_volume_30,
                 ''as order_quantity_30,
                 ''as sales_volume_60,
                 ''as order_quantity_60,
                 sum(quantity) as sales_volume_90,
                 count(plat_order_id) as order_quantity_90,
                 '' as sales_volume_270,
                 '' as order_quantity_270
             FROM
                 dwi_sales_detail
             WHERE
                 type = 'Order'
               AND  (period_id_d BETWEEN SUBSTR(subdate(SYSDATE(), interval 3 month),1,10)and SUBSTR(SYSDATE(),1,10))
             GROUP BY platform_sale_code
             union all

             SELECT
                 platform_sale_code,
                 ''as sales_volume_30,
                 ''as order_quantity_30,
                 ''as sales_volume_60,
                 ''as order_quantity_60,
                 '' as sales_volume_90,
                 '' as order_quantity_90,
                 sum(quantity) as sales_volume_270,
                 count(plat_order_id) as order_quantity_270
             FROM
                 dwi_sales_detail
             WHERE
                 type = 'Order'
               AND  (period_id_d BETWEEN SUBSTR(subdate(SYSDATE(), interval 9 month),1,10)and SUBSTR(SYSDATE(),1,10))
             GROUP BY platform_sale_code
         )a GROUP BY platform_sale_code
    </select>

    <select id="queryFrontSaleTotalByShop" resultType="java.util.Map">
        SELECT
        sum(quantity) quantity ,
        count(plat_order_id) plat_order_id,
        shop_code,
        platform_sale_code,
        platform
        FROM
        dwi_sales_detail
        WHERE
        type = 'Order'
        and shop_code= #{shopCode}
        <if test="asinList != null and asinList.size() != 0">
            and platform_sale_code in
            <foreach collection="asinList" open="(" separator="," close=")" item="asin">
                #{asin}
            </foreach>
        </if>
        and platform_sale_code !=''
        GROUP BY platform,shop_code,platform_sale_code order by quantity desc limit #{size}
    </select>


    <select id="querySaleAndOrderTotal" resultType="java.util.Map">
        SELECT
            sum(quantity) quantity ,
            count(plat_order_id) plat_order_id,
            shop_code,
            platform_sale_code,
            platform
        FROM
            dwi_sales_detail
        WHERE
            type = 'Order' AND period_id_d >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
          <if test="shopCode != null and shopCode != ''">
            and shop_code= #{shopCode}
           </if>
          and platform_sale_code in
            <foreach collection="asinList" open="(" separator="," close=")" item="asin">
                #{asin}
            </foreach>
        GROUP BY platform,shop_code,platform_sale_code
    </select>
    <resultMap id="BiSkuRefundRatePjMap" type="com.suncent.smc.persistence.bi.entity.BiSkuRefundRatePj">
        <result property="periodIdD" column="period_id_d"/>
        <result property="periodIdM" column="period_id_m"/>
        <result property="platform" column="platform"/>
        <result property="classificationName" column="classification_name"/>
        <result property="operationClassification" column="operation_classification"/>
        <result property="sku" column="sku"/>
        <result property="refundRate" column="refund_rate"/>
        <result property="refundRateLow" column="refund_rate_low"/>
        <result property="refundRateUp" column="refund_rate_up"/>
        <result property="refundRateLabel" column="refund_rate_label"/>
        <result property="isNew" column="is_new"/>
        <result property="biLastUpdateTime" column="bi_last_update_time"/>
    </resultMap>


    <select id="queryTodaySkuRefundRateData" resultMap="BiSkuRefundRatePjMap">
        SELECT period_id_d,period_id_m,platform,classification_name,operation_classification,
               sku,refund_rate,refund_rate_low,refund_rate_up,refund_rate_label,is_new,bi_last_update_time FROM dws_label_sku_refund_rate_pj
        WHERE  DATE(period_id_d) = CURDATE();
    </select>

    <resultMap id="BiAsinRefundRatePjMap" type="com.suncent.smc.persistence.bi.entity.BiAsinRefundRatePj">
        <result property="periodIdD" column="period_id_d"/>
        <result property="periodIdM" column="period_id_m"/>
        <result property="platform" column="platform"/>
        <result property="classificationName" column="classification_name"/>
        <result property="operationClassification" column="operation_classification"/>
        <result property="platformSaleCode" column="platform_sale_code"/>
        <result property="refundRate" column="refund_rate"/>
        <result property="refundRateUp" column="refund_rate_up"/>
        <result property="refundRateLabel" column="refund_rate_label"/>
        <result property="isNew" column="is_new"/>
        <result property="biLastUpdateTime" column="bi_last_update_time"/>
    </resultMap>

    <select id="queryTodayListingRefundRateData" resultMap="BiAsinRefundRatePjMap">
        SELECT period_id_d,period_id_m,platform,classification_name,operation_classification,
               platform_sale_code,refund_rate,refund_rate_up,refund_rate_label,is_new,bi_last_update_time
        FROM dws_label_asin_refund_rate_pj
        WHERE  DATE(period_id_d) = CURDATE();
    </select>

  <resultMap id="selectSalesAnalysisMap" type="com.suncent.smc.persistence.bi.entity.BiSalesAnalysis">
        <result property="periodIdD" column="period_id_d"/>
        <result property="platform" column="platform"/>
        <result property="country" column="country"/>
        <result property="shopCode" column="shop_code"/>
        <result property="platformSku" column="platform_sku"/>
        <result property="platformSaleCode" column="platform_sale_code"/>
        <result property="salesQty7" column="sales_qty_7"/>
        <result property="salesQty7O" column="sales_qty_7_o"/>
        <result property="salesQty7Qoq" column="sales_qty_7_qoq"/>
        <result property="salesQty30" column="sales_qty_30"/>
        <result property="salesQty30O" column="sales_qty_30_o"/>
        <result property="salesQty30Qoq" column="sales_qty_30_qoq"/>
        <result property="sessions7" column="sessions_7"/>
        <result property="sessions7O" column="sessions_7_o"/>
        <result property="sessions7Qoq" column="sessions_7_qoq"/>
        <result property="sessions30" column="sessions_30"/>
        <result property="sessions30O" column="sessions_30_o"/>
        <result property="sessions30Qoq" column="sessions_30_qoq"/>
        <result property="adSpend7" column="ad_spend_7"/>
        <result property="adSpend7O" column="ad_spend_7_o"/>
        <result property="adSpend7Qoq" column="ad_spend_7_qoq"/>
        <result property="adSpend30" column="ad_spend_30"/>
        <result property="adSpend30O" column="ad_spend_30_o"/>
        <result property="adSpend30Qoq" column="ad_spend_30_qoq"/>
        <result property="grossProfitRealRate7" column="gross_profit_real_rate_7"/>
        <result property="grossProfitRealRate70" column="gross_profit_real_rate_7_0"/>
        <result property="grossProfitRealRate7Qoq" column="gross_profit_real_rate_7_qoq"/>
        <result property="grossProfitRealRate30" column="gross_profit_real_rate_30"/>
        <result property="grossProfitRealRate30O" column="gross_profit_real_rate_30_o"/>
        <result property="grossProfitRealRate30Qoq" column="gross_profit_real_rate_30_qoq"/>
        <result property="biLastUpdateTime" column="bi_last_update_time"/>
    </resultMap>


    <sql id="selectSalesAnalysisVo">
        SELECT
            period_id_d,
            platform,
            country,
            shop_code,
            platform_sku,
            platform_sale_code,
            sales_qty_7,
            sales_qty_7_o,
            sales_qty_7_qoq,
            sales_qty_30,
            sales_qty_30_o,
            sales_qty_30_qoq,
            sessions_7,
            sessions_7_o,
            sessions_7_qoq,
            sessions_30,
            sessions_30_o,
            sessions_30_qoq,
            ad_spend_7,
            ad_spend_7_o,
            ad_spend_7_qoq,
            ad_spend_30,
            ad_spend_30_o,
            ad_spend_30_qoq,
            gross_profit_real_rate_7,
            gross_profit_real_rate_7_0,
            gross_profit_real_rate_7_qoq,
            gross_profit_real_rate_30,
            gross_profit_real_rate_30_o,
            gross_profit_real_rate_30_qoq,
            bi_last_update_time
        FROM dws_smc_ad_sales_analysis
    </sql>

    <select id="queryTodaySalesAnalysis" resultMap="selectSalesAnalysisMap">
        <include refid="selectSalesAnalysisVo"/>
        WHERE   platform in('EBAY','AMAZON')
        AND DATE(period_id_d) = CURDATE()
        <if test="shopCode != null and shopCode != ''">
            AND shop_code = #{shopCode}
        </if>
    </select>

    <select id="queryTodaySalesAnalysisPaged" resultMap="selectSalesAnalysisMap">
        <include refid="selectSalesAnalysisVo"/>
        WHERE platform in('EBAY','AMAZON')
        AND DATE(period_id_d) = CURDATE()
        <if test="shopCode != null and shopCode != ''">
            AND shop_code = #{shopCode}
        </if>
        ORDER BY period_id_d, platform_sku
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="getTodaySalesAnalysisByAsin" resultMap="selectSalesAnalysisMap">
        <include refid="selectSalesAnalysisVo"/>
        WHERE   platform in('EBAY','AMAZON')
        AND DATE(period_id_d) = CURDATE() AND platform_sale_code = #{asin} AND shop_code = #{shopCode}   limit 1
    </select>
    <select id="listStockToSalesRatio" resultType="java.util.Map">
        SELECT
        sku_full sku,
        period_id_d_full periodIdD,
        sum(IFNULL(stock_overseas_qty,0)),
        sum(IFNULL(fbm_stock_sales_qty_30,0)),
        case
        when sum(IFNULL(stock_overseas_qty,0)) = 0 then 0
        when sum(IFNULL(stock_overseas_qty,0)) >0 and sum(IFNULL(fbm_stock_sales_qty_30,0)) = 0 then 99
        ELSE  sum(IFNULL(stock_overseas_qty,0))/sum(IFNULL(fbm_stock_sales_qty_30,0)) END stockToSalesRatio
        FROM
        dw_stock_purchase_sale_storage_view
        WHERE
        period_id_d_full = SUBSTR(SYSDATE(),1,10)
        and sku_full in <foreach collection="skuList" open="(" separator="," close=")" item="sku"> #{sku} </foreach>
        GROUP BY sku_full
    </select>

    <select id="getYesterdaySale" resultType="java.util.Map">
        SELECT
            period_id_d,
            sku,
            FLOOR(SUM(stock_sales_qty_30) / 30) AS avg_daily_sales_qty
        FROM
            dws_sales_qty_sum_snapshot
        WHERE
            period_id_d = SUBSTR(SYSDATE(), 1, 10)
          AND sku IS NOT NULL
        GROUP BY
            sku
        HAVING
            avg_daily_sales_qty > 0;
    </select>


<!-- 根据ASIN列表获取销量详情 -->
<select id="getCompanyListingDetailByAsinList" resultType="java.util.Map">
    SELECT
        SUBSTR(SYSDATE(),1,10)      as period_id_d,
        a.platform_sale_code,
        sum(a.sales_volume_30)      AS sales_volume_30,
        sum(a.order_quantity_30)  AS order_quantity_30,
        sum(a.sales_volume_60)      AS sales_volume_60,
        sum(a.order_quantity_60)  AS order_quantity_60,
        sum(a.sales_volume_90)      AS sales_volume_90,
        sum(a.order_quantity_90)  AS order_quantity_90,
        sum(a.sales_volume_270)      AS sales_volume_270,
        sum(a.order_quantity_270)  AS order_quantity_270
    from (
             SELECT
                 platform_sale_code,
                 sum(quantity) as sales_volume_30,
                 count(plat_order_id) as order_quantity_30,
                 ''as sales_volume_60,
                 ''as order_quantity_60,
                 ''as sales_volume_90,
                 ''as order_quantity_90,
                 '' as sales_volume_270,
                 '' as order_quantity_270
             FROM
                 dwi_sales_detail
             WHERE type = 'Order'
               AND platform_sale_code is not null
               AND platform_sale_code IN
                <foreach collection="asinList" open="(" separator="," close=")" item="asin">
                    #{asin}
                </foreach>
               AND  (period_id_d BETWEEN SUBSTR(subdate(SYSDATE(), interval 1 month),1,10)and SUBSTR(SYSDATE(),1,10))
             GROUP BY platform_sale_code

             union all

             SELECT
                 platform_sale_code,
                 ''as sales_volume_30,
                 ''as order_quantity_30,
                 sum(quantity) as sales_volume_60,
                 count(plat_order_id) as order_quantity_60,
                 ''as sales_volume_90,
                 ''as order_quantity_90,
                 '' as sales_volume_270,
                 '' as order_quantity_270
             FROM
                 dwi_sales_detail
             WHERE type = 'Order'
               AND platform_sale_code is not null
               AND platform_sale_code IN
                <foreach collection="asinList" open="(" separator="," close=")" item="asin">
                    #{asin}
                </foreach>
               AND  (period_id_d BETWEEN SUBSTR(subdate(SYSDATE(), interval 2 month),1,10)and SUBSTR(SYSDATE(),1,10))
             GROUP BY platform_sale_code

             union all

             SELECT
                 platform_sale_code,
                 ''as sales_volume_30,
                 ''as order_quantity_30,
                 ''as sales_volume_60,
                 ''as order_quantity_60,
                 sum(quantity) as sales_volume_90,
                 count(plat_order_id) as order_quantity_90,
                 '' as sales_volume_270,
                 '' as order_quantity_270
             FROM
                 dwi_sales_detail
             WHERE
                 type = 'Order'
               AND platform_sale_code IN
                <foreach collection="asinList" open="(" separator="," close=")" item="asin">
                    #{asin}
                </foreach>
               AND  (period_id_d BETWEEN SUBSTR(subdate(SYSDATE(), interval 3 month),1,10)and SUBSTR(SYSDATE(),1,10))
             GROUP BY platform_sale_code
             union all

             SELECT
                 platform_sale_code,
                 ''as sales_volume_30,
                 ''as order_quantity_30,
                 ''as sales_volume_60,
                 ''as order_quantity_60,
                 '' as sales_volume_90,
                 '' as order_quantity_90,
                 sum(quantity) as sales_volume_270,
                 count(plat_order_id) as order_quantity_270
             FROM
                 dwi_sales_detail
             WHERE
                 type = 'Order'
               AND platform_sale_code IN
                <foreach collection="asinList" open="(" separator="," close=")" item="asin">
                    #{asin}
                </foreach>
               AND  (period_id_d BETWEEN SUBSTR(subdate(SYSDATE(), interval 9 month),1,10)and SUBSTR(SYSDATE(),1,10))
             GROUP BY platform_sale_code
         )a GROUP BY platform_sale_code
</select>

    <select id="listAsinSales" resultType="java.lang.String">
        SELECT platform_sale_code
        FROM dwi_sales_detail
        WHERE type = 'Order' and platform_sale_code is not null and platform_sale_code !=''
          AND (period_id_d BETWEEN SUBSTR(subdate(SYSDATE(), interval 9 month),1,10) and SUBSTR(SYSDATE(),1,10))
        GROUP BY platform_sale_code
    </select>
</mapper>