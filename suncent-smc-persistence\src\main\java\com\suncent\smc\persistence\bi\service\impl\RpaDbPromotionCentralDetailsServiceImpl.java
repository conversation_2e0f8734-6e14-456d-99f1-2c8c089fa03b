package com.suncent.smc.persistence.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.suncent.smc.common.annotation.DataSource;
import com.suncent.smc.common.enums.DataSourceType;
import com.suncent.smc.persistence.bi.entity.RpaDbPromotionCentral;
import com.suncent.smc.persistence.bi.entity.RpaDbPromotionCentralDetails;
import com.suncent.smc.persistence.bi.mapper.RpaDbPromotionCentralDetailsMapper;
import com.suncent.smc.persistence.bi.service.IRpaDbPromotionCentralDetailsService;
import com.suncent.smc.persistence.bi.service.IRpaDbPromotionCentralService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * RPA库促销中心详情数据服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
@DataSource(DataSourceType.BI2)
@Slf4j
public class RpaDbPromotionCentralDetailsServiceImpl implements IRpaDbPromotionCentralDetailsService {

    @Autowired
    private RpaDbPromotionCentralDetailsMapper rpaDbPromotionCentralDetailsMapper;

    @Autowired
    private IRpaDbPromotionCentralService rpaDbPromotionCentralService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectByPromotionId(String promotionId) {
        if (promotionId == null || promotionId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralDetailsMapper.selectByPromotionId(promotionId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectByPromotionIds(List<String> promotionIds) {
        if (CollUtil.isEmpty(promotionIds)) {
            return new ArrayList<>();
        }

        List<RpaDbPromotionCentralDetails> result = new ArrayList<>();

        // 分批查询，每批1000条（避免 SQL IN 子句过长）
        List<List<String>> promotionIdBatches = CollUtil.split(promotionIds, 1000);
        log.debug("分批查询详情数据，总数: {}, 批次数: {}", promotionIds.size(), promotionIdBatches.size());

        for (int i = 0; i < promotionIdBatches.size(); i++) {
            List<String> batch = promotionIdBatches.get(i);
            log.debug("执行第{}/{}批查询，批次大小: {}", i + 1, promotionIdBatches.size(), batch.size());

            List<RpaDbPromotionCentralDetails> batchResult = rpaDbPromotionCentralDetailsMapper.selectByPromotionIds(batch);
            if (CollUtil.isNotEmpty(batchResult)) {
                result.addAll(batchResult);
                log.debug("第{}批查询完成，获取数据: {}条", i + 1, batchResult.size());
            }
        }

        log.info("批量查询详情数据完成，总计获取: {}条", result.size());
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectByAsin(String asin) {
        if (asin == null || asin.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralDetailsMapper.selectByAsin(asin);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectByAsins(List<String> asins) {
        if (CollUtil.isEmpty(asins)) {
            return new ArrayList<>();
        }

        List<RpaDbPromotionCentralDetails> result = new ArrayList<>();

        // 分批查询，每批1000条
        List<List<String>> asinBatches = CollUtil.split(asins, 1000);
        for (List<String> batch : asinBatches) {
            List<RpaDbPromotionCentralDetails> batchResult = rpaDbPromotionCentralDetailsMapper.selectByAsins(batch);
            if (CollUtil.isNotEmpty(batchResult)) {
                result.addAll(batchResult);
            }
        }

        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public RpaDbPromotionCentralDetails selectByPromotionIdAndAsin(String promotionId, String asin) {
        if (promotionId == null || promotionId.trim().isEmpty()
                || asin == null || asin.trim().isEmpty()) {
            return null;
        }
        return rpaDbPromotionCentralDetailsMapper.selectByPromotionIdAndAsin(promotionId, asin);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectByTimeRange(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralDetailsMapper.selectByTimeRange(startTime, endTime);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectForSync(Date lastSyncTime) {
        return rpaDbPromotionCentralDetailsMapper.selectForSync(lastSyncTime);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectForSyncByPromotionId(String promotionId, Date lastSyncTime) {
        if (promotionId == null || promotionId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralDetailsMapper.selectForSyncByPromotionId(promotionId, lastSyncTime);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int countByPromotionId(String promotionId) {
        if (promotionId == null || promotionId.trim().isEmpty()) {
            return 0;
        }
        return rpaDbPromotionCentralDetailsMapper.countByPromotionId(promotionId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Date selectMaxTimeUpByPromotionId(String promotionId) {
        if (promotionId == null || promotionId.trim().isEmpty()) {
            return null;
        }
        return rpaDbPromotionCentralDetailsMapper.selectMaxTimeUpByPromotionId(promotionId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectByPromotionIdWithPaging(String promotionId, int offset, int limit) {
        if (promotionId == null || promotionId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralDetailsMapper.selectByPromotionIdWithPaging(promotionId, offset, limit);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<String> selectDistinctPromotionIds() {
        return rpaDbPromotionCentralDetailsMapper.selectDistinctPromotionIds();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<String> selectDistinctAsins() {
        return rpaDbPromotionCentralDetailsMapper.selectDistinctAsins();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectByProductSelectionId(String productSelectionId) {
        if (productSelectionId == null || productSelectionId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralDetailsMapper.selectByProductSelectionId(productSelectionId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectBestDealDetails() {
        // 1. 获取所有BestDeal类型的促销记录
        List<RpaDbPromotionCentral> bestDealPromotions = rpaDbPromotionCentralService.selectBestDealPromotions();
        if (CollUtil.isEmpty(bestDealPromotions)) {
            return new ArrayList<>();
        }

        // 2. 提取促销ID列表
        List<String> promotionIds = bestDealPromotions.stream()
                .map(RpaDbPromotionCentral::getPromotionId)
                .filter(id -> id != null && !id.trim().isEmpty())
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(promotionIds)) {
            return new ArrayList<>();
        }

        // 3. 根据促销ID列表查询详情数据
        return selectByPromotionIds(promotionIds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectBestDealDetailsForSync(Date lastSyncTime) {
        // 1. 获取需要同步的BestDeal类型的促销记录
        List<RpaDbPromotionCentral> bestDealPromotions = rpaDbPromotionCentralService.selectBestDealForSync(lastSyncTime);
        if (CollUtil.isEmpty(bestDealPromotions)) {
            return new ArrayList<>();
        }

        // 2. 提取促销ID列表
        List<String> promotionIds = bestDealPromotions.stream()
                .map(RpaDbPromotionCentral::getPromotionId)
                .filter(id -> id != null && !id.trim().isEmpty())
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(promotionIds)) {
            return new ArrayList<>();
        }

        // 3. 批量查询需要同步的详情数据（一次性查询所有promotionId，避免重复查询）
        log.info("开始批量查询{}个促销活动的详情数据", promotionIds.size());
        List<RpaDbPromotionCentralDetails> result = selectByPromotionIds(promotionIds);
        log.info("批量查询完成，获取到{}条详情数据", result.size());

        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectDetailsSummaryByPromotionIds(List<String> promotionIds) {
        if (CollUtil.isEmpty(promotionIds)) {
            return new ArrayList<>();
        }

        return selectByPromotionIds(promotionIds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<RpaDbPromotionCentralDetails> selectLatestByPromotionId(String promotionId) {
        if (promotionId == null || promotionId.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return rpaDbPromotionCentralDetailsMapper.selectLatestByPromotionId(promotionId);
    }
}
