package com.suncent.smc.persistence.publication.mapper;

import com.suncent.smc.persistence.publication.domain.entity.AiGenerationTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI内容生成任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface AiGenerationTaskMapper {
    
    /**
     * 查询AI内容生成任务
     * 
     * @param id AI内容生成任务主键
     * @return AI内容生成任务
     */
    AiGenerationTask selectAiGenerationTaskById(String id);

    /**
     * 根据任务关联ID查询AI内容生成任务
     * 
     * @param taskCorrelationId 任务关联ID
     * @return AI内容生成任务
     */
    AiGenerationTask selectAiGenerationTaskByCorrelationId(String taskCorrelationId);

    /**
     * 查询AI内容生成任务列表
     * 
     * @param aiGenerationTask AI内容生成任务
     * @return AI内容生成任务集合
     */
    List<AiGenerationTask> selectAiGenerationTaskList(AiGenerationTask aiGenerationTask);

    /**
     * 新增AI内容生成任务
     * 
     * @param aiGenerationTask AI内容生成任务
     * @return 结果
     */
    int insertAiGenerationTask(AiGenerationTask aiGenerationTask);

    /**
     * 修改AI内容生成任务
     * 
     * @param aiGenerationTask AI内容生成任务
     * @return 结果
     */
    int updateAiGenerationTask(AiGenerationTask aiGenerationTask);

    /**
     * 根据任务关联ID更新任务状态和结果
     * 
     * @param taskCorrelationId 任务关联ID
     * @param taskStatus 任务状态
     * @param aiResult AI生成结果
     * @return 结果
     */
    int updateTaskStatusAndResult(@Param("taskCorrelationId") String taskCorrelationId, 
                                  @Param("taskStatus") String taskStatus, 
                                  @Param("aiResult") String aiResult,
                                  @Param("beforeStatus") String beforeStatus);

    /**
     * 删除AI内容生成任务
     * 
     * @param id AI内容生成任务主键
     * @return 结果
     */
    int deleteAiGenerationTaskById(String id);

    /**
     * 批量删除AI内容生成任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAiGenerationTaskByIds(String[] ids);

    /**
     * 查询待处理的任务列表
     *
     * @param taskType 任务类型
     * @return 待处理任务列表
     */
    List<AiGenerationTask> selectPendingTasks(@Param("taskType") String taskType);

    /**
     * 更新任务处理状态
     *
     * @param taskCorrelationId 任务关联ID
     * @param processStatus 处理状态
     * @return 结果
     */
    int updateProcessStatus(@Param("taskCorrelationId") String taskCorrelationId,
                           @Param("processStatus") Integer processStatus);

    int countTaskByStatus(@Param("taskId") Integer taskId, @Param("processStatus") int processStatus);
}
