package com.suncent.smc.persistence.ads.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.suncent.smc.common.annotation.Excel;
import lombok.Data;

import java.util.List;

/**
 * 适配图数据实体类
 * 用于合并ads_fitmentdata_bi和ads_fitmentdata_oe表的数据
 */
@Data
public class AdsFitmentImageData {
    /**
     * 产品编码
     */
    @Excel(name = "product_code")
    private String productCode;

    /**
     * OE
     */
    @Excel(name = "OE号")
    private String oeNumber;

    @Excel(name = "MakeName")
    private String makeName;

    /**
     * 品牌-车型-年份
     */
    @Excel(name = "ModelName")
    private String modelName;

    /**
     * 品牌-车型-年份
     */
    @Excel(name = "YearName")
    private String yearName;

    /**
     * 气缸数
     */
    @Excel(name = "BlockType_Cylinders")
    private String blockTypeCylinders;

    /**
     * 气缸数
     */
    @Excel(name = "BodyTypeName")
    private String bodyTypeName;

    /**
     * 排量
     */
    @Excel(name = "Liter_aspiration")
    private String liter;

    /**
     * 排量
     */
    @Excel(name = "SubModelName")
    private String subModelName;

    /**
     * 排量
     */
    @Excel(name = "AspirationName")
    private String aspirationName;
    /**
     * 适配率
     */
    @Excel(name = "VIO")
    private Integer vio;


    /**
     * bi数据列表
     */
    @ExcelIgnore
    private List<AdsFitmentDataVIO> biDataList;

    /**
     * oe数据列表
     */
    @ExcelIgnore
    private List<AdsFitmentDataOe> oeDataList;
} 