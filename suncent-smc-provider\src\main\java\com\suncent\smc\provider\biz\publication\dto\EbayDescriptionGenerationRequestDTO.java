package com.suncent.smc.provider.biz.publication.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * Ebay商品描述生成API请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class EbayDescriptionGenerationRequestDTO {

    /**
     * 店铺名
     */
    @NotBlank(message = "店铺名不能为空")
    private String shop;

    /**
     * 品牌名
     */
    @NotBlank(message = "品牌名不能为空")
    private String brand;

    // 以下字段由后端内部设置，不需要前端传递

    /**
     * 任务关联标识符（后端生成）
     */
    private String taskCorrelationId;

    /**
     * 回调地址（后端构建）
     */
    private String callbackUrl;

    /**
     * 商品列表
     */
    @NotEmpty(message = "商品列表不能为空")
    @Valid
    private List<GoodsItem> goods;

    /**
     * 商品项
     */
    @Data
    public static class GoodsItem {

        /**
         * 商品编码，唯一标识商品
         */
        @NotBlank(message = "商品编码不能为空")
        private String goodsCode;

        /**
         * 商品属性
         * Key: 属性名称（英文）
         * Value: 属性值
         */
        private Map<String, String> attribute;
    }
}
