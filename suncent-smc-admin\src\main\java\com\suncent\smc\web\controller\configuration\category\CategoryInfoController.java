package com.suncent.smc.web.controller.configuration.category;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.suncent.smc.common.annotation.Log;
import com.suncent.smc.common.core.controller.BaseController;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysRole;
import com.suncent.smc.common.core.page.TableDataInfo;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.enums.BusinessType;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.poi.ExcelUtil;
import com.suncent.smc.persistence.cdp.service.IAttributeService;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.configuration.category.domain.entity.CategoryInfo;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigFieldInfo;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigRequiredField;
import com.suncent.smc.persistence.configuration.category.service.ICategoryInfoService;
import com.suncent.smc.persistence.configuration.category.service.IConfigFieldInfoService;
import com.suncent.smc.persistence.configuration.category.service.IConfigRequiredFieldDictService;
import com.suncent.smc.persistence.configuration.category.service.IConfigRequiredFieldService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.publication.domain.entity.AmCategoryTemplateField;
import com.suncent.smc.persistence.publication.domain.entity.AmCategoryTemplateFieldPropEnum;
import com.suncent.smc.persistence.publication.domain.entity.AmCategoryTemplateFieldRelation;
import com.suncent.smc.persistence.publication.service.IAmCategoryTemplateFieldPropEnumService;
import com.suncent.smc.persistence.publication.service.IAmCategoryTemplateFieldRelationService;
import com.suncent.smc.persistence.publication.service.IAmCategoryTemplateFieldService;
import com.suncent.smc.provider.base.service.ISmcBaseConfigService;
import com.suncent.smc.provider.biz.configuration.CategoryInfoHandleBiz;
import com.suncent.smc.system.service.ISysRoleService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类目配置Controller
 *
 * <AUTHOR>
 * @date 2023-01-11
 */
@Controller
@RequestMapping("/configuration/category")
public class CategoryInfoController extends BaseController {
    private String prefix = "configuration/category";

    @InitBinder  //类初始化是调用的方法注解
    public void initBinder(WebDataBinder binder) {
        //给这个controller配置接收list的长度100000，仅在这个controller有效
        binder.setAutoGrowCollectionLimit(100000);
    }

    @Autowired
    private CategoryInfoHandleBiz categoryInfoHandleBiz;

    @Autowired
    private ICategoryInfoService categoryInfoService;

    @Autowired
    private IConfigRequiredFieldService configRequiredFieldService;
    @Autowired
    private IConfigRequiredFieldDictService configRequiredFieldDictService;

    @Autowired
    IConfigFieldInfoService configFieldInfoService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    public IShopService shopService;

    @Autowired
    private ISmcBaseConfigService iSmcBaseConfigService;
    @Autowired
    IPlatformCategoryService platformCategoryService;

    @Autowired
    private IAttributeService attributeService;
    @Autowired
    private IAmCategoryTemplateFieldService amCategoryTemplateFieldService;
    @Autowired
    private IAmCategoryTemplateFieldPropEnumService amCategoryTemplateFieldPropEnumService;
    @Autowired
    private IAmCategoryTemplateFieldRelationService fieldRelationService;
    @RequiresPermissions("configuration:category:view")
    @GetMapping()
    public String category() {
        return prefix + "/category";
    }

    /**
     * 查询类目配置列表
     */
    @RequiresPermissions("configuration:category:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(CategoryInfo categoryInfo) {
        List<SysRole> roles = sysRoleService.selectRoleListByUserId(ShiroUtils.getUserId());
        if (CollectionUtils.isNotEmpty(roles)) {
            //roles只包含了100EB运营则赋值给EB 只101AM运营则赋值AM
            if (StringUtils.isEmpty(categoryInfo.getPlatform())){
                if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "100"))) {
                    categoryInfo.setPlatform(PlatformTypeEnum.EB.name());
                } else if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "101"))) {
                    categoryInfo.setPlatform(PlatformTypeEnum.AM.name());
                }
            }
        }
        String categoryId = categoryInfo.getCategoryId();
        if (ObjectUtils.isNotEmpty(categoryId)){
            PlatformCategory platformCategory = new PlatformCategory();
            platformCategory.setCategoryId(categoryId);
            platformCategory.setPlatformCode(categoryInfo.getPlatform());
            platformCategory.setSite(categoryInfo.getSite());
            List<PlatformCategory> platformCategoryList = platformCategoryService.selectPlatformCategoryList(platformCategory);
            if (ObjectUtils.isEmpty(platformCategoryList)){
                return getDataTable(new ArrayList<>());
            }
            categoryInfo.setPlatformCategoryId(String.valueOf(platformCategoryList.get(0).getId()));
        }
        startPage();
        List<CategoryInfo> list = categoryInfoService.selectCategoryInfoList(categoryInfo);
        return getDataTable(list);
    }

    /**
     * 导出类目配置列表
     */
    @RequiresPermissions("configuration:category:export")
    @Log(title = "类目配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(CategoryInfo categoryInfo) {
        List<CategoryInfo> list = categoryInfoService.selectCategoryInfoList(categoryInfo);
        ExcelUtil<CategoryInfo> util = new ExcelUtil<CategoryInfo>(CategoryInfo.class);
        return util.exportExcel(list, "类目配置数据");
    }

    /**
     * 新增类目配置
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存类目配置
     */
    @RequiresPermissions("configuration:category:add")
    @Log(title = "类目配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(CategoryInfo categoryInfo) {
        return categoryInfoHandleBiz.saveCategoryInfo(categoryInfo);
    }

    /**
     * 修改类目配置
     */
    @RequiresPermissions("configuration:category:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        CategoryInfo categoryInfo = categoryInfoService.selectCategoryInfoById(id);
//        List<String> propertyIdList = configRequiredFieldService.getRequiredFieldIdListByCategoryId(id);
//        categoryInfo.setPropertyIds(StringUtils.join(propertyIdList, ","));
        mmap.put("categoryInfo", categoryInfo);
        mmap.put("attribute",attributeService.getAttributeAllKVList());
        return prefix + "/edit";
    }

    /**
     * 根据产品分类代码获取商品库属性
     *
     * @param code 产品分类代码
     * @return 商品库属性列表
     */
    @GetMapping("/category/{code}")
    @ResponseBody
    public AjaxResult listAttribute(@PathVariable("code") String code) {
        try {
            return AjaxResult.success("查询成功", attributeService.listAttribute(code));
        } catch (Exception e) {
            logger.error("根据产品分类代码获取商品库属性失败，code: {}", code, e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }


    /**
     * 修改保存类目配置
     */
    @RequiresPermissions("configuration:category:edit")
    @Log(title = "类目配置", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(CategoryInfo categoryInfo) {
        return categoryInfoHandleBiz.updateCategoryInfo(categoryInfo);
    }

    /**
     * 删除类目配置
     */
    @RequiresPermissions("configuration:category:remove")
    @Log(title = "类目配置", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(categoryInfoHandleBiz.deleteCategoryInfoByIds(ids));
    }

    /**
     * 根据平台，类目获取对应属性
     *
     * @param platformCode
     * @param categoryId
     * @return
     */
    @ResponseBody
    @PostMapping("/attribute/{platformCode}/{site}/{categoryId}/{shopCode}")
    public TableDataInfo getCategoryAttributeList(@PathVariable String platformCode, @PathVariable String site, @PathVariable String categoryId, @PathVariable String shopCode) {
        List<ConfigRequiredField> configRequiredFieldList = configRequiredFieldService.getRequiredFieldIdListByCategoryIdAndPlatformCode(categoryId, site, platformCode);
        //获取属性字典
        List<ConfigRequiredField> requiredFieldList = configRequiredFieldDictService.putConfigRequiredFieldDict(configRequiredFieldList);
        handField(platformCode, categoryId, shopCode, requiredFieldList);
        return getDataTable(requiredFieldList);
    }

    /**
     * 根据平台，类目获取对应属性
     * @param platformCode
     * @param categoryId
     * @param shopCode
     * @param requiredFieldList
     */
    private void handField(String platformCode, String categoryId, String shopCode, List<ConfigRequiredField> requiredFieldList) {
        if (CollectionUtils.isEmpty(requiredFieldList)) {
            return;
        }
        //若店铺为0表示需要给当前人所有店铺插入数据
        List<String> shopList = new ArrayList<>();
        if(StringUtils.equals(shopCode, "0")) {
            List<String> collect = shopService.selectShopByPlatformCodeList(platformCode).stream().map(KeyValueEntity::getKey).collect(Collectors.toList());
            shopList.add(collect.get(0));
        } else {
            shopList.add(shopCode);
        }
        shopList.forEach(shop -> {
            //处理店铺与个人的类目属性
            ConfigFieldInfo info = new ConfigFieldInfo();
            info.setShopCode(shop);
            info.setPlatform(platformCode);
            info.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
            info.setCategoryId(categoryId);
            List<ConfigFieldInfo> infoList = configFieldInfoService.selectConfigFieldInfoList(info);
            Map<String, List<ConfigFieldInfo>> configFieldInfoMap = infoList.stream().collect(Collectors.groupingBy(e -> e.getFieldId()));
            //未有则新增
            if(CollectionUtils.isEmpty(infoList)) {
                //转成ConfigFieldInfoList
                List<ConfigFieldInfo> dbList = requiredFieldList.stream().map(configRequiredField -> {
                    ConfigFieldInfo db = new ConfigFieldInfo();
                    db.setPlatform(configRequiredField.getPlatformCode());
                    db.setSite(configRequiredField.getSite());
                    db.setShopCode(shop);
                    db.setCategoryId(configRequiredField.getCategoryId());
                    db.setFieldId(configRequiredField.getId());
                    db.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
                    db.setUpdateBy(String.valueOf(ShiroUtils.getUserId()));
                    db.setIsRequire(ObjectUtils.isEmpty(configRequiredField.getIsRequire()) ? "0" : configRequiredField.getIsRequire());
                    return db;
                }).collect(Collectors.toList());
                configFieldInfoService.insertConfigFieldInfoList(dbList);
                return;
            }
            //处理已有的平台类目属性
            requiredFieldList.forEach(configRequiredField -> {
                List<ConfigFieldInfo> configFieldInfos = configFieldInfoMap.get(configRequiredField.getId());
                if (CollUtil.isEmpty(configFieldInfos)){
                    return;
                }
                configRequiredField.setAttributeValue(configFieldInfos.get(0).getFieldValue());
                configRequiredField.setIsRequire(configFieldInfos.get(0).getIsRequire());
            });

//            infoList.forEach(configFieldInfo -> {
//                requiredFieldList.forEach(configRequiredField -> {
//                    if (configFieldInfo.getFieldId().equals(configRequiredField.getId())) {
//                        configRequiredField.setAttributeValue(configFieldInfo.getFieldValue());
//                        configRequiredField.setIsRequire(configFieldInfo.getIsRequire());
//                    }
//                });
//            });
        });
    }


    @ResponseBody
    @PostMapping("/updateIsRequire/{shopCode}/{id}")
    public AjaxResult updateIsRequire(@PathVariable String shopCode,@PathVariable Integer id) {
        configFieldInfoService.updateConfigFieldInfoIsRequire(shopCode,id);
        return AjaxResult.success();
    }

    @PostMapping("/getCategoryInfoByPlatform")
    @ResponseBody
    public List<CategoryInfo> getPlatformCategoryByPlatform(String platform) {
        CategoryInfo categoryInfo = new CategoryInfo();
        categoryInfo.setPlatform(platform);
        List<CategoryInfo> list = categoryInfoService.selectCategoryInfoList(categoryInfo);
        return list;
    }

    @PostMapping("/getPlatformCategoryByPlatformCode")
    @ResponseBody
    public List<KeyValueEntity> getPlatformCategoryByPlatformCode(String platformCode,String isDistribution) {
        List<SysRole> roles = sysRoleService.selectRoleListByUserId(ShiroUtils.getUserId());
        platformCode=null;
        if (CollectionUtils.isNotEmpty(roles)) {
            //roles只包含了100EB运营则赋值给EB 只101AM运营则赋值AM
            if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "100"))) {
                platformCode=PlatformTypeEnum.EB.name();
            } else if (roles.stream().allMatch(role -> StringUtils.equals(role.getRoleId().toString(), "101"))) {
                platformCode=PlatformTypeEnum.AM.name();
            }
        }
        return iSmcBaseConfigService.getPlatformCategoryByPlatformCode(platformCode, Integer.valueOf(isDistribution));

    }


    @GetMapping("/requiredFieldAddColumn")
    public String requiredFieldAddColumn(ModelMap mmap) {
        return prefix + "/addColumn";
    }


    @PostMapping("/requiredFieldAddColumn")
    @ResponseBody
    public AjaxResult AddColumn(ConfigRequiredField configRequiredField) {
        if (ObjUtil.isEmpty(configRequiredField) || StringUtils.isEmpty(configRequiredField.getCategoryId())) {
            return AjaxResult.error("提交参数错误");
        }
        configRequiredField.setAttributeName(configRequiredField.getAttributeCode());
        configRequiredField.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
        configRequiredFieldService.insertConfigRequiredField(configRequiredField);
        return AjaxResult.success();
    }

    @GetMapping("/findCategoryid/{platformCode}/{site}")
    public String findCategoryid(@PathVariable String platformCode, @PathVariable String site, ModelMap mmap) {
        mmap.put("platformCode", platformCode);
        mmap.put("site", site);
        return "publication/category/category";
    }



    /**
     * 修改类目配置(Json新版)
     */
    @GetMapping("/edit/json/{id}/{type}")
    public String editJson(@PathVariable("id") String id, @PathVariable("type") String type, ModelMap mmap) {
        CategoryInfo categoryInfo = categoryInfoService.selectCategoryInfoById(id);
        mmap.put("categoryInfo", categoryInfo);
        mmap.put("type", type);
//        mmap.put("attribute",attributeService.getAttributeAllKVList());
        return prefix + "/editJson";
    }


    /**
     * 根据平台，类目获取对应属性
     * @param site
     * @param categoryId
     * @param shopCode
     * @param source 标识操作
     * @param type 标识vc /sc
     * @return
     */
    @ResponseBody
    @PostMapping("/attribute/json/{site}/{categoryId}/{shopCode}")
    public TableDataInfo getCategoryAttributeListJson(@PathVariable String site, @PathVariable String categoryId, @PathVariable String shopCode, String source, String type) {
        //通过类目id查询productType
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(categoryId));
        if (ObjUtil.isEmpty(platformCategory)){
            return getCoustomDataTable(new ArrayList<>(),0);
        }
        List<AmCategoryTemplateField> categoryAttributeListJson = categoryInfoHandleBiz.getCategoryAttributeListJson(site, platformCategory.getProductType(), shopCode, platformCategory.getId(), source, type);
        return getDataTable(categoryAttributeListJson);
    }


    /**
     * 根据平台，类目选择非必填属性
     *
     * @param
     * @param categoryId
     * @return
     */
    @ResponseBody
    @PostMapping("/attribute/json/{site}/{categoryId}/{shopCode}/chooseAttr")
    public TableDataInfo chooseAttrJson(@PathVariable String site, @PathVariable String categoryId, @PathVariable String shopCode) {
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(categoryId));
        if (ObjUtil.isEmpty(platformCategory)){
            throw new BusinessException("类目不存在");
        }
        List<AmCategoryTemplateField> categoryAttributeListJson = categoryInfoHandleBiz.listChooseCategoryAttributeListJson(site, platformCategory.getProductType(), shopCode, platformCategory.getId());
        return getDataTable(categoryAttributeListJson);
    }

    @GetMapping("/attribute/json/{site}/{categoryId}/{shopCode}/chooseAttr")
    public String chooseAttr(@PathVariable String site, @PathVariable String categoryId, @PathVariable String shopCode, ModelMap mmap) {
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(categoryId));
        if (ObjUtil.isEmpty(platformCategory)){
            throw new BusinessException("类目不存在");
        }
        mmap.put("site", site);
        mmap.put("categoryId", categoryId);
        mmap.put("shopCode", shopCode);
        return prefix + "/chooseAttr";
    }


    /**
     * 修改保存类目配置
     */
    @PostMapping("/edit/json")
    @ResponseBody
    public AjaxResult editSaveJson(CategoryInfo categoryInfo) {
        return categoryInfoHandleBiz.updateCategoryInfoJSON(categoryInfo);
    }


    /**
     * 获取子字段可选值
     */
    @GetMapping("/getRelatedValues")
    @ResponseBody
    public AjaxResult getRelatedValues(
            @RequestParam String productType,
            @RequestParam String parentField,
            @RequestParam String parentValue,
            @RequestParam String childField,
            @RequestParam(required = false, defaultValue = "US") String site,
            @RequestParam(required = false, defaultValue = "Y") String vcFlag) {

        try {
            // 查询父字段枚举值
            AmCategoryTemplateFieldPropEnum parentQuery = new AmCategoryTemplateFieldPropEnum();
            parentQuery.setProductType(productType);
            parentQuery.setPropNodePath(parentField);
            parentQuery.setCode(parentValue);
            parentQuery.setSite(site);
            parentQuery.setVcFlag(vcFlag);
            List<AmCategoryTemplateFieldPropEnum> parentEnums = amCategoryTemplateFieldPropEnumService.selectAmCategoryTemplateFieldPropEnumList(parentQuery);

            if (parentEnums.isEmpty()) {
                return AjaxResult.error("未找到父字段枚举值");
            }

            Long parentEnumId = parentEnums.get(0).getId();

            // 查询关联的子字段值
            AmCategoryTemplateFieldRelation relationQuery = new AmCategoryTemplateFieldRelation();
            relationQuery.setProductType(productType);
            relationQuery.setParentPropNodePath(parentField);
            relationQuery.setParentEnumId(parentEnumId);
            relationQuery.setChildPropNodePath(childField);
            relationQuery.setSite(site);
            relationQuery.setVcFlag(vcFlag);

            List<AmCategoryTemplateFieldRelation> relations = fieldRelationService.selectAmCategoryTemplateFieldRelationList(relationQuery);

            if (relations.isEmpty()) {
                return AjaxResult.success(new ArrayList<>());
            }

            // 获取所有子枚举值ID
            List<Long> childEnumIds = relations.stream()
                    .map(AmCategoryTemplateFieldRelation::getChildEnumId)
                    .collect(Collectors.toList());

            // 查询子字段枚举值信息
            List<AmCategoryTemplateFieldPropEnum> childEnums = amCategoryTemplateFieldPropEnumService.selectBatchByIds(childEnumIds);

            // 转换为前端需要的格式
            List<Map<String, String>> result = childEnums.stream()
                    .map(e -> {
                        Map<String, String> map = new HashMap<>();
                        map.put("code", e.getCode());
                        map.put("name", e.getName());
                        return map;
                    })
                    .collect(Collectors.toList());

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("获取关联字段值失败", e);
            return AjaxResult.error("获取关联字段值失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品类型的所有字段关系
     */
    @GetMapping("/getAllFieldRelations")
    @ResponseBody
    public AjaxResult getAllFieldRelations(String productType, String site, String vcFlag) {
        try {
            // 获取所有字段关系
            Map<String, Map<String, Map<String, List<Map<String, String>>>>> allRelations =
                    fieldRelationService.getAllFieldRelationsByProductType(productType, site, vcFlag);
            return AjaxResult.success(allRelations);
        } catch (Exception e) {
            logger.error("获取字段关系失败", e);
            return AjaxResult.error("获取字段关系失败：" + e.getMessage());
        }
    }


    @PostMapping("/batchUpdateAttrMappingByFile")
    @ResponseBody
    public AjaxResult batchUpdateAttrMappingByFile(MultipartFile file) {
        categoryInfoService.batchUpdateAttrMappingByFile(file);
        return AjaxResult.success();
    }
}