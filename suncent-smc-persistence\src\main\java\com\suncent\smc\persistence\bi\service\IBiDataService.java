package com.suncent.smc.persistence.bi.service;

import com.suncent.smc.persistence.bi.entity.BiAsinRefundRatePj;
import com.suncent.smc.persistence.bi.entity.BiSalesAnalysis;
import com.suncent.smc.persistence.bi.entity.BiSkuRefundRatePj;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/6/28 10:06
 * @Version 1.0
 */
public interface IBiDataService {

    /**
     * 获取公司维度sku的退货退款率
     * @param
     * @Date 2023/6/28 10:18
     * @return
     * <AUTHOR>
     */
    List<Map<String, String>> getCompanyListingDetail();

    /**
     * 分批获取未处理的ASIN列表的销量详情
     * @return 销量详情列表
     */
    List<Map<String, String>> getCompanyListingDetailByBatch(List<String> asinList);

    /**
     * 根据店铺code获取销售量最多的前20条数据
     * @param shopCode
     * @param size
     * @return
     */
    List<Map<String, Objects>> queryFrontSaleTotalByShop(String shopCode,  Long size,List<String> asinList);

    List<Map<String,Objects>> querySaleAndOrderTotal(List<String> asinList, String shopCode);

    /**
     * 查询今日的商品维度的退货率数据（用于同步数据使用）
     */
    List<BiSkuRefundRatePj> queryTodaySkuRefundRateData();

    /**
     * 查询今日的lisitng维度的退货率数据（用于同步数据使用）
     */
    List<BiAsinRefundRatePj> queryTodayListingRefundRateData();

    /**
     * 查询今日的运营综合分析数据
     * @return
     */
    List<BiSalesAnalysis> queryTodaySalesAnalysis(String shopCode);

    /**
     * 分页查询今日的运营综合分析数据
     *
     * @param shopCode 店铺编码
     * @param offset   偏移量
     * @param limit    限制数量
     * @return
     */
    List<BiSalesAnalysis> queryTodaySalesAnalysisPaged(String shopCode, int offset, int limit);

    BiSalesAnalysis getTodaySalesAnalysisByAsin(String shopCode,String asin);

    /**
     * 获取库销比-new
     * 与getStockToSalesRatio是同一张表，但是计算方式改变
     * @param
     */
    List<Map<String, Object>> listStockToSalesRatio(List<String> skuList);

    /**
     * 获取昨日销量数据 （大于0的销量）
     * @return
     */
    List<Map<String, Object>> getYesterdaySale();


    List<String> listAsinSales();
}
