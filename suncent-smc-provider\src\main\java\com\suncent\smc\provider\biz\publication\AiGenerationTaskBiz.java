package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.provider.biz.publication.dto.EbayDescriptionGenerationRequestDTO;
import com.suncent.smc.provider.biz.publication.dto.EbayTitleGenerationRequestDTO;
import com.suncent.smc.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI生成任务业务处理类
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Component
public class AiGenerationTaskBiz {
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private IAiGenerationTaskService aiGenerationTaskService;
    @Resource
    protected IGoodsTaskInfoService goodsTaskInfoService;
    @Autowired
    private EbayContentGenerationHttpRequestBiz ebayContentGenerationHttpRequestBiz;
    @Autowired
    private IGoodsTaskService goodsTaskService;
    @Autowired
    private IGoodsHeadService goodsHeadService;
    @Autowired
    IListingEbayLineService ebayGoodsLineService;
    @Resource
    protected IListingEbayValueService ebayValueService;
    /**
     * 创建并提交AI任务
     *
     * @param taskId 任务名，用于关联任务
     * @param shopCode 店铺编码
     * @param brandCode 品牌编码
     * @param createBy 创建人
     */
    public void createAndSubmitAiTasks(Integer taskId, List<String> goodsCodes, String shopCode, String brandCode, String createBy) {
        log.info("开始创建AI任务，taskId: {}, 店铺: {}", taskId, shopCode);

        // 创建标题生成任务
        String titleTaskCorrelationId = "title_" + taskId +"_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
        AiGenerationTask titleTask = aiGenerationTaskService.createAiGenerationTask(
            titleTaskCorrelationId,
            AiGenerationTask.TaskType.TITLE.getCode(),
            taskId,
            shopCode,
                goodsCodes,
            brandCode,
            createBy
        );

        // 创建描述生成任务
        String descTaskCorrelationId = "desc_"+ taskId +"_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
        AiGenerationTask descTask = aiGenerationTaskService.createAiGenerationTask(
            descTaskCorrelationId,
            AiGenerationTask.TaskType.DESCRIPTION.getCode(),
            taskId,
            shopCode,
                goodsCodes,
            brandCode,
            createBy
        );

        log.info("AI任务创建成功，标题任务ID: {}, 描述任务ID: {}", titleTask.getId(), descTask.getId());
    }

    /**
     * 提交AI生成任务
     *
     * @param task AI生成任务
     */
    public void submitAiGenerationTask(AiGenerationTask task) {
        log.info("开始提交AI生成任务，任务ID: {}, 类型: {}", task.getId(), task.getTaskType());

        // 更新任务状态为处理中
        aiGenerationTaskService.updateTaskStatusAndResult(
            task.getTaskCorrelationId(),
            AiGenerationTask.TaskStatus.PROCESSING.getCode(),
            null,
            AiGenerationTask.TaskStatus.PENDING.getCode()
        );

        // 解析商品编码列表
        List<String> goodsCodes = JSON.parseArray(task.getGoodsCodes(), String.class);

        if (AiGenerationTask.TaskType.TITLE.getCode().equals(task.getTaskType())) {
            // 提交标题生成任务
            submitTitleGenerationTask(task, goodsCodes);
        } else if (AiGenerationTask.TaskType.DESCRIPTION.getCode().equals(task.getTaskType())) {
            // 提交描述生成任务
            submitDescriptionGenerationTask(task, goodsCodes);
        }
    }

    /**
     * 提交标题生成任务
     * 
     * @param task AI生成任务
     * @param goodsCodes 商品编码列表
     */
    private void submitTitleGenerationTask(AiGenerationTask task, List<String> goodsCodes) {
        EbayTitleGenerationRequestDTO requestDTO = new EbayTitleGenerationRequestDTO();
        requestDTO.setShop(task.getShopCode());
        requestDTO.setBrand(task.getBrandCode());
        requestDTO.setTaskCorrelationId(task.getTaskCorrelationId());
        
        // 构建商品列表
        List<EbayTitleGenerationRequestDTO.GoodsItem> goods = goodsCodes.stream()
            .map(goodsCode -> {
                EbayTitleGenerationRequestDTO.GoodsItem item = new EbayTitleGenerationRequestDTO.GoodsItem();
                item.setGoodsCode(goodsCode);
                item.setN(1); // 默认生成1个标题
                return item;
            })
            .collect(Collectors.toList());
        
        requestDTO.setGoods(goods);
        
        // 设置回调地址
        requestDTO.setCallbackUrl(buildCallbackUrl("titleGenerationCallback"));
        
        log.info("调用标题生成接口，任务关联ID: {}", task.getTaskCorrelationId());
        ebayContentGenerationHttpRequestBiz.generateTitles(requestDTO);
    }

    /**
     * 提交描述生成任务
     * 
     * @param task AI生成任务
     * @param goodsCodes 商品编码列表
     */
    private void submitDescriptionGenerationTask(AiGenerationTask task, List<String> goodsCodes) {
        EbayDescriptionGenerationRequestDTO requestDTO = new EbayDescriptionGenerationRequestDTO();
        requestDTO.setShop(task.getShopCode());
        requestDTO.setBrand(task.getBrandCode());
        requestDTO.setTaskCorrelationId(task.getTaskCorrelationId());

        GoodsTask goodsTask = goodsTaskService.selectGoodsTaskById(task.getTaskId() +"");
        // 通过任务找到所有的链接
        GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
        goodsTaskInfo.setTaskId(goodsTask.getId());
        List<GoodsTaskInfo> infoList = goodsTaskInfoService.selectGoodsTaskInfoList(goodsTaskInfo);
        if (CollUtil.isEmpty(infoList)) {
            return;
        }
        List<Integer> headIds = infoList.stream().filter(e -> StrUtil.isNotBlank(e.getListingHeadId())).map(e -> Integer.parseInt(e.getListingHeadId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(headIds)) {
            return;
        }
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(headIds.toArray(new Integer[headIds.size()]));
        if (CollUtil.isEmpty(goodsHeads)) {
            return;
        }

        Set<Integer> alreadySubmitHeadIds = new HashSet<>();
        // 构建商品列表
        List<EbayDescriptionGenerationRequestDTO.GoodsItem> goods = goodsCodes.stream()
            .map(goodsCode -> {
                EbayDescriptionGenerationRequestDTO.GoodsItem item = new EbayDescriptionGenerationRequestDTO.GoodsItem();
                item.setGoodsCode(goodsCode);

                // 找到对应的链接
                GoodsHead goodsHead = goodsHeads.stream().filter(e -> e.getPdmGoodsCode().equals(goodsCode) && !alreadySubmitHeadIds.contains(e.getId())).findFirst().orElse(null);
                if (goodsHead != null) {
                    ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHead.getId());
                    //检测属性
                    List<ListingEbayValue> listingEbayValues = ebayValueService.selectListingEbayValueByLineId(listingEbayLine.getId());
                    if (CollUtil.isNotEmpty(listingEbayValues)) {
                        Map<String, String> attribute  = new HashMap<>();
                        item.setAttribute(attribute);
                        listingEbayValues.forEach(e -> {
                            attribute.put(e.getName(), e.getValue());
                        });
                    }

                    alreadySubmitHeadIds.add(goodsHead.getId());
                }
                return item;
            })
            .collect(Collectors.toList());
        
        requestDTO.setGoods(goods);
        
        // 设置回调地址
        requestDTO.setCallbackUrl(buildCallbackUrl("descriptionGenerationCallback"));
        
        log.info("调用描述生成接口，任务关联ID: {}", task.getTaskCorrelationId());
        ebayContentGenerationHttpRequestBiz.generateDescriptions(requestDTO);
    }

    /**
     * 构建回调地址
     * 
     * @param endpoint 回调端点
     * @return 完整的回调地址
     */
    private String buildCallbackUrl(String endpoint) {
        return configService.selectConfigByKey("ai.callback.base-url") + "/smc/api/" +endpoint;
    }

}
