//SKU库商品列表查询
var origin_prefix = ctx + "system/origin";
function getOrigin(goodsCode,buttonId,subscript) {
    if(buttonId=="description"){
        const templateId = $('#templateSelect').val();
        if (templateId) {
            $.modal.open("sku库商品列表", origin_prefix + "/list/" + goodsCode + "/EB", '1000', '800',  function (index,layero){
                getOriginBySkuCallBack(index, layero, buttonId,"batch",subscript);
            });
        } else {
            $.modal.alertWarning("请先选择描述模板");
        }
    }else {
        $.modal.open("sku库商品列表", origin_prefix + "/list/" + goodsCode + "/EB", '1000', '800', function (index,layero){
            getOriginBySkuCallBack(index, layero, buttonId,"batch",subscript);
        });
    }
}
function rendering(type){
    if (type == "price") {
        $.each(saleGoodsDTOList, function (index, item) {
            //价格库存
            price(index, item);
        });
    }
    if (type == "editPrice") {
        $.each(saleGoodsDTOList, function (index, item) {
            //仅更新价格
            editPrice(index, item);
        });
    }
    if (type == "stock") {
        $.each(saleGoodsDTOList, function (index, item) {
            //库存
            stock(index, item);
        });
    }
    if (type == "attribute") {
        $.each(saleGoodsDTOList, function (index, item) {
            //属性信息
            attribute(index, item);
        });
    }
    if (type == "image") {
        $.each(saleGoodsDTOList, function (index, item) {
            //图片
            image(index, item);
        });
    }
    if (type == "video") {
        $.each(saleGoodsDTOList, function (index, item) {
            //图片
            video(index, item);
        });
    }
    if (type == "desc") {
        $.each(saleGoodsDTOList, function (index, item) {
            //富文本描述
            longDescEdit(index, item);
        });
    }
    if (type == null) {
        $.each(saleGoodsDTOList, function (index, item) {
            //价格库存
            price(index, item);
            //属性信息
            attribute(index, item);
            //包裹信息
            logistics(index, item);
            //富文本描述
            longDesc(index, item);
            //图片
            image(index, item);
        });
    }

    $('#title-data').on('click','.up-caps-all,.up-caps-first',convertTitle);
}

//基本信息👇👇👇👇👇👇👇👇👇👇👇👇👇
function getCategoryOneLabel() {
    let firstCategoryid = $('#firstCategoryid').val();
    if (firstCategoryid == null || firstCategoryid == '') {
        $('#categoryOneLabel').text("");
        $('#categoryOneLabelEn').text("");
        return;
    }
    var config = {
        url: "/configuration/platformCategory/getPlatformCategoryById/"+firstCategoryid,
        success: function (result) {
            $('#categoryOneLabel').text(result.data.categoryDetail);
            $('#categoryOneLabelEn').text(result.data.categoryEnDetail);
            // 重新计算step-1的高度
            if($('#smartwizard').length > 0) {
                $('#smartwizard').smartWizard("fixHeight");
            }
        }
    };
    $.ajax(config);
}

function getCategoryTwoLabel() {
    let secondCategoryid = $('#secondCategoryid').val();
    if (secondCategoryid == null || secondCategoryid == '') {
        $('#categoryTwoLabel').text("");
        $('#categoryTwoLabelEn').text("");
        return;
    }
    var config = {
        url: "/configuration/platformCategory/getPlatformCategoryById/"+secondCategoryid,
        success: function (result) {
            $('#categoryTwoLabel').text(result.data.categoryDetail);
            $('#categoryTwoLabelEn').text(result.data.categoryEnDetail);
        }
    };
    $.ajax(config);
}


//👇👇👇👇👇👇👇👇👇👇👇👇👇价格库存👇👇👇👇👇👇👇👇👇👇👇👇👇
function price(index, item) {
    let standardPrice = item.ebayPrice == null ? (item.standardPrice==null ? 0 : item.standardPrice) : item.ebayPrice;
    let startSellingCount = item.startSellingCount == null ? 1 : item.startSellingCount;
    let redLinePrice  = item.redLinePrice == null ? 0 :item.redLinePrice;
    let publicationDay = item.publicationDay == null ? "GTC" : item.publicationDay;
    let bestOfferFlag = item.bestOfferFlag == null ? 1 : item.bestOfferFlag;
    let goodsHeadId = item.goodsHeadId ? "("+item.goodsHeadId+")" : "";
    let stockPriceHtml =
        "<div id=\"" + item.id + "\"  class=\"form-group\">" +
        " <div class=\"col-sm-2\" align='center' >" +
        "<label class=\"control-label\">" + item.goodsCode + goodsHeadId +  (item.shopCode==undefined?"":(item.shopCode+"-"+item.shopName))+ "</label>" +
        "</div>" +
        "<div class=\"col-sm-10\"/>" +

        //刊登天数
        "<div class=\"row\">"+
        "<label class=\"col-sm-1 control-label\">刊登天数：</label>" +
        "<div class=\"col-sm-1\">\n" +
        "<select class=\"form-control\" name=\"listings[" + index + "].publicationDay\"   value=" + publicationDay + " >" +
        "<option value=\"GTC\">GTC</option>" +
        "<option value=\"1\">1</option>" +
        "<option value=\"3\">3</option>" +
        "<option value=\"5\">5</option>" +
        "<option value=\"7\">7</option>" +
        "<option value=\"10\">10</option>" +
        "</select>" +
        "</div>" +
        // 价格
        "<label class=\"col-sm-1 control-label\">价格：</label>" +
        "<div class=\"col-sm-1\">\n" +
        "<input type=\"text\" id=\"standardPrice\" name=\"listings[" + index + "].standardPrice\" class=\"form-control is-required\" value=" + standardPrice + " required>" +
        "</div>" +

        // 价格
        "<label class=\"col-sm-1 control-label\">红线价：</label>" +
        "<div class=\"col-sm-1\">\n" +
        "<input type=\"text\" id=\"redLinePrice\" name=\"listings[" + index + "].redLinePrice\" class=\"form-control is-required\" value=" + redLinePrice + " readonly>" +
        "</div>" +

        //库存
        "<label class=\"col-sm-1 control-label\">库存：</label>\n" +
        "<div class=\"col-sm-1\">\n" +
        "<input type=\"text\" class=\"form-control\" name=\"listings[" + index + "].stockOnSalesQty\" value=" + item.stockOnSalesQty + " required>\n" +
        "</div>" +

        //起拍数量
        "<label class=\"col-sm-1 control-label\">起卖数量：</label>\n" +
        "<div class=\"col-sm-1\">\n" +
        "<input type=\"text\" class=\"form-control\" name=\"listings[" + index + "].startSellingCount\" value=" + startSellingCount + " required>\n" +
        "</div>" +

        //是否开启议价
        "<label class=\"col-sm-1 control-label\">是否开启议价：</label>" +
        "<div class=\"col-sm-1\">\n" +
        "<select class=\"form-control\" name=\"listings[" + index + "].bestOfferFlag\" value=" + bestOfferFlag + " >" +
        "<option value=\"1\">是</option>" +
        "<option value=\"2\">否</option>" +
        "</select>" +
        "</div>" +
        "</div>"+

        "</div>";


    $("#stock-price-data").append(stockPriceHtml);

    //选中刊登天数
    $("#stock-price-data").find("select[name='listings[" + index + "].publicationDay']").find("option[value='"+publicationDay+"']").attr("selected",true);
    //选中是否开启议价
    $("#stock-price-data").find("select[name='listings[" + index + "].bestOfferFlag']").find("option[value='"+bestOfferFlag+"']").attr("selected",true);

}

//👆👆👆👆👆👆👆👆👆👆👆👆👆👆价格库存👆👆👆👆👆👆👆👆👆👆👆👆👆👆

//👇👇👇👇👇👇👇👇👇👇👇👇👇价格👇👇👇👇👇👇👇👇👇👇👇👇👇
function editPrice(index, item) {
    let standardPrice = item.ebayPrice == null ? (item.standardPrice==null ? 0 : item.standardPrice) : item.ebayPrice;
    let redLinePrice  = item.redLinePrice == null ? 0 :item.redLinePrice;
    let publicationDay = item.publicationDay == null ? 1 : item.publicationDay;
    let bestOfferFlag = item.bestOfferFlag == null ? 1 : item.bestOfferFlag;
    let goodsHeadId = item.goodsHeadId ? "("+item.goodsHeadId+")" : "";
    let stockPriceHtml =
        "<div id=\"" + item.id + "\"  class=\"form-group\">" +
        " <div class=\"col-sm-4\" align='center' >" +
        "<label class=\"control-label\">" + item.goodsCode + goodsHeadId +  (item.shopCode==undefined?"":(item.shopCode+"-"+item.shopName))+ "</label>" +
        "</div>" +
        "<div class=\"col-sm-8\"/>" +

        // 价格
        "<label class=\"col-sm-1 control-label\">价格：</label>" +
        "<div class=\"col-sm-2\">\n" +
        "<input type=\"text\" id=\"standardPrice\" name=\"listings[" + index + "].standardPrice\" class=\"form-control is-required\" value=" + standardPrice + " required>" +
        "</div>" +

        // 价格
        "<label class=\"col-sm-2 control-label\">红线价：</label>" +
        "<div class=\"col-sm-2\">\n" +
        "<input type=\"text\" id=\"redLinePrice\" name=\"listings[" + index + "].redLinePrice\" class=\"form-control is-required\" value=" + redLinePrice + " readonly>" +
        "</div>" +

        "</div>";


    $("#stock-price-data").append(stockPriceHtml);

    //选中刊登天数
    $("#stock-price-data").find("select[name='listings[" + index + "].publicationDay']").find("option[value='"+publicationDay+"']").attr("selected",true);
    //选中是否开启议价
    $("#stock-price-data").find("select[name='listings[" + index + "].bestOfferFlag']").find("option[value='"+bestOfferFlag+"']").attr("selected",true);

}
//👆👆👆👆👆👆👆👆👆👆👆👆👆👆价格👆👆👆👆👆👆👆👆👆👆👆👆👆👆


//👇👇👇👇👇👇👇👇👇👇👇👇👇库存👇👇👇👇👇👇👇👇👇👇👇👇👇
function stock(index, item) {
    let goodsHeadId = item.goodsHeadId ? "("+item.goodsHeadId+")" : "";
    let stockPriceHtml =
        "<div id=\"" + item.id + "\"  class=\"form-group\">" +
        " <div class=\"col-sm-4\" align='center' >" +
        "<label class=\"control-label\">" + item.goodsCode + goodsHeadId +  (item.shopCode==undefined?"":(item.shopCode+"-"+item.shopName))+ "</label>" +
        "</div>" +
        "<div class=\"col-sm-8\"/>" +

        //库存
        "<label class=\"col-sm-1 control-label\">库存：</label>\n" +
        "<div class=\"col-sm-1\">\n" +
        "<input type=\"text\" class=\"form-control\" name=\"listings[" + index + "].stockOnSalesQty\" value=" + item.stockOnSalesQty + " required>\n" +
        "</div>" +

        "</div>";

    $("#stock-price-data").append(stockPriceHtml);

}
//👆👆👆👆👆👆👆👆👆👆👆👆👆👆价格👆👆👆👆👆👆👆👆👆👆👆👆👆👆
//👇👇👇👇👇👇👇👇👇👇👇👇👇属性👇👇👇👇👇👇👇👇👇👇👇👇👇
//属性table
function attribute(index, item) {
    getTrendsColumns(item);
    getCategoryOneLabel();
}

$(document).on('change','#goodsInfoAttribute',function() {
    var _tr = $("#goods-info-table tbody tr").eq($(this).closest("tr").index());
    _tr.find("#attributeMemo").val($(this).find("option:selected").attr("data-type"));
});


//删除字段
function deleteRow(tableId,value){
    table.set(tableId)
    sub.editRow()
    $("#" + tableId).bootstrapTable('remove', { field: "index", values: [value] });
    sub.editRow()
}

//新增字段
function addColumn(tableId) {
    table.set(tableId)
    var count = $("#" + tableId).bootstrapTable('getData').length;
    let row= {
        index: $.table.serialNumber(count),
        isRequire: "0",
        attributeName: "",
        attributeValue:""
    }
    sub.addRow(row,tableId);
}

/**
 * 属性Table 删除空项
 * -- 排除必填属性，value为空的 删除
 * isRequire 字段: 0非必填 1必填
 * @param id tableId
 */
function removeEmptyItem(id) {
    table.set(id);
    sub.editRow();
    let tableDatas = $(`#${id}`).bootstrapTable('getData');
    if(tableDatas && tableDatas.length) {
        let indexs = [];
        for (let i = 0; i < tableDatas.length; i++) {
            if ($.common.isEmpty(tableDatas[i].attributeValue) && tableDatas[i].isRequire != '1') {
                indexs.push(tableDatas[i].index)
            }
        }
        if(indexs.length) {
            $(`#${id}`).bootstrapTable('remove', { field: "index", values: indexs});
        }
    }
}

//👆👆👆👆👆👆👆👆👆👆👆👆👆👆属性👆👆👆👆👆👆👆👆👆👆👆👆👆👆

//物流👇👇👇👇👇👇👇👇👇👇👇👇👇
function logistics(index,item){
    let goodsSpecifications = item.goodsSpecifications;

    let lengthValue = goodsSpecifications?.length ? goodsSpecifications.length : ( item.itemLength ? item.itemLength : "");
    let widthValue = goodsSpecifications?.width ? goodsSpecifications.width : ( item.itemWidth ? item.itemWidth : "");
    let heightValue = goodsSpecifications?.height ? goodsSpecifications?.height : ( item.itemHeight ? item.itemHeight : "");
    let weightValue = goodsSpecifications?.grossWeight ? goodsSpecifications?.grossWeight : ( item.packageWeight ? item.packageWeight : "");

    let goodsHeadId = item.goodsHeadId ? "("+item.goodsHeadId+")" : "";

    let divHtml =
        "<div id=\"" + item.id + "\"  class=\"form-group\">\n" +
        " <div class=\"col-sm-3\" align='center'>" +
        "<label class=\"control-label\">" + item.goodsCode + goodsHeadId +  (item.shopCode==undefined?"":(item.shopCode+"-"+item.shopName))+ "</label> </div>" +

        "<div class=\"col-sm-9\">" +
        "<div class=\"form-group\">\n" +
        "    <div class=\"col-sm-3\">\n" +
        "        <div class=\"col-sm-4\" style=\"text-align: right;\">" +
        "            <label class=\"control-label\">长度(CM)：</label>" +
        "        </div>" +
        "        <div class=\"col-sm-7\">\n" +
        "            <input id=\"itemLength\" class=\"form-control\" " +
        "                   name=\"listings[" + index + "].itemLength\" required value=\"" + lengthValue + "\">" +
        "        </div>" +
        "    </div>" +
        "    <div class=\"col-sm-3\">\n" +
        "        <div class=\"col-sm-4\" style=\"text-align: right;\">\n" +
        "            <label class=\"control-label\">宽度(CM)：</label>\n" +
        "        </div>\n" +
        "        <div class=\"col-sm-7\">\n" +
        "            <input id=\"itemWidth\" class=\"form-control\"\n" +
        "                   name=\"listings[" + index + "].itemWidth\" required value=\""+widthValue+"\">" +
        "        </div>\n" +
        "    </div>\n" +
        "    <div class=\"col-sm-3\">\n" +
        "        <div class=\"col-sm-4\" style=\"text-align: right;\">\n" +
        "            <label class=\"control-label\">高度(CM)：</label>\n" +
        "        </div>\n" +
        "        <div class=\"col-sm-7\">\n" +
        "            <input id=\"itemHeight\" class=\"form-control\"\n" +
        "                   name=\"listings[" + index + "].itemHeight\"\n" +
        "                   required value=\""+heightValue+"\">" +
        "        </div>\n" +
        "    </div>\n" +
        "    <div class=\"col-sm-3\">\n" +
        "        <div class=\"col-sm-4\" style=\"text-align: right;\">\n" +
        "            <label class=\"control-label\">重量(KG)：</label>\n" +
        "        </div>\n" +
        "        <div class=\"col-sm-7\">\n" +
        "            <input id=\"packageWeight\"\n" +
        "                   class=\"form-control\"\n" +
        "                   name=\"listings[" + index + "].packageWeight\"\n" +
        "                   required value=\""+weightValue+"\">" +
        "        </div>\n" +
        "    </div>\n" +
        "</div>" +
        "</div>" +
        "</div>" ;
    $("#logistics-data").append(divHtml);
}

//物流👆👆👆👆👆👆👆👆👆👆👆👆👆👆



//长描述👇👇👇👇👇👇👇👇👇👇👇👇👇
function longDesc(index,item){
    let desc= item.detailDescription ? item.detailDescription : "";
    let goodsHeadId = item.goodsHeadId ? "("+item.goodsHeadId+")" : "";
    let divHtml =
        "<div id=\"" + item.id + "\"  class=\"form-group\">" +
        "<div class=\"col-sm-2\" align='center'>" +
        "<label class=\"control-label\">" + item.goodsCode + goodsHeadId +  (item.shopCode==undefined?"":(item.shopCode+"-"+item.shopName)) + "</label> </div>" +
        "<div class=\"col-sm-5\">" +
        "<div class=\"row\">" +
        "<div class=\"col-sm-12\">" +
        "<div class=\"input-group\">" +
        "<select class=\"form-control noselect2 templateSelect\" name=\"listings[" + index + "].descriptionId\" data-allo-clear=\"true\">" +
        "</select>" +
        "<span class=\"input-group-btn\">" +
        "<a class=\"btn btn-info\" id=\"previewTemplate\" onclick=\"previewTemplate("+index+",this)\"> 预览模板 </a>" +
        "</span>" +
        "</div>" +
        "</div>" +
        "</div>" +
        "</div>"

    ;
    $("#long-desc-data").append(divHtml);
}


//长描述👇👇👇👇👇👇👇👇👇👇👇👇👇
function longDescEdit(index,item){
    let desc= item.shortDescription ? item.shortDescription : "";
    let goodsHeadId = item.goodsHeadId ? "("+item.goodsHeadId+")" : "";
    let divHtml =
        "<div id=\"" + item.id + "\"  class=\"form-group\">" +
        "<div class=\"col-sm-2\" align='center'>" +
        "<label class=\"control-label\">" + item.goodsCode + goodsHeadId +  (item.shopCode==undefined?"":(item.shopCode+"-"+item.shopName)) + "</label> </div>" +

        "<div class=\"col-sm-8\">" +
        "<textarea type=\"text\" class=\"form-control is-required\" name=\"listings[" + index + "].shortDescription\" id=\"shortDescription\" style=\"margin-bottom: 3px\"  placeholder=\"文本描述\">" +
        desc +
        "</textarea>"+
        "</div>"+
        "<div class=\"col-sm-2\">" +
        "<input type=\"text\" class=\"form-control \" name=\"listings[" + index + "].shortDescriptionOld\" style=\"width:200px;height:30px;\">" +
        "<div style=\"text-align:center;\"><a data-index='"+index+"' class=\"btn btn-info replaceDetailDescription\"> 👇 </a></div>" +
        "<input type=\"text\" class=\"form-control \" name=\"listings[" + index + "].shortDescriptionNew\" style=\"width:200px;height:30px;\">" +
        "</div>"

    ;
    $("#long-desc-data").append(divHtml);
}

//长描述👆👆👆👆👆👆👆👆👆👆👆👆👆👆

//👇👇👇👇👇👇👇👇👇👇👇👇👇视频信息👇👇👇👇👇👇👇👇👇👇👇👇👇
function video(index, item) {
    let videoSelect = item.videoSelect;
    let videoSelectMap = item.videoSelectMap;

    let goodsHeadId = item.goodsHeadId ? "(" + item.goodsHeadId + ")" : "";
    let videoHtml =
        "<div id=\"" + item.id + "\" class=\"form-group\">" +
        " <div class=\"col-sm-2\" align='center'>" +
        "<label class=\"control-label\">" + item.goodsCode + goodsHeadId + (item.shopCode == undefined ? "" : (item.shopCode + "-" + item.shopName)) + "</label>" +
        "</div>" +
        "<div class=\"col-sm-10\">" +

        // 视频
        "<div class=\"row\">" +
        "<label class=\"col-sm-1 control-label\">视频：</label>" +
        "<div class=\"col-sm-2\">\n" +
        "<select class=\"form-control\" name=\"listings[" + index + "].videoSelect\">" +
        generateSelectOptions(videoSelectMap, videoSelect) + // Call function to generate options
        "</select>" +
        "</div>" +
        "</div>";

    function generateSelectOptions(videoSelectMap, selectedValue) {
        let optionsHtml = '<option value="">请选择视频</option>';
        for (let key in videoSelectMap) {
            if (videoSelectMap.hasOwnProperty(key)) {
                let isSelected = key === selectedValue ? "selected" : ""; // Check if option should be selected
                optionsHtml += "<option value=\"" + key + "\" " + isSelected + ">" + videoSelectMap[key] + "</option>";
            }
        }
        return optionsHtml;
    }

    $("#video-data").append(videoHtml);
    $("#video-data").find("select[name='listings[" + index + "].videoSelect']").find("option[value='"+videoSelect+"']").attr("selected",true);

}

//👆👆👆👆👆👆👆👆👆👆👆👆👆👆视频信息👆👆👆👆👆👆👆👆👆👆👆👆👆👆




//图片模块👇👇👇👇👇👇👇👇👇👇👇👇👇
function image(index, item) {
    let goodsHeadId = item.goodsHeadId ? "("+item.goodsHeadId+")" : "";
    let imageSelect = saleGoodsDTOList[0].goodsHeadId
        ? "<div style='margin-top: 4px; margin-bottom: 4px;'><a class='btn btn-success' data-type='"+index+"' onclick=\"imageUploadByheadId('"+item.goodsHeadId+"', '"+item.brandCode+"', this)\"><i class='fa fa-edit'></i>图片选择</a></div>"
        : "<div style='margin-top: 4px; margin-bottom: 4px;'><a class='btn btn-success' data-type='"+index+"' onclick=\"imageUploadByGoodsId('"+item.id+"', this)\"><i class='fa fa-edit'></i>图片选择</a></div>";
    let clearImage =
        "<div style='margin-top: 4px; margin-bottom: 4px;'><a class='btn btn-danger' data-type='"+index+"' onclick=\"oneClickClear(this)\"><i class='fa fa-edit'></i>一键清除</a></div>"

    let divHtml =
        "<div id=\"" + item.id + "\"  class=\"form-group\">\n" +
        "<div class=\"col-sm-1\" align='center'>" +
        "<div><label class=\"control-label\">" + item.goodsCode + goodsHeadId +  (item.shopCode==undefined?"":(item.shopCode+"-"+item.shopName)) + "</label> </div>" +

        "<div className=\"btn-group-sm\">"+
        // "<a class=\"btn btn-success\" onclick=\"getOrigin('" + item.goodsCode + "','image')\"><i class=\"fa fa-plus\"></i> sku库替换图片</a>"+
        "</div>"+

        imageSelect + clearImage +

        "<div><input type=\"file\" class=\"upgteimg2\"  data-type=\"" + index + "\" multiple=\"multiple\"/></div>" +
        "</div>" +

        "<div class=\"col-sm-11\">" +
        "<div class=\"file-loading\">" +
        "    <div id=\"showimg\">" +
        "       <ul class='imageSort connectList' id=\"showui" + index + "\">" +
        "</ul>" +
        "       <div id=\"showinput\"></div>" +
        "    </div>\n" +
        "</div>" +
        "</div>";
    $("#image-data").append(divHtml);


    initImage(index, item);

    $(".imageSort").sortable({connectWith: ".connectList"}).disableSelection()

    Fancybox.bind('[data-fancybox]', {});
}

//随机数
function randomString(len) {
    len = len || 32;
    var $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
    var maxPos = $chars.length;
    var pwd = '';
    for (i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
}

//初始化图片
function initImage(index, item) {
    var showui = document.getElementById("showui" + index);
    var goodsImageList = item.goodsImageList;
    if (goodsImageList == null || goodsImageList.length == 0) {
        return;
    }
    for (var i = 0; i < goodsImageList.length; i++) {
        if (goodsImageList[i].imageUrl == null || goodsImageList[i].imageUrl == "" || goodsImageList[i].imageUrl == undefined) {
            continue;
        }
        result = "<div class=\"showdiv\"> <img class=\"center\" src=\"/img/delete.svg\" /> </div> "
            +"<a data-fancybox=\"\" href="+goodsImageList[i].imageUrl+">"
            +"<img class=\"listings[" + index + "].imgArrs scale-img\"  id=\"img" + i + randomString(1) + randomString(2) + randomString(5) + "\" class=\"showimg scale-img\" style=\"width: 150px;\" src=\"" + goodsImageList[i].imageUrl + "\" />"
            +"</a>";
        var li = document.createElement('li');
        li.innerHTML = result;
        showui.appendChild(li);
    }
}

//图片上传
$(document).on('change', '.upgteimg2', function () {
    let index = $(this).attr("data-type");
    readFile(event, index);
});

function readFile(event, index) {
    var showui = document.getElementById("showui" + index);
    fd = new FormData();
    var iLen = event.target.files.length;
    for (var i = 0; i < iLen; i++) {
        if (!event.target['value'].match(/.jpg|.gif|.png|.jpeg|.bmp/i)) {　　 //判断上传文件格式
            return alert("上传的图片格式不正确，请重新选择");
        }
        const formdata = new FormData();
        // 1.单文件上传时
        formdata.append('files', event.target.files[i]);
        $.ajax({
            url: ctx + 'common/uploadFiles',
            type: 'post',
            async: true,
            contentType: false,
            processData: false,
            cache: false,
            data: formdata,
            dataType: 'json',
            success: function (data) {
                result = '<div class="showdiv"> <img class="center" src="/img/delete.svg" /> </div>  '
                    +"<a data-fancybox=\"\" href="+data.urls+">"
                    +'<img class=\'listings[' + index + '].imgArrs\' id="img' + i + randomString(1) + randomString(2) + randomString(5) + '" class="showimg scale-img" style="width: 150px;" src="' + data.urls + '" />'
                    +"</a>";
                var li = document.createElement('li');
                li.innerHTML = result;
                showui.appendChild(li);

                //如果是批量更新  则刷新一下高度
                if($('#smartwizardSingle').length > 0){
                    $('#smartwizardSingle').smartWizard("fixHeight");
                }
                if($('#smartwizard').length > 0){
                    $('#smartwizard').smartWizard("fixHeight");
                }
            }
        })
    }
}

//图片删除
$(document).on('click', '.showdiv', function () {
    let parent = $(this).parent();
    //如果parent是li标签，就删除li标签
    if (parent[0].tagName == "LI") {
        parent.remove();
        return;
    }
    parent = parent.parent();
    if (parent[0].tagName == "LI") {
        parent.remove();
    }
});
//图片模块👆👆👆👆👆👆👆👆👆👆👆👆👆👆


$(document).ready(function () {
    // 工具栏按钮
    var btnFinish = $('<a id="btn-finish"></a>').text('完成')
        .addClass('btn btn-info')
        .on('click', function () {
            submitSave();
        });
    var btnCancel = $('<a id="btn-cancel"></a>').text('取消')
        .addClass('btn btn-danger')
        .on('click', function () {
            $('#smartwizard').smartWizard("reset");
        });
    // 下面两个按钮是为了因为插件默认的是botton,这里换成<a>,也可以选择用样式替换,或者不替换
    var btnNext = $('<a id="btn-next"></a>').text('下一步')
        .addClass('btn btn-info')
        .on('click', function () {
            handleNext()
        });
    var btnPrev = $('<a id="btn-prev"></a>').text('上一步')
        .addClass('btn btn-success disabled')
        .on('click', function () {
            $('#smartwizard').smartWizard("prev");
        });
    // 初始化表单向导组件
    $('#smartwizard').smartWizard({
        theme: 'arrows', // Change theme Default、Arrows、selected、Dots、Progress
        autoAdjustHeight: true, // 自动调整高度, 默认true
        enableURLhash: false, //开启URL hash,开启后点击浏览器前进后退按钮会执行下一步和上一步操作
        transition: {
            animation: 'slide-horizontal', // Effect on navigation, none/fade/slide-horizontal/slide-vertical/slide-swing
        },
        toolbarSettings: {
            showNextButton: false,// 因为上面自定义了下一步按钮, 所以隐藏掉插件自带的按钮, 如果不使用自定义按钮, 需要改为true或者去掉该属性
            showPreviousButton: false,// 因为上面自定义了上一步按钮, 所以隐藏掉插件自带的按钮, 如果不使用自定义按钮, 需要改为true或者去掉该属性
            toolbarExtraButtons: [ btnPrev, btnNext, btnFinish]// 扩展的按钮集合
        },
        keyboardSettings: {
            keyNavigation: false, // Enable/Disable keyboard navigation(left and right keys are used if enabled)
        }
    });
    // 部分编辑 初始化表单向导组件
    $('#smartwizardSingle').smartWizard({
        theme: 'arrows', // Change theme Default、Arrows、selected、Dots、Progress
        autoAdjustHeight: true, // 自动调整高度, 默认true
        enableURLhash: false, //开启URL hash,开启后点击浏览器前进后退按钮会执行下一步和上一步操作
        transition: {
            animation: 'slide-horizontal', // Effect on navigation, none/fade/slide-horizontal/slide-vertical/slide-swing
        },
        toolbarSettings: {
            showNextButton: false,// 因为上面自定义了下一步按钮, 所以隐藏掉插件自带的按钮, 如果不使用自定义按钮, 需要改为true或者去掉该属性
            showPreviousButton: false,// 因为上面自定义了上一步按钮, 所以隐藏掉插件自带的按钮, 如果不使用自定义按钮, 需要改为true或者去掉该属性
            toolbarExtraButtons: []// 扩展的按钮集合
        },
        keyboardSettings: {
            keyNavigation: false, // Enable/Disable keyboard navigation(left and right keys are used if enabled)
        }
    });

});

// 显示步骤时将触发事件
$("#smartwizard").on("showStep", function (e, anchorObject, stepNumber, stepDirection, stepPosition) {
    // 下面按钮是快速操作栏的
    $("#prev-btn").removeClass('disabled');
    $("#next-btn").removeClass('disabled');
    // 下面按钮是工具栏的
    $("#btn-prev").removeClass('disabled');
    $("#btn-save").removeClass('disabled');
    $("#btn-next").removeClass('disabled');
    $("#btn-finish").removeClass('disabled');
    if (stepPosition === 'first') {
        $("#prev-btn").addClass('disabled');// 快速操作栏（演示用）
        $("#btn-prev").addClass('disabled');
        if ( $("#shopCode").val() ){
            $("#btn-save").addClass('disabled');
        }
        $("#btn-finish").addClass('disabled');
    } else if (stepPosition === 'last') {
        $("#next-btn").addClass('disabled');// 快速操作栏（演示用）
        $("#btn-next").addClass('disabled');
    } else {
        $("#prev-btn").removeClass('disabled');// 快速操作栏（演示用）
        $("#next-btn").removeClass('disabled');// 快速操作栏（演示用）
        $("#btn-prev").removeClass('disabled');
        $("#btn-save").removeClass('disabled');
        $("#btn-next").removeClass('disabled');
        $("#btn-finish").addClass('disabled');
    }
});


//构造数据
function setDataEb(data,number) {
    data["platform"] = "EB";
    $('.form').each(function (index, form) {
        $.each($(form).serializeArray(), function (i, field) {
            //店铺 品类
            if (contain(field.name,'firstCategoryid')||contain(field.name,'shopCode')){
                doValue(data, field.name, field.value);
            }
        });
    });
    if (checkTypeValue=='edit'){
        $.each(saleGoodsDTOList, function (index, item) {
            data["listings[" + index + "].goodsCode"] = item.goodsCode;
            data["listings[" + index + "].id"] = item.goodsHeadId;
            data["listings[" + index + "].shopCode"] = item.shopCode;
            data["firstCategoryid"] = item.categoryId;
            data["listings[" + index + "].categoryId"] = item.categoryId;
            data["listings[" + index + "].publishStatus"] = item.publishStatus;
        });}
    if (checkTypeValue=='confim'){
        data["shopCode"] = listingEditVO.shopCode;
        data["firstCategoryid"] = listingEditVO.firstCategoryid;
    }
}
function contain(str1, str2) {
    return str1.indexOf(str2) != -1;
}
function doValue(data,name,value) {
    if (data[name]) {
        data[name] += ("," + value);
    } else {
        data[name] = value;
    }
}
function checkType(number) {
    switch (checkTypeValue){
        case "add":
        case "confim":
            return number == 2 || number == 6;
            break;
        case "edit":
            return number == 1;
            break;
    }
}
function getAjaxData(flag,number) {
    var defer = $.Deferred();
    var data = {};
    setDataEb(data, number);
    $.ajax({
        url: prefix + "/checkViolateWord",
        type: "post",
        dataType: "json",
        async: true,
        data: data,
        success: function (e) {
            defer.resolve(e);
            // flag = +e.code === 0
        }
    });
    return defer;
}
// 该事件在离开某个步骤之前触发
$("#smartwizard").on("leaveStep", function (e, anchorObject, currentStepNumber, nextStepNumber, stepDirection) {
    if (stepDirection == 'forward') {
        var form = $("#step-" + (currentStepNumber + 1)).find('.form');
        if (form.length > 0) {
            return form.validate().form();
        }
        return true;
    }
    return true;
});

$("#reset-btn").on("click", function () {
    // Reset wizard
    $('#smartwizard').smartWizard("reset");
    return true;
});

$("#prev-btn").on("click", function () {
    // Navigate previous
    $('#smartwizard').smartWizard("prev");
    return true;
});

$("#next-btn").on("click", function () {
    // Navigate next
    handleNext();
    return true;
});
function handleNext() {
    var smartWizard = $('#smartwizard').smartWizard("getStepIndex");
    $('#smartwizard').smartWizard("next");

}