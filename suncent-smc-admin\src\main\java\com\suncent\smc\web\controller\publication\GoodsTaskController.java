package com.suncent.smc.web.controller.publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.suncent.smc.common.annotation.Log;
import com.suncent.smc.common.core.controller.BaseController;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.page.TableDataInfo;
import com.suncent.smc.common.enums.BusinessType;
import com.suncent.smc.common.enums.GoodsTaskStatusEnum;
import com.suncent.smc.common.enums.GoodsTaskTypeEnum;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.poi.ExcelUtil;
import com.suncent.smc.persistence.publication.domain.dto.ListingEditDTO;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.service.IAiGenerationTaskService;
import com.suncent.smc.persistence.publication.service.IGoodsTaskInfoService;
import com.suncent.smc.persistence.publication.service.IGoodsTaskService;
import com.suncent.smc.provider.biz.publication.ListingInfoBiz;
import com.suncent.smc.provider.biz.publication.PlatformListingFactory;
import com.suncent.smc.provider.biz.publication.PythonExternalBiz;
import com.suncent.smc.provider.biz.publication.domain.ListingsEditVO;
import com.suncent.smc.provider.biz.publication.dto.PriceCountDTO;
import com.suncent.smc.provider.biz.publication.dto.PriceCountResultDTO;
import com.suncent.smc.provider.biz.publication.service.IBaseListingService;
import com.suncent.smc.provider.biz.task.GoodsTaskBiz;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * SMC-任务管理列Controller
 *
 * <AUTHOR>
 * @date 2023-04-19
 */
@Controller
@RequestMapping("publication/task")
public class GoodsTaskController extends BaseController
{
    private String prefix = "publication/task";

    @Autowired
    private IGoodsTaskService goodsTaskService;

    @Autowired
    private IGoodsTaskInfoService goodsTaskInfoService;
    @Autowired
    private PlatformListingFactory platformListingFactory;
    @Autowired
    private GoodsTaskBiz goodsTaskBiz;
    @Autowired
    ListingInfoBiz listingInfoBiz;
    @Autowired
    private PythonExternalBiz pythonExternalBiz;
    @Autowired
    private IAiGenerationTaskService aiGenerationTaskService;
    @RequiresPermissions("publication:task:view")
    @GetMapping()
    public String task(ModelMap mmap)
    {
        String date = DateUtils.getDate();
        mmap.put("currentDate", date);
        mmap.put("isAdmin", ShiroUtils.getSysUser().isAdmin());
        return prefix + "/task_list";
    }

    /**
     * 查询SMC-任务管理列列表
     */
//    @RequiresPermissions("publication:task:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(GoodsTask goodsTask)
    {
        startPage();
        if (!ShiroUtils.getSysUser().isAdmin()) {
            goodsTask.setCreateBy(getSysUser().getUserId().toString());
        }
        if (StrUtil.isBlank(goodsTask.getCreateBy())){
            goodsTask.setCreateBy(null);
        }
        List<GoodsTask> list = goodsTaskService.selectGoodsTaskList(goodsTask);
        List<GoodsTask> returnList = goodsTaskBiz.getTaskManageErrorNum(list);
        return getDataTable(returnList);
    }

    /**
     * 导出SMC-任务管理列列表
     */
//    @RequiresPermissions("publication:task:export")
    @Log(title = "SMC-任务管理列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(GoodsTask goodsTask)
    {
        List<GoodsTask> list = goodsTaskService.selectGoodsTaskList(goodsTask);
        ExcelUtil<GoodsTask> util = new ExcelUtil<GoodsTask>(GoodsTask.class);
        return util.exportExcel(list, "SMC-任务管理列数据");
    }

    /**
     * 新增SMC-任务管理列
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/task_add";
    }

    /**
     * 新增保存SMC-任务管理列
     */
    @RequiresPermissions("publication:task:add")
    @Log(title = "SMC-任务管理列", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(GoodsTask goodsTask)
    {
        return toAjax(goodsTaskService.insertGoodsTask(goodsTask));
    }

    /**
     * 修改SMC-任务管理列
     */
    @RequiresPermissions("publication:task:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        GoodsTask goodsTask = goodsTaskService.selectGoodsTaskById(id);
        mmap.put("goodsTask", goodsTask);
        return prefix + "/task_edit";
    }

    /**
     * 修改保存SMC-任务管理列
     */
    @RequiresPermissions("publication:task:edit")
    @Log(title = "SMC-任务管理列", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(GoodsTask goodsTask)
    {
        return toAjax(goodsTaskService.updateGoodsTask(goodsTask));
    }

    /**
     * 删除SMC-任务管理列
     */
    @RequiresPermissions("publication:task:remove")
    @Log(title = "SMC-任务管理列", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(goodsTaskService.deleteGoodsTaskByIds(ids));
    }

    /**
     * 查询listing操作日志列表
     */
    @GetMapping("/log/{id}")
    public String log(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("taskId", id);
        return  prefix + "/logInfo";
    }

    /**
     * 查询listing操作日志列表
     */
    @PostMapping("/log/list")
    @ResponseBody
    public TableDataInfo logList(GoodsTaskInfo goodsTaskInfo) {
        List<GoodsTaskInfo> taskInfoList = goodsTaskBiz.getTaskInfoLogByIdAndType(goodsTaskInfo.getTaskId());
        return getDataTable(taskInfoList);
    }


    /**
     * 查询任务列表详情
     */
    @GetMapping("/taskInfo/{id}")
    public String taskInfo(@PathVariable("id") Long id, ModelMap mmap) {
        GoodsTask goodsTask = goodsTaskService.selectGoodsTaskById(String.valueOf(id));
        GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
        goodsTaskInfo.setTaskId(String.valueOf(id));
        List<GoodsTaskInfo> infoList = goodsTaskInfoService.selectGoodsTaskInfoList(goodsTaskInfo);

        try {
            if (Objects.nonNull(goodsTask) && goodsTask.getTaskNum() > infoList.size() ) {
                throw new BusinessException("任务未完成，不能查看详情进行确认.");
            }
            List<Integer> ids = infoList.stream().filter(f->Objects.nonNull(f.getListingHeadId()) && !Objects.equals(f.getListingHeadId(),"null")).map(e->Integer.valueOf(e.getListingHeadId())).collect(Collectors.toList());
            if (Objects.isNull(ids)) {
                throw new BusinessException("该批次未生成有效Listing，不能查看详情进行确认.");
            }
            String platform = goodsTask.getPlatform();
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);

            ListingsEditVO editVO = listingServiceByPlatformType.getListingsEditDTOSByListingIds(ids);

            AiGenerationTask queryTask = new AiGenerationTask();
            queryTask.setTaskId(id.intValue());
            List<AiGenerationTask> taskList = aiGenerationTaskService.selectAiGenerationTaskList(queryTask);
            if (CollUtil.isNotEmpty(taskList) && CollUtil.isNotEmpty(editVO.getListingEditDTOS())) {
                if (CollUtil.isNotEmpty(editVO.getListingEditDTOS().get(0).getListingAmazonAttributeLinesInfo())) {
                    List<ListingAmazonAttributeLine> lines = editVO.getListingEditDTOS().get(0).getListingAmazonAttributeLinesInfo();
                    if (CollUtil.isNotEmpty(lines)) {
                        String brandCode = lines.stream().filter(e -> e.getTableName().equalsIgnoreCase("BRAND")).map(e -> e.getTableValue()).findFirst().orElse(null);
                        editVO.setBrandCode(brandCode);
                    }
                }

                // 对描述字段的调整
                for (ListingEditDTO listingEditDTO : editVO.getListingEditDTOS()) {
                    listingEditDTO.setDetailDescription(listingEditDTO.getShortDescription());
                    listingEditDTO.setShortDescription(null);
                    listingEditDTO.setTaskName(goodsTask.getTaskName());

                    if (CollUtil.isNotEmpty(listingEditDTO.getListingAmazonAttributeLinesInfo())) {
                        listingEditDTO.getListingAmazonAttributeLinesInfo().removeIf(e -> e.getTableName().equalsIgnoreCase("BRAND"));
                    }

                }
                if(goodsTask.getPlatform().equals(PlatformTypeEnum.EB.name())) {
                    mmap.put("source", "AI");
                }
            }
            mmap.put("listingEditVO", editVO);
        } catch (Exception e) {
            logger.error("查看自动Lisitng详情错误", e);
            String message = e.getMessage();
            mmap.put("errorMessage", message);
            return "error/service";
        }

        if(goodsTask.getPlatform().equals(PlatformTypeEnum.EB.name())){
            return  prefix + "/ebay_batch_confim_publish";
        } else {
            return  prefix + "/am_batch_confim_publish";
        }
    }

    /**
     * 查询任务列表详情
     */
    @PostMapping("/taskInfo/list")
    @ResponseBody
    public TableDataInfo taskInfoList(ListingLog listingLog) {
        GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
        goodsTaskInfo.setTaskId(String.valueOf(listingLog.getListingId()));
        startPage();
        List<GoodsTaskInfo> taskInfoList = goodsTaskInfoService.selectGoodsTaskInfoList(goodsTaskInfo);
        return getDataTable(taskInfoList);
    }

    @PostMapping("/selectTaskTypeEnum")
    @ResponseBody
    public AjaxResult selectTaskTypeEnum() {
        List<String> allTaskTypeEnumsInfo = GoodsTaskTypeEnum.getAllTaskTypeEnumsInfo();
        return AjaxResult.success(allTaskTypeEnumsInfo);
    }


    @PostMapping("/cancelTask/{taskId}")
    @ResponseBody
    public AjaxResult cancelTask(@PathVariable("taskId") Long taskId) {
        Long userId = ShiroUtils.getUserId();
        GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
        goodsTaskInfo.setTaskId(String.valueOf(taskId));
        List<GoodsTaskInfo> infoList = goodsTaskInfoService.selectGoodsTaskInfoList(goodsTaskInfo);
        if (CollUtil.isEmpty(infoList)){
            return AjaxResult.error("该任务没有需要取消的listing");
        }
        AtomicInteger successCount = new AtomicInteger();
        AtomicInteger errorCount = new AtomicInteger();
        infoList.parallelStream().forEach(taskInfo->{
            try {
                if ( listingInfoBiz.cancelScheduledPublish(taskInfo.getListingHeadId(), userId) ){
                    successCount.getAndIncrement();
                } else {
                    errorCount.getAndIncrement();
                }
            } catch (Exception e) {
                errorCount.getAndIncrement();
                logger.error("取消listing失败", e);
            }
        });
        if (errorCount.get() > 0){
            return AjaxResult.error("取消成功" + successCount + "个,失败" + errorCount + "个");
        }
        goodsTaskService.updateGoodsTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo(), String.valueOf(taskId));
        return AjaxResult.success("取消成功" + successCount + "个,失败" + errorCount + "个");
    }


    /**
     * 价格测算
     * @param goodsCode
     * @param mmap
     * @return
     */
    @GetMapping("/watchPriceCount/{platform}/{goodsCode}")
    public String watchPriceCount(@PathVariable("platform") String platform,@PathVariable("goodsCode") String goodsCode, ModelMap mmap) {
        mmap.put("platform", platform);
        mmap.put("pdmGoodsCode", goodsCode);
        return prefix + "/price_count";
    }


    @PostMapping("/watchPriceCount")
    @ResponseBody
    public AjaxResult watchPriceCount(@RequestBody PriceCountDTO countDTO) {
        List<PriceCountResultDTO> priceCountResultDTOS = pythonExternalBiz.execPython(countDTO);
        return AjaxResult.success(priceCountResultDTOS);
    }

}