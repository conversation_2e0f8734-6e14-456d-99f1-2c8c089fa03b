package com.suncent.smc.persistence.bi.mapper;

import com.suncent.smc.common.annotation.DataSizeMonitor;
import com.suncent.smc.common.enums.MonitorEnum;
import com.suncent.smc.persistence.bi.entity.BiAsinRefundRatePj;
import com.suncent.smc.persistence.bi.entity.BiSalesAnalysis;
import com.suncent.smc.persistence.bi.entity.BiSkuRefundRatePj;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/6/28 10:09
 * @Version 1.0
 */
public interface BiDataServiceMapper {

    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_100000)
    List<Map<String, String>> getCompanyListingDetail();

    /**
     * 根据ASIN列表获取销量详情
     * @param asinList ASIN列表
     * @return 销量详情列表
     */
    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_100000)
    List<Map<String, String>> getCompanyListingDetailByAsinList(@Param("asinList") List<String> asinList);


    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_100000)
    List<Map<String,Objects>> queryFrontSaleTotalByShop( @Param("shopCode") String shopCode, @Param("size") Long size,@Param("asinList")List<String> asinList);

    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_5000)
    List<Map<String,Objects>> querySaleAndOrderTotal(@Param("asinList") List<String> asinList, @Param("shopCode") String shopCode);

    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_100000)
    List<BiSkuRefundRatePj> queryTodaySkuRefundRateData();

    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_100000)
    List<BiAsinRefundRatePj> queryTodayListingRefundRateData();

    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_100000)
    List<BiSalesAnalysis> queryTodaySalesAnalysis(@Param("shopCode")String shopCode);

    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_5000)
    List<BiSalesAnalysis> queryTodaySalesAnalysisPaged(@Param("shopCode") String shopCode, @Param("offset") int offset, @Param("limit") int limit);

    BiSalesAnalysis getTodaySalesAnalysisByAsin(@Param("shopCode")String shopCode,@Param("asin")String asin);

    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_2000)
    List<Map<String, Object>> listStockToSalesRatio(@Param("skuList") List<String> skuList);

    @DataSizeMonitor(value = MonitorEnum.MONITOR_QUERY_LIMIT_50000)
    List<Map<String, Object>> getYesterdaySale();

    List<String> listAsinSales();
}
