package com.suncent.smc.provider.biz.publication.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.suncent.smc.common.config.SpringTaskRetry;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.framework.web.service.DictService;
import com.suncent.smc.persistence.cdp.service.IBrandService;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.configuration.category.service.IConfigRequiredFieldService;
import com.suncent.smc.persistence.ebay.domain.EbayGoodsDescriptionV2;
import com.suncent.smc.persistence.ebay.domain.EbayGoodsHeadV2;
import com.suncent.smc.persistence.ebay.domain.EbayListingGoodsResourceV2;
import com.suncent.smc.persistence.pdm.domain.dto.GoodsRedPriceDTO;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsImage;
import com.suncent.smc.persistence.pdm.domain.entity.MappingGoods;
import com.suncent.smc.persistence.pdm.service.IMappingGoodsService;
import com.suncent.smc.persistence.product.service.ProductDocumentRecordService;
import com.suncent.smc.persistence.publication.domain.dto.*;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.mapper.ListingLogPullRecordMapper;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.template.service.ITemplateEbayDescriptionService;
import com.suncent.smc.persistence.todo.domain.entity.SyncFailTodo;
import com.suncent.smc.persistence.todo.service.IBrandAdaptImgTodoService;
import com.suncent.smc.persistence.todo.service.ISmcAdaptTodoService;
import com.suncent.smc.persistence.todo.service.ISyncFailTodoService;
import com.suncent.smc.provider.biz.bi.service.OdsCrlCrlVcCatalogDataBiz;
import com.suncent.smc.provider.biz.configuration.CategoryInfoHandleBiz;
import com.suncent.smc.provider.biz.configuration.ViolateWordBiz;
import com.suncent.smc.provider.biz.consumer.AmazonListingResultHandler;
import com.suncent.smc.provider.biz.ebay.EbayGoodsInfoBiz;
import com.suncent.smc.provider.biz.error.ErrorDefine;
import com.suncent.smc.provider.biz.pdm.CreatePlatformCode;
import com.suncent.smc.provider.biz.publication.*;
import com.suncent.smc.provider.biz.todo.SMCTodoBiz;
import com.suncent.smc.provider.update.HandlerListingUpdateModuleComposite;
import com.suncent.smc.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *  Listing管理Service 基础公共类
 *
 * <AUTHOR>
 * @since 2023-03-27 11:03:00
 */
@Component
@Slf4j
public class AbstractBaseListingService {

    @Autowired
    protected CreatePlatformCode createPlatformCode;
    @Autowired
    protected GoodsInfoBiz goodsInfoBiz;
    @Autowired
    protected EbayGoodsInfoBiz goodsInfoEbayBiz;
    @Autowired
    protected IGoodsHeadService goodsHeadService;
    @Autowired
    protected IGoodsResourceService goodsResourceService;
    @Autowired
    protected IGoodsDescriptionService goodsDescriptionService;
    @Autowired
    protected IGoodsSpecificationService goodsSpecificationService;
    @Autowired
    protected IListingAmazonAttributeLineService listingAmazonAttributeLineService;
    @Autowired
    protected IConfigRequiredFieldService configRequiredFieldService;
    @Value("${api.amazon-sellerCode-allAsin-url}")
    protected String getAllAsinUrl;
    @Value("${api.amazon-update-price-url}")
    protected String updatePriceUrl;
    @Autowired
    protected AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;
    @Value("${api.amazon-sellerCode-allAsin-v2-url}")
    protected String getAllAsinV2Url;
    @Value("${api.amazon-update-inventory-url}")
    protected String updateStockUrl;
    @Autowired
    protected ProductDocumentRecordService productDocumentRecordService;
    @Autowired
    protected ViolateWordBiz violateWordBiz;
    @Autowired
    @Lazy
    protected ListingInfoBiz listingInfoBiz;

    @Autowired
    protected IListingService listingService;

    @Autowired
    protected DictService dictService;
    @Autowired
    protected IShopService shopService;
    @Autowired
    protected ListingLogPullRecordMapper listingLogPullRecordMapper;
    @Autowired
    protected IMappingGoodsService mappingGoodsService;
    @Autowired
    protected RedisService redisService;
    @Resource
    protected IGoodsTaskService goodsTaskService;
    @Autowired
    protected AmazonListingResultHandler amazonListingResultHandler;
    @Resource
    @Lazy
    protected CommonInfoBiz commonInfoBiz;
    @Autowired
    protected PDMHttpRequestBiz pdmHttpRequestBiz;
    @Autowired
    protected SMCTodoBiz smcTodoBiz;
    @Autowired
    @Lazy
    protected HandlerListingUpdateModuleComposite handlerListingUpdateModuleComposite;
    @Autowired
    protected OdsCrlCrlVcCatalogDataBiz  odsCrlCrlVcCatalogDataBiz;
    protected ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
            2,
            4,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(500));

    protected ThreadPoolExecutor pullPoolConfig = new ThreadPoolExecutor(
            2,
            8,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(2000));

    @Autowired
    protected ThreadPoolForMonitorManager threadPoolForMonitorManager;
    @Autowired
    @Lazy
    protected ListingUpdateBuilder listingUpdateBuilder;
    @Autowired
    protected ISyncFailTodoService syncFailTodoService;
    @Autowired
    IGoodsOriginService goodsOriginService;
    @Autowired
    protected IUpdateRecordService updateRecordService;
    @Autowired
    protected IAmazonListingPullLackService amazonListingPullLackService;
    @Autowired
    protected IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;
    @Autowired
    protected ISysConfigService configService;
    @Autowired
    protected CategoryInfoHandleBiz categoryInfoHandleBiz;
    @Autowired
    protected IBrandAdaptImgTodoService brandAdaptImgTodoService;
    @Autowired
    SpringTaskRetry retryable;
    @Resource
    protected ISmcAdaptTodoService smcAdaptTodoService;
    @Resource
    protected ITemplateEbayDescriptionService templateEbayDescriptionService;
    @Autowired
    protected IBrandService brandService;
    @Autowired
    protected IAdapterManageService adapterManageService;
    @Autowired
    protected RedissonClient redissonClient;
    /**
     * 组装商品主表数据
     * @param dto
     * @return
     */
    protected GoodsHead getGoodHead(BatchListingDTO dto, String siteCode, ListingDTO listing) {
        String shopCode = dto.getShopCode();
        String platform = dto.getPlatform();
        Integer publishType = dto.getPublishType();

        //保存商品信息
        GoodsHead goodsHead = new GoodsHead();
        //设置商品编码-同步自PDM系统
        goodsHead.setPdmGoodsCode(listing.getGoodsCode());
        // 生成亚马逊平台商品编码
        goodsHead.setSiteCode(siteCode);

        //实际库存 大于 店铺配置库存--->取店铺配置库存
        //实际库存 小于 店铺配置库存--->取实际库存
        goodsHead.setStockOnSalesQty(ObjectUtils.isNotEmpty(listing.getStockOnSalesQty()) ? listing.getStockOnSalesQty() : new BigDecimal(0));
        goodsHead.setStandardPrice(StrUtil.isBlank(listing.getStandardPrice())?"0":listing.getStandardPrice());
        goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(goodsHead.getStandardPrice())));
        goodsHead.setAdaptationStatus(AdaptationStatusEnum.WAIT.getStatus());

        GoodsRedPriceDTO goodsRedPriceDTO = pdmHttpRequestBiz.getRedLinePriceByGoodsCode(siteCode, listing.getGoodsCode());
        if (Objects.equals(PlatformTypeEnum.EB.name(),platform)) {
            goodsHead.setPlatformGoodsCode(listing.getGoodsCode());
            goodsInfoBiz.setStock(goodsHead, shopCode, PlatformTypeEnum.EB.name());
            //校验sku是否需要适配数据
//             goodsHead = listingInfoBiz.checkListingAdaptationBySku(goodsHead);
            String adaptationStatus = listingInfoBiz.checkListingAdaptationBySku(goodsHead.getPdmGoodsCode());
            if (ObjUtil.isNotEmpty(adaptationStatus)){
                goodsHead.setAdaptationStatus(adaptationStatus);
            }
            BigDecimal redLinePrice = BigDecimal.valueOf(0);
            if (ObjectUtils.isNotEmpty(goodsRedPriceDTO)) {
                redLinePrice = BigDecimal.valueOf( ObjUtil.isEmpty(goodsRedPriceDTO.getEbayRedLinePrice()) ? 0 : goodsRedPriceDTO.getEbayRedLinePrice() );
            }
            goodsHead.setRedLinePrice(redLinePrice);
        } else {
            String platformCode = createPlatformCode.getPlatformCode(listing.getGoodsCode(), siteCode);
            goodsHead.setPlatformGoodsCode(platformCode);
            goodsInfoBiz.setStock(goodsHead, shopCode, PlatformTypeEnum.AM.name());

            BigDecimal amazonPrice = commonInfoBiz.getAmazonPrice(publishType, goodsRedPriceDTO);
            goodsHead.setRedLinePrice(amazonPrice);
        }

        goodsHead.setPlatform(platform);
        goodsHead.setShopCode(shopCode);
        goodsHead.setPublishType(publishType);
        goodsHead.setCondition(dto.getCondition());
        goodsHead.setTitle(listing.getTitle());
        goodsHead.setPublishStatus(PublishStatus.DRAFT.getType());
        goodsHead.setBrandCode(dto.getBrandCode());
        //类目
        goodsHead.setCategoryId(dto.getCategoryId());
        //设置刊登状态
        goodsHead.setPublishStatus(0);
        goodsHead.setDelFlag(0);
        goodsHead.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
        return goodsHead;
    }
    /**
     * 组装商品描述数据
     */

    public ListingEbayLine getEbayLine(GoodsHead head,ListingDTO dto) {
        ListingEbayLine listingEbayLine = new ListingEbayLine();
        listingEbayLine.setLocation(dto.getLocation());
        listingEbayLine.setCountry(dto.getCountry());
        listingEbayLine.setCurrency(PlatformSiteEnum.getCurrency(PlatformTypeEnum.EB.name(),dto.getCountry()));
        listingEbayLine.setFirstCategoryid(ObjUtil.isEmpty(dto.getFirstCategoryid())?dto.getProductCategoryCode():dto.getFirstCategoryid());
        listingEbayLine.setSecondCategoryid(dto.getSecondCategoryid());
        listingEbayLine.setFirstShopCategory(dto.getFirstShopCategory());
        listingEbayLine.setSecondShopCategory(dto.getSecondShopCategory());
        listingEbayLine.setSellDay(ObjectUtils.isEmpty(dto.getPublicationDay())?"GTC":
                dto.getPublicationDay().contains(",")
                        ?dto.getPublicationDay().split(",")[0]:dto.getPublicationDay());
        listingEbayLine.setStartSellingCount(ObjectUtils.isEmpty(dto.getStartSellingCount()) ? "1" : String.valueOf(dto.getStartSellingCount()));
        listingEbayLine.setBestOfferFlag(ObjectUtils.isEmpty(dto.getBestOfferFlag()) ? "0" : String.valueOf(dto.getBestOfferFlag()));
        listingEbayLine.setCyclicAuctionFlag(ObjectUtils.isEmpty(dto.getCyclicAuctionFlag()) ? 0 : dto.getCyclicAuctionFlag());

        return listingEbayLine;
    }

    /**
     * 获取商品资源
     * @param dto
     * @param listing
     * @return
     */
    public List<GoodsResource> getResourceList(BatchListingDTO dto, ListingDTO listing) {
        List<GoodsResource> goodsResourceList = new ArrayList<>();
        List<String> imgArrs = listing.getImgArrs();
        if (CollectionUtils.isEmpty(imgArrs)) {
            return goodsResourceList;
        }
        for (int i = 0; i < imgArrs.size(); i++) {
            GoodsResource goodsResource = new GoodsResource();
            goodsResource.setResourceUrl(imgArrs.get(i));
            goodsResource.setResourceName(imgArrs.get(i).substring(imgArrs.get(i).lastIndexOf("/")+1));
            goodsResource.setResourceType("1");
            if (i==0){
                //主图
                goodsResource.setIsMain(1);
                goodsResource.setSortNumber(1);
                //给ebay主图加上水印
                if (!Objects.isNull(dto)){
                   // goodsInfoBiz.addWatermarking(goodsResource, dto.getPlatform(), dto.getShopCode(), listing.getProductCategoryCode());
                }
            }else {
                //副图
                goodsResource.setIsMain(0);
                goodsResource.setSortNumber(i-1);
            }
            goodsResourceList.add(goodsResource);
        }
        return goodsResourceList;
    }

    /**
     * 获取商品资源
     *
     * @param dto
     * @param listing
     * @return
     */
    public List<EbayListingGoodsResourceV2> getEbayResourceList(BatchListingDTO dto, ListingDTO listing) {
        List<EbayListingGoodsResourceV2> goodsResourceList = new ArrayList<>();
        List<String> imgArrs = listing.getImgArrs();
        if (CollectionUtils.isEmpty(imgArrs)) {
            return goodsResourceList;
        }
        for (int i = 0; i < imgArrs.size(); i++) {
            EbayListingGoodsResourceV2 goodsResource = new EbayListingGoodsResourceV2();
            goodsResource.setResourceUrl(imgArrs.get(i));
            goodsResource.setResourceName(imgArrs.get(i).substring(imgArrs.get(i).lastIndexOf("/") + 1));
            goodsResource.setResourceType("1");
            if (i == 0) {
                //主图
                goodsResource.setIsMain(1);
                goodsResource.setSortNumber(1);
                //给ebay主图加上水印
                if (!Objects.isNull(dto)) {
                    // goodsInfoBiz.addWatermarking(goodsResource, dto.getPlatform(), dto.getShopCode(), listing.getProductCategoryCode());
                }
            } else {
                //副图
                goodsResource.setIsMain(0);
                goodsResource.setSortNumber(i - 1);
            }
            goodsResourceList.add(goodsResource);
        }
        return goodsResourceList;
    }


    public GoodsDescription getGoodsDescription(ListingDTO listing) {
        GoodsDescription goodsDescription = new GoodsDescription();
        goodsDescription.setItemDescription1(listing.getItemDescription1());
        goodsDescription.setDescriptionId(listing.getDescriptionId());
        goodsDescription.setItemDescription2(listing.getItemDescription2());
        goodsDescription.setItemDescription3(listing.getItemDescription3());
        goodsDescription.setItemDescription4(listing.getItemDescription4());
        goodsDescription.setItemDescription5(listing.getItemDescription5());
        goodsDescription.setDetailDescription(listing.getDetailDescription());
        goodsDescription.setShortDescription(listing.getShortDescription());
        return goodsDescription;
    }

    public void getGoodsDescription(ListingDTO listing,GoodsDescription goodsDescription) {
        goodsDescription.setItemDescription1(listing.getItemDescription1());
        goodsDescription.setDescriptionId(listing.getDescriptionId());
        goodsDescription.setItemDescription2(listing.getItemDescription2());
        goodsDescription.setItemDescription3(listing.getItemDescription3());
        goodsDescription.setItemDescription4(listing.getItemDescription4());
        goodsDescription.setItemDescription5(listing.getItemDescription5());
        goodsDescription.setDetailDescription(listing.getDetailDescription());
        goodsDescription.setShortDescription(listing.getShortDescription());
    }

    public void getEbayGoodsDescription(ListingDTO listing, EbayGoodsDescriptionV2 goodsDescription) {
        goodsDescription.setDescriptionId(listing.getDescriptionId());
        goodsDescription.setDetailDescription(listing.getDetailDescription());
        goodsDescription.setShortDescription(listing.getShortDescription());
    }

    public GoodsSpecification getGoodsSpecification(ListingDTO listing,Long userId) {
        GoodsSpecification goodsSpecification = new GoodsSpecification();
        goodsSpecification.setItemLength( ObjUtil.isEmpty(listing.getItemLength()) ? BigDecimal.valueOf(0) : listing.getItemLength().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemWidth( ObjUtil.isEmpty(listing.getItemWidth()) ? BigDecimal.valueOf(0) :  listing.getItemWidth().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemHeight( ObjUtil.isEmpty(listing.getItemHeight()) ? BigDecimal.valueOf(0) :  listing.getItemHeight().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemLengthUnit( "cm" );
        goodsSpecification.setItemWidthUnit( "cm" );
        goodsSpecification.setItemHeightUnit( "cm");
        goodsSpecification.setPackageLength( ObjUtil.isEmpty(listing.getItemLength()) ? BigDecimal.valueOf(0) : listing.getItemLength().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setPackageWidth( ObjUtil.isEmpty(listing.getItemWidth()) ? BigDecimal.valueOf(0) :  listing.getItemWidth().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setPackageHeight( ObjUtil.isEmpty(listing.getItemHeight()) ? BigDecimal.valueOf(0) :  listing.getItemHeight().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setPackageLengthUnit( "cm" );
        goodsSpecification.setPackageWidthUnit( "cm" );
        goodsSpecification.setPackageHeightUnit( "cm" );
        goodsSpecification.setPackageWeight( ObjUtil.isEmpty(listing.getPackageWeight()) ? BigDecimal.valueOf(0) :listing.getPackageWeight().setScale(2, RoundingMode.HALF_UP));
        goodsSpecification.setPackageWeightUnit( "kg" );
        goodsSpecification.setNumberOfBoxes( 1 );
        goodsSpecification.setIsIrregularity("1");
        goodsSpecification.setCreateBy(String.valueOf(userId));
        goodsSpecification.setCreateTime( DateUtils.getNowDate());
        return goodsSpecification;
    }

    public GoodsSpecification getGoodsSpecificationNew(ListingDTO listing,Long userId) {
        GoodsSpecification goodsSpecification = new GoodsSpecification();
        goodsSpecification.setItemLength( ObjUtil.isEmpty(listing.getItemLength()) ? BigDecimal.valueOf(0) : listing.getItemLength().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemWidth( ObjUtil.isEmpty(listing.getItemWidth()) ? BigDecimal.valueOf(0) :  listing.getItemWidth().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemHeight( ObjUtil.isEmpty(listing.getItemHeight()) ? BigDecimal.valueOf(0) :  listing.getItemHeight().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemLengthUnit(StrUtil.isBlank(listing.getItemLengthUnit()) ? "cm" : listing.getItemLengthUnit());
        goodsSpecification.setItemWidthUnit(goodsSpecification.getItemLengthUnit());
        goodsSpecification.setItemHeightUnit(goodsSpecification.getItemLengthUnit());

        // 优先使用listing.getPackageLength()，如果为空，则使用listing.getItemLength()
        goodsSpecification.setPackageLength( ObjUtil.isEmpty(listing.getPackageLength()) ? goodsSpecification.getItemLength() : listing.getPackageLength().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setPackageWidth( ObjUtil.isEmpty(listing.getPackageWidth()) ? goodsSpecification.getItemWidth() :  listing.getPackageWidth().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setPackageHeight( ObjUtil.isEmpty(listing.getPackageHeight()) ? goodsSpecification.getItemHeight() :  listing.getPackageHeight().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setPackageLengthUnit(StrUtil.isBlank(listing.getPackageLengthUnit()) ? goodsSpecification.getItemLengthUnit() : listing.getPackageLengthUnit());
        goodsSpecification.setPackageWidthUnit(goodsSpecification.getPackageLengthUnit());
        goodsSpecification.setPackageHeightUnit(goodsSpecification.getPackageLengthUnit());

        goodsSpecification.setPackageWeight( ObjUtil.isEmpty(listing.getPackageWeight()) ? goodsSpecification.getPackageWeight() :listing.getPackageWeight().setScale(2, RoundingMode.HALF_UP));
        goodsSpecification.setPackageWeightUnit(StrUtil.isBlank(listing.getPackageWeightUnit()) ? "kg" : listing.getPackageWeightUnit());
        goodsSpecification.setNumberOfBoxes( 1 );
        goodsSpecification.setIsIrregularity("1");
        goodsSpecification.setCreateBy(String.valueOf(userId));
        goodsSpecification.setCreateTime( DateUtils.getNowDate());
        return goodsSpecification;
    }
    public GoodsSpecification getGoodsSpecification(ListingDTO listing,GoodsSpecification goodsSpecification) {
        goodsSpecification.setItemLength( ObjUtil.isEmpty(listing.getItemLength()) ? BigDecimal.valueOf(0) : listing.getItemLength().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemWidth( ObjUtil.isEmpty(listing.getItemWidth()) ? BigDecimal.valueOf(0) :  listing.getItemWidth().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setItemHeight( ObjUtil.isEmpty(listing.getItemHeight()) ? BigDecimal.valueOf(0) :  listing.getItemHeight().setScale(2, RoundingMode.HALF_UP) );
        goodsSpecification.setPackageWeight( ObjUtil.isEmpty(listing.getPackageWeight()) ? BigDecimal.valueOf(0) :listing.getPackageWeight().setScale(2, RoundingMode.HALF_UP));
        return goodsSpecification;
    }

    public AmazonSummariesDTO getSummaries(JSONObject itemInfo) {
        JSONArray summaries = itemInfo.getJSONArray("summaries");
        if (CollectionUtil.isEmpty(summaries)){
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(summaries.get(0)), AmazonSummariesDTO.class);
    }

    protected String getProductType(JSONObject itemInfo) {
        JSONArray productType = itemInfo.getJSONArray("productTypes");
        if (CollectionUtil.isEmpty(productType)){
            return null;
        }
        HashMap hashMap = JSON.parseObject(JSON.toJSONString(productType.get(0)), HashMap.class);
        return (String) hashMap.get("productType");
    }

    public JSONObject getAttributes(JSONObject itemInfo) {
        return itemInfo.getJSONObject("attributes");
    }

    protected boolean hasVariation(JSONObject itemInfo) {
        return CollectionUtil.isNotEmpty(itemInfo.getJSONArray("variations"));
    }

    protected AmazonAttributeDTO getAttributeInfo(JSONObject attributes, String key) {
        JSONArray jsonArray = attributes.getJSONArray(key);
        if (CollectionUtil.isEmpty(jsonArray)){
            return null;
        }
        AmazonAttributeDTO attributeDTO = JSON.parseObject(JSON.toJSONString(jsonArray.get(0)), AmazonAttributeDTO.class);
        if (Objects.isNull(attributeDTO)){
            return null;
        }
        return attributeDTO;
    }





    /**
     * Amazon 调用AmazonAPI获取店铺下的所有商品 (主要包含库存、价格等)
     * @param shopCode
     * @return
     */
    public List<AmazonAllListingsReportDTO> getAmazonAllListingsReportDTOS(String shopCode) {
        Map<String, String> map = new HashMap<>();
        map.put("sellerCode", shopCode);
        String result = HttpUtils.post(getAllAsinUrl, JSON.toJSONString(map));
        AjaxResult allAsinResult = JSONObject.parseObject(result, AjaxResult.class);
        result = null;
        if( !allAsinResult.isSuccess() ){
            log.error("亚马逊定时处理商品同步--通过sellerCode获取所有asin失败,店铺为:{},异常信息为:{}", shopCode,allAsinResult.get(AjaxResult.MSG_TAG));
            return null;
        }

        List<AmazonAllListingsReportDTO> allListings = JSONObject.parseArray(JSON.toJSONString(allAsinResult.get(AjaxResult.DATA_TAG)), AmazonAllListingsReportDTO.class);
        if (CollectionUtils.isEmpty(allListings)){
            return null;
        }
        return allListings;
    }

    /**
     * Amazon 调用AmazonAPI获取店铺下的所有商品 (主要包含库存、价格等)
     * V2版本
     * 1. 分页获取
     * 2. 一个月内更新的数据
     * @param shopCode
     * @return
     */
    public List<AmazonAllListingsReportDTO> getAmazonAllListingsReportDTOSV2(String shopCode,List<String> platformGoodsCode) {
        int pageNum = 1;
        int pageSize = 2000;

        Map<String, Object> map = new HashMap<>();
        map.put("sellerCode", shopCode);
        map.put("pageNum", String.valueOf(pageNum));
        map.put("pageSize", String.valueOf(pageSize));
        if (CollUtil.isNotEmpty(platformGoodsCode)){
            map.put("platformGoodsCodes", platformGoodsCode);
        }else {
            Date beginTime = DateUtils.addDays(new Date(), -30);
            map.put("beginTime", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, beginTime));
        }

        String result = HttpUtils.post(getAllAsinV2Url, JSON.toJSONString(map));
        AjaxResult allAsinResult = JSONObject.parseObject(result, AjaxResult.class);
        result = null;
        if(!allAsinResult.isSuccess() ){
            log.error("亚马逊定时处理商品同步--通过sellerCode获取所有asin失败,店铺为:{},异常信息为:{}", shopCode,allAsinResult.get(AjaxResult.MSG_TAG));
            return null;
        }
        Object data = allAsinResult.get(AjaxResult.DATA_TAG);
        if (Objects.isNull(data)){
            return null;
        }

        JSONObject jsonObject = (JSONObject) data;
        Integer pages = jsonObject.getInteger("pages");
        Integer total = jsonObject.getInteger("total");
        JSONArray list = jsonObject.getJSONArray("list");
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        List<AmazonAllListingsReportDTO> allList = Lists.newArrayListWithCapacity(total);

        List<AmazonAllListingsReportDTO> subListingList = JSONUtil.toBean(list.toJSONString(), new TypeReference<List<AmazonAllListingsReportDTO>>() {}, false);
        allList.addAll(subListingList);
        subListingList = null;
        list = null;

        for (int i = 2; i <= pages; i++) {
            pageNum = i;
            map.put("pageNum", String.valueOf(pageNum));
            subListingList = doGetListByShopCode(shopCode, map);
            if (CollectionUtils.isNotEmpty(subListingList)){
                allList.addAll(subListingList);
            }
            subListingList = null;
        }
        return allList;
    }

    /**
     * Amazon 调用AmazonAPI获取店铺下的所有商品 (支持分页)
     * @param shopCode 店铺编码
     * @param pageNum 页码 (从1开始)
     * @param pageSize 每页大小
     * @return
     */
    public List<AmazonAllListingsReportDTO> getAmazonAllListingsReportDTOSV2WithPagination(String shopCode,Integer pageNum, Integer pageSize) {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 1000; // 默认分页大小
        }

        Map<String, Object> map = new HashMap<>();
        map.put("sellerCode", shopCode);
        map.put("pageNum", String.valueOf(pageNum));
        map.put("pageSize", String.valueOf(pageSize));
        return doGetListByShopCode(shopCode, map);
    }

    private List<AmazonAllListingsReportDTO> doGetListByShopCode(String shopCode, Map<String, Object> map) {
        String result = HttpUtils.post(getAllAsinV2Url, JSON.toJSONString(map));
        AjaxResult allAsinResult = JSONObject.parseObject(result, AjaxResult.class);
        if(!allAsinResult.isSuccess() ){
            log.error("亚马逊定时处理商品同步--通过sellerCode获取所有asin失败,店铺为:{},异常信息为:{}", shopCode,allAsinResult.get(AjaxResult.MSG_TAG));
            return null;
        }
        Object data = allAsinResult.get(AjaxResult.DATA_TAG);
        if (Objects.isNull(data)){
            return null;
        }
        JSONArray list = ((JSONObject) data).getJSONArray("list");
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return JSONUtil.toBean(list.toJSONString(), new TypeReference<List<AmazonAllListingsReportDTO>>() {}, false);
    }

    /**
     * 设置基础信息 goodsHead->listingEditDTO
     * @param listingEditDTO
     * @param goodsHead
     */
    public void setBasicToEditDTO( ListingEditDTO listingEditDTO, GoodsHead goodsHead) {
        Integer goodsId = goodsHead.getId();
        BeanUtil.copyProperties(goodsHead, listingEditDTO);
        listingEditDTO.setGoodsCode(goodsHead.getPdmGoodsCode());
        listingEditDTO.setGoodsHeadId(goodsId);
        listingEditDTO.setStockOnSalesQty(BigDecimal.valueOf(listingEditDTO.getStockOnSalesQty().intValue()));
        listingEditDTO.setStandardPrice(StrUtil.isBlank(listingEditDTO.getStandardPrice())?"0":listingEditDTO.getStandardPrice());
        listingEditDTO.setSettlementPrice(Convert.toStr(goodsHead.getSettlementPrice()));
        listingEditDTO.setRedLinePrice(Convert.toStr(goodsHead.getRedLinePrice()));
        listingEditDTO.setPlatform(goodsHead.getPlatform());
        listingEditDTO.setTaskName(goodsHead.getTaskName());
    }

    /**
     * 设置基础信息 goodsHead->listingEditDTO
     *
     * @param listingEditDTO
     * @param goodsHead
     */
    public void setBasicToEditDTO(ListingEditDTO listingEditDTO, EbayGoodsHeadV2 goodsHead) {
        Long goodsId = goodsHead.getId();
        BeanUtil.copyProperties(goodsHead, listingEditDTO);
        listingEditDTO.setGoodsCode(goodsHead.getPdmGoodsCode());
        listingEditDTO.setGoodsHeadId(Math.toIntExact(goodsId));
        listingEditDTO.setPlatform(goodsHead.getPlatform());
        listingEditDTO.setTaskName(goodsHead.getTaskName());
    }
    /**
     * 获取图片信息 goodsResourceList-->goodsImages
     * @param goodsResourceList
     * @return
     */
    public List<GoodsImage> getGoodsImages(List<GoodsResource> goodsResourceList) {
        List<GoodsImage> goodsImages=new ArrayList<>();
        if ( CollectionUtils.isNotEmpty(goodsResourceList) ) {
            Map<Integer, List<GoodsResource>> resourceMap = goodsResourceList.stream().filter(f->ObjUtil.isNotEmpty(f.getIsMain())).collect(Collectors.groupingBy(GoodsResource::getIsMain));

            List<GoodsResource> result = new ArrayList<>();

            List<GoodsResource> resourceListMain = resourceMap.get(1);
            if (CollectionUtils.isNotEmpty(resourceListMain)) {
                result.addAll(resourceListMain);
            }

            List<GoodsResource> resourceList = resourceMap.get(0);
            if (CollectionUtils.isNotEmpty(resourceList)) {
                List<GoodsResource> resourceListSort = new ArrayList<>();
                if (resourceList.get(0).getSortNumber() == null) {
                    resourceListSort  = resourceList.stream().sorted(Comparator.comparing(GoodsResource::getId)).collect(Collectors.toList());
                } else {
                    resourceListSort = resourceList.stream().sorted(Comparator.comparing(GoodsResource::getSortNumber)).collect(Collectors.toList());
                }
                result.addAll(resourceListSort);
            }

            if (CollectionUtils.isEmpty(result)){
                return goodsImages;
            }
            result.forEach(resource -> {
                GoodsImage goodsImage = new GoodsImage();
                goodsImage.setType(resource.getResourceType());
                goodsImage.setImageUrl(resource.getResourceUrl());
                goodsImages.add(goodsImage);
            });
        }
        return goodsImages;
    }

    public List<GoodsImage> getEbayGoodsImages(List<EbayListingGoodsResourceV2> goodsResourceList) {
        List<GoodsImage> goodsImages = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsResourceList)) {
            Map<Integer, List<EbayListingGoodsResourceV2>> resourceMap = goodsResourceList.stream()
                    .filter(f -> ObjUtil.isNotEmpty(f.getIsMain())).collect(Collectors.groupingBy(EbayListingGoodsResourceV2::getIsMain));

            List<EbayListingGoodsResourceV2> result = new ArrayList<>();

            List<EbayListingGoodsResourceV2> resourceListMain = resourceMap.get(1);
            if (CollectionUtils.isNotEmpty(resourceListMain)) {
                result.addAll(resourceListMain);
            }

            List<EbayListingGoodsResourceV2> resourceList = resourceMap.get(0);
            if (CollectionUtils.isNotEmpty(resourceList)) {
                List<EbayListingGoodsResourceV2> resourceListSort = new ArrayList<>();
                if (resourceList.get(0).getSortNumber() == null) {
                    resourceListSort = resourceList.stream().sorted(Comparator.comparing(EbayListingGoodsResourceV2::getId)).collect(Collectors.toList());
                } else {
                    resourceListSort = resourceList.stream().sorted(Comparator.comparing(EbayListingGoodsResourceV2::getSortNumber)).collect(Collectors.toList());
                }
                result.addAll(resourceListSort);
            }

            if (CollectionUtils.isEmpty(result)) {
                return goodsImages;
            }
            result.forEach(resource -> {
                GoodsImage goodsImage = new GoodsImage();
                goodsImage.setType(resource.getResourceType());
                goodsImage.setImageUrl(resource.getResourceUrl());
                goodsImages.add(goodsImage);
            });
        }
        return goodsImages;
    }


    /**
     * 设置商品描述信息  goodsDescription-->listingEditDTO
     * @param listingEditDTO
     * @param goodsDescription
     */
    protected void setDescriptionToEditDTO(ListingEditDTO listingEditDTO, GoodsDescription goodsDescription) {
        listingEditDTO.setShortDescription(goodsDescription.getShortDescription());
        listingEditDTO.setDetailDescription(goodsDescription.getDetailDescription());
        listingEditDTO.setItemDescription1(goodsDescription.getItemDescription1());
        listingEditDTO.setItemDescription2(goodsDescription.getItemDescription2());
        listingEditDTO.setItemDescription3(goodsDescription.getItemDescription3());
        listingEditDTO.setItemDescription4(goodsDescription.getItemDescription4());
        listingEditDTO.setItemDescription5(goodsDescription.getItemDescription5());
        listingEditDTO.setGoodsDescriptionId(goodsDescription.getId());
        listingEditDTO.setEbayDescriptionId(goodsDescription.getDescriptionId());
    }

    /**
     * 设置商品描述信息  goodsDescription-->listingEditDTO
     *
     * @param listingEditDTO
     * @param goodsDescription
     */
    protected void setDescriptionToEditDTO(ListingEditDTO listingEditDTO, EbayGoodsDescriptionV2 goodsDescription) {
        listingEditDTO.setShortDescription(goodsDescription.getShortDescription());
        listingEditDTO.setDetailDescription(goodsDescription.getDetailDescription());
        listingEditDTO.setGoodsDescriptionId(goodsDescription.getId());
    }


    /**
     * 设置商品规格信息  goodsSpecification-->listingEditDTO
     * @param goodsSpecification
     * @param listingEditDTO
     */
    protected void setSpecificationToEditDTO(GoodsSpecification goodsSpecification, ListingEditDTO listingEditDTO) {
        if(goodsSpecification != null){
            listingEditDTO.setGoodsSpecificationId(goodsSpecification.getId());
            if(goodsSpecification.getItemLength() != null){
                goodsSpecification.setItemLength(goodsSpecification.getItemLength().setScale(2, RoundingMode.HALF_UP));
            }
            if(goodsSpecification.getItemWidth() != null){
                goodsSpecification.setItemWidth(goodsSpecification.getItemWidth().setScale(2, RoundingMode.HALF_UP));
            }
            if(goodsSpecification.getItemHeight() != null){
                goodsSpecification.setItemHeight(goodsSpecification.getItemHeight().setScale(2, RoundingMode.HALF_UP));
            }
            if(goodsSpecification.getPackageLength() != null){
                goodsSpecification.setPackageLength(goodsSpecification.getPackageLength().setScale(2, RoundingMode.HALF_UP));
            }
            if(goodsSpecification.getPackageWidth() != null){
                goodsSpecification.setPackageWidth(goodsSpecification.getPackageWidth().setScale(2, RoundingMode.HALF_UP));
            }
            if(goodsSpecification.getPackageHeight() != null){
                goodsSpecification.setPackageHeight(goodsSpecification.getPackageHeight().setScale(2, RoundingMode.HALF_UP));
            }
            if(goodsSpecification.getPackageWeight() != null){
                goodsSpecification.setPackageWeight(goodsSpecification.getPackageWeight().setScale(2, RoundingMode.HALF_UP));
            }
            if(goodsSpecification.getPackageTypeLbs() != null){
                goodsSpecification.setPackageTypeLbs(goodsSpecification.getPackageTypeLbs().setScale(2, RoundingMode.HALF_UP));
            }
            if(goodsSpecification.getPackageTypeOz() != null){
                goodsSpecification.setPackageTypeOz(goodsSpecification.getPackageTypeOz().setScale(2, RoundingMode.HALF_UP));
            }
            BeanUtil.copyProperties(goodsSpecification, listingEditDTO);
            //ebay页面中直接塞对象
            listingEditDTO.setGoodsSpecification(goodsSpecification);

            listingEditDTO.setIsIrregularity(dictService.getLabel("sys_yes_no", goodsSpecification.getIsIrregularity()));
        }
    }

    protected void saveLog(String shopCode, String asin,String detail,String remark) {
        ListingLogPullRecord listingLogPullRecord = new ListingLogPullRecord();
        listingLogPullRecord.setPlatformCode(PlatformTypeEnum.AM.name());
        listingLogPullRecord.setShopCode(shopCode);
        listingLogPullRecord.setPlatformGoodsId(asin);
        listingLogPullRecord.setPullDetail(detail);
        listingLogPullRecord.setRemark(remark);
        listingLogPullRecordMapper.insertListingLogPullRecord(listingLogPullRecord);
    }

    protected void savePullLogAndTodoLog(String shopCode, String asin, String remark, ErrorDefine error, String detail) {
        if (StrUtil.isBlank(remark)){
            return;
        }
        ListingLogPullRecord listingLogPullRecord = new ListingLogPullRecord();
        listingLogPullRecord.setPlatformCode(PlatformTypeEnum.AM.name());
        listingLogPullRecord.setShopCode(shopCode);
        listingLogPullRecord.setPlatformGoodsId(asin);
        listingLogPullRecord.setPullDetail(detail);
        listingLogPullRecord.setRemark(remark);
        listingLogPullRecord.setErrorCode(error.getCode());
        listingLogPullRecord.setShortMsg(error.getMessage());
        listingLogPullRecordMapper.insertListingLogPullRecord(listingLogPullRecord);

        // 是否已处理或者已完成
//        int todoCount = syncFailTodoService.countBy(shopCode, asin, PlatformTypeEnum.AM.name());
//        if (todoCount > 0) {
//            return;
//        }
    }

    protected void saveFailTodo(String shopCode, String asin, String remark, ErrorDefine error, String detail, MappingGoods mappingGoods,String platformSku) {
        SyncFailTodo syncFailTodo = new SyncFailTodo();
        syncFailTodo.setShopCode(shopCode);
        syncFailTodo.setPlatformGoodsId(asin);
        syncFailTodo.setOriginFailDesc(detail);
        syncFailTodo.setBatch(ObjUtil.isEmpty(remark) ? shopCode+"_"+System.currentTimeMillis() :remark);
        syncFailTodo.setErrorCode(error.getCode());
        syncFailTodo.setPlatformCode(PlatformTypeEnum.AM.name());
        syncFailTodo.setStatus("0");
        syncFailTodo.setErrorDesc(detail);
        syncFailTodo.setCreateBy("-1");
        if (mappingGoods != null) {
            syncFailTodo.setOperator(mappingGoods.getCreateBy());
            syncFailTodo.setSku(mappingGoods.getGoodsCode());
        }
        syncFailTodo.setPlatformSku(platformSku);
        syncFailTodoService.saveOrUpdate(syncFailTodo);
    }

    protected void saveEBLog(String shopCode, String asin,String detail,String remark) {
        ListingLogPullRecord listingLogPullRecord = new ListingLogPullRecord();
        listingLogPullRecord.setPlatformCode(PlatformTypeEnum.EB.name());
        listingLogPullRecord.setShopCode(shopCode);
        listingLogPullRecord.setPlatformGoodsId(asin);
        listingLogPullRecord.setPullDetail(detail);
        listingLogPullRecord.setRemark(remark);
        listingLogPullRecordMapper.insertListingLogPullRecord(listingLogPullRecord);
    }

    public void assembleWord(Map<String, Set<String>> violateWord, String message) {
        if (StrUtil.isBlank(message)){
            return;
        }
        try {
            if (message.contains("中包含违禁词：")){
                for (int i = 0; i < message.split(";").length; i++) {
                    String[] split = message.split(";");
                    String modelName = split[i].split("中包含违禁词：")[0];
                    String words = split[i].split("中包含违禁词：")[1];

                    if (violateWord.containsKey(modelName)){
                        Set<String> set = violateWord.get(modelName);
                        for (String word : words.split(",")) {
                            set.add(word);
                        }
                    }else {
                        Set<String> set = new TreeSet<>();
                        for (String word : words.split(",")) {
                            set.add(word);
                        }
                        violateWord.put(modelName,set);
                    }
                }
            }
            String key="检测到标题重复";
            if (message.contains(key)){
                if (violateWord.containsKey(key)){
                    Set<String> set = violateWord.get(key);
                    set.add(message);
                }else {
                    Set<String> set = new TreeSet<>();
                    set.add(message);
                    violateWord.put(key,set);
                }
            }
            if (message.contains("包含汽车品牌")){
                if (violateWord.containsKey("包含汽车品牌")){
                    Set<String> set = violateWord.get("包含汽车品牌");
                    set.add(message);
                }else {
                    Set<String> set = new TreeSet<>();
                    set.add(message);
                    violateWord.put("包含汽车品牌",set);
                }
            }
            if (message.contains("包含多个品牌")){
                if (violateWord.containsKey("包含多个品牌")){
                    Set<String> set = violateWord.get("包含多个品牌");
                    set.add(message);
                }else {
                    Set<String> set = new TreeSet<>();
                    set.add(message);
                    violateWord.put("包含多个品牌",set);
                }
            }
        } catch (Exception e) {
            log.error("批量组装Listing检测失败",e);
        }
    }

    public void assembleWordBefore(Map<String, Set<String>> violateWord) {
        StringBuilder sb = new StringBuilder();
        if (ObjUtil.isEmpty(violateWord)){
            return;
        }
        violateWord.forEach((k, v) -> {
            if ( ObjUtil.equals(k,"检测到标题重复") ){
                sb.append(v).append(";");
                return;
            }
            if ( ObjUtil.equals(k,"包含汽车品牌") ){
                sb.append(v);
                return;
            }
            if ( ObjUtil.equals(k,"包含多个品牌") ){
                sb.append(v);
                return;
            }
            sb.append(k).append("中包含违禁词：").append(v).append(";");
        });
        throw new BusinessException(sb.toString());
    }

    public void assembleWordBefore(Map<String, Set<String>> violateWord, List<String> checkPrice) {
        StringBuilder sb = new StringBuilder();

        if (ObjUtil.isNotEmpty(violateWord)){
            violateWord.forEach((k, v) -> {
                if ( ObjUtil.equals(k,"检测到标题重复") ){
                    sb.append(v).append(";");
                    return;
                }
                sb.append(k).append("中包含违禁词：").append(v).append(";");
            });
        }
        if (ObjUtil.isNotEmpty(checkPrice)){
            checkPrice.forEach(s -> {
                sb.append(s);
            });
        }
        if (sb.length()>0){
            throw new BusinessException(sb.toString());
        }
    }


    /**
     * 更新待办状态-完成
     * @param goodsHead
     * @param operationTypeName
     * @param status
     */
//    public void updateTodoDataStatus(GoodsHead goodsHead, String operationTypeName,String status) {
//
//        //红线价待办状态修改
//        if (operationTypeName.equals(OperationTypeEnum.UPDATE.getName())){
//            todoDataService.updateTodoDataStatus("1",goodsHead.getCreateBy(),goodsHead.getPdmGoodsCode(),
//                    String.valueOf(goodsHead.getId()),status);
//            return;
//        }
//        //新增库存到货待办状态修改
//        if (operationTypeName.equals(OperationTypeEnum.PUBLISH.getName())){
//            todoDataService.updateTodoDataStatus("2",goodsHead.getCreateBy(),goodsHead.getPdmGoodsCode(),
//                    null,status);
//        }
//
//    }

    /**
     * 更新ads自动刊登平台销售编码回写
     * @param head
     */
    public void updateAdsPlatformSaleCode(GoodsHead head) {
        if (ObjUtil.isEmpty(head.getPlatformGoodsId())|| !Objects.equals(head.getSmcFlag(),2)){
            return;
        }
        goodsOriginService.updateHeadsGoodsOrigin(head.getPlatformGoodsId(),head.getPdmGoodsCode(),head.getShopCode(),head.getPlatform());
    }



    /**
     * 初始化更新记录
     * @param goodsHead
     * @param updateMoudleList
     */
    public void intoUpdateRecord(GoodsHead goodsHead, List<String> updateMoudleList,Integer type) {
        if (CollUtil.isEmpty(updateMoudleList)){
            return;
        }
        UpdateRecord updateRecord = new UpdateRecord();
        updateRecord.setPlatform(goodsHead.getPlatform());
        updateRecord.setSite(goodsHead.getSiteCode());
        updateRecord.setShopCode(goodsHead.getShopCode());
        updateRecord.setListingId(goodsHead.getId());
        updateRecord.setModule(JSON.toJSONString(updateMoudleList));
        updateRecord.setStatus(SMCCommonEnum.PROCESSING_COMPLETE.getValue());
        updateRecord.setType(type);
        updateRecord.setDelFlag(0);
        updateRecord.setCreateTime(DateUtils.getNowDate());
        updateRecord.setUpdateTime(DateUtils.getNowDate());
        updateRecordService.insertUpdateRecord(updateRecord);
    }

}