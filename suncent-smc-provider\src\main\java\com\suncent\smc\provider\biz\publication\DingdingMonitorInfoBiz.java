package com.suncent.smc.provider.biz.publication;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.suncent.dingtalk.robot.model.MonitorServerEnum;
import com.suncent.dingtalk.robot.model.MonitorTemplate;
import com.suncent.dingtalk.robot.utils.DingdingMessageUtil;
import com.suncent.smc.common.config.ExceptionNotificationConfig;
import com.suncent.smc.common.config.RuoYiConfig;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.enums.MonitorEnum;
import com.suncent.smc.common.enums.PublishType;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.EnvUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.file.FileUtils;
import com.suncent.smc.common.utils.spring.SpringUtils;
import com.suncent.smc.oss.IAliOssService;
import com.suncent.smc.persistence.bi.service.IOdsCrlCrlVcCatalogDataService;
import com.suncent.smc.persistence.common.CommonUtilsSmc;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreInfoService;
import com.suncent.smc.persistence.ebay.domain.EbayGoodsHeadV2;
import com.suncent.smc.persistence.ebay.domain.EbayListingSpecsItemV2;
import com.suncent.smc.persistence.ebay.service.IEbayListingSpecsItemV2Service;
import com.suncent.smc.persistence.pdm.domain.entity.MappingGoods;
import com.suncent.smc.persistence.publication.domain.dto.ItemBackUpDTO;
import com.suncent.smc.persistence.publication.domain.dto.ListingDTO;
import com.suncent.smc.persistence.publication.domain.entity.AmazonListingPullLack;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHeadBackup;
import com.suncent.smc.persistence.publication.domain.entity.MonitorMessage;
import com.suncent.smc.persistence.publication.domain.vo.ShopSyncStats;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO;
import com.suncent.smc.persistence.publication.service.IGoodsHeadBackupService;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.persistence.publication.service.IMonitorMessageService;
import com.suncent.smc.persistence.todo.domain.dto.MatchTodoDataDTO;
import com.suncent.smc.persistence.todo.domain.dto.MatchTodoDataDetailDTO;
import com.suncent.smc.persistence.publication.domain.entity.VcLinkErrorDataVO;
import com.suncent.smc.persistence.todo.domain.entity.AbnormalDingding;
import com.suncent.smc.persistence.todo.domain.entity.LoseCartTodo;
import com.suncent.smc.persistence.todo.service.ILoseCartTodoService;
import com.suncent.smc.provider.biz.ebay.EbayListingInfoBiz;
import com.suncent.smc.provider.dingding.domain.ActionCardMsgDto;
import com.suncent.smc.provider.dingding.domain.DingDingDetailVO;
import com.suncent.smc.provider.dingding.domain.TODOTaskCardMsgDto;
import com.suncent.smc.provider.dingding.service.IDingAsyncSendService;
import com.suncent.smc.provider.todo.HandlerTodoComposite;
import com.suncent.smc.provider.todo.TodoResolver;
import com.suncent.smc.provider.todo.domain.TodoEnum;
import com.suncent.smc.provider.todo.resolver.LostCartTodoResolver;
import com.suncent.smc.system.service.ISysConfigService;
import com.suncent.smc.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.DateUtils;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.CHINESE_DATE_FORMAT;
import static com.suncent.smc.common.enums.MonitorEnum.AM_QUICK_UPDATE;


@Slf4j
@Component
public class DingdingMonitorInfoBiz {
    @Autowired
    ILoseCartTodoService loseCartTodoService;
    @Autowired
    public RedisService redisService;
    @Autowired
    protected ISysUserService userService;
    @Autowired
    protected IGoodsHeadBackupService goodsHeadBackupService;
    @Autowired
    protected IMonitorMessageService monitorMessageService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private ExceptionNotificationConfig exceptionNotificationConfig;
    @Autowired
    private IDingAsyncSendService dingAsyncSendService;
    @Resource
    protected IConfigStoreInfoService configStoreInfoService;
    @Resource
    protected DingdingMessageUtil dingdingMessageUtil;
    @Autowired
    private IOdsCrlCrlVcCatalogDataService odsCrlCrlVcCatalogDataService;
    @Autowired
    private PDMHttpRequestBiz pdmHttpRequestBiz;
    @Autowired
    @Lazy
    private HandlerTodoComposite handlerTodoComposite;
    @Autowired
    public IAliOssService aliOssService;
    @Value("${aliyun.oss.defaultBucketName}")
    public String bucketName;
    @Value("${aliyun.oss.suncentUrlPrefix}")
    public String aliOssAddress;
    private List<Integer> allCustomer;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    IEbayListingSpecsItemV2Service ebayListingSpecsItemV2Service;
    @Autowired
    @Lazy
    private AmazonProductBiz amazonProductBiz;
    @Autowired
    @Lazy
    private ListingInfoBiz listingInfoBiz;
    @Autowired
    private IGoodsHeadService goodsHeadService;

    /**
     * AM删除、AM更新异常监控
     * 发送钉钉消息
     * @param goodsHeads
     * @param monitorEnum
     * @return
     * true 可以执行下一步操作
     * false 不可以执行下一步操作
     */
    public Boolean monitorAMListingAndSend(List<GoodsHead> goodsHeads, MonitorEnum monitorEnum) {
        //如果商品数量小于监控数量，不进行监控
        if ( goodsHeads.size() < monitorEnum.getMonitorNum() ){
            return null;
        }

        //根据监控类型，获取该批次商品的有问题的数量
        int errorNum = getErrorNum(goodsHeads,monitorEnum);

        //如果问题商品数量小于监控数量，不进行监控
        if ( errorNum < monitorEnum.getMonitorNum() ){
            return null;
        }

        GoodsHead goodsHead = goodsHeads.get(0);

        String loginName = null;
        try {
            String updateBy = StringUtils.isBlank(goodsHead.getUpdateBy()) || Objects.equals(goodsHead.getUpdateBy(),"-1") ? goodsHead.getCreateBy() : goodsHead.getUpdateBy();
            SysUser sysUser = userService.selectUserById(Long.valueOf(updateBy));
            loginName = sysUser.getUserName();
        } catch (Exception e) {
            log.error(monitorEnum.getMonitorName()+"查询对应用户异常",e);
        }
        
        //年月日
        String key = monitorEnum.getKeyPrefix() + goodsHead.getShopCode()+"_"+ DateUtils.formatDate(new Date(), "yyyyMMdd");
        // 快捷更新首先不做拦截，刚开始只有提醒，如果发现异常需要管理员介入手动拦截
        if (Objects.equals(monitorEnum, AM_QUICK_UPDATE)) {
            if (StrUtil.isBlank(goodsHead.getUpdateBy())) {
                return null;
            }
            key = key + "_" + goodsHead.getUpdateBy();

            //如果缓存中有值，说明已经发送过钉钉了
            if (redisService.exists(key)) {
                String cacheResult = redisService.getCacheObject(key);
                //如果是false，说明管理员拦截，不进行下一步操作
                if (Objects.equals(cacheResult, "false") ){
                    return false;
                }else {
                    return null;
                }
            } else {
                sendDingTalk(monitorEnum, errorNum, loginName, goodsHead, key);
                redisService.setCacheObject(key, "true", 1L, TimeUnit.DAYS);
            }
            return null;
        }
        //如果缓存中有值，说明已经发送过钉钉了
        if (redisService.exists(key)){
            String cacheResult = redisService.getCacheObject(key);
            //如果是false，说明管理员还没有处理，不进行下一步操作
            //如果是true，说明管理员已经处理过了，将redis设置为system
            //如果是system，说明系统已经扫描过了，不需要再次扫描更新
            if ( Objects.equals(cacheResult, "false") ){
                return false;
            }else if( Objects.equals(cacheResult, "true") ){
                redisService.setCacheObject(key, "system", 1L, TimeUnit.DAYS);
                return true;
            }else {
                return null;
            }
        }

        sendDingTalk(monitorEnum, errorNum, loginName, goodsHead, key);

        //设置缓存，防止重复发送钉钉,设置一天过期
        redisService.setCacheObject(key, "false", 1L, TimeUnit.DAYS);

        return false;
    }

    private void sendDingTalk(MonitorEnum monitorEnum, int errorNum, String loginName, GoodsHead goodsHead, String key) {
        String prefix = "";
        if (Objects.equals(monitorEnum, AM_QUICK_UPDATE)) {
            prefix = "快捷更新";
        }
        //发送钉钉
        Map<String, String> featureMap = new HashMap<>();
        featureMap.put(prefix + "操作异常条数", errorNum +" 条");
        featureMap.put(prefix + "操作用户", loginName);
        featureMap.put("操作店铺", goodsHead.getShopCode());
        featureMap.put("redisKey", key);
        if (EnvUtils.isProdProfile()) {
            this.monitorSend(monitorEnum.getMonitorName(), monitorEnum.getMonitorType(), "AM-异常操作监控报警", featureMap);
        } else {
            log.info("AM-异常操作监控报警,{}", featureMap);
        }
    }

    /**
     * EB下架、EB更新异常监控
     * 发送钉钉消息
     * @param goodsHeads
     * @param monitorEnum
     * @return
     * true 可以执行下一步操作
     * false 不可以执行下一步操作
     */
    public boolean monitorEBListingAndSend(List<GoodsHead> goodsHeads, MonitorEnum monitorEnum) {
        //如果商品数量小于监控数量，不进行监控
        if ( goodsHeads.size() < monitorEnum.getMonitorNum() ){
            return true;
        }

        //根据监控类型，获取该批次商品的有问题的数量
        int errorNum = getErrorNum(goodsHeads,monitorEnum);

        //如果问题商品数量小于监控数量，不进行监控
        if ( errorNum < monitorEnum.getMonitorNum() ){
            return true;
        }

        GoodsHead goodsHead = goodsHeads.get(0);

        //年月日
        String key =  monitorEnum.getKeyPrefix() + goodsHead.getShopCode()+"_"+ DateUtils.formatDate(new Date(), "yyyyMMdd");
        //如果缓存中有值，说明已经发送过钉钉了
        if (redisService.exists(key)){
            String cacheResult = redisService.getCacheObject(key);
            //如果是false，说明管理员还没有处理，不进行下一步操作
            //如果是true，说明管理员已经处理过了，可以进行下一步操作
            return Objects.equals(cacheResult, "false") ? false : true;
        }

        String loginName = null;
        try {
            String updateBy = StringUtils.isBlank(goodsHead.getUpdateBy()) || Objects.equals(goodsHead.getUpdateBy(),"-1") ? goodsHead.getCreateBy() : goodsHead.getUpdateBy();
            SysUser sysUser = userService.selectUserById(Long.valueOf(updateBy));
            loginName = sysUser.getUserName();
        } catch (Exception e) {
            log.error(monitorEnum.getMonitorName()+"查询对应用户异常",e);
        }


        //发送钉钉
        Map<String, String> featureMap = new HashMap<>();
        featureMap.put("操作异常条数", errorNum+" 条");
        featureMap.put("操作店铺", goodsHead.getShopCode());
        featureMap.put("redisKey", key);
        if (!Objects.equals(monitorEnum,MonitorEnum.AM_INVENTORY_UPDATE_ZERO_FAIL)||!Objects.equals(monitorEnum,MonitorEnum.EB_INVENTORY_UPDATE_ZERO_FAIL)){
            featureMap.put("操作用户", loginName);
        }
        this.monitorSend(monitorEnum.getMonitorName(),monitorEnum.getMonitorType(),"EB-异常操作监控报警",featureMap);

        //设置缓存，防止重复发送钉钉,设置一天过期
        redisService.setCacheObject(key, "false", 1L, TimeUnit.DAYS);

        return false;
    }

    /**
     * EB下架、EB更新异常监控
     * 发送钉钉消息
     *
     * @param goodsHeads
     * @param monitorEnum
     * @return true 可以执行下一步操作
     * false 不可以执行下一步操作
     */
    public boolean monitorEBListingAndSendV2(List<EbayGoodsHeadV2> goodsHeads, MonitorEnum monitorEnum) {
        //如果商品数量小于监控数量，不进行监控
        if (goodsHeads.size() < monitorEnum.getMonitorNum()) {
            return true;
        }

        //根据监控类型，获取该批次商品的有问题的数量
        int errorNum = getEbayErrorNumV2(goodsHeads, monitorEnum);

        //如果问题商品数量小于监控数量，不进行监控
        if (errorNum < monitorEnum.getMonitorNum()) {
            return true;
        }

        EbayGoodsHeadV2 goodsHead = goodsHeads.get(0);

        //年月日
        String key = monitorEnum.getKeyPrefix() + goodsHead.getShopCode() + "_" + DateUtils.formatDate(new Date(), "yyyyMMdd");
        //如果缓存中有值，说明已经发送过钉钉了
        if (redisService.exists(key)) {
            String cacheResult = redisService.getCacheObject(key);
            //如果是false，说明管理员还没有处理，不进行下一步操作
            //如果是true，说明管理员已经处理过了，可以进行下一步操作
            return Objects.equals(cacheResult, "false") ? false : true;
        }

        String loginName = null;
        try {
            String updateBy = StringUtils.isBlank(goodsHead.getUpdateBy()) || Objects.equals(goodsHead.getUpdateBy(), "-1") ? goodsHead.getCreateBy() : goodsHead.getUpdateBy();
            SysUser sysUser = userService.selectUserById(Long.valueOf(updateBy));
            loginName = sysUser.getUserName();
        } catch (Exception e) {
            log.error(monitorEnum.getMonitorName() + "查询对应用户异常", e);
        }


        //发送钉钉
        Map<String, String> featureMap = new HashMap<>();
        featureMap.put("操作异常条数", errorNum + " 条");
        featureMap.put("操作店铺", goodsHead.getShopCode());
        featureMap.put("redisKey", key);
        if (!Objects.equals(monitorEnum, MonitorEnum.AM_INVENTORY_UPDATE_ZERO_FAIL) || !Objects.equals(monitorEnum, MonitorEnum.EB_INVENTORY_UPDATE_ZERO_FAIL)) {
            featureMap.put("操作用户", loginName);
        }
        this.monitorSend(monitorEnum.getMonitorName(), monitorEnum.getMonitorType(), "EB-异常操作监控报警", featureMap);

        //设置缓存，防止重复发送钉钉,设置一天过期
        redisService.setCacheObject(key, "false", 1L, TimeUnit.DAYS);

        return false;
    }

    private int getEbayErrorNumV2(List<EbayGoodsHeadV2> goodsHeads, MonitorEnum monitorEnum) {

        //如果是删除或者下架，不需要比较价格
        if (Objects.equals(monitorEnum, MonitorEnum.AM_DELET) || Objects.equals(monitorEnum, MonitorEnum.EB_OFFSALE) || Objects.equals(monitorEnum, MonitorEnum.AM_INVENTORY_UPDATE_ZERO_FAIL) || Objects.equals(monitorEnum, MonitorEnum.EB_INVENTORY_UPDATE_ZERO_FAIL)) {
            return goodsHeads.size();
        }

        AtomicInteger errorNum = new AtomicInteger();
        goodsHeads.forEach(goodsHead -> {
            //取出备份表中最近的一条数据
            GoodsHeadBackup goodsHeadBackup = goodsHeadBackupService.selectGoodsHeadBackupByGoodsId(String.valueOf(goodsHead.getId()));
            if (goodsHeadBackup == null) {
                //如果没有备份数据，发钉钉让管理员检查是否有问题
                errorNum.getAndIncrement();
                return;
            }
            ItemBackUpDTO itemBackUpDTO = JSONObject.parseObject(goodsHeadBackup.getContext(), ItemBackUpDTO.class);
            List<EbayListingSpecsItemV2> oldSpecList = itemBackUpDTO.getEbayListingSpecsItemV2List();

            List<EbayListingSpecsItemV2> newSpecList = ebayListingSpecsItemV2Service.selectEbayListingSpecsItemV2ListByHeadId(goodsHead.getId());
            if (ObjUtil.isEmpty(oldSpecList) && ObjUtil.isNotEmpty(newSpecList)) {
                errorNum.getAndIncrement();
            }
            if (ObjUtil.isNotEmpty(oldSpecList) && ObjUtil.isEmpty(newSpecList)) {
                errorNum.getAndIncrement();
            }
            //对比每个规格的价格
            for (EbayListingSpecsItemV2 nowSpec : newSpecList) {
                EbayListingSpecsItemV2 oldSpec = oldSpecList.stream().filter(old -> Objects.equals(old.getId(), nowSpec.getId())).findFirst().orElse(null);
                if (ObjUtil.isEmpty(oldSpec)) {
                    errorNum.getAndIncrement();
                    return;
                }
                String standardPriceOld = oldSpec.getPrice();
                String standardPrice = nowSpec.getPrice();
                //standardPriceOld 和 standardPrice 相差70%以上,说明价格有问题
                if (isDifferenceAbovePercent(standardPriceOld, standardPrice, 70)) {
                    errorNum.getAndIncrement();
                    return;
                }
            }

        });
        return errorNum.get();
    }


    private int getErrorNum(List<GoodsHead> goodsHeads,MonitorEnum monitorEnum) {

        //如果是删除或者下架，不需要比较价格
        if ( Objects.equals(monitorEnum, MonitorEnum.AM_DELET) || Objects.equals(monitorEnum, MonitorEnum.EB_OFFSALE)||Objects.equals(monitorEnum,MonitorEnum.AM_INVENTORY_UPDATE_ZERO_FAIL)||Objects.equals(monitorEnum,MonitorEnum.EB_INVENTORY_UPDATE_ZERO_FAIL)){
            return goodsHeads.size();
        }
        if (Objects.equals(monitorEnum, AM_QUICK_UPDATE)) {
            return quickUpdateErrorNum(goodsHeads);
        }

        int errorNum = 0;
        for (GoodsHead goodsHead : goodsHeads) {
            //取出备份表中最近的一条数据
            GoodsHeadBackup goodsHeadBackup = goodsHeadBackupService.selectGoodsHeadBackupByGoodsId(String.valueOf(goodsHead.getId()));
            if (goodsHeadBackup == null){
                //如果没有备份数据，发钉钉让管理员检查是否有问题
                errorNum++;
                continue;
            }
            ItemBackUpDTO itemBackUpDTO = JSONObject.parseObject(goodsHeadBackup.getContext(), ItemBackUpDTO.class);

            String standardPriceOld = itemBackUpDTO.getGoodsHead().getStandardPrice();
            String standardPrice = goodsHead.getStandardPrice();

            //standardPriceOld 和 standardPrice 相差70%以上,说明价格有问题
            if (StrUtil.isNotBlank(standardPrice) && isDifferenceAbovePercent(standardPriceOld,standardPrice, 70) ) {
                errorNum++;
                continue;
            }
        }
        return errorNum;
    }

    private int quickUpdateErrorNum(List<GoodsHead> goodsHeads) {
        int errorNum = 0;
        for (GoodsHead goodsHead : goodsHeads) {
            // 变动幅度的比例,VC链接的价格变动超过10%就监控，原因：VC链接的costprice只能下降，不能上涨，如果是误操作导致的下降幅度过大，系统无法修正回以前的值
            int radioValue = goodsHead.getShopCode().contains("VC") ? 10 : 85;
            //取出备份表中最近的一条数据
            GoodsHeadBackup goodsHeadBackup = goodsHeadBackupService.selectGoodsHeadBackupByGoodsId(String.valueOf(goodsHead.getId()));
            if (goodsHeadBackup == null){
                //如果没有备份数据，发钉钉让管理员检查是否有问题
                errorNum++;
                continue;
            }
            ItemBackUpDTO itemBackUpDTO = JSONObject.parseObject(goodsHeadBackup.getContext(), ItemBackUpDTO.class);

            // 价格变动
            String standardPrice = goodsHead.getStandardPrice();
            String standardPriceOld = itemBackUpDTO.getGoodsHead().getStandardPrice();

            //standardPriceOld 和 standardPrice 相差70%以上,说明价格有问题
            if ( isDifferenceAbovePercent(standardPriceOld,standardPrice, radioValue) ) {
                errorNum++;
                continue;
            }

            // 库存变动
            BigDecimal stockOnSalesQty = goodsHead.getStockOnSalesQty();
            BigDecimal stockOnSalesQtyOld = itemBackUpDTO.getGoodsHead().getStockOnSalesQty();
            if (stockOnSalesQty != null && isDifferenceAbovePercent(stockOnSalesQtyOld,stockOnSalesQty, radioValue)) {
                errorNum++;
                continue;
            }
        }
        return errorNum;
    }

    public boolean isDifferenceAbovePercent(String oldPriceStr, String newPriceStr, double radioValue) {
        try {
            Double oldPrice = new Double(oldPriceStr);
            Double newPrice = new Double(newPriceStr);

            double difference = Math.abs(oldPrice - newPrice);

            double percentDifference = (difference / oldPrice) * 100.0;

            return percentDifference >= radioValue;
        } catch (Exception e) {
            log.error("监控价格比较转换时出现异常,管理员注意,oldPriceStr:{},newPriceStr:{}",oldPriceStr,newPriceStr,e);
            return true;
        }
    }

    public boolean isDifferenceAbovePercent(BigDecimal oldValue, BigDecimal newValue, double radioValue) {
        try {
            double difference = Math.abs(oldValue.doubleValue() - newValue.doubleValue());

            double percentDifference = (difference / oldValue.doubleValue()) * 100.0;

            return percentDifference >= radioValue;
        } catch (Exception e) {
            log.error("监控数值比较转换时出现异常,管理员注意,oldValue:{},newValue:{}",oldValue,newValue,e);
            return true;
        }
    }


    /**
     *  中风险监控 直接 发送钉钉消息
     * @param context
     * @param monitorEnum
     * @return
     */
    public void monitorMediumRiskAndSend(MonitorEnum monitorEnum,String context) {
        MonitorMessage monitorMessage = new MonitorMessage();
        monitorMessage.setMonitorType(monitorEnum.getMonitorType());
        monitorMessage.setMonitorName(monitorEnum.getMonitorName());
        monitorMessage.setMonitorContext(context);
        monitorMessage.setCreateBy("1");
        monitorMessage.setCreateTime(new Date());
        monitorMessageService.insertMonitorMessage(monitorMessage);
    }


    public void monitorSend(String title,String type, String context,Map<String, String> featureMap) {
        MonitorTemplate monitorTemplate = new MonitorTemplate();
        monitorTemplate.setServerName(MonitorServerEnum.SMC);
        monitorTemplate.setMessageName(title);
        monitorTemplate.setLevel( MonitorEnum.getMonitorLevelEnum(type) );
        monitorTemplate.setErrorMsg(context);
        monitorTemplate.setFeatureMap(featureMap);
        if (EnvUtils.isProdProfile()) {
            dingdingMessageUtil.sendMsgByMiddleRotbot(monitorTemplate);
        } else {
            log.info("钉钉报警,{}",monitorTemplate);
        }
    }


    /**
     * 推送钉钉待办
     * @param map
     * @param title
     */
    public void dingDingBacklog(Map<String, DingDingDetailVO> map,String title) {
        if (MapUtil.isEmpty(map)) {
            return;
        }
        List<String> stringList = getDefaultUserCode();
        Map<Long, String> userMap = CommonUtilsSmc.handSysUser(3);
        int i = 1;
        for (String key : map.keySet()) {
            try {
                TODOTaskCardMsgDto todoTaskCardMsgDto=new TODOTaskCardMsgDto();
                String userCode= CollUtil.isNotEmpty(stringList) ? stringList.get(0) : userMap.get(Long.parseLong(key));
                if (StringUtils.isBlank(userCode)) {
                    continue;
                }
                DingDingDetailVO dingDingDetailVO = map.get(key);
                String description = URLEncoder.encode(String.valueOf(dingDingDetailVO.getDescription()), "UTF-8");
                String path = dingDingDetailVO.getPath();
                String url = exceptionNotificationConfig.getUrl() + "description=" + description + "&path=" + path + "&userCode=" + userCode+"&title=" + title;
                todoTaskCardMsgDto.setTitle(title);
                todoTaskCardMsgDto.setAppUrl(url);
                todoTaskCardMsgDto.setPcUrl(url);
                todoTaskCardMsgDto.setExecutorCodes(ListUtil.toList(userCode));
                dingAsyncSendService.sendTOdoTask(todoTaskCardMsgDto);

                log.info("发送钉钉待办成功,title:{},createBy:{},url:{}",title,key,url);
                if (CollUtil.isNotEmpty(stringList) && i >= stringList.size()) {
                    //默认配置了接受方 多余的就不发了
                    return;
                }
                i++;
            } catch (Exception e) {
                log.error("发送钉钉待办失败,createBy:{}",key,e);
            }
        }
    }

    private List<String> getDefaultUserCode() {
        String notifications = sysConfigService.selectConfigByKey("to:do:notifications");
        List<String> stringList=new ArrayList<>();
        if ( StringUtils.isNotBlank(notifications) && !ObjUtil.equals("null",notifications) ) {
            stringList = CommonUtilsSmc.toStringList(notifications);
        }
        return stringList;
    }


    /**
     * 获取接收人
     * 1. 如果创建人为空或者-1,则取更新人
     * 2. 如果更新人为空或者-1,则取店铺负责人
     * 3. 如果店铺负责人为空或者-1,则取it管理员
     * @param receiver
     * @param updateBy
     * @param shopCode
     * @return
     */
    public String getReceiver(String receiver,String updateBy, String shopCode) {
        if (ObjUtil.isEmpty(receiver) || "-1".equals(receiver)) {
            receiver = updateBy;
        }
        if (ObjUtil.isEmpty(receiver) || "-1".equals(receiver)) {
            ConfigStoreInfo configStoreInfo = configStoreInfoService.selectConfigStoreInfoByShopCode(shopCode);
            if (ObjUtil.isNotEmpty(configStoreInfo)) {
                receiver = String.valueOf(configStoreInfo.getShopManger());
            }
        }
        if (ObjUtil.isEmpty(receiver) || "-1".equals(receiver)) {
            receiver = "1";
        }
        return receiver;
    }



    /**
     * 获取vc缺失映射的列表
     * @return
     */
    public List<AmazonListingPullLack> getListingPullLacks() {
        List<AmazonListingPullLack> returnList = new ArrayList<>();
        List<AmazonListingPullLack> amazonListingPullLacksMapping = odsCrlCrlVcCatalogDataService.selectLackMappingData();
        if (CollUtil.isEmpty(amazonListingPullLacksMapping)){
            return returnList;
        }

        Map<String, List<AmazonListingPullLack>> collect = amazonListingPullLacksMapping.stream().collect(Collectors.groupingBy(AmazonListingPullLack::getShopCode));
        for (String shopCode : collect.keySet()) {
            List<AmazonListingPullLack> amazonListingPullLacksM = collect.get(shopCode);
            List<String> allPlatformGoodsCode = amazonListingPullLacksM.stream().map(AmazonListingPullLack::getPlatformGoodsCode).collect(Collectors.toList());
            List<MappingGoods> mappingGoods = pdmHttpRequestBiz.queryPdmMappingList(shopCode, allPlatformGoodsCode);
            if (CollUtil.isEmpty(mappingGoods)){
                returnList.addAll(amazonListingPullLacksM);
                continue;
            }

            //这些sku 没有映射
            List<String> platformSku = mappingGoods.stream().filter(f -> StringUtils.isEmpty(f.getAsin())).map(f -> f.getPlatformSku()).collect(Collectors.toList());

            List<AmazonListingPullLack> collect1 = amazonListingPullLacksM.stream().filter(f -> platformSku.contains(f.getPlatformGoodsCode())).collect(Collectors.toList());
            returnList.addAll(collect1);
        }
        return returnList;
    }


    /**
     * 获取配置的客服人员
     * @return
     */
    public List<SysUser> getConfigCustomer() {
        String value = sysConfigService.selectConfigByKey("todo.dingding.customer");
        if (ObjUtil.isEmpty(value)) {
            return Collections.emptyList();
        }
        allCustomer = Arrays.stream(value.split(","))
                .map(String::trim)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        if (ObjUtil.isEmpty(allCustomer)){
            return Collections.emptyList();
        }
        return sysUserService.selectUserListByUserIds(allCustomer);
    }


    public void handleSendTodo(List<SysUser> sysUsers,boolean todo) {
        if (CollUtil.isEmpty(sysUsers)){
            throw new BusinessException("发送的运营人员不存在,请重新确认");
        }
        for (SysUser user : sysUsers) {
            MatchTodoDataDTO matchTodoDataDTO = new MatchTodoDataDTO();
            matchTodoDataDTO.setSysUser(user);
            //设置角色
            if (ObjUtil.isNotEmpty(allCustomer) && ObjUtil.isNotEmpty(user.getUserId())) {
                matchTodoDataDTO.setRole(allCustomer.contains(user.getUserId().intValue()) ? "customer" : null);
            }
            //客服发送之前的数据
            Object dingDingDetail = redisService.getCacheObject("TODO_DINGDING_CUSTOMER");
            if (ObjUtil.isNotEmpty(matchTodoDataDTO.getRole()) && matchTodoDataDTO.getRole().equals("customer") && ObjUtil.isNotEmpty(dingDingDetail)) {
                DingDingDetailVO dingDingDetailVO = JSON.parseObject(String.valueOf(dingDingDetail), DingDingDetailVO.class);
                log.info("客服:{}发送之前数据", user.getUserName());

                Map<String, DingDingDetailVO> resultMap = new HashMap<>();
                resultMap.put(user.getUserId().toString(), dingDingDetailVO);
                this.dingDingBacklog(resultMap, "SMC-首页待办提醒");
                continue;
            }

            searchTodoList(matchTodoDataDTO,todo);

            sendDingTalk(matchTodoDataDTO,todo);
        }
    }

    private void sendDingTalk(MatchTodoDataDTO matchTodoDataDTO,boolean todo) {
        String tit = todo ? "待办清单" : "监控清单";
        String userName = matchTodoDataDTO.getSysUser().getUserName();
        Long userId = matchTodoDataDTO.getSysUser().getUserId();
        List<MatchTodoDataDetailDTO> todoList = matchTodoDataDTO.getTodoList();
        if (CollUtil.isEmpty(todoList)) {
            return;
        }
        String fileName;
        if (ObjUtil.isNotEmpty(matchTodoDataDTO.getRole())&& matchTodoDataDTO.getRole().equals("customer")){
            fileName= DateUtil.format(new Date(), CHINESE_DATE_FORMAT) + "-"  + System.currentTimeMillis() + "-"+tit+".xlsx";

        }else {
            fileName=DateUtil.format(new Date(), CHINESE_DATE_FORMAT) + "-" + userName + System.currentTimeMillis() + "-"+tit+".xlsx";
        }
        String path = RuoYiConfig.getDownloadPath() + fileName;

        //上传到Oss
        //oss文件
        String url = "smc/dingDingFiles/" + com.suncent.smc.common.utils.DateUtils.getDate() + "/" + fileName;

        try {
            // 写入数据
            try (ExcelWriter excelWriter = EasyExcel.write(path).build()) {
                for (int i = 0; i < todoList.size(); i++) {
                    MatchTodoDataDetailDTO todoData = todoList.get(i);

                    WriteSheet writeSheet = EasyExcel.writerSheet(i, todoData.getTodoName()).head(todoData.getTodoDataClass()).build();
                    excelWriter.write(todoData.getTodoData(), writeSheet);
                }
            }

            aliOssService.putObjectByFile(bucketName, url, path);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }


        DingDingDetailVO dingDingDetailVO = new DingDingDetailVO();
        try {
            StringBuilder stringBuilder = new StringBuilder();
            int total = todoList.stream().map(MatchTodoDataDetailDTO::getTodoData).map(List::size).reduce(0, Integer::sum);
            stringBuilder.append("<p> SMC今日"+tit+"，总共：").append(total).append("条 </p>");
            appendDetail(todoList, stringBuilder);
            dingDingDetailVO.setDescription(stringBuilder);
            dingDingDetailVO.setPath(aliOssAddress + url);

            //客服数据设置同一份
            if (ObjUtil.isNotEmpty(matchTodoDataDTO.getRole())&& matchTodoDataDTO.getRole().equals("customer")&&ObjUtil.isEmpty(redisService.getCacheObject("TODO_DINGDING_CUSTOMER"))){
                redisService.setCacheObject("TODO_DINGDING_CUSTOMER",JSON.toJSON(dingDingDetailVO),1L, TimeUnit.DAYS);
            }

        } catch (Exception e) {
            log.error("待办钉钉信息通知失败,接受人:{}", userName, e);
        } finally {
            FileUtils.deleteFile(path);
        }
        Map<String, DingDingDetailVO> resultMap = new HashMap<>();
        resultMap.put(userId.toString(), dingDingDetailVO);
        this.dingDingBacklog(resultMap,"SMC-首页"+tit+"提醒");
    }

    private void appendDetail(List<MatchTodoDataDetailDTO> todoList, StringBuilder stringBuilder) {
        if (CollUtil.isEmpty(todoList)) {
            return;
        }
        for (MatchTodoDataDetailDTO matchTodoDataDetailDTO : todoList) {
            if (CollUtil.isEmpty(matchTodoDataDetailDTO.getTodoData()))  {
                continue;
            }

            stringBuilder.append("<p> ").append(matchTodoDataDetailDTO.getTodoName()).append(":").append(matchTodoDataDetailDTO.getTodoData().size()).append("条 </p>");
        }
    }

    private void searchTodoList(MatchTodoDataDTO matchTodoDataDTO,boolean todo) {
        //需要发送钉钉通知的待办类型
        List<TodoResolver> todoResolverMap = new ArrayList();
        if (todo){
            //适配文案变更
            todoResolverMap.add(handlerTodoComposite.getTodoResolver(TodoEnum.ADAPTIVE_UPDATE.name()));
            //适配图变更
            todoResolverMap.add(handlerTodoComposite.getTodoResolver(TodoEnum.IMAGE_UPDATE.name()));
            //节点异常
            todoResolverMap.add(handlerTodoComposite.getTodoResolver(TodoEnum.NODE_INCORRECT.name()));
        } else {
            //变体监控提醒
            todoResolverMap.add(handlerTodoComposite.getTodoResolver(TodoEnum.VARIANT_MONITORING.name()));
            //掉购物车
            todoResolverMap.add(handlerTodoComposite.getTodoResolver(TodoEnum.LOST_CART.name()));
            //VC价格变动
            todoResolverMap.add(handlerTodoComposite.getTodoResolver(TodoEnum.VC_PRICE_CHANGE.name()));
            //被跟卖
            todoResolverMap.add(handlerTodoComposite.getTodoResolver(TodoEnum.MONITORING_BELONG_SHOP.name()));
            todoResolverMap.add(handlerTodoComposite.getTodoResolver(TodoEnum.MONITORING_AM_DELETE_LISTING.name()));
        }
        todoResolverMap.forEach(todoResolver -> {
            todoResolver.matchTodoData(matchTodoDataDTO);
        });
    }

    /**
     * 发送VC链接异常数据给运营
     * @param users
     * @param period 推送周期：day-按日推送，week-按周推送
     */
    public void sendVcLinkErrorDataToOperate(List<SysUser> users, String period) {
        if (CollUtil.isEmpty(users)) {
            return;
        }
        for (SysUser user : users) {
            sendVcLinkErrorDataToOperate(user, period);
        }
    }

    private void sendVcLinkErrorDataToOperate(SysUser user, String period) {
        try {
            List<MatchTodoDataDetailDTO> todoList =new ArrayList<>();
            // 获取VC链接异常数据，直接在SQL层面按周期筛选
            List<VcLinkErrorDataVO> vcLinkErrorDataList = goodsHeadService.listVcErrorData(user.getUserId(), period);
            if (CollUtil.isEmpty(vcLinkErrorDataList)) {
                return;
            }

            // 掉购物车的数据
            List<Integer> lostCartHeadIds = new ArrayList<>();
            List<LoseCartTodo> cartTodoList = loseCartTodoService.selectDingTalkList(user.getUserId());
            if (CollUtil.isNotEmpty(cartTodoList)) {
                lostCartHeadIds = cartTodoList.stream().map(LoseCartTodo::getHeadId).collect(Collectors.toList());
            }

            List<VcLinkErrorDataVO> resultList = new ArrayList<>();

            // 指标数据在label和labelType字段，需要将指标数据合并到一行
            Map<Long, List<VcLinkErrorDataVO>> groupMap = vcLinkErrorDataList.stream().collect(Collectors.groupingBy(VcLinkErrorDataVO::getId));
            for (Long key : groupMap.keySet()) {
                List<VcLinkErrorDataVO> list = groupMap.get(key);
                VcLinkErrorDataVO result = new VcLinkErrorDataVO();
                result.setId(key);
                result.setShopCode(list.get(0).getShopCode());
                result.setPdmGoodsCode(list.get(0).getPdmGoodsCode());
                result.setPlatformGoodsCode(list.get(0).getPlatformGoodsCode());
                result.setPlatformGoodsId(list.get(0).getPlatformGoodsId());
                result.setOperator(list.get(0).getOperator());
                result.setOnlineTime(list.get(0).getOnlineTime());
                result.setListPrice(list.get(0).getListPrice());
                result.setCostPrice(list.get(0).getCostPrice());
                result.setCategoryName(list.get(0).getCategoryName());
                result.setCategoryNameEn(list.get(0).getCategoryNameEn());
                result.setPublishType(list.get(0).getPublishType());
                if (lostCartHeadIds.contains(key.intValue())) {
                    result.setLostCart("是");
                }else {
                    result.setLostCart("否");
                }
                // 指标数据
                list.forEach(e -> {
                    // 变狗
                    if (e.getLabel().equals("变狗")) {
                        result.setMonitor404("是");
                    }
                    // HighCost
                    if (e.getLabel().equals("HighCost")) {
                        result.setHighCost("是");
                    }
                    // NetPPM
                    if (e.getLabelType().equalsIgnoreCase("api-netppm")) {
                        String netPpm = e.getLabel();
                        if (StrUtil.isNotBlank(netPpm)) {
                            String[] split = netPpm.split(":");
                            if (split.length == 2) {
                                String netPpmValue = split[1];
                                if (StrUtil.isNotBlank(netPpmValue)) {
                                    netPpmValue = netPpmValue.trim().replace("%", "");
                                    double netPpmValueDouble = Double.parseDouble(netPpmValue);
                                    result.setNetPpm(netPpmValueDouble+"");
                                }
                            }
                        }
                    }
                    // AndonCord
                    if (e.getLabel().equals("AndonCord")) {
                        result.setAndonCord("是");
                    }
                    // LP异常
                    if (e.getLabel().equals("LP异常")) {
                        result.setLpException("是");
                    }
                    // BD异常
                    if (e.getLabel().equals("BD异常")) {
                        result.setBdException("是");
                    }
                    // Coupon异常
                    if (e.getLabel().equals("Coupon异常")) {
                        result.setCouponException("是");
                    }
                });

                // 对没有值的指标设置成否
                if (StrUtil.isBlank(result.getMonitor404())) {
                    result.setMonitor404("否");
                }
                if (StrUtil.isBlank(result.getHighCost())) {
                    result.setHighCost("否");
                }
                if (StrUtil.isBlank(result.getAndonCord())) {
                    result.setAndonCord("否");
                }
                if (StrUtil.isBlank(result.getLpException())) {
                    result.setLpException("否");
                }
                if (StrUtil.isBlank(result.getBdException())) {
                    result.setBdException("否");
                }
                if (StrUtil.isBlank(result.getCouponException())) {
                    result.setCouponException("否");
                }
                // netPpm
                if (StrUtil.isBlank(result.getNetPpm())) {
                    result.setNetPpm("否");
                }
                resultList.add(result);
            }
            

            // 对netppm的数据单独处理，移除netppm有值但是值低于20
            if ("week".equals(period)) {
                // 周维度，而且其他指标都是否
                resultList.removeIf(e ->  e.getMonitor404().equals("否") && e.getHighCost().equals("否") && e.getAndonCord().equals("否") && e.getLpException().equals("否") && e.getBdException().equals("否") && e.getLostCart().equals("否") && e.getCouponException().equals("否")
                 && StrUtil.isNotBlank(e.getNetPpm()) && !"否".equals(e.getNetPpm()) && Double.parseDouble(e.getNetPpm()) >= 20);
            }

            if (CollUtil.isEmpty(resultList)) {
                return;
            }

            MatchTodoDataDetailDTO matchTodoDataDetailDTO = new MatchTodoDataDetailDTO();
            matchTodoDataDetailDTO.setTodoName("VC链接异常数据");
            matchTodoDataDetailDTO.setTodoData(resultList);
            matchTodoDataDetailDTO.setTodoDataClass(VcLinkErrorDataVO.class);
            todoList.add(matchTodoDataDetailDTO);

            // 获取VC-PO实际库存为0数量
            getActulCount(user, todoList);

            // 获取VC-PO库销比>=5
            getAbnormalDingdings(user, todoList);

            // // 统计各种异常数量
            // long dogCount = vcLinkErrorDataList.stream().filter(e -> StrUtil.isNotBlank(e.getMonitor404()) && "是".equals(e.getMonitor404())).count();
            // long highCostCount = vcLinkErrorDataList.stream().filter(e -> StrUtil.isNotBlank(e.getHighCost()) && "是".equals(e.getHighCost())).count();
            // long netPpmCount = vcLinkErrorDataList.stream().filter(e -> StrUtil.isNotBlank(e.getNetPpm()) && "是".equals(e.getNetPpm())).count();
            // long andonCordCount = vcLinkErrorDataList.stream().filter(e -> StrUtil.isNotBlank(e.getAndonCord()) && "是".equals(e.getAndonCord())).count();
            // long lpExceptionCount = vcLinkErrorDataList.stream().filter(e -> StrUtil.isNotBlank(e.getLpException()) && "是".equals(e.getLpException())).count();
            // long bdExceptionCount = vcLinkErrorDataList.stream().filter(e -> StrUtil.isNotBlank(e.getBdException()) && "是".equals(e.getBdException())).count();
            // long couponExceptionCount = vcLinkErrorDataList.stream().filter(e -> StrUtil.isNotBlank(e.getCouponException()) && "是".equals(e.getCouponException())).count();

            // // 取最大异常数量
            // long maxCount = Math.max(dogCount, highCostCount);
            // maxCount = Math.max(maxCount, netPpmCount);
            // maxCount = Math.max(maxCount, andonCordCount);
            // maxCount = Math.max(maxCount, lpExceptionCount);
            // maxCount = Math.max(maxCount, bdExceptionCount);
            // maxCount = Math.max(maxCount, couponExceptionCount);
            if (CollUtil.isEmpty(todoList)) {
                return;
            }
            int total = todoList.stream().map(MatchTodoDataDetailDTO::getTodoData).map(List::size).reduce(0, Integer::sum);
            String periodDesc = "day".equals(period) ? "日维度" : "周维度";
            String messageContent = "VC链接异常数量(" + periodDesc + "):" + total;

            // 发送钉钉消息
            sendDingTalkMessage(user, todoList, messageContent, period);
        } catch (Exception e) {
            log.error("VC异常链接待办钉钉信息通知失败,接受人:{}", user.getUserName(), e);
        }
    }



    private List<AbnormalDingding> getAbnormalDingdings(SysUser user, List<MatchTodoDataDetailDTO> todoList) {
        List<AbnormalDingding> kxList = new ArrayList<>();

        MatchTodoDataDetailDTO todo3 = new MatchTodoDataDetailDTO();
        todo3.setTodoName("VC-PO库销比>=5");
        todo3.setTodoDataClass(AbnormalDingding.class);
        todo3.setTodoData(kxList);

        List<String> platformGoodsIds = odsCrlCrlVcCatalogDataService.listVCInventorySalesRatio(user.getUserName());
        if (CollUtil.isNotEmpty(platformGoodsIds) && platformGoodsIds.size()<=10000){
            GoodsHead query = new GoodsHead();
            query.setPlatformGoodsId(platformGoodsIds.stream().map(String::valueOf).collect(Collectors.joining(" ")));
            query.setPublishType(PublishType.VCPO.getType());
            List<GoodsHeadVO> goodsHeads = goodsHeadService.selectListingGoodsHeadVOList(query);

            kxList = getAbnormalDingdings(goodsHeads);
            todo3.setTodoData(kxList);
        }
        todoList.add(todo3);
        return kxList;
    }

    private int getActulCount(SysUser user, List<MatchTodoDataDetailDTO> todoList) {
        List<AbnormalDingding> abnormalDingdings =new ArrayList<>();
        MatchTodoDataDetailDTO todo2 = new MatchTodoDataDetailDTO();
        todo2.setTodoName("VC-PO实际库存为0");
        todo2.setTodoDataClass(AbnormalDingding.class);

        int actulCount=0;
        ListingDTO listingDTO= new ListingDTO();
        listingDTO.setActualStockOnSalesQtyStart(new BigDecimal(0));
        listingDTO.setActualStockOnSalesQtyEnd(new BigDecimal(0));
        GoodsHead goodsHeadParam=new GoodsHead();
        goodsHeadParam.setPublishType(PublishType.VCPO.getType());
        goodsHeadParam.setCreateBy(user.getUserId()+"");
        //获取实际库存为0的商品编码
        boolean actulStockScoop = listingInfoBiz.getGoodHeadParamByActulStockScoop(listingDTO, goodsHeadParam);
        if (!actulStockScoop) {
            todo2.setTodoData(abnormalDingdings);
            todoList.add(todo2);
            return 0;
        }

        //通过商品编码获取主键id
        List<Integer> ids = goodsHeadService.selectListIds(goodsHeadParam);
        actulCount = ids.size();

        //如果数量过多或者为空，则不查询数据
        if (actulCount<=10000 && CollUtil.isNotEmpty(ids)){
            abnormalDingdings = putListTodingDTO(ids);
        }

        todo2.setTodoData(abnormalDingdings);
        todoList.add(todo2);
        return actulCount;
    }



    private List<AbnormalDingding> getAbnormalDingdings(List<GoodsHeadVO> goodsHeads) {
        List<AbnormalDingding> kxList = new ArrayList<>();
        for (GoodsHeadVO goodsHead : goodsHeads) {
            AbnormalDingding abnormalDingding = new AbnormalDingding();
            abnormalDingding.setShopCode(goodsHead.getShopCode());
            abnormalDingding.setPlatformCode(goodsHead.getPlatform());
            abnormalDingding.setHeadId(goodsHead.getId());
            abnormalDingding.setSku(goodsHead.getPdmGoodsCode());
            abnormalDingding.setPlatformSku(goodsHead.getPlatformGoodsCode());
            kxList.add(abnormalDingding);
        }
        return kxList;
    }

    private List<AbnormalDingding> putListTodingDTO(List<Integer> ids) {
        GoodsHead query = new GoodsHead();
        query.setPublishType(PublishType.VCPO.getType());
        query.setIds(ids.stream().map(String::valueOf).collect(Collectors.joining(" ")));
        List<GoodsHeadVO> goodsHeads = goodsHeadService.selectListingGoodsHeadVOList(query);
        if (CollUtil.isEmpty(goodsHeads)){
            return new ArrayList<>();
        }
        List<AbnormalDingding> abnormalDingdings = getAbnormalDingdings(goodsHeads);
        return abnormalDingdings;
    }


    private void sendDingTalkMessage(SysUser user, List<MatchTodoDataDetailDTO> todoList, String messageContent) {
        sendDingTalkMessage(user, todoList, messageContent, "day");
    }

    private void sendDingTalkMessage(SysUser user, List<MatchTodoDataDetailDTO> todoList, String messageContent, String period) {
        if (CollUtil.isEmpty(todoList)) {
            return;
        }
        List<MatchTodoDataDetailDTO> detailDTOList = todoList.stream().filter(f -> CollUtil.isNotEmpty(f.getTodoData())).collect(Collectors.toList());
        if (CollUtil.isEmpty(detailDTOList)) {
            return;
        }
        String periodDesc = "day".equals(period) ? "日维度" : "周维度";
        String tit = "VC链接异常数据(" + periodDesc + ")";
        String userName = user.getUserName();
        Long userId = user.getUserId();
      
        String fileName= DateUtil.format(new Date(), CHINESE_DATE_FORMAT) + "-"  + System.currentTimeMillis() + "-"+tit+".xlsx";

        String path = RuoYiConfig.getDownloadPath() + fileName;

        //上传到Oss
        //oss文件
        String url = "smc/dingDingFiles/" + com.suncent.smc.common.utils.DateUtils.getDate() + "/" + fileName;

        try {
            // 写入数据
//            try (ExcelWriter excelWriter = EasyExcel.write(path).build()) {
//                WriteSheet writeSheet = EasyExcel.writerSheet(0, "VC链接异常数据").head(VcLinkErrorDataVO.class).build();
//                excelWriter.write(vcLinkErrorDataList, writeSheet);
//            }

            // 写入数据
            try (ExcelWriter excelWriter = EasyExcel.write(path).build()) {
                for (int i = 0; i < todoList.size(); i++) {
                    MatchTodoDataDetailDTO todoData = todoList.get(i);

                    WriteSheet writeSheet = EasyExcel.writerSheet(i, todoData.getTodoName()).head(todoData.getTodoDataClass()).build();
                    excelWriter.write(todoData.getTodoData(), writeSheet);
                }
            }

            aliOssService.putObjectByFile(bucketName, url, path);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }


        try {
            ActionCardMsgDto actionCardMsgDto = new ActionCardMsgDto();
            actionCardMsgDto.setTargetDingUserId(user.getUserCode());
//            actionCardMsgDto.setTargetDingUserId("16754079460789038");
            actionCardMsgDto.setMessageTitle("SMC-VC链接异常通知(" + periodDesc + ")");
            actionCardMsgDto.setMessageContent("# SMC-VC链接异常通知(" + periodDesc + ")" + messageContent);
            actionCardMsgDto.setMessageUrl(aliOssAddress + url);
            if (EnvUtils.isProdProfile()){
                dingAsyncSendService.asyncSend(actionCardMsgDto);
            }
            log.info("发送钉钉待办成功,createBy:{},url:{}",userId,aliOssAddress + url);
        } catch (Exception e) {
            log.error("VC链接异常数据钉钉信息通知失败,接受人:{},url:{}", userName,aliOssAddress + url, e);
        } finally {
            FileUtils.deleteFile(path);
        }
    }


    public void sendDingTalkMessageBySync(List<SysUser> sysUsers, List<ShopSyncStats> abnormalShops) {
        if (CollUtil.isEmpty(abnormalShops)) {
            return;
        }

        String tit = "最近24小时链接同步情况";
        String fileName= DateUtil.format(new Date(), CHINESE_DATE_FORMAT) + "-"  + System.currentTimeMillis() + "-"+tit+".xlsx";
        String path = RuoYiConfig.getDownloadPath() + fileName;
        //上传到Oss
        //oss文件
        String url = "smc/dingDingFiles/" + com.suncent.smc.common.utils.DateUtils.getDate() + "/" + fileName;

        try {
            // 写入数据
            try (ExcelWriter excelWriter = EasyExcel.write(path).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "链接同步情况").head(ShopSyncStats.class).build();
                excelWriter.write(abnormalShops, writeSheet);
            }

            aliOssService.putObjectByFile(bucketName, url, path);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        try {
            for (SysUser user : sysUsers) {
                try {
                    Long userId = user.getUserId();

                    ActionCardMsgDto actionCardMsgDto = new ActionCardMsgDto();
                    actionCardMsgDto.setTargetDingUserId(user.getUserCode());
                    // actionCardMsgDto.setTargetDingUserId("1683690782193554");
                    actionCardMsgDto.setMessageTitle("SMC-链接同步情况");
                    actionCardMsgDto.setMessageContent("SMC-链接同步情况");
                    actionCardMsgDto.setMessageUrl(aliOssAddress + url);
                    if (EnvUtils.isProdProfile()) {
                        dingAsyncSendService.asyncSend(actionCardMsgDto);
                    }
                    log.info("发送钉钉待办成功,createBy:{},url:{}", userId, url);
                }catch (Exception e) {
                    log.error("VC链接异常数据钉钉信息通知失败,接受人:{}", user.getUserName(), e);
                }
            }
        } finally {
            FileUtils.deleteFile(path);
        }
    }
}
