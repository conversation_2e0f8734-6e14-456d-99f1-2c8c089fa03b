package com.suncent.smc.web.controller.publication.property;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.suncent.smc.common.annotation.Log;
import com.suncent.smc.common.core.controller.BaseController;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysDictData;
import com.suncent.smc.common.core.page.TableDataInfo;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.enums.BusinessType;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.utils.PageUtils;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.poi.ExcelUtil;
import com.suncent.smc.persistence.ads.domain.AdsFitmentDataEbay;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigFieldInfo;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigRequiredField;
import com.suncent.smc.persistence.configuration.category.service.IConfigFieldInfoService;
import com.suncent.smc.persistence.configuration.category.service.IConfigRequiredFieldDictService;
import com.suncent.smc.persistence.configuration.category.service.IConfigRequiredFieldService;
import com.suncent.smc.persistence.configuration.upc.domain.ConfigUpcPool;
import com.suncent.smc.persistence.configuration.upc.service.IConfigUpcPoolService;
import com.suncent.smc.persistence.pdm.service.IGoodsPackageService;
import com.suncent.smc.persistence.publication.domain.dto.AttributeQueryDTO;
import com.suncent.smc.persistence.publication.domain.dto.AttributesQueryDTO;
import com.suncent.smc.persistence.publication.domain.entity.ListingAdaptiveTemp;
import com.suncent.smc.persistence.publication.domain.entity.ListingAttributeTemp;
import com.suncent.smc.persistence.publication.service.IListingAdaptiveTempService;
import com.suncent.smc.persistence.publication.service.IListingAttributeTempService;
import com.suncent.smc.persistence.temu.domain.entity.TemuConfigRequiredFieldExtend;
import com.suncent.smc.persistence.temu.domain.entity.TemuShopBrand;
import com.suncent.smc.persistence.temu.service.ITemuConfigRequiredFieldExtendService;
import com.suncent.smc.persistence.temu.service.ITemuShopBrandService;
import com.suncent.smc.provider.biz.pdm.CreatePlatformCode;
import com.suncent.smc.provider.biz.publication.ListingInfoBiz;
import com.suncent.smc.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品属性信息临时Controller
 *
 * <AUTHOR>
 * @date 2023-01-13
 */
@Controller
@RequestMapping("/publication/goods/property")
@Slf4j
public class ListingAttributeTempController extends BaseController {
    private String prefix = "publication/goods/property";

    @Autowired
    private IListingAttributeTempService listingAttributeTempService;

    @Autowired
    private IConfigRequiredFieldService configRequiredFieldService;

    @Autowired
    private IConfigUpcPoolService configUpcPoolService;

    @Autowired
    IConfigFieldInfoService configFieldInfoService;
    @Autowired
    IGoodsPackageService goodsPackageService;
    @Autowired
    ListingInfoBiz listingInfoBiz;


    @GetMapping()
    public String temp() {
        return prefix + "/temp";
    }

    @Autowired
    private CreatePlatformCode createPlatformCode;
    @Autowired
    private IConfigRequiredFieldDictService configRequiredFieldDictService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private IListingAdaptiveTempService listingAdaptiveTempService;
    @Autowired
    private ITemuShopBrandService temuShopBrandService;
    @Autowired
    private ITemuConfigRequiredFieldExtendService temuConfigRequiredFieldExtendService;

    /**
     * 查询商品属性信息临时列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ListingAttributeTemp listingAttributeTemp, String uuid) {
        listingAttributeTemp.setUuid(uuid);
        if (PlatformTypeEnum.EB.name().equals(listingAttributeTemp.getPlatform())) {
            ListingAdaptiveTemp q = new ListingAdaptiveTemp();
            q.setPdmGoodsCode(listingAttributeTemp.getPdmGoodsCode());
            q.setUuid(uuid);
            q.setPageSize(listingAttributeTemp.getPageSize());
            q.setPageNum(listingAttributeTemp.getPageNum());
            PageInfo<ListingAdaptiveTemp> pageInfo  = listingAdaptiveTempService.selectPageInfoListingAdaptiveTempList(q);
            List<ListingAdaptiveTemp> listingAdaptiveTemps = pageInfo.getList();
            List<AdsFitmentDataEbay> adsFitmentDataEbayList = listingAdaptiveTemps.stream().map(o -> {
                AdsFitmentDataEbay item = new AdsFitmentDataEbay();
                item.setEngine(o.getAdaptiveEngine());
                item.setMake(o.getAdaptiveMake());
                item.setNotes(o.getNotes());
                item.setTrim(o.getAdaptiveTrim());
                item.setModel(o.getAdaptiveModel());
                item.setSubmodel(o.getAdaptiveSubmodel());
                item.setYear(o.getAdaptiveYear());
                item.setType(o.getType());
                item.setPlatform(o.getPlatform());
                item.setId(o.getId());
                return item;
            }).collect(Collectors.toList());

            return getCoustomDataTable(adsFitmentDataEbayList,pageInfo.getTotal());
        } else {
            PageUtils.clearPage();
            startPage();
            List<ListingAttributeTemp> list = listingAttributeTempService.selectListingAttributeTempList(listingAttributeTemp);
            return getDataTable(list);
        }
    }

    /**
     * 导出商品属性信息临时列表
     */
    @Log(title = "商品属性信息临时", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ListingAttributeTemp listingAttributeTemp) {
        List<ListingAttributeTemp> list = listingAttributeTempService.selectListingAttributeTempList(listingAttributeTemp);
        ExcelUtil<ListingAttributeTemp> util = new ExcelUtil<ListingAttributeTemp>(ListingAttributeTemp.class);
        return util.exportExcel(list, "商品属性信息临时数据");
    }

    /**
     * 新增商品属性信息临时
     */
    @GetMapping("/add/{goodsCode}")
    public String add(@PathVariable("goodsCode") String goodsCode, ModelMap mmap, String uuid, String categoryId) {
        mmap.put("uuid", uuid);
        mmap.put("pdmGoodsCode", goodsCode);
        mmap.put("platform", "EB");
        mmap.put("categoryId", categoryId);
        return prefix + "/add_temp";
    }

    /**
     * 新增保存商品属性信息临时
     */
    @Log(title = "商品属性信息临时", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ListingAttributeTemp listingAttributeTemp) {
        ListingAttributeTemp temp = new ListingAttributeTemp();
        temp.setUuid(listingAttributeTemp.getUuid());
        List<ListingAttributeTemp> listingAttributeTemps = listingAttributeTempService.selectListingAttributeTempList(temp);
        if (listingAttributeTemps.stream().anyMatch(l -> l.getTableName().equals(listingAttributeTemp.getTableName()))) {
            throw new RuntimeException("商品属性名称不允许重复");
        }
        return toAjax(listingAttributeTempService.insertListingAttributeTemp(listingAttributeTemp));
    }

    /**
     * 修改商品属性信息临时
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Integer id, ModelMap mmap) {
        ListingAttributeTemp listingAttributeTemp = listingAttributeTempService.selectListingAttributeTempById(id);
        mmap.put("listingAttributeTemp", listingAttributeTemp);
        return prefix + "/edit_temp";
    }

    /**
     * 修改保存商品属性信息临时
     */
    @Log(title = "商品属性信息临时", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ListingAttributeTemp listingAttributeTemp) {
        return toAjax(listingAttributeTempService.updateListingAttributeTemp(listingAttributeTemp));
    }

    /**
     * 删除商品属性信息临时
     */
    @Log(title = "商品属性信息临时", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(listingAttributeTempService.deleteListingAttributeTempByIds(ids));
    }

    /**
     * 删除商品属性信息临时
     */
    @Log(title = "商品属性信息临时", businessType = BusinessType.DELETE)
    @PostMapping("/removeByUuid")
    @ResponseBody
    public AjaxResult removeByUuid(String uuid) {
        return toAjax(listingAttributeTempService.deleteListingAttributeTempByUuid(uuid));
    }

    /**
     * 新增亚马逊商品属性信息
     */
    @GetMapping("/am/add")
    public String addAttribute(AttributeQueryDTO attributeDto, ModelMap mmap) {
        mmap.put("attributeDto", attributeDto);
        return "publication/goods/attribute/add";
    }

    /**
     * 修改亚马逊商品属性信息
     */
    @GetMapping("/am/edit/{id}")
    public String editAttribute(@PathVariable("id") Integer id, ModelMap mmap) {
        ListingAttributeTemp listingAttributeTemp = listingAttributeTempService.selectListingAttributeTempById(id);
        mmap.put("listingAttributeTemp", listingAttributeTemp);
        return "publication/goods/attribute/edit";
    }

    /**
     * 获取类目必填字段
     */
    @PostMapping("/selectByCategoryId/{categoryId}/{attributeType}/{goodsCode}/{siteCode}/{shopCode}")
    @ResponseBody
    public TableDataInfo selectRequireFieldByCategoryId(@PathVariable Integer categoryId, @PathVariable Integer attributeType, @PathVariable String goodsCode, @PathVariable String siteCode,
                                                        @PathVariable String shopCode) {
        List<ConfigRequiredField> requiredFieldList = configRequiredFieldService.selectByCategoryIdAndType(categoryId, attributeType);
        if (CollectionUtils.isEmpty(requiredFieldList)) {
            return getDataTable(requiredFieldList);
        }
        requiredFieldList = configRequiredFieldDictService.putConfigRequiredFieldDict(requiredFieldList);
        //处理店铺类目默认值
        ConfigFieldInfo configFieldInfo = new ConfigFieldInfo();
        configFieldInfo.setCategoryId(String.valueOf(categoryId));
        configFieldInfo.setIsRequire("1");
        if (ObjectUtils.isNotEmpty(shopCode) && shopCode.contains(",")) {
            String[] split = shopCode.split(",");
            configFieldInfo.setShopCodes(Arrays.asList(split));
        } else {
            configFieldInfo.setShopCode(shopCode);
        }
        configFieldInfo.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
        List<ConfigFieldInfo> fieldInfoList = configFieldInfoService.selectConfigFieldInfoList(configFieldInfo);
        Map<String, List<ConfigFieldInfo>> configFieldInfoMap= new HashMap<>();
        if (CollectionUtils.isNotEmpty(fieldInfoList)) {
            configFieldInfoMap = fieldInfoList.stream().collect(Collectors.groupingBy(ConfigFieldInfo::getFieldId));
        }
        boolean isTemu = CollectionUtils.isNotEmpty(requiredFieldList) && requiredFieldList.stream().anyMatch(r -> Objects.equals(r.getPlatformCode(), "TEMU"));
        if (isTemu && CollectionUtils.isNotEmpty(requiredFieldList)) {
            List<String> fileIds = requiredFieldList.stream().map(ConfigRequiredField::getId).collect(Collectors.toList());

            TemuConfigRequiredFieldExtend query = new TemuConfigRequiredFieldExtend();
            query.setFieIdIds(fileIds);
            List<TemuConfigRequiredFieldExtend> temuConfigRequiredFieldExtends = temuConfigRequiredFieldExtendService.selectList(query);
            // to map
            Map<Long,  TemuConfigRequiredFieldExtend> temuConfigRequiredFieldExtendMap = temuConfigRequiredFieldExtends.stream().collect(Collectors.toMap(TemuConfigRequiredFieldExtend::getFieldId, a -> a, (k1, k2) -> k1));
            for (ConfigRequiredField configRequiredField : requiredFieldList) {
                TemuConfigRequiredFieldExtend temuConfigRequiredFieldExtend = temuConfigRequiredFieldExtendMap.get(Long.valueOf(configRequiredField.getId()));
                configRequiredField.setParentJson(temuConfigRequiredFieldExtend.getParentListJson());
                configRequiredField.setTemplatePid(temuConfigRequiredFieldExtend.getTemplatePid());
                configRequiredField.setParentTemplatePid(temuConfigRequiredFieldExtend.getParentTemplatePid());
                configRequiredField.setChooseMaxNum(temuConfigRequiredFieldExtend.getChooseMaxNum());
                configRequiredField.setIsSale(temuConfigRequiredFieldExtend.getIsSale());
                configRequiredField.setParentSpecId(temuConfigRequiredFieldExtend.getParentSpecId());
            }
        }

        for (ConfigRequiredField r : requiredFieldList) {
            r.setAttributeValue( configFieldInfoMap.containsKey(r.getId()) ? configFieldInfoMap.get(r.getId()).get(0).getFieldValue() :null);
            if (Objects.equals(r.getPlatformCode(),"TEMU")){
                if (Objects.equals(r.getAttributeCode(),"品牌名")){
                    TemuShopBrand temuShopBrand = new TemuShopBrand();
                    temuShopBrand.setShopCode(shopCode);
                    List<TemuShopBrand> temuShopBrands = temuShopBrandService.selectTemuShopBrandList(temuShopBrand);
                    if (CollectionUtils.isNotEmpty(temuShopBrands)) {
                        List<KeyValueEntity> temuShopBrandList = temuShopBrands.stream().map(temuShopBrand1 -> {
                            KeyValueEntity keyValueEntity = new KeyValueEntity();
                            keyValueEntity.setKey(temuShopBrand1.getBrandNameEn());
                            keyValueEntity.setValue(temuShopBrand1.getVid());
                            if(StrUtil.isNotBlank(r.getAttributeValue()) && r.getAttributeValue().equalsIgnoreCase(temuShopBrand1.getBrandNameEn())) {
                                r.setAttributeValue(temuShopBrand1.getVid());
                            }
                            return keyValueEntity;
                        }).collect(Collectors.toList());
                        r.setDictObjValues(temuShopBrandList);
                    }else {
                        r.setDictObjValues(new ArrayList<>());
                    }
                    r.setIsRequire("1");
                }
                continue;
            }

            r.setIsRequire(configFieldInfoMap.containsKey(r.getId()) ? configFieldInfoMap.get(r.getId()).get(0).getIsRequire() :"0");
            if (!Objects.equals("part_number", r.getAttributeName())) {
                continue;
            }
            if (Objects.equals(goodsCode, "0")) {
                continue;
            }
            String platformCode = createPlatformCode.getPlatformCode(goodsCode, siteCode);
            r.setAttributeValue(platformCode);
        }
        //筛选必填的属性
        if (!isTemu) {
            requiredFieldList = requiredFieldList.stream().filter(requiredField -> "1".equals(requiredField.getIsRequire())).collect(Collectors.toList());
        }
        return getDataTable(requiredFieldList);
    }

    /**
     * 获取类目非必填字段
     * Amazon单个新建、修改
     */
    @PostMapping("/getNotRequireFieldByCategoryId/{categoryId}/{shopCode}")
    @ResponseBody
    public AjaxResult selectNotRequireFieldByCategoryId(@PathVariable Integer categoryId, @PathVariable String shopCode) {
        ConfigRequiredField configRequiredField = new ConfigRequiredField();
        configRequiredField.setCategoryId(Convert.toStr(categoryId));
        List<ConfigRequiredField> requiredFieldList = configRequiredFieldService.selectConfigRequiredFieldList(configRequiredField);
        //处理店铺类目默认值
        ConfigFieldInfo configFieldInfo = new ConfigFieldInfo();
        configFieldInfo.setCategoryId(String.valueOf(categoryId));
        configFieldInfo.setIsRequire("0");
        if (ObjectUtils.isNotEmpty(shopCode) && shopCode.contains(",")) {
            String[] split = shopCode.split(",");
            configFieldInfo.setShopCodes(Arrays.asList(split));
        } else {
            configFieldInfo.setShopCode(shopCode);
        }
        configFieldInfo.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
        List<ConfigFieldInfo> fieldInfoList = configFieldInfoService.selectConfigFieldInfoList(configFieldInfo);
        Map<String, List<ConfigFieldInfo>> configFieldInfoMap= new HashMap<>();
        if (CollectionUtils.isNotEmpty(fieldInfoList)) {
            configFieldInfoMap = fieldInfoList.stream().collect(Collectors.groupingBy(ConfigFieldInfo::getFieldId));
        }
        for (ConfigRequiredField r : requiredFieldList) {
             r.setAttributeValue( configFieldInfoMap.containsKey(r.getId()) ? configFieldInfoMap.get(r.getId()).get(0).getFieldValue() :null);
             r.setIsRequire( configFieldInfoMap.containsKey(r.getId()) ? configFieldInfoMap.get(r.getId()).get(0).getIsRequire() :"0");
        }
        //处理平台商品编码
        SysDictData sysDictData=new SysDictData();
        sysDictData.setStatus("0");
        sysDictData.setDictType("external_product_id_type");
        List<SysDictData> list = dictDataService.selectDictDataList(sysDictData);
        if (CollectionUtils.isNotEmpty(list)){
            list.forEach(editor->{
                ConfigRequiredField config=new ConfigRequiredField();
                config.setAttributeCode(editor.getDictValue());
                config.setFlag("1");
                requiredFieldList.add(config);
            });
        }

//        Map<String,List<ConfigRequiredField>> resMap = new HashMap<>();
//        List<ConfigRequiredField> importantList = requiredFieldList.stream().filter(requiredField -> "0".equals(requiredField.getAttributeType())).collect(Collectors.toList());
//        resMap.put("importantList",importantList);
//        //商品详情
//        List<ConfigRequiredField> goodsDetailsList = requiredFieldList.stream().filter(requiredField -> "1".equals(requiredField.getAttributeType())).collect(Collectors.toList());
//        resMap.put("goodsDetailsList",goodsDetailsList);
//        //安全与合规
//        List<ConfigRequiredField> safetyComplianceList = requiredFieldList.stream().filter(requiredField -> "2".equals(requiredField.getAttributeType())).collect(Collectors.toList());
//        resMap.put("safetyComplianceList",safetyComplianceList);
        return AjaxResult.success(requiredFieldList);
    }


    /**
     * 获取类目必填字段
     * Amazon批量新建刊登
     */
    @PostMapping("/getRequireFieldByCategoryId/{categoryId}/{shopCode}")
    @ResponseBody
    public TableDataInfo selectRequireFieldByCategoryId(@PathVariable Integer categoryId, @PathVariable String shopCode) {
        List<ConfigRequiredField> requiredFieldList = configRequiredFieldService.getConfigRequiredFields(categoryId, shopCode,String.valueOf(ShiroUtils.getUserId()));
        return getDataTable(requiredFieldList);
    }

    /**
     * 获取类目必填和已填字段
     * Amazon批量新建刊登
     */
    @PostMapping("/getRequireAndAlreadyField")
    @ResponseBody
    public TableDataInfo selectRequireFieldByCategoryId(AttributesQueryDTO attributesQueryDTO) {
        List<ConfigRequiredField> requiredFieldList = configRequiredFieldService.getConfigRequiredFields(attributesQueryDTO.getCategoryId(), attributesQueryDTO.getShopCode(),String.valueOf(ShiroUtils.getUserId()));

        List<ConfigRequiredField> resultList = listingInfoBiz.getAMListingAttributeByEbayHeadIds(requiredFieldList, attributesQueryDTO.getListingIds());
        return getDataTable(resultList);
    }

    /**
     * 通过fieldId获取属性
     *
     * @param fieldId
     * @return
     */
    @PostMapping("/getRequireFieldByFieldId/{fieldId}")
    @ResponseBody
    public TableDataInfo selectRequireFieldByFieldId(@PathVariable Integer fieldId) {
        ConfigRequiredField configRequiredField = configRequiredFieldService.selectConfigRequiredFieldById(fieldId);
        List<ConfigRequiredField> configRequiredFieldList = configRequiredFieldDictService.putConfigRequiredFieldDict(Arrays.asList(configRequiredField));
        return getDataTable(configRequiredFieldList);
    }

    /**
     * ebay单个、批量新建刊登
     * @param
     * @return
     */
    @PostMapping("/getAllFieldByCategoryId/{categoryId}/{attributeType}/{goodsCode}/{shopCode}")
    @ResponseBody
    public TableDataInfo selectNotRequireFieldByCategoryId(@PathVariable Integer categoryId, @PathVariable Integer attributeType, @PathVariable String goodsCode, @PathVariable String shopCode) {
        ConfigRequiredField configRequiredField = new ConfigRequiredField();
        configRequiredField.setAttributeType(Long.valueOf(attributeType == 4 ? 3 : attributeType));
        configRequiredField.setCategoryId(Convert.toStr(categoryId));
        List<ConfigRequiredField> requiredFieldList = configRequiredFieldService.selectConfigRequiredFieldList(configRequiredField);
        //处理ebay数据
        if (3 == (attributeType)) {
            handleEbay(categoryId, goodsCode, requiredFieldList, shopCode);
        }
        return getDataTable(requiredFieldList);
    }


    /**
     * ebay单个、批量新建刊登
     * @param
     * @return
     */
    @PostMapping("/getAllFieldByCategoryId")
    @ResponseBody
    public TableDataInfo selectNotRequireFieldByCategoryId(AttributesQueryDTO attributesQueryDTO) {
        ConfigRequiredField configRequiredField = new ConfigRequiredField();
        configRequiredField.setAttributeType(attributesQueryDTO.getAttributeType());
        configRequiredField.setCategoryId(Convert.toStr(attributesQueryDTO.getCategoryId()));
        List<ConfigRequiredField> requiredFieldList = configRequiredFieldService.selectConfigRequiredFieldList(configRequiredField);

        //处理ebay数据
        handleEbay(attributesQueryDTO.getCategoryId(), null, requiredFieldList, attributesQueryDTO.getShopCode());

        List<ConfigRequiredField> resultList = listingInfoBiz.getListingAttributeByEbayHeadIds(requiredFieldList, attributesQueryDTO.getListingIds());
        // 屏蔽品牌
        if ("AI".equals(attributesQueryDTO.getSource()) && CollUtil.isNotEmpty(requiredFieldList)) {
            resultList.removeIf(r -> r.getAttributeCode().equals("Brand"));
        }
        return getDataTable(resultList);
    }

    private void handleEbay(Integer categoryId, String goodsCode, List<ConfigRequiredField> requiredFieldList, String shopCode) {
        //处理店铺类目默认值
        ConfigFieldInfo configFieldInfo = new ConfigFieldInfo();
        configFieldInfo.setCategoryId(String.valueOf(categoryId));
        if (ObjectUtils.isNotEmpty(shopCode) && shopCode.contains(",")) {
            String[] split = shopCode.split(",");
            configFieldInfo.setShopCodes(Arrays.asList(split));
        } else {
            configFieldInfo.setShopCode(shopCode);
        }
        configFieldInfo.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
        List<ConfigFieldInfo> fieldInfoList = configFieldInfoService.selectConfigFieldInfoList(configFieldInfo);
        if (CollectionUtils.isNotEmpty(fieldInfoList)) {
            fieldInfoList.forEach(configFieldInfo1 -> {
                requiredFieldList.stream().filter(r -> r.getId().equals(configFieldInfo1.getFieldId())).forEach(r -> {
                    r.setAttributeValue(configFieldInfo1.getFieldValue());
                    r.setIsRequire(configFieldInfo1.getIsRequire());
                });
            });
        } else {
            requiredFieldList.stream().forEach(r -> {
                r.setAttributeValue(null);
                r.setIsRequire("0");
            });
        }

        //处理upc
        requiredFieldList.stream().filter(r -> r.getAttributeCode().equals("UPC")).forEach(r -> {
            ConfigUpcPool q = new ConfigUpcPool();
            q.setStatus("0");
            q.setUserId(ShiroUtils.getUserId());
            ConfigUpcPool upcPool = configUpcPoolService.getConfigUpcPoolLimitOne(q);
            if (Objects.isNull(upcPool)) {
                return;
            }
            r.setAttributeCode(upcPool.getUpcCode());
        });
    }

    @PostMapping("/getDistinctUnionFieldByPlatform/{require}/{platformCode}")
    @ResponseBody
    public TableDataInfo selectDistinctUnionConfigRequiredList(@PathVariable String require, @PathVariable String platformCode) {
        if (StrUtil.isEmpty(platformCode)) {
            return getDataTable(new ArrayList<>());
        }
        ConfigRequiredField configRequiredField = new ConfigRequiredField();
        configRequiredField.setPlatformCode(platformCode);
        if (!StrUtil.equals("all", require)) {
            configRequiredField.setIsRequire(require);
        }
        List<ConfigRequiredField> requiredFieldList = configRequiredFieldService.selectDistinctUnionConfigRequiredList(configRequiredField);
        return getDataTable(requiredFieldList);
    }



    /**
     * 获取ebay listing属性
     * @param listingIds
     * @return
     */
    @PostMapping("/getListingAttributeByEbayHeadIds")
    @ResponseBody
    public TableDataInfo getListingAttributeByEbayHeadIds(@RequestParam List<Integer> listingIds){
        List<ConfigRequiredField> requiredFieldList = listingInfoBiz.getListingAttributeByEbayHeadIds(listingIds);
        return getDataTable(requiredFieldList);
    }

}
