package com.suncent.smc.provider.update.resolver;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.google.common.collect.Lists;
import com.suncent.smc.common.enums.AmFieldEnum;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.persistence.publication.domain.dto.ItemDTO;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.GoodsResource;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingJSONFeedVO;
import com.suncent.smc.provider.update.HandlerListingUpdateModuleComposite;
import com.suncent.smc.provider.update.ListingUpdateModuleResolver;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * 图片更新模板执行器
 */
@Slf4j
@Component
public class AmazonImageV2ListingUpdateModuleResolver extends HandlerListingUpdateModuleComposite implements ListingUpdateModuleResolver {

    @Resource
    private HandlerListingUpdateModuleComposite handlerListingUpdateModuleComposite;

    @PostConstruct
    public void init() {
        handlerListingUpdateModuleComposite.moduleResolverMap.put(PlatformTypeEnum.AM.name() + ListingModuleType.IMAGE_V2.name(), this);
    }

    @Override
    public ItemDTO build(ItemDTO item) throws Exception {
        if (ObjectUtils.isEmpty(item) || ObjectUtils.isEmpty(item.getGoodsHead())) {
            return item;
        }
        List<AmazonListingJSONFeedVO.Attributes> attributesParam = item.getAttributesParam();
        if (CollUtil.isEmpty(attributesParam)){
            attributesParam = new ArrayList<>();
        }
        amazonPlatformListingService.removeOnSaleImg(item.getGoodsHead());

//        try {
//            GoodsHead goodsHead = listingGoodsHeadService.selectListingGoodsHeadById(item.getGoodsHead().getId());
//            List<GoodsResource> goodsResources = goodsResourceService.selectListingGoodsResourceByHeadId(item.getGoodsHead().getId());
//
//            imageHandleBiz.replaceAMResourcesUrl(goodsResources, goodsHead.getPublishType(), goodsHead.getShopCode(), "amazon-part-image-process");
//        } catch (Exception e) {
//            log.error(String.format("商品ID：%s，替换图片资源URL异常", item.getGoodsHead().getId()));
//        }

        ArrayList<AmFieldEnum> amFieldEnums = Lists.newArrayList(AmFieldEnum.IMAGE, AmFieldEnum.IMAGE_1, AmFieldEnum.IMAGE_2, AmFieldEnum.IMAGE_3, AmFieldEnum.IMAGE_4, AmFieldEnum.IMAGE_5, AmFieldEnum.IMAGE_6, AmFieldEnum.IMAGE_7, AmFieldEnum.IMAGE_8);
        List<AmazonListingJSONFeedVO.Attributes> amazonUpdateAttributeList = amazonPlatformListingService.getAmazonUpdateAttributeList(item.getGoodsHead(), amFieldEnums);
        if (CollUtil.isNotEmpty(amazonUpdateAttributeList)) {
            attributesParam.addAll(amazonUpdateAttributeList);
        }else {
            log.info("Amazon 图片更新模板执行器,没有需要更新的图片, headID: {}", item.getGoodsHead().getId());
        }
        item.setAttributesParam(attributesParam);
        return item;
    }

    @Override
    public ItemDTO compareData(ItemDTO item) throws Exception {
        List<String> updateMoudleList = item.getModuleType();
        if (ObjUtil.isEmpty(item)) {
            throw new BusinessException("对比Amazon 图片数据异常,ItemDTO为空");
        }
        List<GoodsResource> goodsResourceListOld = item.getItemDTOOld().getGoodsResourceList();
        if (ObjUtil.isEmpty(goodsResourceListOld)) {
            throw new BusinessException("对比Amazon 图片数据异常,goodsResourceListOld为空");
        }
        List<GoodsResource> goodsResourceList = item.getGoodsResourceList();
        if (ObjUtil.isEmpty(goodsResourceList)) {
            throw new BusinessException("对比Amazon 图片数据异常,goodsResourceList为空");
        }
        //图片
        if ( !Objects.equals(goodsResourceListOld.size(), goodsResourceList.size()) ) {
            updateMoudleList.add(ListingModuleType.IMAGE_V2.name());
        }
        item.setModuleType(updateMoudleList);
        return item;
    }
}