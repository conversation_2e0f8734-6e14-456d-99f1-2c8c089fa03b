$("#firstCategoryid").change(function () {
    getTrendsColumns();
    getCategoryOneLabel();
});
$("#shopCode").change(function () {
    getTrendsColumns();
    getCategoryOneLabel();
    getTemplateHtml();
});

// 动态获取列
function getTrendsColumns() {
    setColumns(saleGoodsDTOList);
}

function getTemplateHtml(){
    var templateUrl=ctx+"template/description/listByShopCode";

    var config = {
        url: templateUrl,
        type: "post",
        dataType: "json",
        data: {'shopCode': $("#shopCode").val()},
        success: function (result) {
            if(result.code == web_status.SUCCESS){
                const options = [];
                for (let item of result.data) {
                    options.push(`<option value='${item.id}'>${item.templateName}</option>`);
                }
             
                // .templateSelect遍历多个
                $(".templateSelect").each(function() {
                    $(this).html(options.join(''));
                });

                // 需要判断saleGoodsDTOList的descriptionId字段是否有值，每行需要回显对应的描述ID
                for(let i=0;i<saleGoodsDTOList.length;i++){
                    if(saleGoodsDTOList[i].ebayDescriptionId){
                        let select2 = $('[name="listings[' + i + '].descriptionId"]');
                        select2.val(saleGoodsDTOList[i].ebayDescriptionId).trigger('change');
                    }
                }
            }
        }
    };
    $.ajax(config)
}
// 设置列
function setColumns(list) {
    var columns = [];
    columns.push(
    {
        field: 'index',
        align: 'center',
        width: 50,
        title: "序号",
        formatter: function (value, row, index) {
            var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
            return columnIndex + $.table.serialNumber(index);
        }
    },{
        field : 'attributeCode',
        title : '属性名称',
        width: 400,
        formatter: function(value, row, index) {
            if($.common.isEmpty(value) || value=='undefined'){value = ''};
            if( row.isRequire == '1') {
                return $.common.sprintf("<div><input name='tableName' style='width: 200px;' class='form-control tableName' readonly id='tableName' data-id='"+row.id+"' value='" + value + "'></div>", index, value);
            }else{
                return $.common.sprintf("<div><input name='tableName' style='width: 200px;' class='form-control tableName' id='tableName' data-id='"+row.id+"' value='" + value + "'></div>", index, value);
            }
        }
    },{
        field : 'attributeMemo',
        title : '属性释义',
        width: 150,
        formatter: function(value, row, index) {
            if($.common.isEmpty(value) || value=='undefined'){value = ''};
            return $.common.sprintf("<div><input name='attributeMemo' style='width: 100px;' class='form-control' readonly id='attributeMemo' value=" + value + "></div>", index, value);
        }
    })

    var oneGoodsCode="";
    list.forEach(function(item) {
        columns.push({
            field : 'attributeValue'+item.goodsCode,
            title : '属性值('+item.goodsCode+')'+
                "<div className=\"btn-group-sm\">"+
                // "<a class=\"btn btn-success\" onclick=\"getOrigin('" + item.goodsCode + "','attribute')\"><i class=\"fa fa-plus\"></i> sku库替换属性</a>"+
                "</div>",

            formatter: function (value, row, index) {
                if($.common.isEmpty(value)){value = ''};

                var actions = [];
                if ( !oneGoodsCode || oneGoodsCode == item.goodsCode ){
                    actions.push("<div class='col-sm-10' style=\"width:80px; height:auto; float:left; display:inline\">");
                }
                actions.push("<input name='tableValue' class='form-control' style=\"width:90px; \" data-key='"+row.attributeCode+"'  data-type='3' data-goodscode='"+item.goodsCode+"' type='text' id='tableValue'  value='"+value+"'>");
                // 加入一个隐藏框，存储pdmAttributeName
                if (row.pdmAttributeName) {
                    actions.push("<input name='pdmAttributeName' class='form-control' style=\"width:90px; \" data-key='"+row.attributeCode+"'  data-type='3' data-goodscode='"+item.goodsCode+"' type='hidden' id='pdmAttributeName'  value='"+row.pdmAttributeName+"'>");
                }
                if ( !oneGoodsCode || oneGoodsCode == item.goodsCode ){
                    actions.push("</div>");
                    oneGoodsCode=item.goodsCode;
                    actions.push('<div class="col-sm-2" style="width:20px; height:auto; float:left; display:inline"><a class="btn " onclick="attributeAppli(this)" ><i class="fa"></i>👉</a></div>');
                }
                return actions.join('');

            }
        })
    });
    columns.push({
        field : 'optionals',
        align: 'center',
        title: '操作',
        formatter: function (value, row, index) {
            if(row.isRequire == '0') {
                var value = $.common.isNotEmpty(row.index) ? row.index : $.table.serialNumber(index);
                return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="deleteRow(\'bootstrap-table\',\'' + value + '\')"><i class="fa fa-remove"></i>删除</a>';
            }else{
                return '';
            }
        }
    });
    initTable(columns);

}
// 刷新表格
function refreshTable(columns) {
    var options = {
        columns: columns
    };
    $("#" + table.options.id).bootstrapTable('refreshOptions',options);
}
// 初始化表格
function initTable(columns){
    $.table.destroy("bootstrap-table");
    let firstCategoryidVal = $("#firstCategoryid").val();
    if (firstCategoryidVal == null || firstCategoryidVal == "") {
        return;
    }
    let shopCode = $("#shopCode").val();
    if (shopCode == null || shopCode == "") {
        return;
    }
    let listingIds =[];
    if ( $.common.isNotEmpty(saleGoodsDTOList) && saleGoodsDTOList.length > 0 ){
        saleGoodsDTOList.forEach(function(item) {
            listingIds.push(item.goodsHeadId);
        });
    }
    let source = typeof sourceType !== 'undefined' ? sourceType : 'normal';
    $.ajax({
        type: "post",
        dataType: "json",
        data: {"categoryId": firstCategoryidVal, "attributeType": 3, "shopCode": shopCode,"listingIds": listingIds, "source": source},
        // url: ctx + "publication/goods/property" + "/getAllFieldByCategoryId/"+$("#firstCategoryid").val()+"/3/" + $("#goodsCode").val()+"/"+shopCode,
        url: ctx + "publication/goods/property/getAllFieldByCategoryId",
        success: function(data) {
            let rows = data.rows;
            rows.forEach(function( row) {
                saleGoodsDTOList.forEach(function(item) {
                    let newValue = row.attributeValue;
                    if (item.listingAmazonAttributeLinesInfo != null){
                        item.listingAmazonAttributeLinesInfo.forEach(function(lines) {
                            if(lines.tableName == row.attributeCode){
                                newValue = lines.tableValue;
                            }
                        });
                    }
                    let newAttributeValue = "attributeValue"+item.goodsCode;
                    row[newAttributeValue] = newValue;
                });
            });
            appendAttrDefaultValue(columns, data.rows, function (rows) {
                var options = {
                    type: 'post',
                    showSearch: false,
                    showRefresh: false,
                    showToggle: false,
                    showColumns: false,
                    columns: columns,
                    pagination: false,
                    data: rows,
                    fixedColumns: true,
                    fixedNumber: 3,
                };
                $.table.init(options);
            });
        }
    });
}


function appendAttrDefaultValue(columns, list, callback) {
    // 解析商品编码，格式：attributeValueKX1ALT00100U1(1)，解析为：KX1ALT00100U1
    let goodsCodes = []
    for (let column of columns) {
        if (column.field.startsWith('attributeValue')) {
            let tempCode = column.field.substring(14, column.field.length);
            // 去掉(),使用正则
            let goodsCode = tempCode.match(/^([^(\s]+)/)[1];
            goodsCodes.push(goodsCode)
        }
    }

    let obj = {}
    obj.shopCode = $("#shopCode").val();
    obj.categoryId = $("#categoryId").val();
    obj.goodsCode = goodsCodes
    obj.brandCode = $("#brandCode").val();
    obj.requiredFieldList = list;
    obj.platformCode = 'EB'
    obj.goodsIds = saleGoodsDTOList.map(item => item.id)
    obj.publishType= $("#publishType").val();

    $.ajax({
        url: prefix + "/refreshAttrDefaultValue",
        type: "post",
        data: JSON.stringify(obj),
        contentType: "application/json",
        success: function (result) {
            if (result.code == web_status.SUCCESS) {
                let data = result.data
                filter(data, list)
                callback(list)
            } else {
                $.modal.msgError(result.msg);
            }
        }
    });
}


function filter(data, list) {
    for (let datum of data) {
        list.forEach((item) => {
            for (let key in item) {
                if (key.includes(datum.goodsCode)) {
                    let target = datum.requiredFieldList.find((field) => field.attributeCode === item.attributeCode);
                    if(target) {
                        item[key] = target.attributeValue
                    }
                }
            }
        });

    }
}

//刷新属性数据
function refreshTransmitAttributeData() {
    transmitAttributeArr=new Array();
    var dataColumns = [];
    if ($.common.isEmpty(table.options.columns)){
        return;
    }
    for (var columnIndex = 0; columnIndex < table.options.columns.length; columnIndex++) {
        if (table.options.columns[columnIndex].visible != false) {
            dataColumns.push(table.options.columns[columnIndex]);
        }
    }

    var params = new Array();
    var data = $("#" + table.options.id).bootstrapTable('getData');
    var count = data.length;
    for (var dataIndex = 0; dataIndex < count; dataIndex++) {
        var columns = $('#' + table.options.id + ' tr[data-index="' + dataIndex + '"] td');
        var obj = new Object();
        for (var i = 0; i < columns.length; i++) {
            var isRequireInfo = i!=0 && i!=1 && i!=2 && i != columns.length-1 ? true : false;

            var inputValue = $(columns[i]).find('input');
            var selectValue = $(columns[i]).find('select');
            var textareaValue = $(columns[i]).find('textarea');
            var key = dataColumns[i].field;

            var transmitObj = new Object();
            if ($.common.isNotEmpty(inputValue.val())) {
                obj[key] = inputValue.val();

                if (isRequireInfo){
                    transmitObj['tableValue'] = inputValue.val();
                    transmitObj['tableType'] = inputValue.data('type');
                    transmitObj['tableName'] = (inputValue.data('key') == "undefined" ? $(columns[1]).find('input').val() : inputValue.data('key'))  ;
                    transmitObj['pdmGoodsCode'] = inputValue.data('goodscode');
                }
            } else if ($.common.isNotEmpty(selectValue.val())) {
                obj[key] = selectValue.val();

                if (isRequireInfo){
                    transmitObj['tableValue'] = selectValue.val();
                    transmitObj['tableType'] = selectValue.data('type');
                    transmitObj['tableName'] = (selectValue.data('key') == "undefined" ? $(columns[1]).find('select').val() : selectValue.data('key'))  ;
                    transmitObj['pdmGoodsCode'] = selectValue.data('goodscode');
                }
            } else if ($.common.isNotEmpty(textareaValue.val())) {
                obj[key] = textareaValue.val();

                if (isRequireInfo){
                    transmitObj['tableValue'] = textareaValue.val();
                    transmitObj['tableType'] = textareaValue.data('type');
                    transmitObj['tableName'] = (textareaValue.data('key') == "undefined" ? $(columns[1]).find('textarea').val() : textareaValue.data('key'))  ;
                    transmitObj['pdmGoodsCode'] = textareaValue.data('goodscode');
                }
            } else {
                if (key == "index" && $.common.isNotEmpty(data[dataIndex].index)) {
                    obj[key] = data[dataIndex].index;
                } else {
                    obj[key] = "";
                }
            }

            //需要的字段  并且不为空
            if (isRequireInfo && JSON.stringify(transmitObj)!=JSON.stringify(new Object()) ){
                transmitAttributeArr.push(transmitObj);
            }
        }
        var item = data[dataIndex];
        var extendObj = $.extend({}, item, obj);
        params.push({ index: dataIndex, row: extendObj });
    }
    $("#" + table.options.id).bootstrapTable("updateRow", params);
}


function  attributeAppli(e){
    let val = $(e).parent().parent().find("#tableValue").val();
    $(e).parent().parent().parent().find('input[name="tableValue"]').each(function(){
        $(this).val(val);
        //  $(this).attr("value",val);
    });
    $(e).parent().parent().parent().find('select[name="tableValue"]').each(function(){
        $(this).val(val);
    });
}

