package com.suncent.smc.provider.biz.promotion.batch;

import cn.hutool.core.collection.CollUtil;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.persistence.bi.entity.RpaDbPromotionCentral;
import com.suncent.smc.persistence.bi.entity.RpaDbPromotionCentralDetails;
import com.suncent.smc.provider.biz.promotion.PromotionManagementBiz;
import com.suncent.smc.provider.biz.promotion.sync.PromotionSyncHandler;
import com.suncent.smc.provider.biz.promotion.sync.PromotionSyncHandlerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 促销数据批处理同步服务
 * 负责管理促销数据的批量同步，包括主表和详情表的关联处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
@Slf4j
public class PromotionBatchSyncService {

    private static final String PROMOTION_THREAD_POOL_NAME = "promotionSyncPool";
    // 每批处理50个促销活动
    private static final int DEFAULT_BATCH_SIZE = 50;
    // 详情表处理的批次大小（避免单个促销活动数据量过大）
    private static final int DETAILS_BATCH_SIZE = 20;

    @Autowired
    private ThreadPoolForMonitorManager threadPoolManager;

    @Autowired
    private PromotionSyncHandlerFactory handlerFactory;

    /**
     * 批量同步促销数据（主表+详情表关联处理）
     *
     * @param promotionType  促销类型
     * @param mainTableData  主表数据
     * @param detailsDataMap 详情表数据映射（key: promotionId, value: 详情列表）
     * @param fullSync       是否全量同步
     * @return 综合同步结果
     */
    public PromotionManagementBiz.ComprehensiveResult batchSyncPromotionData(
            String promotionType,
            List<RpaDbPromotionCentral> mainTableData,
            Map<String, List<RpaDbPromotionCentralDetails>> detailsDataMap,
            boolean fullSync) {

        log.info("开始批量同步{}促销数据，主表数据量: {}, 详情数据覆盖促销数: {}",
                promotionType, mainTableData.size(), detailsDataMap.size());

        PromotionManagementBiz.ComprehensiveResult result = new PromotionManagementBiz.ComprehensiveResult();
        result.setStartTime(new Date());
        result.setOperationType(fullSync ? "FULL_SYNC" : "INCREMENTAL_SYNC");

        try {
            // 获取处理器
            PromotionSyncHandler handler = handlerFactory.getHandler(promotionType);
            if (handler == null) {
                result.setSuccess(false);
                result.setErrorMessage("未找到支持促销类型 " + promotionType + " 的处理器");
                return result;
            }

            // 获取线程池
            ThreadPoolExecutor executor = threadPoolManager.getThreadPoolExecutor(PROMOTION_THREAD_POOL_NAME);

            // 1. 批量处理主表数据
            PromotionManagementBiz.SyncResult mainTableResult = batchProcessMainTable(
                    handler, mainTableData, executor, fullSync);
            result.setMainTableResult(mainTableResult);

            if (!mainTableResult.isSuccess()) {
                result.setSuccess(false);
                result.setErrorMessage("主表数据同步失败: " + mainTableResult.getErrorMessage());
                return result;
            }

            // 2. 批量处理详情表数据（如果支持）
            if (handler.supportsDetailsSync() && !detailsDataMap.isEmpty()) {
                PromotionManagementBiz.DetailsSyncResult detailsResult = batchProcessDetailsTable(
                        handler, detailsDataMap, executor);
                result.setDetailsResult(detailsResult);

                if (!detailsResult.isSuccess()) {
                    log.warn("详情数据同步失败: {}", detailsResult.getErrorMessage());
                    // 详情同步失败不影响整体结果
                }
            }

            result.generateSummary();
            result.setSuccess(true);

            log.info("{}促销数据批量同步完成，总耗时: {}ms", promotionType, result.getTotalDuration());

        } catch (Exception e) {
            log.error("批量同步促销数据失败", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        } finally {
            result.setEndTime(new Date());
        }

        return result;
    }

    /**
     * 批量处理主表数据
     */
    private PromotionManagementBiz.SyncResult batchProcessMainTable(
            PromotionSyncHandler handler,
            List<RpaDbPromotionCentral> mainTableData,
            ThreadPoolExecutor executor,
            boolean fullSync) {

        log.info("开始批量处理主表数据，数据量: {}", mainTableData.size());

        PromotionManagementBiz.SyncResult result = new PromotionManagementBiz.SyncResult();
        result.setPromotionType(handler.getSupportedPromotionType().getCode());
        result.setFullSync(fullSync);
        result.setStartTime(new Date());
        result.setTotalCount(mainTableData.size());

        try {
            // 分批处理
            List<List<RpaDbPromotionCentral>> batches = CollUtil.split(mainTableData, DEFAULT_BATCH_SIZE);
            AtomicInteger completedBatches = new AtomicInteger(0);

            // 并行处理各批次
            List<CompletableFuture<PromotionManagementBiz.SyncResult>> futures = batches.stream()
                    .map(batch -> CompletableFuture.supplyAsync(() -> {
                        try {
                            PromotionManagementBiz.SyncResult batchResult = handler.syncPromotions(batch, fullSync);
                            int completed = completedBatches.incrementAndGet();
                            log.info("主表批次处理进度: {}/{}", completed, batches.size());
                            return batchResult;
                        } catch (Exception e) {
                            log.error("主表批次处理失败", e);
                            PromotionManagementBiz.SyncResult errorResult = new PromotionManagementBiz.SyncResult();
                            errorResult.setSuccess(false);
                            errorResult.setErrorMessage(e.getMessage());
                            return errorResult;
                        }
                    }, executor))
                    .collect(Collectors.toList());

            // 等待所有批次完成并合并结果
            for (CompletableFuture<PromotionManagementBiz.SyncResult> future : futures) {
                PromotionManagementBiz.SyncResult batchResult = future.get();
                result.merge(batchResult);
            }

            result.setSuccess(true);
            result.setEndTime(new Date());

            log.info("主表数据批量处理完成，处理总数: {}, 新增: {}, 更新: {}, 跳过: {}, 失败: {}",
                    result.getTotalCount(), result.getInsertCount(), result.getUpdateCount(),
                    result.getSkipCount(), result.getErrorCount());

        } catch (Exception e) {
            log.error("主表数据批量处理异常", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(new Date());
        }

        return result;
    }

    /**
     * 批量处理详情表数据
     */
    private PromotionManagementBiz.DetailsSyncResult batchProcessDetailsTable(
            PromotionSyncHandler handler,
            Map<String, List<RpaDbPromotionCentralDetails>> detailsDataMap,
            ThreadPoolExecutor executor) {

        log.info("开始批量处理详情表数据，促销活动数: {}", detailsDataMap.size());

        PromotionManagementBiz.DetailsSyncResult result = new PromotionManagementBiz.DetailsSyncResult();
        result.setStartTime(new Date());

        try {
            AtomicInteger completedPromotions = new AtomicInteger(0);
            int totalPromotions = detailsDataMap.size();

            // 分批处理促销活动，避免线程池过载
            List<List<Map.Entry<String, List<RpaDbPromotionCentralDetails>>>> promotionBatches =
                    CollUtil.split(new ArrayList<>(detailsDataMap.entrySet()), DETAILS_BATCH_SIZE);

            log.info("详情数据分批处理，总批次: {}, 每批处理: {}个促销活动", promotionBatches.size(), DETAILS_BATCH_SIZE);

            // 并行处理各批次的促销活动详情数据
            List<CompletableFuture<PromotionManagementBiz.DetailsSyncResult>> futures = promotionBatches.stream()
                    .map(batch -> CompletableFuture.supplyAsync(() -> {
                        PromotionManagementBiz.DetailsSyncResult batchResult = new PromotionManagementBiz.DetailsSyncResult();
                        batchResult.setStartTime(new Date());

                        for (Map.Entry<String, List<RpaDbPromotionCentralDetails>> entry : batch) {
                            String promotionId = entry.getKey();
                            List<RpaDbPromotionCentralDetails> promotionDetails = entry.getValue();
                            try {
                                // 针对BestDeal处理器，直接调用单个促销活动的详情同步方法
                                PromotionManagementBiz.DetailsSyncResult promotionResult;
                                if (handler instanceof com.suncent.smc.provider.biz.promotion.sync.impl.BestDealSyncHandler) {
                                    com.suncent.smc.provider.biz.promotion.sync.impl.BestDealSyncHandler bestDealHandler =
                                            (com.suncent.smc.provider.biz.promotion.sync.impl.BestDealSyncHandler) handler;
                                    // 直接调用单个促销活动的同步方法，避免重复处理
                                    promotionResult = bestDealHandler.syncBestDealDetails(promotionId);
                                } else {
                                    // 其他处理器的通用处理方式
                                    promotionResult = handler.syncPromotionDetails(null);
                                }

                                // 合并单个促销活动的结果到批次结果
                                batchResult.merge(promotionResult);

                                int completed = completedPromotions.incrementAndGet();
                                log.info("详情处理进度: {}/{}, promotionId: {}, 处理数量: {}",
                                        completed, totalPromotions, promotionId, promotionDetails.size());

                            } catch (Exception e) {
                                log.error("详情处理失败，promotionId: {}, 数据量: {}", promotionId, promotionDetails.size(), e);
                                batchResult.setErrorCount(batchResult.getErrorCount() + promotionDetails.size());
                                batchResult.setSuccess(false);
                                if (batchResult.getErrorMessage() == null) {
                                    batchResult.setErrorMessage(e.getMessage());
                                }
                            }
                        }

                        batchResult.setEndTime(new Date());
                        return batchResult;
                    }, executor))
                    .collect(Collectors.toList());

            // 等待所有详情处理完成并合并结果
            int completedBatches = 0;
            for (CompletableFuture<PromotionManagementBiz.DetailsSyncResult> future : futures) {
                PromotionManagementBiz.DetailsSyncResult batchResult = future.get();
                result.merge(batchResult);
                completedBatches++;
                log.info("详情批次处理进度: {}/{}, 当前批次结果: 新增={}, 更新={}, 跳过={}, 失败={}",
                        completedBatches, futures.size(),
                        batchResult.getInsertCount(), batchResult.getUpdateCount(),
                        batchResult.getSkipCount(), batchResult.getErrorCount());
            }

            result.setSuccess(true);
            result.setEndTime(new Date());

            log.info("详情表数据批量处理完成，处理总数: {}, 新增: {}, 更新: {}, 跳过: {}, 失败: {}",
                    result.getTotalCount(), result.getInsertCount(), result.getUpdateCount(),
                    result.getSkipCount(), result.getErrorCount());

        } catch (Exception e) {
            log.error("详情表数据批量处理异常", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(new Date());
        }

        return result;
    }


}
