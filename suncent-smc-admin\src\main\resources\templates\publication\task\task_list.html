<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
  <th:block th:include="include :: header('SMC-任务管理列列表')" />
  <th:block th:include="include :: select2-css"/>
</head>
<body class="gray-bg">
<div class="container-div">
  <div class="row">
    <div class="col-sm-12 search-collapse">
      <form id="formId">
        <div class="select-list">
          <ul>
            <li class="show-4">
              <label>平台：</label>
              <select name="platform" id="platform" class="form-control">
                <option value="">请选择</option>
                <option value="AM">AM</option>
                <option value="EB">EB</option>
                <option value="EB_V2">EB_V2</option>
                  <option value="TEMU">TEMU</option>
              </select>
            </li>
            <li class="show-4">
              <label>任务类型：</label>
              <select name="taskType" id="taskType" class="form-control">
                <option value="">请选择</option>
              </select>
            </li>
            <li class="select-time show-4">
              <label>创建时间：</label>
              <input style="width: 135px;" type="text" class="time-input" id="startTime"
                     placeholder="开始时间"
                     name="params[beginTime]" />
              <span style="padding: 0px 8px;">-</span>
              <input style="width: 135px;" type="text" class="time-input" id="endTime"
                     placeholder="结束时间"
                     name="params[endTime]" />
            </li>
            <li class="show-4" th:if="${isAdmin}">
                <label>创建人：</label>
                <select  name="createBy" id="createBy" class="form-control m-b" data-allow-clear="true" data-placeholder="请选择创建人">
                </select>
            </li>
            <li class="show-4">
              <label >任务编号：</label>
              <input type="text" style="width: 235px"name="taskName" id="taskName" placeholder="请输入任务编号"/>
            </li>
            <li class="show-4">
              <label>任务状态： </label>
              <select name="taskStatus" id="taskStatus" class="form-control">
                <option value="">请选择</option>
                <option value="待处理">待处理</option>
                <option value="进行中">进行中</option>
                <option value="已完成">已完成</option>
                <option value="已确认">已确认</option>
              </select>
            </li>

            <li class="show-4">
              <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
              <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
            </li>
          </ul>
        </div>
      </form>
    </div>
    <div class="col-sm-12 select-table table-striped">
      <table id="bootstrap-table"></table>
    </div>
  </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: select2-js"/>
<script th:inline="javascript">
  var userDatas = [[${@sysBaseConfig.getSysUserNameAllKVList()}]];
  var prefix = ctx + "publication/task";

  function initCreate() {
    var html = "<option value=''>请选择</option>";
    $(userDatas).each(function (index, row) {
      html += "<option value='" + row.key + "'>" + row.value + "</option>";
    })
    $("#createBy").html(html);
  }

  $(function() {
    initTaskType();

    var options = {
      url: prefix + "/list",
      createUrl: prefix + "/add",
      updateUrl: prefix + "/edit/{id}",
      removeUrl: prefix + "/remove",
      exportUrl: prefix + "/export",
      modalName: "SMC-任务管理列",
      columns: [{
        checkbox: true
      },
        {
          field: 'id',
          title: '主键ID',
          visible: false
        },
        {
          field: 'platform',
          title: '平台'
        },
        {
          field: 'taskType',
          title: '任务类型'
        },
        {
          field: 'taskName',
          title: '任务编号'
        },
        {
          field: 'taskNum',
          title: '任务数量',
          formatter: function(value, row, index) {
            return '<a href="javascript:void(0)" onclick="jump(\'' + row.id + '\', \'' + "sum" + '\',\'' + row.platform + '\')"><span style="color: blue">' + value + '</span></a>';

          }
        },
        {
          field: 'successNum',
          title: '成功数量',
          formatter: function(value, row, index) {
            if (value == '0'){
              return '<span style="color: green">'+value+'</span>';
            }else {
              return '<a href="javascript:void(0)" onclick="jump(\'' + row.id + '\', \'' + "success" + '\',\'' + row.platform + '\')"><span style="color: green">' + value + '</span></a>';
            }
          }
        },
        {
          field: 'errorNum',
          title: '失败数量',
          formatter: function(value, row, index) {
            //如果value的值为0，不做跳转
            if (value == '0'){
              return '<span style="color: red">'+value+'</span>';
            }else {
              return '<a href="javascript:void(0)" onclick="jump(\'' + row.id + '\', \'' + "fail" + '\',\'' + row.platform + '\')"><span style="color: red">' + value + '</span></a>';
            }
          }
        },
        {
          field: 'taskStatus',
          title: '任务状态'
        },
        {
          field: 'createBy',
          title: '创建人' ,
          formatter: function (value, row, index) {
            var label = '';
            $(userDatas).each(function(i,site){
              if(site.key == value){
                label = site.value;
                return false;
              }
            });
            if (value == '-1'){
              return 'system';
            }
            if (label == '')
              return value;
            else
              return label;
          }
        },{
          field: 'createTime',
          title: '创建时间'
        },{
          field: 'updateTime',
          title: '最后更新时间'
        },{
          field: 'remark',
          title: '备注'
        },
        {
          title: '操作',
          align: 'center',
          formatter: function(value, row, index) {
            var actions = [];
            if (row.taskType=="临时保存"){
              actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="confirm(\'' + row.id + '\',\'' + row.taskStatus + '\')"><i class="fa fa-edit"></i>确认任务</a> ');
            }
            if (row.taskType=="导入修改"){
              actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="downLoadFile(\'' + row.url + '\')"><i class="fa fa-edit"></i>下载文件</a> ');

            }
            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="logInfo(\'' + row.id + '\')"><i class="fa fa-edit"></i>日志</a> ');
            if (row.taskType=="定时重上架" || row.taskType=="定时刊登"){
              actions.push('<a class="btn btn-warning btn-xs " href="javascript:void(0)" onclick="cancelTask(\'' + row.id  + '\')"><i class="fa fa-edit"></i>取消任务</a> ');
            }
            return actions.join('');
          }
        }]
    };
    $.table.init(options);

    //初始化创建人
    initCreate();
  });

  //批量取消定时刊登
  function cancelTask(taskId) {
    $.modal.confirm("确认要取消该定时任务吗?", function () {
      var url = prefix + "/cancelTask/"+taskId;
      $.operate.post(url, "", function (result) {
        if (result.code == web_status.SUCCESS) {
          $.modal.alertSuccess(result.msg);
          $.table.refresh();
        } else {
          $.modal.alertError(result.msg);
        }
      });
    });
  }
  function logInfo(id) {
    var options = {
      title: "日志信息",
      url: prefix + "/log/" + id,
      width: $(window).width() - 200,
      height: $(window).height() - 50,
      yes: false,
      btn: ['<i class="fa fa-close"></i> 关闭'],
    };
    $.modal.openOptions(options);
  }

  function confirm(id,taskStatus) {
    if (taskStatus == '进行中'){
      $.modal.alertWarning("任务正在进行中，请稍后确认！");
      return;
    }
    if (taskStatus == '已确认'){
      $.modal.alertWarning("任务已确认，请不要重复确认！");
      return;
    }
    var options = {
      title: "刊登任务详情",
      url: prefix + "/taskInfo/" + id,
      width: $(window).width() - 200,
      height: $(window).height() - 50,
      yes: false,
      btn: false
    };
    $.modal.openOptions(options);
  }

  function jump(id,type,platform){
    //创建一个新的标签页
    if ($.common.isEmpty(id)||$.common.isEmpty(type)||$.common.isEmpty(platform)){
      return;
    }
    var name= $.common.equals(platform,"EB_V2")? 'Ebay listing管理' : 'Listing管理';
    $.modal.openTab(name, "/publication/listing/"+platform+"/"+type+"/"+id);
  }

  function  downLoadFile(url){
    if (url== null || url == ""||url== "null"){
      $.modal.alertWarning("无文件可下载！");
      return;
    }
    var link = document.createElement('a');
    link.href = url;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  function initTaskType(){
    var config = {
      url: ctx + "publication/task/selectTaskTypeEnum",
      contentType: "application/json; charset=utf-8",
      dataType: 'JSON',
      type: 'POST',
      success: function (result) {
        if (result.code == web_status.SUCCESS) {
          //optinonOne 不为空时，不显示请选择
          var html  = "<option value=''>--请选择--</option>";
          $(result.data).each(function (index, row) {
            html+= "<option value='"+row+"'>"+row+"</option>";
          })
          $("#taskType").html(html);
        }
      }
    };
    $.ajax(config)
  }


  function reset() {
    $('#platform').val("").trigger('change');
    $('#taskType').val("").trigger('change');
    $('#startTime').val("").trigger('change');
    $('#endTime').val("").trigger('change');
    $('#taskName').val("").trigger('change');
    $('#createBy').val("").trigger('change');
      $('#taskStatus').val("").trigger('change');
  }
</script>
</body>
</html>