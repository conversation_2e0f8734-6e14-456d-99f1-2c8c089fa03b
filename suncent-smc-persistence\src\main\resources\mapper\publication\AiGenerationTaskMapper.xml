<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suncent.smc.persistence.publication.mapper.AiGenerationTaskMapper">
    
    <resultMap type="AiGenerationTask" id="AiGenerationTaskResult">
        <result property="id"                    column="id"                    />
        <result property="taskCorrelationId"    column="task_correlation_id"   />
        <result property="taskId"               column="task_id"               />
        <result property="taskType"             column="task_type"             />
        <result property="taskStatus"           column="task_status"           />
        <result property="goodsCodes"           column="goods_codes"           />
        <result property="aiResult"             column="ai_result"             />
        <result property="shopCode"             column="shop_code"             />
        <result property="brandCode"            column="brand_code"            />
        <result property="delFlag"              column="del_flag"              />
        <result property="processStatus"        column="process_status"        />
        <result property="createBy"             column="create_by"             />
        <result property="createTime"           column="create_time"           />
        <result property="updateBy"             column="update_by"             />
        <result property="updateTime"           column="update_time"           />
    </resultMap>

    <sql id="selectAiGenerationTaskVo">
        select id, task_correlation_id, task_id, task_type, task_status, goods_codes, ai_result,
               shop_code, brand_code, del_flag, process_status, create_by, create_time, update_by, update_time
        from sc_smc_ai_generation_task
    </sql>

    <select id="selectAiGenerationTaskList" parameterType="AiGenerationTask" resultMap="AiGenerationTaskResult">
        <include refid="selectAiGenerationTaskVo"/>
        <where>
            del_flag = 0
            <if test="taskCorrelationId != null and taskCorrelationId != ''"> and task_correlation_id = #{taskCorrelationId}</if>
            <if test="taskType != null and taskType != ''"> and task_type = #{taskType}</if>
            <if test="taskStatus != null and taskStatus != ''"> and task_status = #{taskStatus}</if>
            <if test="shopCode != null and shopCode != ''"> and shop_code = #{shopCode}</if>
            <if test="brandCode != null and brandCode != ''"> and brand_code = #{brandCode}</if>
            <if test="taskStatusList != null and taskStatusList.size() > 0"> and task_status in
                <foreach item="item" index="index" collection="taskStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="taskId != null"> and task_id = #{taskId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAiGenerationTaskById" parameterType="String" resultMap="AiGenerationTaskResult">
        <include refid="selectAiGenerationTaskVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <select id="selectAiGenerationTaskByCorrelationId" parameterType="String" resultMap="AiGenerationTaskResult">
        <include refid="selectAiGenerationTaskVo"/>
        where task_correlation_id = #{taskCorrelationId} and del_flag = 0
    </select>

    <select id="selectPendingTasks" parameterType="String" resultMap="AiGenerationTaskResult">
        <include refid="selectAiGenerationTaskVo"/>
        where task_status = 'PENDING' and del_flag = 0
        <if test="taskType != null and taskType != ''"> and task_type = #{taskType}</if>
        order by create_time asc
    </select>
    <select id="countTaskByStatus" resultType="java.lang.Integer">
        select count(1) from sc_smc_ai_generation_task
        where task_id = #{taskId} and process_status = #{processStatus} and del_flag = 0
    </select>

    <insert id="insertAiGenerationTask" parameterType="AiGenerationTask" useGeneratedKeys="true" keyProperty="id">
        insert into sc_smc_ai_generation_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskCorrelationId != null">task_correlation_id,</if>
            <if test="taskType != null">task_type,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="goodsCodes != null">goods_codes,</if>
            <if test="aiResult != null">ai_result,</if>
            <if test="shopCode != null">shop_code,</if>
            <if test="brandCode != null">brand_code,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskCorrelationId != null">#{taskCorrelationId},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="goodsCodes != null">#{goodsCodes},</if>
            <if test="aiResult != null">#{aiResult},</if>
            <if test="shopCode != null">#{shopCode},</if>
            <if test="brandCode != null">#{brandCode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAiGenerationTask" parameterType="AiGenerationTask">
        update sc_smc_ai_generation_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskCorrelationId != null">task_correlation_id = #{taskCorrelationId},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="goodsCodes != null">goods_codes = #{goodsCodes},</if>
            <if test="aiResult != null">ai_result = #{aiResult},</if>
            <if test="shopCode != null">shop_code = #{shopCode},</if>
            <if test="brandCode != null">brand_code = #{brandCode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateTaskStatusAndResult">
        update sc_smc_ai_generation_task
        set task_status = #{taskStatus},
            ai_result = #{aiResult},
            update_time = now()
        where task_correlation_id = #{taskCorrelationId} and del_flag = 0 and task_status = #{beforeStatus}
    </update>

    <update id="updateProcessStatus">
        update sc_smc_ai_generation_task
        set process_status = #{processStatus},
            update_time = now()
        where task_correlation_id = #{taskCorrelationId} and del_flag = 0
    </update>

    <delete id="deleteAiGenerationTaskById" parameterType="String">
        update sc_smc_ai_generation_task set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteAiGenerationTaskByIds" parameterType="String">
        update sc_smc_ai_generation_task set del_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
