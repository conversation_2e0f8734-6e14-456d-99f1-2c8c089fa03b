<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suncent.smc.persistence.promotion.mapper.AmBestDealAsinMapper">
    
    <resultMap type="AmBestDealAsin" id="AmBestDealAsinResult">
        <result property="id"                    column="id"                    />
        <result property="refBestDealId"         column="ref_best_deal_id"      />
        <result property="headId"                column="head_id"               />
        <result property="promotionId"           column="promotion_id"          />
        <result property="platformGoodsId"       column="platform_goods_id"     />
        <result property="platformGoodsCode"     column="platform_goods_code"   />
        <result property="pdmGoodsCode"          column="pdm_goods_code"        />
        <result property="standardPrice"         column="standard_price"        />
        <result property="referencePrice"        column="reference_price"       />
        <result property="expectedDemand"        column="expected_demand"       />
        <result property="committedUnits"        column="committed_units"       />
        <result property="lowestDiscount"        column="lowest_discount"       />
        <result property="actualDiscount"        column="actual_discount"       />
        <result property="dealPrice"             column="deal_price"            />
        <result property="perUnitFunding"        column="per_unit_funding"      />
        <result property="createBy"              column="create_by"             />
        <result property="createTime"            column="create_time"           />
        <result property="updateBy"              column="update_by"             />
        <result property="updateTime"            column="update_time"           />
        <result property="remark"                column="remark"                />
        <result property="delFlag"               column="del_flag"              />
    </resultMap>

    <sql id="selectAmBestDealAsinVo">
        select id, ref_best_deal_id, head_id, promotion_id, platform_goods_id, platform_goods_code, pdm_goods_code, standard_price, reference_price, expected_demand, committed_units, lowest_discount, actual_discount, deal_price, per_unit_funding, create_by, create_time, update_by, update_time, remark, del_flag from am_best_deal_asin
    </sql>

    <select id="selectAmBestDealAsinList" parameterType="AmBestDealAsin" resultMap="AmBestDealAsinResult">
        <include refid="selectAmBestDealAsinVo"/>
        <where>  
            <if test="refBestDealId != null "> and ref_best_deal_id = #{refBestDealId}</if>
            <if test="promotionId != null  and promotionId != ''"> and promotion_id = #{promotionId}</if>
            <if test="platformGoodsId != null  and platformGoodsId != ''"> and platform_goods_id = #{platformGoodsId}</if>
            <if test="platformGoodsCode != null  and platformGoodsCode != ''"> and platform_goods_code = #{platformGoodsCode}</if>
            <if test="pdmGoodsCode != null  and pdmGoodsCode != ''"> and pdm_goods_code = #{pdmGoodsCode}</if>
            and del_flag = 0
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAmBestDealAsinById" parameterType="Long" resultMap="AmBestDealAsinResult">
        <include refid="selectAmBestDealAsinVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <select id="selectAmBestDealAsinByRefId" parameterType="Long" resultMap="AmBestDealAsinResult">
        <include refid="selectAmBestDealAsinVo"/>
        where ref_best_deal_id = #{refBestDealId} and del_flag = 0
        order by create_time desc
    </select>

    <select id="selectAmBestDealAsinByAsin" parameterType="String" resultMap="AmBestDealAsinResult">
        <include refid="selectAmBestDealAsinVo"/>
        where platform_goods_id = #{platformGoodsId} and del_flag = 0
        order by create_time desc
    </select>
        
    <insert id="insertAmBestDealAsin" parameterType="AmBestDealAsin" useGeneratedKeys="true" keyProperty="id">
        insert into am_best_deal_asin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refBestDealId != null">ref_best_deal_id,</if>
            <if test="headId != null">head_id,</if>
            <if test="promotionId != null and promotionId != ''">promotion_id,</if>
            <if test="platformGoodsId != null and platformGoodsId != ''">platform_goods_id,</if>
            <if test="platformGoodsCode != null and platformGoodsCode != ''">platform_goods_code,</if>
            <if test="pdmGoodsCode != null and pdmGoodsCode != ''">pdm_goods_code,</if>
            <if test="standardPrice != null">standard_price,</if>
            <if test="referencePrice != null">reference_price,</if>
            <if test="expectedDemand != null">expected_demand,</if>
            <if test="committedUnits != null">committed_units,</if>
            <if test="lowestDiscount != null">lowest_discount,</if>
            <if test="actualDiscount != null">actual_discount,</if>
            <if test="dealPrice != null">deal_price,</if>
            <if test="perUnitFunding != null">per_unit_funding,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            del_flag
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refBestDealId != null">#{refBestDealId},</if>
            <if test="headId != null">#{headId},</if>
            <if test="promotionId != null and promotionId != ''">#{promotionId},</if>
            <if test="platformGoodsId != null and platformGoodsId != ''">#{platformGoodsId},</if>
            <if test="platformGoodsCode != null and platformGoodsCode != ''">#{platformGoodsCode},</if>
            <if test="pdmGoodsCode != null and pdmGoodsCode != ''">#{pdmGoodsCode},</if>
            <if test="standardPrice != null">#{standardPrice},</if>
            <if test="referencePrice != null">#{referencePrice},</if>
            <if test="expectedDemand != null">#{expectedDemand},</if>
            <if test="committedUnits != null">#{committedUnits},</if>
            <if test="lowestDiscount != null">#{lowestDiscount},</if>
            <if test="actualDiscount != null">#{actualDiscount},</if>
            <if test="dealPrice != null">#{dealPrice},</if>
            <if test="perUnitFunding != null">#{perUnitFunding},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            0
         </trim>
    </insert>

    <update id="updateAmBestDealAsin" parameterType="AmBestDealAsin">
        update am_best_deal_asin
        <trim prefix="SET" suffixOverrides=",">
            <if test="refBestDealId != null">ref_best_deal_id = #{refBestDealId},</if>
            <if test="headId != null">head_id = #{headId},</if>
            <if test="promotionId != null and promotionId != ''">promotion_id = #{promotionId},</if>
            <if test="platformGoodsId != null and platformGoodsId != ''">platform_goods_id = #{platformGoodsId},</if>
            <if test="platformGoodsCode != null and platformGoodsCode != ''">platform_goods_code = #{platformGoodsCode},</if>
            <if test="pdmGoodsCode != null and pdmGoodsCode != ''">pdm_goods_code = #{pdmGoodsCode},</if>
            <if test="standardPrice != null">standard_price = #{standardPrice},</if>
            <if test="referencePrice != null">reference_price = #{referencePrice},</if>
            <if test="expectedDemand != null">expected_demand = #{expectedDemand},</if>
            <if test="committedUnits != null">committed_units = #{committedUnits},</if>
            <if test="lowestDiscount != null">lowest_discount = #{lowestDiscount},</if>
            <if test="actualDiscount != null">actual_discount = #{actualDiscount},</if>
            <if test="dealPrice != null">deal_price = #{dealPrice},</if>
            <if test="perUnitFunding != null">per_unit_funding = #{perUnitFunding},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- UPSERT 操作：插入或更新 ASIN 记录（基于 promotion_id + platform_goods_id 唯一约束） -->
    <insert id="upsertAmBestDealAsin" parameterType="AmBestDealAsin" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO am_best_deal_asin (ref_best_deal_id,
                                       head_id,
                                       promotion_id,
                                       platform_goods_id,
                                       platform_goods_code,
                                       pdm_goods_code,
                                       standard_price,
                                       reference_price,
                                       expected_demand,
                                       committed_units,
                                       lowest_discount,
                                       actual_discount,
                                       deal_price,
                                       per_unit_funding,
                                       create_by,
                                       create_time,
                                       update_by,
                                       update_time,
                                       remark,
                                       del_flag)
        VALUES (#{refBestDealId},
                #{headId},
                #{promotionId},
                #{platformGoodsId},
                #{platformGoodsCode},
                #{pdmGoodsCode},
                #{standardPrice},
                #{referencePrice},
                #{expectedDemand},
                #{committedUnits},
                #{lowestDiscount},
                #{actualDiscount},
                #{dealPrice},
                #{perUnitFunding},
                #{createBy},
                #{createTime},
                #{updateBy},
                #{updateTime},
                #{remark},
                0) ON DUPLICATE KEY
        UPDATE
            ref_best_deal_id =
        VALUES (ref_best_deal_id), head_id =
        VALUES (head_id), platform_goods_code =
        VALUES (platform_goods_code), pdm_goods_code =
        VALUES (pdm_goods_code), standard_price =
        VALUES (standard_price), reference_price =
        VALUES (reference_price), expected_demand =
        VALUES (expected_demand), committed_units =
        VALUES (committed_units), lowest_discount =
        VALUES (lowest_discount), actual_discount =
        VALUES (actual_discount), deal_price =
        VALUES (deal_price), per_unit_funding =
        VALUES (per_unit_funding), update_by =
        VALUES (update_by), update_time =
        VALUES (update_time), remark =
        VALUES (remark)
    </insert>

    <delete id="deleteAmBestDealAsinById" parameterType="Long">
        update am_best_deal_asin set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteAmBestDealAsinByIds" parameterType="String">
        update am_best_deal_asin set del_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAmBestDealAsinByRefId" parameterType="Long">
        update am_best_deal_asin set del_flag = 1 where ref_best_deal_id = #{refBestDealId}
    </delete>

    <!-- 根据促销ID查询BD促销ASIN列表（去重，每个promotionId+asin组合只返回最新记录） -->
    <select id="selectLatestAmBestDealAsinByPromotionId" parameterType="String" resultMap="AmBestDealAsinResult">
        SELECT t1.*
        FROM am_best_deal_asin t1
                 INNER JOIN (SELECT promotion_id,
                                    platform_goods_id,
                                    MAX(create_time) as max_create_time,
                                    MAX(id)          as max_id
                             FROM am_best_deal_asin
                             WHERE promotion_id = #{promotionId}
                               AND del_flag = 0
                             GROUP BY promotion_id, platform_goods_id) t2 ON t1.promotion_id = t2.promotion_id
            AND t1.platform_goods_id = t2.platform_goods_id
            AND t1.create_time = t2.max_create_time
            AND t1.id = t2.max_id
        WHERE t1.del_flag = 0
        ORDER BY t1.platform_goods_id
    </select>

    <!-- 清理重复的ASIN记录（保留最新记录） -->
    <delete id="cleanupDuplicateAsinRecords">
        DELETE
        a1 FROM am_best_deal_asin a1
        INNER JOIN (
            SELECT
                promotion_id,
                platform_goods_id,
                MAX(id) as max_id
            FROM am_best_deal_asin
            WHERE del_flag = 0
            GROUP BY promotion_id, platform_goods_id
            HAVING COUNT(id) > 1
        ) a2 ON a1.promotion_id = a2.promotion_id
            AND a1.platform_goods_id = a2.platform_goods_id
        WHERE a1.del_flag = 0
          AND a1.id &lt; a2.max_id
    </delete>

    <!-- 批量更新缺失的head_id和pdm_goods_code -->
    <update id="fillMissingGoodsHeadData">
        UPDATE am_best_deal_asin a
            INNER JOIN sc_smc_listing_goods_head g
        ON a.platform_goods_id = g.platform_goods_id
            SET
                a.head_id = g.id, a.pdm_goods_code = g.pdm_goods_code, a.update_time = NOW()
        WHERE a.del_flag = 0
          AND g.del_flag = 0
          AND g.platform = 'AM'
          AND (a.head_id IS NULL
           OR a.pdm_goods_code IS NULL)
    </update>

    <!-- 原子插入ASIN记录（如果不存在则插入，存在则忽略） -->
    <insert id="insertIgnoreAmBestDealAsin" parameterType="AmBestDealAsin" useGeneratedKeys="true" keyProperty="id">
        INSERT
        IGNORE INTO am_best_deal_asin (
            ref_best_deal_id,
            head_id,
            promotion_id,
            platform_goods_id,
            platform_goods_code,
            pdm_goods_code,
            standard_price,
            reference_price,
            expected_demand,
            committed_units,
            lowest_discount,
            actual_discount,
            deal_price,
            per_unit_funding,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            del_flag
        )
        SELECT #{refBestDealId},
               #{headId},
               #{promotionId},
               #{platformGoodsId},
               #{platformGoodsCode},
               #{pdmGoodsCode},
               #{standardPrice},
               #{referencePrice},
               #{expectedDemand},
               #{committedUnits},
               #{lowestDiscount},
               #{actualDiscount},
               #{dealPrice},
               #{perUnitFunding},
               #{createBy},
               #{createTime},
               #{updateBy},
               #{updateTime},
               #{remark},
               0
        FROM DUAL
        WHERE NOT EXISTS (SELECT 1
                          FROM am_best_deal_asin
                          WHERE promotion_id = #{promotionId}
                            AND platform_goods_id = #{platformGoodsId}
                            AND del_flag = 0)
    </insert>

    <!-- 使用SELECT FOR UPDATE锁定记录进行查询 -->
    <select id="selectForUpdateByPromotionIdAndAsin" resultMap="AmBestDealAsinResult">
        <include refid="selectAmBestDealAsinVo"/>
        WHERE promotion_id = #{promotionId}
        AND platform_goods_id = #{platformGoodsId}
        AND del_flag = 0
        FOR UPDATE
    </select>

    <!-- 根据BD记录ID列表批量查询ASIN列表 -->
    <select id="selectAmBestDealAsinByRefIds" resultMap="AmBestDealAsinResult">
        <include refid="selectAmBestDealAsinVo"/>
        WHERE ref_best_deal_id IN
        <foreach collection="refBestDealIds" item="refBestDealId" open="(" separator="," close=")">
            #{refBestDealId}
        </foreach>
        AND del_flag = 0
        ORDER BY ref_best_deal_id, id
    </select>

</mapper>
