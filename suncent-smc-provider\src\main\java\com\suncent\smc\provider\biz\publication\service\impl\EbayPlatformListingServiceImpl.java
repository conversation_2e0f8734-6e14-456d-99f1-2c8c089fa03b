package com.suncent.smc.provider.biz.publication.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.ebay.soap.eBLBaseComponents.*;
import com.suncent.smc.common.config.SpringTaskRetry;
import com.suncent.smc.common.constant.Constants;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.domain.UrlReplaceEntity;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.Utils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.common.utils.uuid.UUID;
import com.suncent.smc.persistence.api.domain.EbayCompatibilityProperties;
import com.suncent.smc.persistence.api.domain.EbayShippingExcludeLocation;
import com.suncent.smc.persistence.api.service.IEbayShippingExcludeLocationService;
import com.suncent.smc.persistence.cdp.domain.entity.Shop;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.configuration.category.domain.entity.CategoryInfo;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigEbayCategoryRelation;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigRequiredField;
import com.suncent.smc.persistence.configuration.category.service.ICategoryInfoService;
import com.suncent.smc.persistence.configuration.category.service.IConfigEbayCategoryRelationService;
import com.suncent.smc.persistence.configuration.message.domain.entity.MessageDingSchedule;
import com.suncent.smc.persistence.configuration.message.service.IMessageDingScheduleService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreInfoService;
import com.suncent.smc.persistence.ebay.domain.EbayGoodsHeadV2;
import com.suncent.smc.persistence.inventory.domain.InventoryUpdateBlack;
import com.suncent.smc.persistence.inventory.service.IInventoryExcludeRecordService;
import com.suncent.smc.persistence.inventory.service.IInventoryUpdateBlackService;
import com.suncent.smc.persistence.pdm.domain.dto.GetGoodsDetailQueryDTO;
import com.suncent.smc.persistence.pdm.domain.dto.GoodsRedPriceDTO;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.dto.ThirdpartyFbmDTO;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsImage;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsSpecifications;
import com.suncent.smc.persistence.pdm.domain.entity.MappingGoods;
import com.suncent.smc.persistence.pdm.service.IMappingGoodsService;
import com.suncent.smc.persistence.product.domain.entity.PublicationDay;
import com.suncent.smc.persistence.publication.domain.dto.*;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.mapper.ListingLogPullRecordMapper;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.template.domain.entity.TemplateEbayPolicy;
import com.suncent.smc.persistence.template.service.ITemplateEbayPolicyService;
import com.suncent.smc.persistence.temu.domain.entity.TemuGoodsHead;
import com.suncent.smc.persistence.todo.domain.entity.SmcAdaptTodo;
import com.suncent.smc.persistence.todo.service.*;
import com.suncent.smc.persistence.word.domain.entity.WordAiUsage;
import com.suncent.smc.persistence.word.service.IWordAiUsageService;
import com.suncent.smc.provider.base.service.ICdpBaseConfigService;
import com.suncent.smc.provider.biz.configuration.BuildEbayItemBiz;
import com.suncent.smc.provider.biz.configuration.InventoryExcludeBiz;
import com.suncent.smc.provider.biz.configuration.PushListingBiz;
import com.suncent.smc.provider.biz.error.PlatformErrorBiz;
import com.suncent.smc.provider.biz.image.ImageHandleBiz;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.publication.BaseEbayProductBiz;
import com.suncent.smc.provider.biz.publication.DingdingMonitorInfoBiz;
import com.suncent.smc.provider.biz.publication.ListingUpdateBuilder;
import com.suncent.smc.provider.biz.publication.PlatformListingFactory;
import com.suncent.smc.provider.biz.publication.domain.*;
import com.suncent.smc.provider.biz.publication.service.AbstractBaseListingService;
import com.suncent.smc.provider.biz.publication.service.IBaseListingService;
import com.suncent.smc.provider.update.HandlerListingUpdateModuleComposite;
import com.suncent.smc.provider.update.ListingUpdateModuleResolver;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import com.suncent.smc.system.service.ISysConfigService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.suncent.smc.provider.biz.publication.AiGenerationTaskBiz;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.suncent.smc.common.enums.PublishStatus.UPDATING;

/**
 * Ebay Listing管理Service 接口
 *
 * <AUTHOR>
 * @since 2023-03-27 11:04:00
 */
@Slf4j
@Service
public class EbayPlatformListingServiceImpl extends AbstractBaseListingService implements IBaseListingService {

    @Autowired
    protected PlatformListingFactory platformListingFactory;

    @Autowired
    protected IListingLogService listingLogService;

    @Autowired
    private AiGenerationTaskBiz aiGenerationTaskBiz;

    @PostConstruct
    public void init() {
        platformListingFactory.platformNodeMap.put(PlatformTypeEnum.EB.name(), this);
    }

    @Autowired
    protected ITaskConfigurationService taskConfigurationService;
    @Autowired
    protected IGoodsHeadService goodsHeadService;
    @Autowired
    protected ThirdpartyInventoryBiz inventoryBiz;

    @Autowired
    protected IPlatformCategoryService platformCategoryService;
    @Autowired
    protected ICdpBaseConfigService cdpBaseConfig;

    @Autowired
    protected IShopService shopService;

    @Autowired
    protected ListingLogPullRecordMapper listingLogPullRecordMapper;
    @Autowired
    protected IMappingGoodsService mappingGoodsService;

    @Autowired
    IListingEbayLineService ebayGoodsLineService;

    @Autowired
    IListingEbayValueService ebayValueService;

    @Autowired
    IListingEbayAdaptiveService ebayAdaptiveService;

    @Autowired
    IListingEbayShippingHeadService ebayShippingHeadService;

    @Autowired
    protected IListingShippingTypeLineService shippingTypeLineService;

    @Autowired
    protected IListingShippingLocationLineService shippingLocationLineService;

    @Autowired
    IListingEbayPolicyService ebayPolicyService;
    @Autowired
    @Qualifier("ebayPlatformListingServiceImpl")
    @Lazy
    protected EbayPlatformListingServiceImpl ebayPlatformListingService;
    @Autowired
    @Qualifier("ebayPlatformListingV2ServiceImpl")
    @Lazy
    protected EbayPlatformListingV2ServiceImpl ebayPlatformListingV2Service;

    @Autowired
    protected ITemplateEbayPolicyService templateEbayPolicyService;

    @Autowired
    protected IListingEbayValueService listingEbayValueService;

    @Autowired
    protected IInventoryExcludeRecordService inventoryExcludeRecordService;
    @Autowired
    InventoryExcludeBiz inventoryExcludeBiz;

    @Autowired
    protected BaseEbayProductBiz baseEbayProductBiz;
    @Autowired
    protected IListingEbayVideoService ebayVideoService;
    @Autowired
    protected IShopEbayVideoService shopEbayVideoService;
    @Autowired
    IInventoryUpdateBlackService inventoryUpdateBlackService;

    @Value("${api.ebay-reviseInventoryStatus-url}")
    protected String REVISE_INVENTORY_STATUS_URL;

    @Value("${api.ebay-getItem-url}")
    protected String GET_ITEM_URL;

    @Value("${api.ebay-all-listingItemId-url}")
    protected String EBAY_ALL_LISTINGITEMID_URL;

    @Value("${api.ebay-updateSellerListingHandleStatus-url}")
    protected String UPDATE_SELLER_LISTING_HANDLE_STATUSURL;
    @Resource
    protected SpringTaskRetry retryable;

    @Resource
    protected IGoodsTaskInfoService goodsTaskInfoService;

    @Autowired
    protected ICategoryInfoService categoryInfoService;
    @Autowired
    protected IConfigEbayCategoryRelationService configEbayCategoryRelationService;

    @Autowired
    @Lazy
    protected BuildEbayItemBiz buildEbayItemBiz;
    @Autowired
    protected ISysConfigService configService;

    @Autowired
    protected PushListingBiz pushListingBiz;
    @Autowired
    protected DingdingMonitorInfoBiz dingdingMonitorInfoBiz;
    @Resource
    protected IMessageDingScheduleService dingdingScheduleService;

    @Autowired
    protected IStockArrivesTodoService stockArrivesTodoService;

    @Autowired
    protected IRedLinePriceTodoService redLinePriceTodoService;
    @Autowired
    protected PlatformErrorBiz platformErrorBiz;

    @Autowired
    IEbayShippingExcludeLocationService ebayShippingExcludeLocationService;

    ThreadPoolExecutor resurfacePoolConfig = new ThreadPoolExecutor(
            4,
            8,
            0L,
            TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(1000));
    ThreadPoolExecutor ebayPullPoolConfig = new ThreadPoolExecutor(
            2,
            8,
            0L,
            TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(1500));
    ThreadPoolExecutor ebaySingelPullPoolConfig = new ThreadPoolExecutor(
            2,
            8,
            0L,
            TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(1500));

    ThreadPoolExecutor findKeywordPoolConfig = new ThreadPoolExecutor(2, 4, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(10000));

    private static ThreadPoolExecutor eidtPoolConfig = new ThreadPoolExecutor(
            6,
            6,
            1L,
            TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(2000));



    static {
        eidtPoolConfig.allowCoreThreadTimeOut(true);
    }

    @Resource
    protected BaseEbayProductBiz ebayProductBiz;

    @Autowired
    ThirdpartyInventoryBiz thirdpartyInventoryBiz;

    @Autowired
    @Lazy
    private ListingUpdateBuilder listingUpdateBuilder;

    @Autowired
    protected ImageHandleBiz imageHandleBiz;

    @Autowired
    IConfigStoreInfoService configStoreInfoService;
    @Autowired
    private IWordAiUsageService wordAiUsageService;
    @Autowired
    private ILoseCartTodoService loseCartTodoService;
    @Autowired
    private IInventoryLowTodoService inventoryLowTodoService;
    @Autowired
    IBrandAdaptImgTodoService brandAdaptImgTodoService;
    @Resource
    IConfigStoreInfoService storeInfoService;
    @Autowired
    @Lazy
    private HandlerListingUpdateModuleComposite handlerListingUpdateModuleComposite;
    @Autowired
    IListingPullDateService listingPullDateService;

    /**
     * 根据saleGoodId批量获取刊登需要的商品信息
     *
     * @param saleGoodIds
     * @return
     */
    @Override
    public SaleGoodsVO getSaleGoodsDTOListByGoodsIds(List<Long> saleGoodIds, Boolean isVc) {
        //商品信息
        GetGoodsDetailQueryDTO goodsDetailQueryDTO = new GetGoodsDetailQueryDTO();
        goodsDetailQueryDTO.setGoodsIdList(saleGoodIds);
        List<SaleGoodsDTO> returnList = pdmHttpRequestBiz.listGoodsDetail(goodsDetailQueryDTO);
        if (CollectionUtils.isEmpty(returnList)) {
            return new SaleGoodsVO();
        }

        AddPlatformPublishDTO platformPublishDTO = getAddPlatformPublishDTO(returnList, isVc);
        //三方库存
        Map<String, List<ThirdpartyFbmDTO>> thirdpartyInventoryBaseMap = platformPublishDTO.getThirdpartyInventoryBaseMap();
        //类目
        Map<String, List<PlatformCategory>> categoryMap = platformPublishDTO.getCategoryMap();
        //品牌
        List<KeyValueEntity> brandList = platformPublishDTO.getBrandList();

        for (SaleGoodsDTO goodsDto : returnList) {
            // 商品规格数据
            goodsDto.setGoodsSpecifications(goodsDto.getSpecifications());

            //库存
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = thirdpartyInventoryBaseMap.get(goodsDto.getGoodsCode());
            goodsDto.setStockOnSalesQty(0);
            if (CollectionUtils.isNotEmpty(thirdpartyFbmDTOList)) {
                Integer availableQty = thirdpartyFbmDTOList.stream().mapToInt(ThirdpartyFbmDTO::getSellableQty).sum();
                goodsDto.setStockOnSalesQty(availableQty);
            }

            //价格
            GoodsRedPriceDTO goodsPriceInfo = goodsDto.getGoodsPriceInfo();
            goodsDto.setEbayPrice(ObjectUtils.isNotEmpty(goodsPriceInfo) && goodsPriceInfo.getEbayRedLinePrice() != null  ? BigDecimal.valueOf(goodsPriceInfo.getEbayRedLinePrice()) : new BigDecimal(0));

            //对应平台类目
            List<PlatformCategory> list = categoryMap.get(goodsDto.getProductCategoryCode());
            if (CollectionUtils.isNotEmpty(list)) {
                goodsDto.setProductCategoryCode(String.valueOf(list.get(0).getId()));
            }

            //品牌名
            if (CollectionUtils.isNotEmpty(brandList)) {
                brandList.forEach(brand -> {
                    if (Objects.equals(brand.getKey(), goodsDto.getBrandCode())) {
                        goodsDto.setBrandName(brand.getValue());
                        goodsDto.setBrandCode(brand.getValue());
                    }
                });
            }
            //站点
            goodsDto.setSiteCode("US");
            //刊登天数
            ArrayList<PublicationDay> publicationDays = new ArrayList<>();
            PublicationDay publicationDay = new PublicationDay(0, "GTC", true);
            PublicationDay publicationDay1 = new PublicationDay(1, "1", false);
            PublicationDay publicationDay2 = new PublicationDay(2, "3", false);
            PublicationDay publicationDay3 = new PublicationDay(3, "5", false);
            PublicationDay publicationDay4 = new PublicationDay(4, "7", false);
            PublicationDay publicationDay5 = new PublicationDay(5, "10", false);
            publicationDays.add(publicationDay);
            publicationDays.add(publicationDay1);
            publicationDays.add(publicationDay2);
            publicationDays.add(publicationDay3);
            publicationDays.add(publicationDay4);
            publicationDays.add(publicationDay5);
            goodsDto.setPublicationDayList(publicationDays);

            //图片
            List<GoodsImage> goodsImageList = goodsDto.getGoodsImageList();
            if (CollectionUtils.isNotEmpty(goodsImageList)) {
                //白底图
                goodsDto.setGoodsImageList(goodsImageList.stream().filter(goodsImage -> Objects.equals(goodsImage.getType(), ImageType.WHITE_BACKGROUND.getType().toString())).collect(Collectors.toList()));
            }

        }
        if (CollUtil.isNotEmpty(returnList)) {
            returnList = returnList.stream().sorted(Comparator.comparing(SaleGoodsDTO::getGoodsCode)).collect(Collectors.toList());
        }

        SaleGoodsVO saleGoodsVO = new SaleGoodsVO();
        saleGoodsVO.setSaleGoodsDTOList(returnList);
        saleGoodsVO.setSiteCode("US");
        return saleGoodsVO;
    }

    /**
     * 获取商品相关数据
     *
     * @param returnList
     * @return
     */
    private AddPlatformPublishDTO getAddPlatformPublishDTO(List<SaleGoodsDTO> returnList, Boolean isVc) {
        AddPlatformPublishDTO platformPublishDTO = new AddPlatformPublishDTO();

        List<String> goodsCodes = returnList.stream().map(goodsDto -> goodsDto.getGoodsCode()).collect(Collectors.toList());
        List<String> productCategoryCode = returnList.stream().map(goodsDto -> goodsDto.getProductCategoryCode()).collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(goodsCodes)) {
            //三方库存
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectStockShareAndPartGoodsCode(goodsCodes, "US", isVc);
            Map<String, List<ThirdpartyFbmDTO>> thirdpartyInventoryBaseMap = new HashMap<>();
            //默认先取us站点的库存
            if (CollUtil.isNotEmpty(thirdpartyFbmDTOList)) {
                thirdpartyInventoryBaseMap = thirdpartyFbmDTOList.stream().filter(t->Objects.equals(t.getWhCountry(),PlatformSiteEnum.US.getName()))
                        .collect(Collectors.groupingBy(ThirdpartyFbmDTO::getSku));
            }


            platformPublishDTO.setThirdpartyInventoryBaseMap(thirdpartyInventoryBaseMap);
        }

        if (CollectionUtils.isNotEmpty(productCategoryCode)) {
            //类目
            List<PlatformCategory> categoryList = platformCategoryService.selectPlatformCategoryListByPdmCodes(productCategoryCode, PlatformTypeEnum.AM.name());
            Map<String, List<PlatformCategory>> categoryMap = categoryList.stream().collect(Collectors.groupingBy(PlatformCategory::getPdmCode));

            platformPublishDTO.setCategoryMap(categoryMap);
        }

        //品牌
        List<KeyValueEntity> brandList = cdpBaseConfig.getBrandAllKVList();
        platformPublishDTO.setBrandList(brandList);
        return platformPublishDTO;
    }



    /**
     * 批量保存商品信息
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveBatchListingDTO(BatchListingDTO dto) {
        List<String> businessIds = new ArrayList<>();
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return businessIds;
        }

        String siteCode = shopService.selectSiteCodeByShopCode(dto.getShopCode());

        //获取商品对应的属性数据
        List<String> listingAttributeLine = dto.getListingAttributeLine();
        if (CollectionUtils.isEmpty(listingAttributeLine)) {
            throw new RuntimeException("属性行全部为空,请检查.");
        }
        Map<String, List<ListingAmazonAttributeLine>> goodsCodeMap = getGoodsCodeAttributeLineMap(dto, true);
        //校验批量刊登的违禁词
//        this.checkBatchListing(dto, siteCode, listings);

        for (ListingDTO listing : listings) {
            try {
                //组装头信息
                //校验价格
                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(null,listing.getGoodsCode(), listing.getStandardPrice(), dto.getPublishType(),siteCode,null,listing.getStandardPrice());
                listing.setStandardPrice(price);

                GoodsHead goodHead = getGoodHead(dto, siteCode, listing);
                goodHead.setCategoryId(ObjectUtils.isEmpty(dto.getFirstCategoryid()) ? null : Integer.valueOf(dto.getFirstCategoryid()));
                goodHead.setSiteCode(ObjectUtils.isEmpty(dto.getSiteCode()) ? "US" : dto.getSiteCode());
                goodHead.setStandardPrice(ObjectUtils.isEmpty(listing.getStandardPrice()) ? "0" : String.valueOf(listing.getStandardPrice()));
                goodHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));

                setlistingInfoByDTO(dto, listing);
                listing.setCountry(goodHead.getSiteCode());
                //组装行信息
                ListingEbayLine ebayLine = getEbayLine(goodHead, listing);

                //组装图片信息
                List<String> imgArrs = listing.getImgArrs();
                if (CollectionUtils.isEmpty(imgArrs)) {
                    throw new BusinessException("图片不能为空");
                }
                List<GoodsResource> resourceList = getResourceList(dto, listing);

                //组装描述信息
                GoodsDescription goodsDescription = getGoodsDescription(listing);
                if("batchSave".equals(dto.getTag())) {
                    goodsDescription.setShortDescription(goodsDescription.getDetailDescription());
                    goodsDescription.setDetailDescription(null);
                }
                //组装规格信息
                GoodsSpecification goodsSpecification = getGoodsSpecification(listing, userId);
             

                ItemDTO itemDTO = new ItemDTO();
                itemDTO.setGoodsHead(goodHead);
                itemDTO.setEbayLine(ebayLine);
                itemDTO.setOperationFlag(ListingBatchOptions.ADD_BATCH.name());
                itemDTO.setGoodsResourceList(resourceList);
                itemDTO.setGoodDescription(goodsDescription);
                itemDTO.setGoodsSpecification(goodsSpecification);
                itemDTO.setGoodsAttributeLineList(goodsCodeMap.get(goodHead.getPdmGoodsCode()));
                itemDTO.setUserId(userId);
                if (Objects.nonNull(listing.getId())) {
                    goodHead.setTaskName(listing.getTaskName());
                    itemDTO.setAutoCreateHeadId(listing.getId());
                }
                ebayPlatformListingService.saveEbayInfo(itemDTO);
                businessIds.add(String.valueOf(goodHead.getId()));

                if ("Y".equals(dto.getUseAI())) {
                     // 保存AI使用记录
                     saveAIUsageRecord(goodHead);
                }
            } catch (Exception e) {
                log.error("批量新建商品编码为：[" + listing.getGoodsCode() + "]listing草稿失败", e);
                throw e;
            }
        }
        return businessIds;
    }

    private void saveAIUsageRecord(GoodsHead goodHead) {
        // 保存AI使用记录
        WordAiUsage wordAiUsage = new WordAiUsage();
        wordAiUsage.setBusinessId(String.valueOf(goodHead.getId()));
        wordAiUsage.setActionType("auto_gen");
        wordAiUsage.setButtonId("none");
        wordAiUsage.setOriginalText("default");
        wordAiUsage.setAiGeneratedText("");
        wordAiUsage.setCreateBy(goodHead.getCreateBy());
        wordAiUsage.setCreateTime(new Date());
        wordAiUsage.setUpdateTime(new Date());
        wordAiUsage.setUpdateBy(goodHead.getCreateBy());
        wordAiUsageService.insertWordAiUsage(wordAiUsage);
    }

    /**
     * 对ebay提交的属性进行批量操作
     * 支持校验
     *
     * @param dto
     * @param check
     * @return
     */
    private Map<String, List<ListingAmazonAttributeLine>> getGoodsCodeAttributeLineMap(BatchListingDTO dto, Boolean check) {
        List<String> listingAttributeLine = dto.getListingAttributeLine();
        Integer Categoryid = ObjectUtils.isEmpty(dto.getFirstCategoryid()) ? null : Integer.valueOf(dto.getFirstCategoryid());
        if (CollectionUtils.isEmpty(listingAttributeLine)) {
            return new HashMap<>();
        }
        List<ListingAmazonAttributeLine> attributeLines = new ArrayList<>();
        for (String line : listingAttributeLine) {
            ListingAmazonAttributeLine attributeLine = JSON.parseObject(line, ListingAmazonAttributeLine.class);
            if (ObjectUtils.isEmpty(attributeLine)) {
                continue;
            }
            if (ObjUtil.isEmpty(dto.getTempFlag()) && StringUtils.isEmpty(attributeLine.getPdmGoodsCode())) {
                continue;
            }
            if (StringUtils.isEmpty(attributeLine.getTableValue())) {
                continue;
            }
            attributeLines.add(attributeLine);
        }
        if (ObjUtil.isNotEmpty(dto.getTempFlag())) {
            //如果是修改的话，返回已goodsId为key的map
            return attributeLines.stream().collect(Collectors.groupingBy(e -> String.valueOf(e.getGoodsId())));
        }
        Map<String, List<ListingAmazonAttributeLine>> goodsCodeMap = attributeLines.stream().collect(Collectors.groupingBy(ListingAmazonAttributeLine::getPdmGoodsCode));

        //缺少必填的属性得校验
        List<ConfigRequiredField> configRequiredFieldList = configRequiredFieldService.selectByCategoryId(Categoryid);
        List<String> requireAllName = configRequiredFieldList.stream().filter(e -> Objects.equals(e.getIsRequire(), "1")).map(e -> e.getAttributeCode()).collect(Collectors.toList());

        // 品牌的属性
        ConfigRequiredField brandField = configRequiredFieldList.stream().filter(e -> Objects.equals(e.getAttributeCode(), "Brand")).findFirst().orElse(null);

        for (String goodsCode : goodsCodeMap.keySet()) {
            List<ListingAmazonAttributeLine> attributeLinesData = goodsCodeMap.get(goodsCode);
            // attributeLinesData 没有品牌信息就添加品牌信息
            boolean hasBrand = attributeLinesData.stream().anyMatch(e -> Objects.equals(e.getTableName(), brandField.getAttributeCode()));
            if (ObjUtil.isNotEmpty(brandField) && !hasBrand) {
                ListingAmazonAttributeLine brandLine = new ListingAmazonAttributeLine();
                brandLine.setTableName(brandField.getAttributeCode());
                brandLine.setTableType(brandField.getAttributeType().intValue());
                brandLine.setPdmGoodsCode(goodsCode);
                brandLine.setTableValue(dto.getBrandCode());
                attributeLinesData.add(brandLine);
            }
        }

        //不校验的话直接返回
        if (!check) {
            return goodsCodeMap;
        }


        Map<String, List<String>> checkMap = new HashMap<>();
        for (String goodsCode : goodsCodeMap.keySet()) {

            List<ListingAmazonAttributeLine> attributeLinesData = goodsCodeMap.get(goodsCode);
            for (ListingAmazonAttributeLine ebayValue : attributeLinesData) {
                ebayValue.setIsRequire(requireAllName.contains(ebayValue.getTableName()) ? "1" : "0");
            }

            List<String> requireHaveDataName = attributeLinesData.stream().filter(e -> Objects.equals(e.getIsRequire(), "1")).map(e -> e.getTableName()).collect(Collectors.toList());
            requireAllName.removeAll(requireHaveDataName);
            if (CollectionUtils.isEmpty(requireAllName)) {
                continue;
            }

            checkMap.put(goodsCode, requireAllName);
        }

        //没有缺少必填的属性
        if (ObjectUtils.isEmpty(checkMap)) {
            return goodsCodeMap;
        }

        //缺少必填的属性 抛出异常
        StringBuilder msg = new StringBuilder();
        for (String goodsCode : checkMap.keySet()) {
            msg.append("商品编码为[" + goodsCode + "]的属性行缺少必填的属性[" + checkMap.get(goodsCode) + "];");
        }
        throw new RuntimeException(msg.toString());
    }

    /**
     * 保存Ebay商品信息
     *
     * @param itemDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer saveEbayInfo(ItemDTO itemDTO) {
        //给头表设置品牌
        if (ObjectUtils.isNotEmpty(itemDTO.getGoodsAttributeLineList())) {
            String brand = itemDTO.getGoodsAttributeLineList().stream()
                    .filter(attributeLine -> Objects.equals("BRAND", attributeLine.getTableName().trim().toUpperCase()))
                    .map(ListingAmazonAttributeLine::getTableValue).findFirst().orElse(null);

            itemDTO.getGoodsHead().setBrandCode(brand);
        }
        GoodsHead goodHead = itemDTO.getGoodsHead();
        //保存商品头信息
        goodsHeadService.insertListingGoodsHead(goodHead);
        Integer headId = goodHead.getId();
        //处理视频
        String videoId = itemDTO.getVideoId();
        if(ObjUtil.isNotEmpty(videoId)){
            ListingEbayVideo ebayVideo = new ListingEbayVideo();
            ebayVideo.setGoodsId(String.valueOf(headId));
            ebayVideo.setVideoId(videoId);
            ebayVideo.setCreateBy(goodHead.getCreateBy());
            ebayVideo.setUpdateBy(goodHead.getCreateBy());
            ebayVideoService.insertListingEbayVideo(ebayVideo);
        }
        //处理分销listing
        CategoryInfo category = new CategoryInfo();
        category.setPlatformCategoryId(String.valueOf(goodHead.getCategoryId()));
        List<CategoryInfo> categoryInfoList = categoryInfoService.selectCategoryInfoList(category);
        itemDTO.setIsDistribution(ObjectUtils.isEmpty(categoryInfoList) ? 0 : categoryInfoList.get(0).getIsDistribution());
        //处理ebay数据
        goodsInfoBiz.saveEbayData(itemDTO);
        Integer autoCreateHeadId = itemDTO.getAutoCreateHeadId();
        //如果是自动创建的listing，需要更新自动创建的listing的headId跟任务状态
        if (ObjectUtils.isNotEmpty(autoCreateHeadId)) {
            //更新自动创建的listing的headId
            goodsTaskInfoService.updateConfimHeadIdByAutoHeadId(autoCreateHeadId, headId);
            //修改对应task的状态
            goodsTaskInfoService.updateGoodsTaskStatusByAutoHeadId(autoCreateHeadId, "已确认");
        }
        return headId;
    }


    public Integer saveAndUpdateEbayInfo(ItemDTO itemDTO) {
        GoodsHead goodHead = itemDTO.getGoodsHead();
        if (ObjectUtils.isEmpty(goodHead.getId())) {
            //给头表设置品牌
            if (ObjectUtils.isNotEmpty(itemDTO.getGoodsAttributeLineList())) {
                String brand = itemDTO.getGoodsAttributeLineList().stream()
                        .filter(attributeLine -> Objects.equals("BRAND", attributeLine.getTableName().trim().toUpperCase()))
                        .map(ListingAmazonAttributeLine::getTableValue).findFirst().orElse(null);
                itemDTO.getGoodsHead().setBrandCode(brand);
            }
            return saveEbayInfo(itemDTO);
        } else {
            //给头表设置品牌
            if (ObjectUtils.isNotEmpty(itemDTO.getGoodsAttributeLineList())) {
                String brand = itemDTO.getGoodsAttributeLineList().stream()
                        .filter(attributeLine -> Objects.equals("BRAND", attributeLine.getTableName().trim().toUpperCase()))
                        .map(ListingAmazonAttributeLine::getTableValue).findFirst().orElse(null);
                itemDTO.getGoodsHead().setBrandCode(brand);
            }
            //修改商品头信息
            goodsHeadService.updateListingGoodsHead(goodHead);
            //处理ebay数据
            goodsInfoBiz.updateEbayData(itemDTO);
            return goodHead.getId();
        }

    }

    /**
     * 同步listing信息
     * 目前版本:
     * 仅仅支持单个listing同步信息，与平台信息保持一致。
     *
     * @param ids
     */
    @Override
    public void syncListingInfo(List<Integer> ids, String userId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(ids.toArray(new Integer[0]));

        Map<String, List<GoodsHead>> shopCodeMap = goodsHeads.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));
        List<Integer> goodsId = goodsHeads.stream().map(GoodsHead::getId).collect(Collectors.toList());
        GoodsTask goodsTask = goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.SYNC_LISTING, goodsId, String.valueOf(userId));

        for (String shopCode : shopCodeMap.keySet()) {
            List<GoodsHead> goodsHeadList = shopCodeMap.get(shopCode);
            Shop shop = shopService.selectShopByShopCode(shopCode);
            if (shop == null) {
                log.error("Ebay商品同步--店铺不存在,店铺为:{}", shopCode);
                continue;
            }
            goodsHeadList.forEach(goodsHead -> {
                try {
                    ebaySingelPullPoolConfig.execute(() -> {
                        AjaxResult itemResp = retryable.retryableToApiMsg(() -> getEbayItem(goodsHead));
                        if (itemResp.isSuccess()) {
                            ItemType item = JSONObject.toJavaObject((JSONObject)itemResp.get(AjaxResult.DATA_TAG) , ItemType.class);
                            EbayItemTypeDTO dto = new EbayItemTypeDTO();
                            BeanUtil.copyProperties(item, dto);
                            syncListing(goodsHead, dto,userId);

                            listingLogService.insertSuccessListingLog("平台销售编码为：[" + goodsHead.getPlatformGoodsId() + "]手动同步listing成功", userId, goodsHead.getId());
                            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), Collections.singletonList(GoodsTaskTypeEnum.SYNC_LISTING), GoodsTaskSubStatusEnum.NORAML, "");

                        } else {
                            listingLogService.insertErrorListingLog("平台销售编码为：[" + goodsHead.getPlatformGoodsId() + "]手动同步listing失败", userId, goodsHead.getId(), String.valueOf(itemResp.get(AjaxResult.MSG_TAG)));
                            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), Collections.singletonList(GoodsTaskTypeEnum.SYNC_LISTING), GoodsTaskSubStatusEnum.ERROR, "手动同步Listing信息失败,平台销售编码：[" + goodsHead.getPlatformGoodsId() + "]，原因：" + itemResp.get(AjaxResult.MSG_TAG));
                        }
                    });
                } catch (Exception e) {
                    listingLogService.insertErrorListingLog("平台销售编码为：[" + goodsHead.getPlatformGoodsId() + "]同步listing失败", userId, goodsHead.getId(), String.valueOf(e.getMessage()));
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), Collections.singletonList(GoodsTaskTypeEnum.SYNC_LISTING), GoodsTaskSubStatusEnum.ERROR, "手动同步Listing信息失败,平台销售编码：[" + goodsHead.getPlatformGoodsId() + "]，原因：" + e.getMessage());
                }
            });
        }
    }


    /**
     * 同步listing信息(兼容新增&&修改 且会同步api处理状态)
     *
     * @param goodsHead 有id则表示smc存在
     * @param listing ebay同步数据
     */
    public void syncListing(GoodsHead goodsHead, EbayItemTypeDTO listing,String userId) {
//        long currentTimeMillisOne = System.currentTimeMillis();
        Integer headId = ebayPlatformListingService.getHeadId(goodsHead, listing,userId);
        //同步api处理状态
        retryable.retryableTOApi(()-> verifyConsumer(goodsHead.getShopCode(), goodsHead.getPlatformGoodsId()));
//        long currentTimeMillisTwo = System.currentTimeMillis();
        //回写pdm mapping 和 价格相关处理
        this.intoPdmMapping(headId, listing);
//        long currentTimeMillisThree = System.currentTimeMillis();
//        log.info("同步ebay处理，headId:{},第一阶段耗时：{},第二阶段耗时:{}" ,headId, (currentTimeMillisTwo - currentTimeMillisOne), (currentTimeMillisThree - currentTimeMillisTwo));
        //记录同步时间
        listingPullDateService.upsertListingPullDate(Long.valueOf(headId),PlatformTypeEnum.EB.name());
    }


    private void intoPdmMapping(Integer headId,EbayItemTypeDTO listing) {
        GoodsHead head = goodsHeadService.selectListingGoodsHeadById(headId);
        if (ObjectUtils.isEmpty(head)) {
            return;
        }
        baseEbayProductBiz.addPdmStatus(head, false);

        //自动补货
        autoReplenishment(head);

        //低于红线价格 需 更新至平台
        refreshPrice(head,listing);

    }

    /**
     * 自动补货
     * @param goodsHead
     */
    public void autoReplenishment(GoodsHead goodsHead) {
        //查询启用库存更新的店铺
        ConfigStoreInfo storeInfo = new ConfigStoreInfo();
        storeInfo.setAutoReplenishment(SMCCommonEnum.ENABLE.getValue());
        storeInfo.setShopCode(goodsHead.getShopCode());
        List<ConfigStoreInfo> storeInfoList = storeInfoService.selectConfigStoreInfoList(storeInfo);
        if (CollectionUtils.isEmpty(storeInfoList)) {
            return;
        }
        ConfigStoreInfo shop = storeInfoList.get(0);
        List<GoodsHead> updateStockList=Collections.singletonList(goodsHead);

        updateStockList =updateStockList.stream().filter(h -> PublishStatus.getSaleStatus().contains(h.getPublishStatus())).collect(Collectors.toList());

        thirdpartyInventoryBiz.removeExcludeListing(updateStockList, shop);

        if (ObjectUtil.isEmpty(updateStockList)) {
            return;
        }

        ebayProductBiz.updateEbayStock(updateStockList, shop, new HashMap<>(),"自动补货库存更新");
    }

    /**
     * 更新listing 价格 对比价格以ebay线上的价格为准
     * @param head
     * @param listing
     */
    public void refreshPrice(GoodsHead head,EbayItemTypeDTO listing) {
        try {
            String refreshPrice = configService.selectConfigByKey("ebay-pull-refreshPrice");
            if (!Objects.equals(refreshPrice, "true")) {
                return;
            }
            String goodsCode = head.getPdmGoodsCode();
            if (ObjUtil.isEmpty(goodsCode) || ObjUtil.isEmpty(head.getSiteCode())) {
                return;
            }
            //判断是否属于sku红线价黑名单
            boolean belong = commonInfoBiz.checkSkuBelongBlockSku(goodsCode);
            if (belong){
                return;
            }
            GoodsRedPriceDTO goodsPriceDTO = pdmHttpRequestBiz.getRedLinePriceByGoodsCode(head.getSiteCode(), goodsCode);
            if (ObjUtil.isEmpty(goodsPriceDTO)) {
                return;
            }

            //是否强制改价
            if (ObjUtil.isEmpty(goodsPriceDTO.getEbayForcePriceFlag()) || !goodsPriceDTO.getEbayForcePriceFlag() ){
                return;
            }

            double sellerPrice = listing.getStartPrice().getValue();
            Double ebayRedPrice = goodsPriceDTO.getEbayRedLinePrice();
            BigDecimal ebayRedPriceAdd = BigDecimal.valueOf(ebayRedPrice).add(BigDecimal.valueOf(0.01));
            if ( Double.valueOf(sellerPrice).compareTo(ebayRedPrice) < 0 ) {
                //线上listing有做折扣
                if (ObjUtil.isNotEmpty(listing.getSellingStatus())
                        && ObjUtil.isNotEmpty(listing.getSellingStatus().getPromotionalSaleDetails())
                        && ObjUtil.isNotEmpty(listing.getSellingStatus().getPromotionalSaleDetails().getOriginalPrice())) {
                    double originalPrice = listing.getSellingStatus().getPromotionalSaleDetails().getOriginalPrice().getValue();

                    if (originalPrice != sellerPrice) {
                        String detail = "同步价格发现该listing有促销折扣，折扣前价格为：[" + originalPrice + "],折扣后价格为：[" + sellerPrice + "]";
                        listingLogService.insertSuccessListingLog(detail,"1", head.getId());
                    }
                }

                head.setStandardPrice(String.valueOf(sellerPrice));
                AjaxResult result = retryable.retryableToApiMsg(()->updateEbayPriceInventory(head));

                if (result.isSuccess()) {
                    GoodsHead updateHead = new GoodsHead();
                    updateHead.setId(head.getId());
                    updateHead.setStandardPrice(String.valueOf(ebayRedPriceAdd));
                    updateHead.setSettlementPrice(ebayRedPriceAdd);
                    goodsHeadService.updateListingGoodsHead(updateHead);
                    String detail = "同步刷新价格,商品编码为：[" + head.getPdmGoodsCode() + "]低于红线价格,更新至平台成功,原价格为：[" + sellerPrice + "],更新后价格为：[" + ebayRedPriceAdd + "]";
                    listingLogService.insertSuccessListingLog(detail,"1", head.getId());

                    //记录同步刷新价格
                    String receiver = dingdingMonitorInfoBiz.getReceiver(head.getCreateBy(),head.getUpdateBy(), head.getShopCode());
                    MessageDingSchedule messageDingSchedule = new MessageDingSchedule();
                    messageDingSchedule.setGoodsId(Long.valueOf(head.getId()));
                    messageDingSchedule.setReceiverType(DingdingReceiverEnum.PRICE_MONITOR_RECORD.getKey());
                    messageDingSchedule.setReceiverBy(receiver);
                    messageDingSchedule.setCreateBy("1");
                    messageDingSchedule.setCreateTime(new Date());
                    messageDingSchedule.setReceiverContext( detail );
                    dingdingScheduleService.insertMessageDingSchedule(messageDingSchedule);
                }else {
                    listingLogService.insertErrorListingLog("价格监控强制改价失败","1", head.getId(),""+result.get(AjaxResult.MSG_TAG));
                }
            }
        } catch (Exception e) {
            log.error("Ebay商品同步刷新价格--商品编码为：[" + head.getPdmGoodsCode() + "]低于红线价格,更新至平台失败", e);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public Integer getHeadId(GoodsHead goodsHead, EbayItemTypeDTO listing,String userId) {
        boolean flag = ObjectUtils.isEmpty(goodsHead.getId());
        //基础头表数据 goodsHeadService
        Integer headId = this.intoGoodsHead(goodsHead, listing,userId);
        goodsHead.setId(headId);
        //ebay行表数据 ebayGoodsLineService
        Integer lineId = this.intoGoodsLine(flag, goodsHead, listing);

        //商品属性信息
        this.intoEbayValue(flag, lineId, listing);

        // 包裹信息数据 goodsSpecificationService
        this.intoGoodsSpecification(flag, headId, listing);

        //适配信息 ebayAdaptiveService
        this.intoAdapterList(lineId, listing,goodsHead);

        //商品图片数据 goodsResourceService
        this.intoGoodsResource( headId, listing);

        //视频数据 goodsVideoService
        this.intoGoodsVideo(headId, listing);

        //商品描述数据  goodsDescriptionService
        this.intoGoodsDescription( headId, listing);

        //物流行 ebayShippingHeadService
        this.intoEbayShipLine(flag, lineId, listing);

        //付款信息 & 退货信息 ebayPolicyService
        this.intoEbayPolicy(flag, lineId, listing);
        return headId;
    }

    /**
     * 视频数据
     * @param headId
     * @param listing
     */
    private void intoGoodsVideo(Integer headId, EbayItemTypeDTO listing) {
        if (ObjUtil.isEmpty(listing.getVideoDetails())){
            return;
        }
        ListingEbayVideo listingEbayVideo = ebayVideoService.selectListingEbayVideoByGoodsId(headId);
        if (ObjUtil.isEmpty(listingEbayVideo)){
            ListingEbayVideo addVideo = new ListingEbayVideo();
            addVideo.setGoodsId(String.valueOf(headId));
            addVideo.setVideoId(listing.getVideoDetails().getVideoID());
            ebayVideoService.insertListingEbayVideo(addVideo);
        }else {
            listingEbayVideo.setVideoId(listing.getVideoDetails().getVideoID());
            ebayVideoService.updateListingEbayVideo(listingEbayVideo);
        }

    }

    /**
     * 入库适配数据
     * @param lineId
     * @param listing
     * @param goodsHead
     */
    private void intoAdapterList(Integer lineId, EbayItemTypeDTO listing,GoodsHead goodsHead) {
        List<ListingEbayAdaptive> adaptives = ebayAdaptiveService.packageAllAdaptives(listing);
        if (CollectionUtils.isEmpty(adaptives)) {
            return;
        }
        for (ListingEbayAdaptive adaptive : adaptives) {
            adaptive.setListingLineId(lineId);
            adaptive.setCreateBy(listing.getCreateBy());
            adaptive.setCreateTime(DateUtils.getNowDate());
        }
        //新增适配
        ebayAdaptiveService.deleteListingEbayAdaptiveByLineId(lineId);
        List<List<ListingEbayAdaptive>> split = ListUtil.split(adaptives, 500);
        for (List<ListingEbayAdaptive> list : split) {
            ebayAdaptiveService.batchInsertListingEbayAdaptive(list);
        }
        /**
         * 同步不改变listing适配状态
         */
        //更新适配状态
//        String adaptionStatus = AdaptationStatusEnum.getAdaptionByListingStatus(goodsHead.getPublishStatus(), ObjectUtils.isNotEmpty(adaptives));
//        GoodsHead updateAdapter = new GoodsHead();
//        updateAdapter.setId(goodsHead.getId());
//        updateAdapter.setAdaptationStatus(adaptionStatus);
//        goodsHeadService.updateListingGoodsHead(updateAdapter);
    }

    /**
     * 入库 头表
     *
     * @param goodsHead
     * @param listing
     * @return
     */
    private Integer intoGoodsHead(GoodsHead goodsHead, EbayItemTypeDTO listing,String userId) {

        GoodsHead db = new GoodsHead();
        MappingGoods mappingGoods=new MappingGoods();
        // pdm商品编码
        if (ObjUtil.isNotEmpty(listing.getSKU())) {
            mappingGoods = mappingGoodsService.selectMappingGoodsByPlatformSku(listing.getSKU(), goodsHead.getShopCode());
            if (Objects.isNull(mappingGoods)) {
                try {
                    List<String> goodsCodes = mappingGoodsService.selectMappingGoodsRelationByPlatformSku(listing.getSKU(), goodsHead.getShopCode());
                    if (goodsCodes.size() == 1) {
                        log.info("Ebay商品同步--查询特殊mapping映射关系,根据平台sku:{},shopCode:{}查询到pdm商品编码为", listing.getSKU(), goodsHead.getShopCode());
                        mappingGoods = mappingGoodsService.selectMappingGoodsByPlatformSku(goodsCodes.get(0), goodsHead.getShopCode());
                    }
                } catch (Exception e) {
                    log.error("Ebay商品同步--查询特殊mapping映射关系匹配到多条数据,根据平台sku:{},shopCode:{}", listing.getSKU(), goodsHead.getShopCode(), e);
                }
            }
            if (ObjectUtils.isEmpty(goodsHead.getPdmGoodsCode())) {
                db.setPdmGoodsCode(Objects.isNull(mappingGoods) ? "" : mappingGoods.getGoodsCode());
            }
            db.setPlatformGoodsCode(listing.getSKU());
        }

        // itemId
        if (ObjUtil.isNotEmpty(listing.getItemID())) {
            db.setPlatformGoodsId(listing.getItemID());
        }

        // 平台
        db.setPlatform(PlatformTypeEnum.EB.name());

        // 店铺
        if (ObjUtil.isNotEmpty(goodsHead.getShopCode())) {
            listing.setShopCode(goodsHead.getShopCode());
            db.setShopCode(listing.getShopCode());
        }

        // 刊登类型
        if (ObjUtil.isNotEmpty(listing)) {
            db.setPublishType(getPublishType(goodsHead.getShopCode(), listing));
        }

        // 品牌
        if (ObjUtil.isNotEmpty(listing)) {
            db.setBrandCode(getBrand(listing));
        }

        // 商品状况
        if (ObjUtil.isNotEmpty(listing.getConditionID())) {
            db.setCondition(listing.getConditionID().toString());
        }

        // 标题
        if (ObjUtil.isNotEmpty(listing.getTitle())) {
            db.setTitle(listing.getTitle());
        }

        // 副标题
        if (ObjUtil.isNotEmpty(listing.getSubTitle())) {
            db.setSubtitle(listing.getSubTitle());
        }

        // 刊登状态
        if (ObjUtil.isNotEmpty(listing.getSellingStatus())) {
            db.setPublishStatus(getPublishStatus(listing.getSellingStatus().getListingStatus()));
        }

        // 站点
        if (ObjUtil.isNotEmpty(goodsHead.getSiteCode())) {
            db.setSiteCode(goodsHead.getSiteCode());
        }

        // 当前售价
        if (ObjUtil.isNotEmpty(listing)) {
            db.setStandardPrice(getStandardPrice(goodsHead.getSiteCode(),goodsHead.getShopCode(), listing));
        }

        // 刊登原价
        if (ObjUtil.isNotEmpty(listing)) {
            db.setOriginalPrice(getOriginalPrice(listing));
            if (ObjUtil.isEmpty(db.getOriginalPrice())) {
                goodsHeadService.clearOriginalPrice(goodsHead);
            }
        }

        // 库存
        db.setStockOnSalesQty(getStockOnSalesQty(listing));

        // 类目id 对应 sc_smc_platform_category
        if (ObjUtil.isNotEmpty(listing.getPrimaryCategory())) {
            PlatformCategory platformCategory = platformCategoryService.getPlatformCategoryByCategoryId(goodsHead.getSiteCode(), listing.getPrimaryCategory().getCategoryID());
            if (ObjectUtils.isEmpty(platformCategory)) {
                ListingLogPullRecord log = new ListingLogPullRecord();
                log.setShopCode(goodsHead.getShopCode());
                log.setPlatformCode(PlatformTypeEnum.EB.name());
                log.setPlatformGoodsId(listing.getItemID());
                log.setPullDetail("平台为" + listing.getPrimaryCategory().getCategoryID() + "未映射到平台类目");
                listingLogPullRecordMapper.insertListingLogPullRecord(log);
            }
            db.setCategoryId(Integer.valueOf(ObjectUtils.isEmpty(platformCategory) ? listing.getPrimaryCategory().getCategoryID() : platformCategory.getId().toString()));
        } else {
            db.setCategoryId(goodsHead.getCategoryId());
        }

        // 创建人
        if (ObjectUtils.isEmpty(goodsHead.getPdmGoodsCode()) && ObjUtil.isNotEmpty(mappingGoods)) {
            listing.setCreateBy(mappingGoods.getOperators());
            db.setCreateBy(listing.getCreateBy());
        }

        // 刊登标识
        if (ObjUtil.isNotEmpty(goodsHead.getSmcFlag())) {
            db.setSmcFlag(goodsHead.getSmcFlag());
        } else {
            db.setSmcFlag(1);
        }

        // 上架时间
        if (ObjUtil.isNotEmpty(listing.getListingDetails().getStartTime())) {
            db.setOnlineTime(listing.getListingDetails().getStartTime().getTime());
        }

        // 下架时间
        if (ObjUtil.isNotEmpty(listing.getListingDetails().getEndTime())) {
            db.setOffTime(listing.getListingDetails().getEndTime().getTime());
        }

        if (ObjUtil.isNotEmpty(goodsHead.getId())) {
            db.setId(goodsHead.getId());

            //记录日志
            saveListingLog(userId, db);
            // 修改
            goodsHeadService.updateListingGoodsHead(db);
            return db.getId();
        }

        // 新增前校验是否已存在
        GoodsHead query = new GoodsHead();
        query.setPlatform(PlatformTypeEnum.EB.name());
        query.setPlatformGoodsId(listing.getItemID());
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadList(query);
        if (!goodsHeadList.isEmpty()) {
            db.setId(goodsHeadList.get(0).getId());

            //记录日志
            saveListingLog(userId, db);
            //修改
            goodsHeadService.updateListingGoodsHead(db);
            return db.getId();
        }
        //记录日志
        saveListingLog(userId, db);

        // 新增
        goodsHeadService.insertListingGoodsHead(db);
        return db.getId();
    }

    private void saveListingLog(String userId, GoodsHead db) {
        StringBuilder sb = new StringBuilder("Listing同步成功,");
        Integer id = db.getId();
        if (ObjUtil.isNotEmpty(id)){
            GoodsHead oldHead = goodsHeadService.selectListingGoodsHeadById(id);
            if (ObjUtil.isEmpty(oldHead) && ObjUtil.isEmpty(db)){
                return;
            }
            if (!ObjUtil.equals(oldHead.getPdmGoodsCode(), db.getPdmGoodsCode()) && ObjUtil.isNotEmpty(db.getPdmGoodsCode())){
                sb.append("商品编码:").append(oldHead.getPdmGoodsCode()).append("-->").append(db.getPdmGoodsCode()).append(";");
            }
            if (!ObjUtil.equals(oldHead.getPlatformGoodsCode(), db.getPlatformGoodsCode())){
                sb.append("平台商品编码:").append(oldHead.getPlatformGoodsCode()).append("-->").append(db.getPlatformGoodsCode()).append(";");
            }
            if (!ObjUtil.equals(oldHead.getPlatformGoodsId(), db.getPlatformGoodsId())){
                sb.append("平台销售编码:").append(oldHead.getPlatformGoodsId()).append("-->").append(db.getPlatformGoodsId()).append(";");
            }
            if (!ObjUtil.equals(oldHead.getStandardPrice(), db.getStandardPrice())){
                sb.append("eBay售价:").append(oldHead.getStandardPrice()).append("-->").append(db.getStandardPrice()).append(";");
            }
            if (!ObjUtil.equals(oldHead.getOriginalPrice(), db.getOriginalPrice())){
                sb.append("原价:").append(oldHead.getOriginalPrice()).append("-->").append(db.getOriginalPrice()).append(";");
            }
        }else {
            if (StrUtil.isNotEmpty(db.getPdmGoodsCode())){
                sb.append("商品编码:").append(db.getPdmGoodsCode()).append(";");
            }
            sb.append("平台商品编码:").append(db.getPlatformGoodsCode()).append(";")
                    .append("平台销售编码:").append(db.getPlatformGoodsId()).append(";")
                    .append("eBay售价:").append(db.getStandardPrice()).append(";");
            if (StrUtil.isNotEmpty(db.getPdmGoodsCode())){
                sb.append("原价:").append(db.getOriginalPrice()).append(";");
            }
        }

        if (!ObjUtil.equals(sb.toString(), "Listing同步成功,")){
            listingLogService.insertSuccessListingLog(sb.toString(), userId, db.getId());
        }
    }

    protected BigDecimal getStockOnSalesQty(EbayItemTypeDTO listing) {
        BigDecimal stockOnSalesQty =BigDecimal.ZERO;
        if (ObjUtil.isNotEmpty(listing.getReasonHideFromSearch()) && ObjUtil.isNotEmpty(listing.isHideFromSearch())) {
            if (Objects.equals(listing.getReasonHideFromSearch(), ReasonHideFromSearchCodeType.OUT_OF_STOCK) &&
                    Objects.equals(listing.isHideFromSearch(), true)) {
                stockOnSalesQty=BigDecimal.ZERO;
                if (listing.getQuantity() > 0) {
                    log.info("Ebay商品同步--商品平台销售编码为：[" + listing.getItemID() + "]状态为OUT_OF_STOCK，但是平台库存为：[" + listing.getQuantity() + "]");
                }
            }
        }else {
            Integer quantity = listing.getQuantity();
            Integer quantitySold = listing.getSellingStatus().getQuantitySold();
            if (ObjectUtils.isNotEmpty(quantity)){
                stockOnSalesQty=getOnlineQuantity(quantity, quantitySold);
            }
        }
        return stockOnSalesQty;
    }

    /**
     * 获取ebay在售库存
     * @param quantity
     * @param quantitySold
     * @return
     */
    public BigDecimal getOnlineQuantity(Integer quantity, Integer quantitySold) {
        if (ObjUtil.isEmpty(quantitySold)||ObjUtil.equal(quantitySold,0)){
            return BigDecimal.valueOf(quantity);
        }
        int stock = quantity - quantitySold;

        return BigDecimal.valueOf(stock);
    }


    /**
     * 获取刊登原价
     * @param listing
     * @return
     */
    protected String getOriginalPrice(EbayItemTypeDTO listing) {
        SellingStatusType sellingStatus = listing.getSellingStatus();
        if (ObjectUtils.isEmpty(sellingStatus)) {
            return null;
        }
        PromotionalSaleDetailsType promotionalSaleDetails = sellingStatus.getPromotionalSaleDetails();
        if (ObjectUtils.isEmpty(promotionalSaleDetails)) {
            return null;
        }
        AmountType originalPrice = promotionalSaleDetails.getOriginalPrice();
        if (ObjectUtils.isEmpty(originalPrice)) {
            return null;
        }
        return String.valueOf(originalPrice.getValue());
    }

    /**
     * ebay 同步获取价格
     * @param siteCode
     * @param shopCode
     * @param listing
     * @return
     */
    public String getStandardPrice(String siteCode,String shopCode, ItemType listing) {
        // 获取同步价格类型配置
        ConfigStoreInfo config = getConfigStoreInfo(siteCode,shopCode);

        // 检查配置并返回价格
        if (isDefaultPrice(config)) {
            return getCurrentOrStartPrice(listing);
        } else {
            return getPromotionalOrStartPrice(listing);
        }
    }

    /**
     * 获取店铺信息
     * @param siteCode
     * @param shopCode
     * @return
     */
    public ConfigStoreInfo getConfigStoreInfo(String siteCode,String shopCode) {
        if (siteCode == null || shopCode == null) {
          throw new BusinessException("ebay listing同步价格同步,店铺信息为空");
        }
        ConfigStoreInfo info = new ConfigStoreInfo();
        info.setSite(siteCode);
        info.setShopCode(shopCode);

        List<ConfigStoreInfo> infoList = configStoreInfoService.selectConfigStoreInfoList(info);
        return ObjectUtils.isEmpty(infoList) ? null : infoList.get(0);
    }

    /**
     * 是否为当前价格
     * @param config
     * @return
     */
    public boolean isDefaultPrice(ConfigStoreInfo config) {
        if (config == null) {
            return true;
        }
        return ObjectUtils.isEmpty(config.getSyncPriceType()) ||
                Objects.equals(config.getSyncPriceType(), 1);
    }

    /**
     * 获取当前价格
     * @param listing
     * @return
     */
    private String getCurrentOrStartPrice(ItemType listing) {
        if (listing == null) {
            throw new BusinessException("ebay listing同步价格同步,获取当前价格 ItemType为空");
        }
        if (ObjectUtils.isNotEmpty(listing.getStartPrice())) {
            return String.valueOf(listing.getStartPrice().getValue());
        }
        return ObjectUtils.isNotEmpty(listing.getSellingStatus()) ?
                String.valueOf(listing.getSellingStatus().getCurrentPrice().getValue()) : "0";
    }

    /**
     * 获取刊登原价
     * @param listing
     * @return
     */
    private String getPromotionalOrStartPrice(ItemType listing) {
        if (listing == null) {
            throw new BusinessException("ebay listing同步价格同步,获取刊登原价 ItemType为空");
        }
        if (ObjectUtils.isNotEmpty(listing.getSellingStatus())) {
            if (ObjectUtils.isNotEmpty(listing.getSellingStatus().getPromotionalSaleDetails())) {
                return String.valueOf(listing.getSellingStatus().getPromotionalSaleDetails().getOriginalPrice().getValue());
            }
        }
        return ObjectUtils.isNotEmpty(listing.getStartPrice()) ?
                String.valueOf(listing.getStartPrice().getValue()) : "0";
    }


    /**
     * 从ebayitem对象获取品牌
     *
     * @param listing
     * @return
     */
    protected String getBrand(EbayItemTypeDTO listing) {
        ProductListingDetailsType productListingDetails = listing.getProductListingDetails();
        if (ObjectUtils.isNotEmpty(productListingDetails)) {
            return ObjectUtils.isEmpty(productListingDetails.getBrandMPN()) ? null : productListingDetails.getBrandMPN().getBrand();
        }
        return null;
    }

    /**
     * 入库 listing行表
     *
     * @param goodsHead
     * @param listing
     * @return
     */
    private Integer intoGoodsLine(boolean flag, GoodsHead goodsHead, EbayItemTypeDTO listing) {
        ListingEbayLine ebayLine = new ListingEbayLine();
        ebayLine.setListingHeadId(goodsHead.getId());

        // 物品所在地
        if (ObjectUtils.isNotEmpty(listing.getLocation())) {
            ebayLine.setLocation(listing.getLocation().contains(",") ? listing.getLocation().split(",")[1].trim() : listing.getLocation());
        }

        // 国家
        if (ObjectUtils.isNotEmpty(listing.getCountry())) {
            ebayLine.setCountry(listing.getCountry().value());
        }

        // 邮编
        if (ObjectUtils.isNotEmpty(listing.getPostalCode())) {
            ebayLine.setPostcode(listing.getPostalCode());
        }

        // 活动天数
        ebayLine.setSellDay(getSellDay(goodsHead.getShopCode(), listing));

        // 类目id
        if (ObjectUtils.isNotEmpty(listing.getPrimaryCategory())) {
            PlatformCategory platformCategory = platformCategoryService.getPlatformCategoryByCategoryId(goodsHead.getSiteCode(), listing.getPrimaryCategory().getCategoryID());
            ebayLine.setFirstCategoryid(ObjectUtils.isEmpty(platformCategory) ? listing.getPrimaryCategory().getCategoryID() : platformCategory.getCategoryId());
        }

        // 店铺品类
        if (ObjectUtils.isNotEmpty(listing.getStorefront())) {
            ebayLine.setFirstShopCategory(String.valueOf(listing.getStorefront().getStoreCategoryID()));
        }

        // 店铺第二品类
        if (ObjectUtils.isNotEmpty(listing.getStorefront())) {
            ebayLine.setSecondShopCategory(String.valueOf(listing.getStorefront().getStoreCategory2ID()));
        }

        // 建立平台品类与店铺品类关系
        if (ObjectUtils.isNotEmpty(listing.getStorefront()) && ObjectUtils.isNotEmpty(ebayLine.getFirstCategoryid()) && ObjectUtils.isNotEmpty(ebayLine.getFirstShopCategory())) {
            ConfigEbayCategoryRelation configEbayCategoryRelation = new ConfigEbayCategoryRelation();
            configEbayCategoryRelation.setCategoryId(Long.valueOf(ebayLine.getFirstCategoryid()));
            configEbayCategoryRelation.setShopCode(listing.getShopCode());
            configEbayCategoryRelation.setSite(listing.getSiteCode());
            configEbayCategoryRelation.setShopCategoryId(Long.valueOf(ebayLine.getFirstShopCategory()));

            List<ConfigEbayCategoryRelation> relationList = configEbayCategoryRelationService.selectConfigEbayCategoryRelationList(configEbayCategoryRelation);
            if (CollectionUtils.isEmpty(relationList)) {
                configEbayCategoryRelationService.insertConfigEbayCategoryRelation(configEbayCategoryRelation);
            }
        }

        // 最低售价 - 拍卖时填
        ebayLine.setReservePrice(getReservePrice(goodsHead.getPublishType(), listing));

        // 一口价 - 拍卖时填
        ebayLine.setOnePrice(getOnePrice(goodsHead.getPublishType(), listing));

        // 币种
        String currency = PlatformSiteEnum.getCurrency("EB", goodsHead.getSiteCode());
        ebayLine.setCurrency(StringUtil.isNotBlank(currency) ? currency : CurrencyCodeType.USD.value());

        // 起卖数量
        if (ObjectUtils.isNotEmpty(listing.getQuantityInfo())) {
            ebayLine.setStartSellingCount(listing.getQuantityInfo().getMinimumRemnantSet().toString());
        } else {
            ebayLine.setStartSellingCount("1");
        }

        // 是否开启议价
        if (ObjectUtils.isNotEmpty(listing.getBestOfferDetails())) {
            ebayLine.setBestOfferFlag(listing.getBestOfferDetails().isBestOfferEnabled() ? "1" : "0");
        }
        //uuid
        if (ObjectUtils.isNotEmpty(listing.getUUID())) {
            ebayLine.setUuid(listing.getUUID());
        }

        // 插入或更新数据
        if (flag) {
            ebayGoodsLineService.insertListingEbayLine(ebayLine);
        } else {
            ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHead.getId());
            if (ObjectUtils.isNotEmpty(listingEbayLine)) {
                ebayLine.setId(listingEbayLine.getId());
                ebayGoodsLineService.updateListingEbayLine(ebayLine);
            } else {
                ebayGoodsLineService.insertListingEbayLine(ebayLine);
            }
        }

        return ebayLine.getId();
    }


    protected String getOnePrice(Integer publishType, EbayItemTypeDTO listing) {
        ListingDetailsType listingDetails = listing.getListingDetails();
        if (!Objects.equals(publishType,PublishType.CHINESE.getType())||ObjectUtils.isEmpty(listing)||ObjectUtils.isEmpty(listingDetails)){
            return null;
        }
        AmountType convertedBuyItNowPrice = listingDetails.getConvertedBuyItNowPrice();
        if (ObjectUtils.isEmpty(convertedBuyItNowPrice)){
            return null;
        }
        return String.valueOf(convertedBuyItNowPrice.getValue());
    }

    /**
     *
     * @param publishType
     * @param listing
     * @return
     */
    protected String getReservePrice(Integer publishType, EbayItemTypeDTO listing) {
        ListingDetailsType listingDetails = listing.getListingDetails();
        if (!Objects.equals(publishType,PublishType.CHINESE.getType())||ObjectUtils.isEmpty(listing)||ObjectUtils.isEmpty(listingDetails)){
            return null;
        }
        AmountType convertedReservePrice = listingDetails.getConvertedReservePrice();
        if (ObjectUtils.isEmpty(convertedReservePrice)){
            return null;
        }
        return String.valueOf(convertedReservePrice.getValue());
    }

    /**
     * 插入属性数据
     * @param flag
     * @param lineId
     * @param listing
     */
    private void intoEbayValue(boolean flag, Integer lineId, EbayItemTypeDTO listing) {
        if (ObjectUtils.isEmpty(listing.getItemSpecifics()) || ObjectUtils.isEmpty(listing.getItemSpecifics().getNameValueList())) {
            return;
        }
        if (ObjectUtils.isNotEmpty(listing.getItemSpecifics())) {
            if (!flag) {
                //先删除
                ebayValueService.deleteListingEbayValueByListingLineId(lineId);
            }
            for (NameValueListType nameValueListType : listing.getItemSpecifics().getNameValueList()) {
                ListingEbayValue ebayValue = new ListingEbayValue();
                ebayValue.setListingLineId(lineId);
                ebayValue.setName(nameValueListType.getName());
                if (nameValueListType.getValue().length > 1) {
                    ebayValue.setValue(ObjectUtils.isNotEmpty(nameValueListType.getValue()) ? Arrays.toString(nameValueListType.getValue()).substring(1, Arrays.toString(nameValueListType.getValue()).length() - 1) : null);
                } else {
                    ebayValue.setValue(nameValueListType.getValue()[0]);
                }
                ebayValue.setCreateBy(listing.getCreateBy());
                ebayValueService.insertListingEbayValue(ebayValue);
            }
        }
    }


    /**
     * 入库 包裹信息
     *
     * @param flag
     * @param headId
     * @param listing
     */
    private void intoGoodsSpecification(boolean flag, Integer headId, EbayItemTypeDTO listing) {
        if (ObjectUtils.isNotEmpty(listing.getShippingPackageDetails())) {
            if (!flag) {
                //先删除
                goodsSpecificationService.deleteListingGoodsSpecificationByGoodsId(headId);
            }
            GoodsSpecification goodsSpecification = new GoodsSpecification();
            goodsSpecification.setGoodsId(headId);
            goodsSpecification.setPackageLength(ObjectUtils.isNotEmpty(listing.getShippingPackageDetails().getPackageLength()) ? listing.getShippingPackageDetails().getPackageLength().getValue() : null);//长
            goodsSpecification.setPackageWidth(ObjectUtils.isNotEmpty(listing.getShippingPackageDetails().getPackageWidth()) ? listing.getShippingPackageDetails().getPackageWidth().getValue() : null);//宽
            goodsSpecification.setPackageHeight(ObjectUtils.isNotEmpty(listing.getShippingPackageDetails().getPackageDepth()) ? listing.getShippingPackageDetails().getPackageDepth().getValue() : null);//高
            goodsSpecification.setPackageTypeLbs(listing.getShippingPackageDetails().getWeightMajor().getValue());//lbs
            goodsSpecification.setPackageTypeOz(listing.getShippingPackageDetails().getWeightMinor().getValue());//oz
            goodsSpecification.setMeasurementUnit(ObjectUtils.isNotEmpty(listing.getShippingPackageDetails().getPackageWidth()) ? listing.getShippingPackageDetails().getPackageWidth().getMeasurementSystem().value() : null);//测量单位
            goodsSpecification.setIsIrregularity(listing.getShippingPackageDetails().isShippingIrregular() ? "1" : "0");//是否不规则
            goodsSpecificationService.insertListingGoodsSpecification(goodsSpecification);
        }
    }


    /**
     * 入库 图片
     *
     * @param headId
     * @param listing
     */
    private void intoGoodsResource(Integer headId, EbayItemTypeDTO listing) {

        // 检查 PictureDetails 和 PictureURL 是否为空
        if (ObjectUtils.isNotEmpty(listing.getPictureDetails()) && ObjectUtils.isNotEmpty(listing.getPictureDetails().getPictureURL())) {
            String[] pictureURL = listing.getPictureDetails().getPictureURL();

            // 删除旧的图片资源记录
            goodsResourceService.deleteListingGoodsResourceByHeadId(headId);

            for (int i = 0; i < pictureURL.length; i++) {
                // 检查图片 URL 是否为空
                if (ObjectUtils.isNotEmpty(pictureURL[i])) {
                    GoodsResource goodsResource = new GoodsResource();
                    goodsResource.setGoodsId(headId);
                    goodsResource.setResourceUrl(imageHandleBiz.handleEpsUrl(pictureURL[i]));
                    goodsResource.setResourceType("1");

                    if (i == 0) {
                        // 主图
                        goodsResource.setIsMain(1);
                    } else {
                        // 副图
                        goodsResource.setIsMain(0);
                    }

                    // 插入图片资源记录
                    goodsResourceService.insertListingGoodsResource(goodsResource);
                }
            }
        }
    }



    /**
     * 入库 描述信息
     *
     * @param headId
     * @param listing
     */
    private void intoGoodsDescription(Integer headId, EbayItemTypeDTO listing) {
        // 检查 listing 的 Description 是否为空
        if (ObjectUtils.isNotEmpty(listing) && ObjectUtils.isNotEmpty(listing.getDescription())) {
            GoodsDescription goodsDescription = new GoodsDescription();
            goodsDescription.setGoodsId(headId);
            goodsDescription.setDetailDescription(listing.getDescription());

            // 查询旧的描述信息
            GoodsDescription goodsDescriptionOld = goodsDescriptionService.selectDescriptionListByGoodsId(headId);

            // 如果存在旧的描述且描述ID不为空，直接返回
            if (ObjectUtils.isNotEmpty(goodsDescriptionOld) && ObjectUtils.isNotEmpty(goodsDescriptionOld.getDescriptionId())) {
                return;
            }

            // 如果有旧描述但描述ID为空，先删除旧的描述记录
            if (ObjectUtils.isNotEmpty(goodsDescriptionOld)) {
                goodsDescriptionService.deleteListingGoodsDescriptionByHeadId(headId);
            }

            // 插入新的描述信息
            goodsDescriptionService.insertListingGoodsDescription(goodsDescription);
        }
    }

    /**
     * 物流信息
     * @param flag
     * @param lineId
     * @param listing
     */
    private void intoEbayShipLine(boolean flag, Integer lineId, EbayItemTypeDTO listing) {
        if (ObjUtil.isEmpty(listing.getDispatchTimeMax())) {
            return;
        }

        if (!flag) {
            ListingEbayShippingHead shippingHead = ebayShippingHeadService.selectListingEbayShippingHeadByLineId(lineId);
            if (ObjectUtils.isNotEmpty(shippingHead)) {
                ebayShippingHeadService.deleteListingEbayShippingHeadById(shippingHead.getId());
            }
        }

        if (ObjectUtils.isNotEmpty(listing)) {
            ListingEbayShippingHead shippingHead = new ListingEbayShippingHead();
            shippingHead.setListingLineId(lineId);
            shippingHead.setCreateBy(listing.getCreateBy());

            if (ObjectUtils.isNotEmpty(listing.getDispatchTimeMax())) {
                shippingHead.setHandlingTime(String.valueOf(listing.getDispatchTimeMax()));
            }

            ebayShippingHeadService.insertListingEbayShippingHead(shippingHead);

            Integer shippingHeadId = shippingHead.getId();
            // 设置物流详情数据
            this.setShippingDetails(shippingHeadId, listing);
        }
    }

    /**
     * 物流详情数据
     * @param shippingHeadId
     * @param listing
     */
    private void setShippingDetails(Integer shippingHeadId, EbayItemTypeDTO listing) {
        ShippingDetailsType shippingDetails = listing.getShippingDetails();

        if (ObjectUtils.isNotEmpty(shippingDetails)) {
            ShippingServiceOptionsType[] shippingServiceOptions = shippingDetails.getShippingServiceOptions();

            if (ObjectUtils.isNotEmpty(shippingServiceOptions)) {
                // 物流信息
                List<ListingShippingTypeLine> shippingTypeLineList = new ArrayList<>();
                for (ShippingServiceOptionsType shippingServiceOption : shippingServiceOptions) {
                    if (ObjectUtils.isNotEmpty(shippingServiceOption)) {
                        ListingShippingTypeLine shippingTypeLine = new ListingShippingTypeLine();
                        shippingTypeLine.setShippingHeadId(Long.valueOf(shippingHeadId));

                        if (ObjectUtils.isNotEmpty(shippingServiceOption.getShippingService())) {
                            shippingTypeLine.setShippingService(shippingServiceOption.getShippingService());
                        }
                        if (ObjectUtils.isNotEmpty(shippingServiceOption.getShippingServiceCost())) {
                            shippingTypeLine.setShippingCost(String.valueOf(shippingServiceOption.getShippingServiceCost().getValue()));
                        }
                        if (ObjectUtils.isNotEmpty(shippingServiceOption.getShippingTimeMax())) {
                            shippingTypeLine.setShippingTimeMax(String.valueOf(shippingServiceOption.getShippingTimeMax()));
                        }
                        if (ObjectUtils.isNotEmpty(shippingServiceOption.getShippingTimeMin())) {
                            shippingTypeLine.setShippingTimeMin(String.valueOf(shippingServiceOption.getShippingTimeMin()));
                        }
                        if (ObjectUtils.isNotEmpty(shippingServiceOption.isFreeShipping())) {
                            shippingTypeLine.setFreeShippingFlag(Boolean.TRUE.equals(shippingServiceOption.isFreeShipping()) ? "1" : "0");
                        }

                        shippingTypeLine.setCreateBy(listing.getCreateBy());
                        shippingTypeLineList.add(shippingTypeLine);
                    }
                }
                shippingTypeLineService.insertList(shippingTypeLineList);
            }

            // 排除区域
            String[] excludeShipToLocation = shippingDetails.getExcludeShipToLocation();
            List<ListingShippingLocationLine> dbLocationLineList = new ArrayList<>();
            if (ObjUtil.isNotEmpty(excludeShipToLocation)) {
                List<EbayShippingExcludeLocation> ebayShippingExcludeLocationList = ebayShippingExcludeLocationService.getEbayShippingExcludeListByRegionAndSiteCode(null, ObjUtil.isNotEmpty(listing.getCountry()) ? listing.getCountry().value() : null);
                if (ObjUtil.isEmpty(ebayShippingExcludeLocationList)) {
                    return;
                }
                //该站点所有区域
                List<String> regionList = ebayShippingExcludeLocationList.stream().map(EbayShippingExcludeLocation::getRegion).distinct().collect(Collectors.toList());
                for (String e : excludeShipToLocation) {
                    if (regionList.contains(e)) {
                        //ebayShippingExcludeLocationList对应该区域的所有数据都需要加入到dbLocationLineList
                        List<EbayShippingExcludeLocation> regionExcludeLocationList = ebayShippingExcludeLocationList.stream().filter(f -> Objects.equals(f.getRegion(), e)).collect(Collectors.toList());
                        regionExcludeLocationList.forEach(f -> {
                            ListingShippingLocationLine locationLine = new ListingShippingLocationLine();
                            locationLine.setShippingHeadId(Long.valueOf(shippingHeadId));
                            locationLine.setRegion(f.getRegion());
                            locationLine.setLocation(f.getLocation());
                            dbLocationLineList.add(locationLine);
                        });
                    } else {
                        ListingShippingLocationLine locationLine = new ListingShippingLocationLine();
                        locationLine.setShippingHeadId(Long.valueOf(shippingHeadId));
                        locationLine.setRegion(Objects.requireNonNull(ebayShippingExcludeLocationList.stream().filter(f -> Objects.equals(f.getLocation(), e)).findFirst().orElse(null)).getRegion());
                        if (Objects.equals("PO Box",e)){
                            locationLine.setRegion("PO Box");
                        }
                        locationLine.setLocation(e);
                        dbLocationLineList.add(locationLine);
                    }
                }
                if (CollectionUtils.isNotEmpty(dbLocationLineList)) {
                    shippingLocationLineService.insertList(dbLocationLineList);
                }
            }
        }
    }


    /**
     * 入库 付款&&退货信息
     *
     * @param flag
     * @param lineId
     * @param listing
     */
    private void intoEbayPolicy(boolean flag, Integer lineId, EbayItemTypeDTO listing) {
        ListingEbayPolicy ebayPolicy = new ListingEbayPolicy();
        if (!flag) {
            ListingEbayPolicy existingPolicy = ebayPolicyService.selectListingEbayPolicyByLineId(lineId);
            if (ObjectUtils.isNotEmpty(existingPolicy)) {
                ebayPolicy = existingPolicy;
            }
        }
        ebayPolicy.setListingLineId(lineId);

        if (ObjectUtils.isNotEmpty(listing)) {
            ebayPolicy.setCreateBy(listing.getCreateBy());

            TemplateEbayPolicy templateEbayPolicy = templateEbayPolicyService.selectTemplateEbayPolicyByShopCode(listing.getShopCode());
            if (ObjectUtils.isNotEmpty(templateEbayPolicy)) {
                ebayPolicy.setCollectionAccount(templateEbayPolicy.getCollectionAccount());
            }

            this.setPaymentMethods(ebayPolicy, listing);
            this.setReturnPolicyType(ebayPolicy, listing);

            if (flag) {
                ebayPolicyService.insertListingEbayPolicy(ebayPolicy);
            } else {
                ebayPolicyService.updateListingEbayPolicy(ebayPolicy);
            }
        }
    }

    /**
     * 支付信息
     * @param ebayPolicy
     * @param listing
     */
    private void setPaymentMethods(ListingEbayPolicy ebayPolicy, EbayItemTypeDTO listing) {
        BuyerPaymentMethodCodeType[] paymentMethods = listing.getPaymentMethods();

        if (ObjectUtils.isNotEmpty(paymentMethods)) {
            ebayPolicy.setPaymentPolicy(paymentMethods[0].value());
            ebayPolicy.setImmediatePaymentFlag("1");
        }
    }

    /**
     * 退货信息
     * @param ebayPolicy
     * @param listing
     */
    private void setReturnPolicyType(ListingEbayPolicy ebayPolicy, EbayItemTypeDTO listing) {
        ReturnPolicyType returnPolicy = listing.getReturnPolicy();

        if (ObjectUtils.isEmpty(returnPolicy)) {
            return;
        }

        // 国内是否支持退货
        ebayPolicy.setDomesticReturnFlag("ReturnsAccepted".equals(returnPolicy.getReturnsAcceptedOption()) ? "1" : "0");
        if ("ReturnsAccepted".equals(returnPolicy.getReturnsAcceptedOption())) {
            // 国内退货天数
            if (ObjectUtils.isNotEmpty(returnPolicy.getReturnsWithinOption())) {
                ebayPolicy.setDomesticReturnDay(returnPolicy.getReturnsWithinOption().split("Days_")[1]);
            }
            ebayPolicy.setDomesticReturnBearer(returnPolicy.getShippingCostPaidByOption());
        }

        // 国际是否支持退货
        ebayPolicy.setInternationalReturnFlag("ReturnsAccepted".equals(returnPolicy.getInternationalReturnsAcceptedOption()) ? "1" : "0");
        if ("ReturnsAccepted".equals(returnPolicy.getInternationalReturnsAcceptedOption())) {
            // 国际退货天数
            if (ObjectUtils.isNotEmpty(returnPolicy.getInternationalReturnsWithinOption())) {
                ebayPolicy.setInternationalReturnDay(returnPolicy.getInternationalReturnsWithinOption().split("Days_")[1]);
            }
            ebayPolicy.setInternationalReturnBearer(returnPolicy.getInternationalShippingCostPaidByOption());
        }
    }


    public Integer getPublishStatus(ListingStatusCodeType listingStatus) {
        Integer publishStatus;
        if (Objects.equals(listingStatus.value(), "Active")) {
            publishStatus = PublishStatus.SALEING.getType();
        } else if (Objects.equals(listingStatus.value(), "Ended")) {
            publishStatus = PublishStatus.OFF_EBAY_ENDED.getType();  //已结束
        } else if (Objects.equals(listingStatus.value(), "Completed")) {
            publishStatus = PublishStatus.OFF_EBAY_COMPLETED.getType(); //已完成
        } else {
            publishStatus = PublishStatus.OFF_SALE.getType();  //非在售
        }
        return publishStatus;
    }

    protected Integer getPublishType(String shopCode, ItemType listing) {
        Integer publishType;
        if ( ObjUtil.isNotEmpty(listing.getVariations()) ){
            publishType = 2;
        }else if (ObjectUtils.isNotEmpty(listing.getListingType()) &&
                Objects.equals(listing.getListingType().value(), "FixedPriceItem")) {
            publishType = 3;
        } else if (ObjectUtils.isNotEmpty(listing.getListingType()) &&
                Objects.equals(listing.getListingType().value(), "Chinese")) {
            publishType = 4;

        } else {
            ListingLogPullRecord log = new ListingLogPullRecord();
            log.setShopCode(shopCode);
            log.setPlatformCode(PlatformTypeEnum.EB.name());
            log.setPlatformGoodsId(listing.getItemID());
            log.setPullDetail("刊登类型为" + (ObjectUtils.isEmpty(listing.getListingType()) ? "空" : listing.getListingType().value()) + "数据不全");
            listingLogPullRecordMapper.insertListingLogPullRecord(log);
            publishType = null;
        }
        return publishType;
    }

    protected String getSellDay(String shopCode, ItemType listing) {
        if ("GTC".equals(listing.getListingDuration())) {
            return "0";
        } else if (ObjUtil.isNotEmpty(listing.getListingDuration()) && listing.getListingDuration().contains("Days_")) {
            return listing.getListingDuration().split("Days_")[1];
        } else {
            ListingLogPullRecord log = new ListingLogPullRecord();
            log.setPlatformCode(PlatformTypeEnum.EB.name());
            log.setShopCode(shopCode);
            log.setPlatformGoodsId(listing.getItemID());
            log.setPullDetail("刊登天数为" + listing.getListingDuration() + "数据不全");
            listingLogPullRecordMapper.insertListingLogPullRecord(log);
            return null;
        }
    }

    /**
     * 重新上架lisitng
     *
     * @param goodsHeads
     * @param headIdTaskMap
     */
    @Override
    public void relistItem(List<GoodsHead> goodsHeads, String createBy, Map<String, List<TaskConfiguration>> headIdTaskMap) {
        if (CollectionUtils.isEmpty(goodsHeads)) {
            return;
        }

        String userId = StringUtils.isBlank(createBy) ? String.valueOf(ShiroUtils.getUserId()) : createBy;
        Map<String, List<GoodsHead>> shopCodeMap = goodsHeads.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));


        for (String shopCode : shopCodeMap.keySet()) {
            List<GoodsHead> goodsHeadList = shopCodeMap.get(shopCode);
            Shop shop = shopService.selectShopByShopCode(shopCode);
            if (ObjUtil.isEmpty(shop)) {
                log.error("Ebay商品重上架--店铺不存在,店铺为:{}", shopCode);
                continue;
            }
            goodsHeadList.forEach(goodsHead -> {
                resurfacePoolConfig.execute(() -> {
                    String platformGoodsId = goodsHead.getPlatformGoodsId();
                    try {
                        String price = commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), goodsHead.getPublishType(), goodsHead.getSiteCode(),null,goodsHead.getStandardPrice());
                        goodsHead.setStandardPrice(price);

                        //回写原适配
                        ebayAdaptiveService.writerEbayAdapter(platformGoodsId, goodsHead.getId(), goodsHead.getShopCode());

                        List<TaskConfiguration> taskConfigurations = headIdTaskMap.get(String.valueOf(goodsHead.getId()));
                        if (ObjUtil.isEmpty(taskConfigurations)) {
                            grounding(goodsHead);
                        } else {
                            //定时下架
                            TaskConfiguration taskConfiguration = taskConfigurations.get(0);
                            String isOff = taskConfiguration.getIsOff();
                            //如果是自定义下架的数据且已经下架成功，则执行重上架
                            if (ObjUtil.equals(isOff, String.valueOf(SMCCommonEnum.VICTORY.getValue()))) {
                                relistByApi(goodsHead);
                            } else {
                                //没有下架成功或者不是自定义下架的数据都执行先下架再上架
                                grounding(goodsHead);
                            }
                        }
                    } catch (Exception e) {
                        log.error(String.format("Ebay商品重上架--商品ID为:%s,商品编码为:%s的商品重上架失败", goodsHead.getId(), goodsHead.getPdmGoodsCode()), e);
                        listingLogService.insertErrorListingLog("重上架主键[" + goodsHead.getId() + "]商品刊登失败，原平台销售编码[" + platformGoodsId + "]", userId, goodsHead.getId(), e.getMessage());
                        //更新任务表状态为3
                        TaskConfiguration taskConfiguration = new TaskConfiguration();
                        taskConfiguration.setIsSuccess(String.valueOf(SMCCommonEnum.RE_FAIL.getValue()));
                        taskConfiguration.setHeadId(String.valueOf(goodsHead.getId()));
                        taskConfigurationService.updateTaskConfigurationByHeadId(taskConfiguration);
                        //更新头表为刊登失败
                        GoodsHead goodsHead1 = new GoodsHead();
                        goodsHead1.setId(goodsHead.getId());
                        goodsHead1.setPublishStatus(PublishStatus.RE_LISTING_PUBLISH_FAIL.getType());
                        goodsHeadService.updateListingGoodsHead(goodsHead1);

                        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.TIMING_RESURFACE, GoodsTaskTypeEnum.DIRECT_RESURFACE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
                    }
                });
            });

        }

    }

    private void grounding(GoodsHead goodsHead) throws Exception {
        boolean isSuccess = ebayProductBiz.offEbay(goodsHead, PublishStatus.TIMED_RE_LISTING);
        if (!isSuccess) {
            return;
        }
        relistByApi(goodsHead);
    }


    /**
     * 直接调用additem接口进行重售
     *
     * @param goodsHead
     */
    private void relistByApi(GoodsHead goodsHead) throws Exception {
        ItemType ebayItem = buildEbayItemBiz.buildEbayItem(goodsHead.getId(), null);
        if (ObjectUtils.isEmpty(ebayItem)) {
            return;
        }
        ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHead.getId());
        if (ObjectUtil.isEmpty(listingEbayLine)) {
            return;
        }

        //重上架需要重新设置UUID
        String uuid = UUID.randomUUID().toString().replace("-", "");
        listingEbayLine.setUuid(uuid);
        ebayGoodsLineService.updateListingEbayLine(listingEbayLine);


        //清空平台销售编码
        goodsHead.setPlatformGoodsId(null);
        ItemDTO itemDTO = new ItemDTO();
        itemDTO.setEbayItem(ebayItem);
        itemDTO.setGoodsHead(goodsHead);
        itemDTO.setOperationName(OperationTypeEnum.RELIST.getName());
        itemDTO.setEbayUUID(uuid);
        GoodsHead goodsHead1 = new GoodsHead();
        goodsHead1.setId(goodsHead.getId());
        goodsHead1.setPublishStatus(PublishStatus.PUBLISHING.getType());
        goodsHeadService.clearPlatformGoodId(goodsHead1);

        //直接调用接口重售
        pushListingBiz.pushListing(goodsHead.getPlatform(), itemDTO, itemDTO.getOperationName());
        List<GoodsTaskTypeEnum> taskTypeEnum = new ArrayList<>();
        taskTypeEnum.add(GoodsTaskTypeEnum.TIMING_PUBLISH);
        taskTypeEnum.add(GoodsTaskTypeEnum.TIMING_RESURFACE);
        taskTypeEnum.add(GoodsTaskTypeEnum.DIRECT_RESURFACE);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
        taskTypeEnum.add(GoodsTaskTypeEnum.CIRCULATE_TIMED_TASK);
        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), taskTypeEnum, GoodsTaskSubStatusEnum.NORAML, "");

    }

    @Override
    public AjaxResult listingSave(ListingDTO listingDTO) {
        Long userId = ShiroUtils.getSysUser().getUserId();
        List<String> bussinessIds = new ArrayList<>();
        if (ObjUtil.equals(listingDTO.getPublishType(), PublishType.CHINESE.getType())) {
            listingDTO.setStandardPrice(String.valueOf(listingDTO.getEbayPrice()));
        }
        //1、基础数据,返回对应头表中的headId，有几个店铺就返回多少headId
        List<GoodsHead> goodsHeadList = goodsInfoBiz.packagingBasis(listingDTO);

        //2、商品图片视频数据
        ArrayList<GoodsResource> goodsResourceList = goodsInfoBiz.getGoodsResourceList(listingDTO);

        //3、商品描述数据
        GoodsDescription goodsDescription = new GoodsDescription();
        BeanUtil.copyProperties(listingDTO, goodsDescription);
        goodsDescription.setShortDescription(listingDTO.getShortDescription());
        goodsDescription.setDetailDescription(listingDTO.getDetailDescription());

        //4、商品属性数据
        List<ListingAmazonAttributeLine> attributeLinesInfo = listingDTO.getListingAmazonAttributeLinesInfo();

        // 处理品牌
        Optional<String> brandOptional = attributeLinesInfo.stream()
                .filter(attributeLine -> Objects.equals("BRAND", attributeLine.getTableName().trim().toUpperCase()))
                .map(ListingAmazonAttributeLine::getTableValue)
                .findFirst();
        //给头表设置品牌
        brandOptional.ifPresent(s -> goodsHeadList.forEach(goodsHead -> goodsHead.setBrandCode(s)));

        //5、商品规格数据
        GoodsSpecification goodsSpecification = new GoodsSpecification();
        BeanUtil.copyProperties(listingDTO, goodsSpecification);


        goodsHeadList.forEach(goodHead -> {
            //违禁词检测
            violateWordBiz.checkViolateWord(true,goodsDescription, attributeLinesInfo, goodHead);
        });

        for (GoodsHead goodHead : goodsHeadList) {
            try {
                listingDTO.setCountry(goodHead.getSiteCode());
                listingDTO.setLocation(goodHead.getCondition());
                goodsDescription.setDescriptionId(goodHead.getDescriptionId());
                //6、ebay行数据
                ListingEbayLine ebayLine = getEbayLine(goodHead, listingDTO);
                ItemDTO itemDTO = new ItemDTO();
                itemDTO.setGoodsHead(goodHead);
                itemDTO.setEbayLine(ebayLine);
                itemDTO.setUuid(listingDTO.getUuid());
                itemDTO.setOperationFlag(ListingBatchOptions.ADD.name());
                itemDTO.setGoodsResourceList(goodsResourceList);
                itemDTO.setGoodDescription(goodsDescription);
                itemDTO.setVideoId(goodHead.getVideoId());
                itemDTO.setGoodsSpecification(goodsSpecification);
                itemDTO.setGoodsAttributeLineList(listingDTO.getListingAmazonAttributeLinesInfo());
                itemDTO.setUserId(userId);
                Integer goodsId = ebayPlatformListingService.saveEbayInfo(itemDTO);
                bussinessIds.add(String.valueOf(goodsId));
                //处理库存黑名单
                inventoryExcludeBiz.addListingBySkuBlack(goodHead.getPlatform(), goodHead.getSiteCode(), goodHead.getShopCode(), Collections.singletonList(goodsId), userId);
            } catch (Exception e) {
                log.error("Ebay新建商品编码为：[" + listingDTO.getGoodsCode() + "]listing草稿失败", e);
                throw new RuntimeException(e);
            }
        }

        //根据条件将待办更新为处理中
        stockArrivesTodoService.updateStatusToProcessing(listingDTO.getGoodsCode(),Convert.toStr(userId), Convert.toInt(DateUtils.parseDateToStr("yyyyMMdd", new Date())));

        //处理AI文案使用表的id
        if("1".equals(listingDTO.getUseAI())) {
            try {
                handleAiUseId(listingDTO.getUuid(), goodsHeadList);
            } catch (Exception ex) {
                log.error("Ebay新建商品编码为：[" + listingDTO.getGoodsCode() + "]记录AI文案使用表失败", ex);
            }
        }

        // 处理一键刊登
        if (null != listingDTO.getPublishStatus()
                && PublishStatus.PUBLISHING.getType().toString().equals(listingDTO.getPublishStatus())) {
            List<String> ids = goodsHeadList.stream().map(o -> o.getId().toString()).collect(Collectors.toList());
            return listingInfoBiz.publishListingByIds(String.join(",", ids), userId);
        }

        return AjaxResult.success(bussinessIds);
    }

    private void handleAiUseId(String uuid, List<GoodsHead> goodsHeadList) {
        for (GoodsHead goodsHead : goodsHeadList) {
            String businessId = uuid + "#" + goodsHead.getShopCode();
            wordAiUsageService.updateBusinessId(businessId, goodsHead.getId()+"");
        }
    }


    /**
     * tips:ebay的适配数据并不在这个方法进行修改
     * 所以适配状态的扭转在下面进行
     * com.suncent.smc.web.controller.publication.property.ListingEbayAdaptiveController
     *
     * @param listingEditDTO
     */
    @Override
    public void listingEdit(ListingEditDTO listingEditDTO) {
        // 头部基础数据
        GoodsHead dbHead = goodsHeadService.selectListingGoodsHeadById(listingEditDTO.getGoodsHeadId());
        if(dbHead.getDelFlag().equals(2)) {
            throw new BusinessException("该链接已删除");
        }
        GoodsHead goodsHead = new GoodsHead();
        BeanUtil.copyProperties(listingEditDTO, goodsHead);
        goodsHead.setId(listingEditDTO.getGoodsHeadId());
        goodsHead.setCreateBy(dbHead.getCreateBy());
        boolean isUpdateSku = !ObjUtil.equal(dbHead.getPdmGoodsCode(), listingEditDTO.getGoodsCode());

        //ebay支持修改商品编码
        if (isUpdateSku) {
            goodsHead.setPdmGoodsCode(listingEditDTO.getGoodsCode());
        }

        //设置ebay价格
        String price = commonInfoBiz.checkPriceAndReturnSellerPrice(dbHead.getId(),listingEditDTO.getGoodsCode(), listingEditDTO.getStandardPrice(), dbHead.getPublishType(),dbHead.getSiteCode(),dbHead.getOnlineTime(),dbHead.getStandardPrice());
        goodsHead.setStandardPrice(price);
        goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
        //设置ebay类目
        goodsHead.setCategoryId(Integer.valueOf(listingEditDTO.getFirstCategoryid()));
        //状态流转
        if (StringUtils.isNotEmpty(listingEditDTO.getPublishStatus())) {
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(Integer.valueOf(listingEditDTO.getPublishStatus()), PlatformTypeEnum.EB.name()));
        } else {
            Integer publishStatus = dbHead.getPublishStatus();
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(publishStatus, PlatformTypeEnum.EB.name()));
        }


        // 商品图片视频数据
        GoodsResource goodsResource = new GoodsResource();
        BeanUtil.copyProperties(listingEditDTO, goodsResource);

        // 商品描述数据
        GoodsDescription goodsDescription = new GoodsDescription();
        BeanUtil.copyProperties(listingEditDTO, goodsDescription);
        goodsDescription.setId(listingEditDTO.getGoodsDescriptionId());
        goodsDescription.setGoodsId(listingEditDTO.getGoodsHeadId());
        goodsDescription.setDescriptionId(listingEditDTO.getTemplateSelect());

        // 商品规格数据
        GoodsSpecification goodsSpecification = new GoodsSpecification();
        BeanUtil.copyProperties(listingEditDTO, goodsSpecification);
        goodsSpecification.setId(listingEditDTO.getGoodsSpecificationId());
        if (StringUtils.isNotEmpty(listingEditDTO.getIsIrregularity())) {
            if (Objects.equals(goodsSpecification.getIsIrregularity(), "是")) {
                goodsSpecification.setIsIrregularity("1");
            } else if (Objects.equals(goodsSpecification.getIsIrregularity(), "否")) {
                goodsSpecification.setIsIrregularity("2");
            }
        }
        if (isUpdateSku) {
            //规格数据取新sku的
            GetGoodsDetailQueryDTO goodsDetailQueryDTO = new GetGoodsDetailQueryDTO();
            goodsDetailQueryDTO.setGoodsCodes(Collections.singletonList(goodsHead.getPdmGoodsCode()));
            List<SaleGoodsDTO> saleGoodsDTOS = pdmHttpRequestBiz.listGoodsDetail(goodsDetailQueryDTO);

            if (ObjUtil.isEmpty(saleGoodsDTOS)) {
                throw new BusinessException("商品不存在,商品编码:" + goodsHead.getPdmGoodsCode());
            }
            SaleGoodsDTO goodsDto = saleGoodsDTOS.get(0);
            GoodsSpecifications specifications = goodsDto.getSpecifications();
            if (ObjUtil.isNotEmpty(specifications)) {
                goodsSpecification.setPackageHeight(specifications.getHeight());
                goodsSpecification.setPackageHeightUnit(specifications.getHeightUnit());
                goodsSpecification.setPackageLength(specifications.getLength());
                goodsSpecification.setPackageLengthUnit(specifications.getLengthUnit());
                goodsSpecification.setPackageWidth(specifications.getWidth());
                goodsSpecification.setPackageWidthUnit(specifications.getWidthUnit());
                goodsSpecification.setPackageWeight(specifications.getGrossWeight());
                goodsSpecification.setPackageWeightUnit(specifications.getGrossWeightUnit());
            }

            //处理适配数据
            ebayAdaptiveService.updateAdapterByAds(listingEditDTO.getListingLineId(), goodsHead);
        }

        //4、商品属性数据
        List<ListingAmazonAttributeLine> attributeLinesInfo = listingEditDTO.getListingAmazonAttributeLinesInfo();

        // 处理品牌
        Optional<String> brandOptional = attributeLinesInfo.stream()
                .filter(attributeLine -> Objects.equals("BRAND", attributeLine.getTableName().trim().toUpperCase()))
                .map(ListingAmazonAttributeLine::getTableValue)
                .findFirst();
        //给头表设置品牌
        brandOptional.ifPresent(goodsHead::setBrandCode);

        // 违禁词检测
        violateWordBiz.checkViolateWord(true,goodsDescription, listingEditDTO.getListingAmazonAttributeLinesInfo(), goodsHead);
        ItemDTO itemDTO = new ItemDTO();
        itemDTO.setGoodsHead(goodsHead);
        itemDTO.setGoodDescription(goodsDescription);
        itemDTO.setGoodsSpecification(goodsSpecification);
        itemDTO.setVideoId(listingEditDTO.getVideoSelect());

        ebayPlatformListingService.updateEbayInfoToDB(listingEditDTO, itemDTO);

        if (ObjUtil.isEmpty(listingEditDTO.getTemplateSelect())) {
            goodsDescriptionService.updateListingDescriptionIdIsNull(listingEditDTO.getGoodsDescriptionId());
        }

        //去将待办改为处理中 更新中的listing 待办就是处理中
        TodoStatusEnum todoStatusEnum = ObjUtil.equals(goodsHead.getPublishStatus(), UPDATING.getType()) ? TodoStatusEnum.HANDLE_STATUS : TodoStatusEnum.FINISH_STATUS;
        smcTodoBiz.updateTodoStatusByListingUpdate(goodsHead.getId(),todoStatusEnum);
        //sku进行了修改 且链接是在售的
        if (isUpdateSku) {
            listingLogService.insertSuccessListingLog("ebay单个编辑修改商品编码由" + dbHead.getPdmGoodsCode() + "变更为" + goodsHead.getPdmGoodsCode() + "成功",
                    dbHead.getCreateBy(), goodsHead.getId());
        }
        if (isUpdateSku && ObjUtil.isNotEmpty(dbHead.getPlatformGoodsId()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus())) {
            ebayProductBiz.addPdmStatus(goodsHead, true);

        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEbayInfoToDB(ListingEditDTO listingEditDTO, ItemDTO itemDTO) {
        //给头表设置品牌
        if (ObjectUtils.isNotEmpty(listingEditDTO.getListingAmazonAttributeLinesInfo())) {
            String brand = listingEditDTO.getListingAmazonAttributeLinesInfo().stream()
                    .filter(attributeLine -> Objects.equals("BRAND", attributeLine.getTableName().trim().toUpperCase()))
                    .map(ListingAmazonAttributeLine::getTableValue).findFirst().orElse(null);

            itemDTO.getGoodsHead().setBrandCode(brand);
        }
        GoodsHead goodsHead = itemDTO.getGoodsHead();
        GoodsDescription goodsDescription = itemDTO.getGoodDescription();
        GoodsSpecification goodsSpecification = itemDTO.getGoodsSpecification();
        //设置适配状态流转
        List<ListingEbayAdaptive> ebayAdaptiveList = ebayAdaptiveService.selectListByEbayLineId(listingEditDTO.getListingLineId());
        goodsHead.setAdaptationStatus(AdaptationStatusEnum.getAdaptionByListingStatus(goodsHead.getPublishStatus(), ObjectUtils.isNotEmpty(ebayAdaptiveList)));

        goodsHeadService.updateListingGoodsHead(goodsHead);

        goodsSpecificationService.updateListingGoodsSpecification(goodsSpecification);

        //修改描述
        goodsDescriptionService.updateListingGoodsDescriptionByHeadId(goodsDescription);
        //处理主图的问题
        listingInfoBiz.saveMasterImgUrl(listingEditDTO);
        //处理视频的问题
        listingInfoBiz.saveAndUpdateVide(goodsHead.getId(),itemDTO.getVideoId());
        //处理了附图12张的问题
        listingInfoBiz.saveGoodsResourceList(listingEditDTO);
        //ebay修改数据
        listingInfoBiz.eBayFixData(listingEditDTO, listingEditDTO.getListingLineId());

        //处理了ebay的属性
        listingInfoBiz.saveEbayAttributeTable(listingEditDTO, listingEditDTO.getListingLineId());
        //处理修改的付款方式
        listingInfoBiz.saveUpdateEbayPolicy(listingEditDTO, listingEditDTO.getListingLineId());
        //处理修改的运输方式
        listingInfoBiz.saveUpdateEbayShip(listingEditDTO, listingEditDTO.getListingLineId());
    }


    /**
     * 批量删除Listing
     *
     * @param goodsHeads Listing ID集合
     * @return
     */
    @Override
    public Integer deleteListing(List<GoodsHead> goodsHeads) {
        if (CollectionUtils.isEmpty(goodsHeads)) {
            return 0;
        }
        List<Integer> idList = goodsHeads.stream().map(GoodsHead::getId).collect(Collectors.toList());
        String ids = idList.stream().map(String::valueOf).collect(Collectors.joining(","));
        for (Integer id : idList) {
            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(id), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_DELETE), GoodsTaskSubStatusEnum.NORAML, "");
        }
        return listingService.deleteListingByIds(ids);
    }

    /**
     * 通过headId获取listingEditDTO
     *
     * @param listingIds
     * @return
     */
    @Override
    public ListingsEditVO getListingsEditDTOSByListingIds(List<Integer> listingIds) {
        if (CollectionUtils.isEmpty(listingIds)) {
            throw new BusinessException("该批次未生成有效Listing，不能查看详情进行确认");
        }
        Map<String, String> shopMap = shopService.listShop().stream().collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue()));
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));

        Map<Integer, GoodsHead> headMap = goodsHeadList.stream().map(e -> {
            if (StringUtils.isNotEmpty(e.getTitle())) {
                e.setTitle(e.getTitle().replaceAll("\"", "“"));
                e.setTitle(e.getTitle().replaceAll("'", "’"));
            }
            return e;
        }).collect(Collectors.toMap(GoodsHead::getId, e -> e));

        List<String> shopList = goodsHeadList.stream().map(GoodsHead::getShopCode).distinct().collect(Collectors.toList());
        String[] shopCodes = shopList.toArray(new String[shopList.size()]);


        Map<Integer, List<GoodsDescription>> descMap = goodsDescriptionService.selectDescriptionListByGoodsIdList(listingIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsDescription::getGoodsId));

        Map<Integer, List<GoodsResource>> resourceMap = goodsResourceService.selectListingGoodsResourceByHeadIds(listingIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsResource::getGoodsId));

        Map<Integer, List<GoodsSpecification>> specificationMap = goodsSpecificationService.selectSpecificationListByGoodsIds(listingIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsSpecification::getGoodsId));
        List<ListingEbayLine> ebayLines = ebayGoodsLineService.selectListingEbayLineByHeadIds(listingIds.stream().toArray(Integer[]::new));
        Map<Integer, List<ListingEbayLine>> lineMap = ebayLines.stream().collect(Collectors.groupingBy(ListingEbayLine::getListingHeadId));

        List<Integer> linesId = ebayLines.stream().map(ListingEbayLine::getId).collect(Collectors.toList());

        Map<Integer, List<ListingEbayValue>> ebayAttribute = ebayValueService.selectListingEbayValueByLineIds(linesId)
                .stream()
                .collect(Collectors.groupingBy(ListingEbayValue::getListingLineId));

        Map<String, List<ListingEbayVideo>> listingVideoMap = ebayVideoService.selectListingEbayVideoByGoodsIds(listingIds).stream().collect(Collectors.groupingBy(ListingEbayVideo::getGoodsId));
        Map<String, List<ShopEbayVideo>> shopVideoMap = shopEbayVideoService.selectLiveShopEbayVideoList(shopCodes).stream().collect(Collectors.groupingBy(ShopEbayVideo::getShopCode));



        Map<String, String> shopCodeNameMap = new HashMap<>();
        List<ListingEditDTO> arrayList = new ArrayList<>();
        for (Integer id : listingIds) {
            ListingEditDTO listingEditDTO = new ListingEditDTO();
            GoodsHead goodsHead = headMap.get(id);
            Integer goodsId = goodsHead.getId();

            // 商品基础数据
            setBasicToEditDTO(listingEditDTO, goodsHead);
            if (shopCodeNameMap.containsKey(goodsHead.getShopCode())) {
                listingEditDTO.setShopName(shopCodeNameMap.get(goodsHead.getShopCode()));
            } else {
                String shopName = shopService.selectShopNameByShopCode(goodsHead.getShopCode());
                listingEditDTO.setShopName(shopName);
                shopCodeNameMap.put(goodsHead.getShopCode(), shopName);
            }

            // 商品图片视频数据
            List<GoodsResource> goodsResourceList = resourceMap.get(goodsId);
            listingEditDTO.setGoodsImageList(getGoodsImages(goodsResourceList));

            // 商品描述数据
            List<GoodsDescription> goodsDescriptions = descMap.get(goodsId);
            if (CollUtil.isNotEmpty(goodsDescriptions)) {
                GoodsDescription goodsDescription = goodsDescriptions.get(0);
                setDescriptionToEditDTO(listingEditDTO, goodsDescription);
            }

            // 商品规格数据
            List<GoodsSpecification> goodsSpecifications = specificationMap.get(goodsId);
            if (CollUtil.isNotEmpty(goodsSpecifications)) {
                GoodsSpecification goodsSpecification = goodsSpecifications.get(0);
                setSpecificationToEditDTO(goodsSpecification, listingEditDTO);
            }

            // 商品属性数据
            List<ListingEbayLine> listingEbayLines = lineMap.get(goodsId);
            if (CollUtil.isEmpty(listingEbayLines)) {
                throw new BusinessException("数据异常,请联系管理员!id:"+ goodsId);
            }
            List<ListingEbayValue> listingEbayValues = ebayAttribute.get(lineMap.get(goodsId).get(0).getId());
            if (CollUtil.isNotEmpty(listingEbayValues)) {
                listingEditDTO.setListingAmazonAttributeLinesInfo(listingEbayValues.stream().map(e -> {
                    ListingAmazonAttributeLine info = new ListingAmazonAttributeLine();
                    info.setTableName(e.getName());
                    info.setTableValue(e.getValue());
                    return info;
                }).collect(Collectors.toList()));
            }

            //基础信息其他等
            ListingEbayLine ebayLine = lineMap.get(goodsId).get(0);
            listingEditDTO.setFirstCategoryid(ObjUtil.isEmpty(ebayLine.getFirstCategoryid()) ? null : String.valueOf(ebayLine.getFirstCategoryid()));
            listingEditDTO.setFirstShopCategory(ObjUtil.isEmpty(ebayLine.getFirstShopCategory()) ? null : String.valueOf(ebayLine.getFirstShopCategory()));
            listingEditDTO.setSecondCategoryid(ObjUtil.isEmpty(ebayLine.getSecondCategoryid()) ? null : String.valueOf(ebayLine.getSecondCategoryid()));
            listingEditDTO.setSecondShopCategory(ObjUtil.isEmpty(ebayLine.getSecondShopCategory()) ? null : String.valueOf(ebayLine.getSecondShopCategory()));
            listingEditDTO.setLocation(ebayLine.getLocation());
            listingEditDTO.setPublicationDay(ebayLine.getSellDay());
            listingEditDTO.setStartSellingCount(ebayLine.getStartSellingCount());
            listingEditDTO.setBestOfferFlag(ebayLine.getBestOfferFlag());
            listingEditDTO.setProductCategoryCode(ObjUtil.isEmpty(goodsHead.getCategoryId()) ? null : String.valueOf(goodsHead.getCategoryId()));
            listingEditDTO.setShopName(shopMap.get(goodsHead.getShopCode()));
            //视频信息
            listingEditDTO.setVideoSelect(ObjUtil.isEmpty(listingVideoMap.get(String.valueOf(goodsId))) ? null : listingVideoMap.get(String.valueOf(goodsId)).get(0).getVideoId());
            if (ObjUtil.isNotEmpty(shopVideoMap)) {
                List<ShopEbayVideo> videoList = shopVideoMap.get(goodsHead.getShopCode());
                if (videoList != null && !videoList.isEmpty()) {
                    Map<String, String> videoSelectMap = videoList.stream()
                            .collect(Collectors.toMap(ShopEbayVideo::getVideoId, ShopEbayVideo::getTitle));
                    listingEditDTO.setVideoSelectMap(videoSelectMap);
                }
            }
            arrayList.add(listingEditDTO);
        }
        if (CollectionUtils.isEmpty(arrayList)) {
            throw new BusinessException("该批次未生成有效Listing，不能查看详情进行确认");
        }
        //按照商品编码排序
        arrayList = arrayList.stream().sorted(Comparator.comparing(ListingEditDTO::getGoodsCode)).collect(Collectors.toList());
        ListingsEditVO listingsEditVO = new ListingsEditVO();
        listingsEditVO.setListingEditDTOS(arrayList);
        listingsEditVO.setSiteCode(ObjUtil.isEmpty(arrayList.get(0).getSiteCode()) ? "US" : arrayList.get(0).getSiteCode());
        listingsEditVO.setCondition(arrayList.get(0).getCondition());
        listingsEditVO.setPublishType(arrayList.get(0).getPublishType());
        listingsEditVO.setPublishTypeName(PublishType.getTypeName(arrayList.get(0).getPublishType()));
        listingsEditVO.setShopCode(arrayList.get(0).getShopCode());
        listingsEditVO.setFirstCategoryid(arrayList.get(0).getFirstCategoryid());
        listingsEditVO.setFirstShopCategory(arrayList.get(0).getFirstShopCategory());
        listingsEditVO.setSecondCategoryid(arrayList.get(0).getSecondCategoryid());
        listingsEditVO.setSecondShopCategory(arrayList.get(0).getSecondShopCategory());
        listingsEditVO.setLocation(arrayList.get(0).getLocation());
        listingsEditVO.setPublicationDay(arrayList.get(0).getPublicationDay());
        listingsEditVO.setStartSellingCount(arrayList.get(0).getStartSellingCount());
        listingsEditVO.setBestOfferFlag(arrayList.get(0).getBestOfferFlag());
        return listingsEditVO;
    }

    @Override
    public void syncShopListingInfo(SyncListingDTO dto) {
        if (ObjectUtils.isEmpty(dto) || CollectionUtils.isEmpty(dto.getShopList())) {
            throw new BusinessException("同步店铺信息失败，店铺为空");
        }
        List<Shop> shopList = dto.getShopList();
        String userId = dto.getUserId();
        for (Shop shop : shopList) {
            String version = shop.getShopCode() + "_" + ((int) Math.floor(Math.random() * (9999 - 1000 + 1) + 1000)) + "_" + System.currentTimeMillis();
            GoodsTask goodsTask = goodsTaskService.insertStoreSyncTask(version, PlatformTypeEnum.EB.name(), 0, userId);

            //设置redis缓存，防止同步任务重复执行 过期时间6H
            if (redisService.exists(RedisKeyEnum.EBAY_SYNC_SHOP_Listing.getKey() + shop.getShopCode())) {
                goodsTask.setTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo());
                goodsTask.setRemark("该店铺在6小时之内已同步过，请不要重复同步.");
                goodsTaskService.updateGoodsTask(goodsTask);

                throw new BusinessException("店铺" + shop.getShopCode() + "正在同步中，请稍后再试");
            }
            ebayPullPoolConfig.execute(() -> {
                try {
                    redisService.setCacheObject(RedisKeyEnum.EBAY_SYNC_SHOP_Listing.getKey() + shop.getShopCode(), shop.getShopCode(), 6L, TimeUnit.HOURS);

                    AjaxResult handleResult =  retryable.retryableToApiMsg(()-> getEbayListingByShop(dto, shop));
                    if (handleResult.isSuccess() && ObjectUtils.isNotEmpty(handleResult.get(AjaxResult.DATA_TAG))) {
                        List<EbaySellerListing> apiData = JSON.parseArray(handleResult.get(AjaxResult.DATA_TAG).toString(), EbaySellerListing.class);

                        //回写任务总数
                        goodsTask.setTaskStatus(GoodsTaskStatusEnum.IN_PROGRESS.getInfo());
                        goodsTask.setTaskNum(apiData.size());
                        goodsTaskService.updateGoodsTask(goodsTask);

                        //优先处理handlerStatus为0的数据
                        List<EbaySellerListing> addList = apiData.stream().filter(e -> e.getHandlerStatus() == 0).collect(Collectors.toList());
                        syncListingByShop(SyncEBListingDTO.builder().shop(shop).apiData(addList).version(version).goodsTask(goodsTask).userId(userId).build());
                        List<EbaySellerListing> updateList = apiData.stream().filter(e -> e.getHandlerStatus() == 1).collect(Collectors.toList());
                        syncListingByShop(SyncEBListingDTO.builder().shop(shop).apiData(updateList).version(version).goodsTask(goodsTask).userId(userId).build());

                        goodsTaskService.updateGoodsTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo(), goodsTask.getId());
                    } else {
                        throw new BusinessException("错误信息：" + handleResult.get(AjaxResult.MSG_TAG));
                    }
                } catch (Exception e) {
                    log.error("同步店铺信息失败，店铺编码：{}，错误信息：{}", shop.getShopCode(), e);
                    goodsTask.setTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo());
                    goodsTask.setRemark(e.getMessage());
                    goodsTaskService.updateGoodsTask(goodsTask);
                }
                log.info("同步店铺信息成功，店铺编码：{}", shop.getShopCode());
            });
        }


    }

    /**
     * 根据店铺以及时间查询listing
     * @param dto
     * @param shop
     * @return
     */
    protected AjaxResult getEbayListingByShop(SyncListingDTO dto, Shop shop) {
        EbayGetSellerVO vo = new EbayGetSellerVO();
        vo.setAccountCode(shop.getShopCode());
        //开始时间时间为空查询店铺所有的listing
        vo.setStartTime(ObjectUtils.isNotEmpty(dto.getStartTime()) ? dto.getStartTime() : null);
        vo.setEndTime(ObjectUtils.isNotEmpty(dto.getStartTime()) ? dto.getStartTime() : DateUtils.getNowDate());
        String handle = HttpUtils.post(EBAY_ALL_LISTINGITEMID_URL, JSON.toJSONString(vo));
        AjaxResult handleResult = JSON.parseObject(handle, AjaxResult.class);
        return handleResult;
    }

    /**
     * 更新api该数据处理状态
     *
     * @param shopCode
     * @param itemID
     */
    public AjaxResult verifyConsumer(String shopCode, String itemID) {
        //更新api该数据处理状态
        EbayGetSellerVO vo = new EbayGetSellerVO();
        vo.setAccountCode(Utils.getShopCode(shopCode));
        vo.setItemIdList(Collections.singletonList(itemID));
        String resp = HttpUtils.post(UPDATE_SELLER_LISTING_HANDLE_STATUSURL, JSON.toJSONString(vo));
        AjaxResult handleResult = JSON.parseObject(resp, AjaxResult.class);
        log.info("店铺:{},Ebay同步listing处理状态 -平台销售编码：{},更新api该数据处理状态：{}", shopCode, itemID, resp);
        return handleResult;
    }

    /**
     * 同步店铺listing信息
     *
     * @param syncDTO
     */
    private void syncListingByShop(SyncEBListingDTO syncDTO) {
        Shop shop = syncDTO.getShop();
        List<EbaySellerListing> apiData = syncDTO.getApiData();
        String version = syncDTO.getVersion();
        GoodsTask goodsTask = syncDTO.getGoodsTask();
        String userId = syncDTO.getUserId();

        for (EbaySellerListing listing : apiData) {
            try {

                GoodsHead head = new GoodsHead();
                head.setPlatform(PlatformTypeEnum.EB.name());
                head.setShopCode(shop.getShopCode());
                head.setPlatformGoodsId(listing.getItemId());
                AjaxResult itemResp = getEbayItemAjaxResult(shop.getShopCode(), listing.getItemId());

                if (itemResp.isSuccess()) {
                    ItemType item = JSON.parseObject(String.valueOf(itemResp.get(AjaxResult.DATA_TAG)), ItemType.class);

                    List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadList(head);
                    //只同步在售的listing
                    if (!ObjectUtils.equals(item.getSellingStatus().getListingStatus().value(), "Active")) {
                        retryable.retryableToApiMsg(()->verifyConsumer(shop.getShopCode(), listing.getItemId()));
                        for (GoodsHead goodsHead : goodsHeadList) {
                            goodsHead.setPublishStatus(PublishStatus.OFF_SALE.getType());
                            goodsHeadService.updateListingGoodsHead(goodsHead);
                            listingLogService.insertSuccessListingLog("商品编码为：[" + goodsHead.getPdmGoodsCode() + "]同步listing成功", userId, goodsHead.getId());
                        }
                        goodsTaskService.addSuccessNum(goodsTask.getId());
                        continue;
                    }
                    if (CollectionUtils.isEmpty(goodsHeadList)) {
                        GoodsHead addHead = new GoodsHead();
                        addHead.setShopCode(shop.getShopCode());
                        addHead.setSiteCode(PlatformSiteEnum.US.getName());
                        EbayItemTypeDTO dto = new EbayItemTypeDTO();
                        BeanUtil.copyProperties(item, dto);
                        syncListing(addHead, dto,userId);
                    } else {
                        //这里出现多条数据，需要删除多余数据，在同一个店铺中itemId是唯一的
                        if (ObjectUtils.isNotEmpty(goodsHeadList)) {
                            handleListingGoodsHead(goodsHeadList);
                        }

                        EbayItemTypeDTO dto = new EbayItemTypeDTO();
                        BeanUtil.copyProperties(item, dto);
                        syncListing(goodsHeadList.get(0), dto,userId);
                    }


                    goodsTaskService.addSuccessNum(goodsTask.getId());
                } else {
                    log.error("同步店铺信息失败，店铺编码：{}，平台销售编码：{}，错误信息：{}", shop.getShopCode(), listing.getItemId(), itemResp.get(AjaxResult.MSG_TAG));
                    saveEBLog(shop.getShopCode(), listing.getItemId(), "获取Listing详情失败:" + itemResp.get(AjaxResult.MSG_TAG), version);
                }
            } catch (Exception e) {
                saveEBLog(shop.getShopCode(), listing.getItemId(), "组装Listing失败:" + e.getMessage(), version);
            }

        }
    }

    /**
     * 获取ebay item详情
     * @param goodsHead
     * @return
     */
    public AjaxResult getEbayItem(GoodsHead goodsHead) {
        String accountCode = goodsHead.getShopCode();
        String itemId = goodsHead.getPlatformGoodsId();
        AjaxResult itemResp = getEbayItemAjaxResult(accountCode, itemId);
        if (!itemResp.isSuccess()) {
            platformErrorBiz.handlePlatformError(goodsHead, String.valueOf(itemResp.get(AjaxResult.MSG_TAG)));
        }
        return itemResp;
    }

    /**
     * 获取ebay item详情
     *
     * @param goodsHead
     * @return
     */
    public AjaxResult getEbayItemV2(EbayGoodsHeadV2 goodsHead) {
        String accountCode = goodsHead.getShopCode();
        String itemId = goodsHead.getPlatformGoodsId();
        AjaxResult itemResp = getEbayItemAjaxResult(accountCode, itemId);
        if (!itemResp.isSuccess()) {
            platformErrorBiz.handleEbayErrorV2(goodsHead, String.valueOf(itemResp.get(AjaxResult.MSG_TAG)));
        }
        return itemResp;
    }


    public AjaxResult getEbayItemAjaxResult(String accountCode, String itemId) {
        UrlReplaceEntity urlReplaceEntity = new UrlReplaceEntity();
        urlReplaceEntity.setUrl(GET_ITEM_URL);
        urlReplaceEntity.setAccountCode(accountCode);
        urlReplaceEntity.setItemId(itemId);
        String itemData = HttpUtils.get(Utils.replaceUrl(urlReplaceEntity));
        AjaxResult itemResp = JSONObject.parseObject(itemData, AjaxResult.class);
        return itemResp;
    }

    @Override
    public void syncShopLostListingInfo(SyncListingDTO dto) {
        if (ObjectUtils.isEmpty(dto) || CollectionUtils.isEmpty(dto.getShopList()) || ObjectUtils.isEmpty(dto.getItemId())) {
            throw new BusinessException("同步店铺信息失败，店铺或者平台销售编码为空");
        }
        List<Shop> shopList = dto.getShopList();
        List<String> ids = dto.getItemId();
        String userId = dto.getUserId();
        //查询店铺下面是否在smc中存在
        GoodsHead query = new GoodsHead();
        query.setPlatform(PlatformTypeEnum.EB.name());
        query.setShopCode(shopList.get(0).getShopCode());
        query.setPlatformGoodsIdList(ids);
        List<GoodsHead> existList = goodsHeadService.selectListingGoodsHeadList(query);
        //这里出现多条数据，需要删除多余数据，在同一个店铺中itemId是唯一的
        if (CollectionUtils.isNotEmpty(existList)) {
            handleListingGoodsHead(existList);
        }
        GoodsTask goodsTask = goodsTaskService.insertListingTotalTask(GoodsTaskTypeEnum.SYNC_LOST, PlatformTypeEnum.EB.name(), ids.size(), String.valueOf(userId));
        GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
        goodsTaskInfo.setTaskId(goodsTask.getId());
        goodsTaskInfo.setPlatform(PlatformTypeEnum.EB.name());
        goodsTaskInfo.setCreateBy(String.valueOf(userId));

        for (Shop shop : shopList) {
            ebayPullPoolConfig.execute(() -> {
                for (String id : ids) {
                    GoodsHead head = new GoodsHead();
                    head.setPlatformGoodsId(id);
                    head.setShopCode(shop.getShopCode());
                    AjaxResult itemResp = getEbayItem(head);

                    if (itemResp.isSuccess()) {
                        ItemType item = JSONObject.toJavaObject((JSONObject)itemResp.get(AjaxResult.DATA_TAG) , ItemType.class);

                        GoodsHead goodsHead = existList.stream().filter(e -> ObjectUtils.equals(e.getPlatformGoodsId(), id)).findFirst().orElse(new GoodsHead());
                        goodsHead.setShopCode(shop.getShopCode());
                        goodsHead.setSiteCode(StrUtil.isEmpty(shop.getSiteCode()) ? PlatformSiteEnum.US.getName() : shop.getSiteCode());
                        EbayItemTypeDTO itemDto = new EbayItemTypeDTO();
                        BeanUtil.copyProperties(item, itemDto, true);
                        syncListing(goodsHead, itemDto,userId);
                        listingLogService.insertSuccessListingLog("手动同步缺失listing,平台销售编码为：[" + id+ "]同步listing成功", userId, goodsHead.getId());

                        goodsTaskService.addSuccessNum(goodsTask.getId());
                        goodsTaskInfo.setListingHeadId(String.valueOf(goodsHead.getId()));
                        goodsTaskInfo.setStatus(GoodsTaskSubStatusEnum.NORAML.getSubStatus());
                        goodsTaskInfoService.insertGoodsTaskInfo(goodsTaskInfo);
                    } else {
                        goodsTaskInfo.setListingHeadId(id);
                        goodsTaskInfo.setStatus(GoodsTaskSubStatusEnum.ERROR.getSubStatus());
                        goodsTaskInfo.setErrorMsg("同步缺失Listing信息失败,平台销售编码：" + id + "，原因：" + itemResp.get(AjaxResult.MSG_TAG));
                        goodsTaskInfoService.insertGoodsTaskInfo(goodsTaskInfo);
                        throw new BusinessException("同步缺失Listing信息失败，店铺编码：" + shop.getShopCode() + "，平台销售编码：" + id + "，原因：" + itemResp.get(AjaxResult.MSG_TAG));
                    }
                }
            });

        }

        if (ObjUtil.isNotEmpty(goodsTask.getId())) {
            goodsTaskService.updateGoodsTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo(), goodsTask.getId());
        }


    }

    /**
     * 处理已经存在的listing
     * 现以smc_flag分别做不同的处理
     *
     * @param existList
     */
    public void handleListingGoodsHead(List<GoodsHead> existList) {
        if (ObjectUtils.isEmpty(existList)) {
            return;
        }
        if (existList.size()==1){
            return;
        }
        //根据platform_goods_id分组
        Map<String, List<GoodsHead>> listMap = existList.stream().collect(Collectors.groupingBy(GoodsHead::getPlatformGoodsId));
        List<GoodsHead> removeList = new ArrayList<>();
        //循环listMap处理
        listMap.forEach((k, v) -> {
            //对同一个platform_goods_id的数据按照smc_flag进行分组
            Map<Integer, List<GoodsHead>> smcFlagMap = v.stream().collect(Collectors.groupingBy(GoodsHead::getSmcFlag));
            List<GoodsHead> localListingList = smcFlagMap.get(0);
            if (ObjectUtils.isNotEmpty(localListingList)) {
                //只保留localListingList createTime最早的一条数据  并把其他smc_flag的数据从existList移除
                GoodsHead goodsHead = localListingList.stream().min(Comparator.comparing(GoodsHead::getCreateTime)).orElse(new GoodsHead());
                removeList.addAll(existList.stream().filter(e -> ObjectUtils.equals(e.getPlatformGoodsId(), goodsHead.getPlatformGoodsId())
                        && !ObjectUtils.equals(e.getId(), goodsHead.getId())).collect(Collectors.toList()));
                existList.removeIf(e -> ObjectUtils.equals(e.getPlatformGoodsId(), goodsHead.getPlatformGoodsId()) && !ObjectUtils.equals(e.getId(), goodsHead.getId()));
                return;
            }
            List<GoodsHead> autoListingList = smcFlagMap.get(2);
            if (ObjectUtils.isNotEmpty(autoListingList)) {
                //只保留autoListingList createTime最早的一条数据  并把其他smc_flag的数据从existList移除
                GoodsHead goodsHead = autoListingList.stream().min(Comparator.comparing(GoodsHead::getCreateTime)).orElse(new GoodsHead());
                removeList.addAll(existList.stream().filter(e -> ObjectUtils.equals(e.getPlatformGoodsId(), goodsHead.getPlatformGoodsId())
                        && !ObjectUtils.equals(e.getId(), goodsHead.getId())).collect(Collectors.toList()));
                existList.removeIf(e -> ObjectUtils.equals(e.getPlatformGoodsId(), goodsHead.getPlatformGoodsId()) && !ObjectUtils.equals(e.getId(), goodsHead.getId()));
                return;

            }
            List<GoodsHead> syncListingList = smcFlagMap.get(1);
            if (ObjectUtils.isNotEmpty(syncListingList)) {
                //只保留syncListingList createTime最早的一条数据  并把其他smc_flag的数据从existList移除
                GoodsHead goodsHead = syncListingList.stream().min(Comparator.comparing(GoodsHead::getCreateTime)).orElse(new GoodsHead());
                removeList.addAll(existList.stream().filter(e -> ObjectUtils.equals(e.getPlatformGoodsId(), goodsHead.getPlatformGoodsId())
                        && !ObjectUtils.equals(e.getId(), goodsHead.getId())).collect(Collectors.toList()));
                existList.removeIf(e -> ObjectUtils.equals(e.getPlatformGoodsId(), goodsHead.getPlatformGoodsId()) && !ObjectUtils.equals(e.getId(), goodsHead.getId()));

            }
        });
        //删除多余的数据
        if (CollUtil.isNotEmpty(removeList)) {
            goodsHeadService.deleteListingGoodsHeadByIds("删除重复listing", removeList.stream().map(GoodsHead::getId).collect(Collectors.toList()).toArray(new Integer[removeList.size()]));
        }
    }


    @Override
    public void saveBatchTempListingDTO(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }

        String siteCode = shopService.selectSiteCodeByShopCode(dto.getShopCode());
        String taskName = listings.get(0).getTaskName();
        boolean save = StrUtil.isEmpty(taskName);

        //获取商品对应的属性数据
        List<String> listingAttributeLine = dto.getListingAttributeLine();
        if (CollectionUtils.isEmpty(listingAttributeLine)) {
            throw new RuntimeException("属性行全部为空,请检查.");
        }
        //获取商品对应的属性数据
        Map<String, List<ListingAmazonAttributeLine>> goodsCodeMap = getGoodsCodeAttributeLineMap(dto, false);

        List<ItemDTO> itemDTOList = new ArrayList<>();
        for (ListingDTO listing : listings) {
            //组装头信息
            GoodsHead goodHead = getGoodHead(dto, siteCode, listing);
            goodHead.setCategoryId(ObjectUtils.isEmpty(dto.getFirstCategoryid()) ? null : Integer.valueOf(dto.getFirstCategoryid()));
            goodHead.setSiteCode(ObjectUtils.isNotEmpty(dto.getSiteCode()) ? dto.getSiteCode() : "US");
            goodHead.setTaskName(taskName);
            goodHead.setDelFlag(3);
            if (!save) {
                goodHead.setId(listing.getId());
            }

            setlistingInfoByDTO(dto, listing);
            listing.setCountry(goodHead.getSiteCode());

            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(goodHead);
            itemDTO.setEbayLine(getEbayLine(goodHead, listing));
            itemDTO.setOperationFlag(ListingBatchOptions.ADD_BATCH.name());
            itemDTO.setGoodsResourceList(getResourceList(dto, listing));
            GoodsDescription goodsDescription = getGoodsDescription(listing);
            if("batchSave".equals(dto.getTag())) {
                goodsDescription.setShortDescription(goodsDescription.getDetailDescription());
                goodsDescription.setDetailDescription(null);
            }
            itemDTO.setGoodDescription(goodsDescription);
            itemDTO.setGoodsSpecification(getGoodsSpecification(listing, userId));
            itemDTO.setGoodsAttributeLineList(goodsCodeMap.get(goodHead.getPdmGoodsCode()));
            itemDTO.setUserId(userId);
            itemDTOList.add(itemDTO);
        }

        Map<String, Set<String>> violateWord = new HashMap<>();
        List<String> checkPrice = new LinkedList<>();
        for (ItemDTO itemDTO : itemDTOList) {
            try {
                GoodsHead goodsHead = itemDTO.getGoodsHead();
                //校验价格
                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), dto.getPublishType(), goodsHead.getSiteCode(),null,goodsHead.getStandardPrice());
                goodsHead.setStandardPrice(price);
                goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
            } catch (Exception e) {
                checkPrice.add(e.getMessage());
            }
        }

        if (ObjUtil.isNotEmpty(violateWord) || CollectionUtil.isNotEmpty(checkPrice)) {
            assembleWordBefore(violateWord, checkPrice);
        }

        //创建任务
        GoodsTask goodsTask = new GoodsTask();
        if (save) {
            taskName = dto.getShopCode() + ((int) Math.floor(Math.random() * (9999 - 1000 + 1) + 1000)) + System.currentTimeMillis();
            goodsTask.setTaskName(taskName);
            goodsTask.setTaskType(GoodsTaskTypeEnum.BATCH_TEMP_SAVE.getInfo());
            goodsTask.setTaskNum(listings.size());
            goodsTask.setTaskStatus("进行中");
            goodsTask.setSuccessNum(0);
            goodsTask.setPlatform(PlatformTypeEnum.EB.name());
            goodsTask.setCreateBy(String.valueOf(userId));
            goodsTask.setRemark("手动保存");
            goodsTaskService.insertGoodsTask(goodsTask);
        }

            resurfacePoolConfig.execute(() -> {
                List<Integer> ids = new ArrayList<>();
                for (ItemDTO itemDTO : itemDTOList) {
                    GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
                    goodsTaskInfo.setTaskId(goodsTask.getId());
                    goodsTaskInfo.setPlatform(PlatformTypeEnum.EB.name());
                    goodsTaskInfo.setCreateBy(String.valueOf(userId));
                    goodsTaskInfo.setPdmGoodsCode(itemDTO.getGoodsHead().getPdmGoodsCode());
                    try {
                        ebayPlatformListingService.saveAndUpdateEbayInfo(itemDTO);
                        goodsTaskInfo.setStatus("0");
                        goodsTaskInfo.setListingHeadId(String.valueOf(itemDTO.getGoodsHead().getId()));
                        ids.add(itemDTO.getGoodsHead().getId());
                    } catch (Exception e) {
                        goodsTaskInfo.setStatus("1");
                        goodsTaskInfo.setErrorMsg(e.toString());
                        log.error("临时批量保存ebay listing信息失败，店铺编码：{}，错误信息：{}", dto.getShopCode(), e);
                    }
                    //任务详情记录
                    if (save) {
                        goodsTaskInfoService.insertGoodsTaskInfo(goodsTaskInfo);
                    }
                }

                if ("Y".equals(dto.getUseAI())) {
                    List<String> pdmGoodsCodes = itemDTOList.stream().map(ItemDTO::getGoodsHead).map(GoodsHead::getPdmGoodsCode).collect(Collectors.toList());
                    try {
                        aiGenerationTaskBiz.createAndSubmitAiTasks(
                                Integer.valueOf(goodsTask.getId()),
                                pdmGoodsCodes,
                                dto.getShopCode(),
                                dto.getBrandCode(),
                                String.valueOf(userId)
                        );
                    }catch (Exception ex) {
                        log.error("创建AI任务失败,商品编码:{}", pdmGoodsCodes, ex);
                    }
                } else {
                    GoodsTask updateTask = new GoodsTask();
                    updateTask.setId(goodsTask.getId());
                    updateTask.setTaskStatus("已完成");
                    updateTask.setSuccessNum(save ? ids.size() : null);
                    goodsTaskService.updateGoodsTask(updateTask);
                }
            });

    }

    /**
     * 批量修改Listing商品信息
     *
     * @param dto
     */
    @Override
    public void updateBatchListingDTO(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        //是否临时保存 true 临时保存 ，false 直接保存
        Boolean tempFlag = ObjUtil.equals(dto.getTempFlag(), "true");
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }

        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.BATCH_EDIT, goodsIds, String.valueOf(userId));

        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.stream().toArray(Integer[]::new)).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));

        Map<Integer, List<GoodsDescription>> descMap = goodsDescriptionService.selectDescriptionListByGoodsIdList(goodsIds).stream().collect(Collectors.groupingBy(GoodsDescription::getGoodsId));

        Map<Integer, List<GoodsSpecification>> specificationMap = goodsSpecificationService.selectSpecificationListByGoodsIds(goodsIds).stream().collect(Collectors.groupingBy(GoodsSpecification::getGoodsId));

        Map<Integer, List<ListingEbayLine>> lineMap = ebayGoodsLineService.selectListingEbayLineByHeadIds(goodsIds.stream().toArray(Integer[]::new)).stream().collect(Collectors.groupingBy(ListingEbayLine::getListingHeadId));

        Map<String, List<ListingAmazonAttributeLine>> goodsIdMap = getGoodsCodeAttributeLineMap(dto, false);

        for (ListingDTO listing : listings) {
            GoodsHead goodsHead = headMap.get(listing.getId());
            try {
                ListingEbayLine listingEbayLine = lineMap.get(listing.getId()).get(0);
                GoodsSpecification goodsSpecification = specificationMap.get(listing.getId()).get(0);
                GoodsDescription goodsDescription = descMap.get(listing.getId()).get(0);
                Integer statusByEdit = PublishStatus.getStatusByEdit(goodsHead.getPublishStatus(), PlatformTypeEnum.EB.name());

                //组装头信息
                GoodsHead updateHead = new GoodsHead();
                updateHead.setShopCode(goodsHead.getShopCode());
                updateHead.setId(listing.getId());
                updateHead.setStockOnSalesQty(listing.getStockOnSalesQty());
                updateHead.setTitle(listing.getTitle());
                if (StringUtils.isNotEmpty(updateHead.getTitle())) {
                    updateHead.setTitle(updateHead.getTitle().replaceAll("“", "\""));
                    updateHead.setTitle(updateHead.getTitle().replaceAll("’", "'"));
                }
                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),goodsHead.getPdmGoodsCode(), listing.getStandardPrice(), goodsHead.getPublishType(),goodsHead.getSiteCode(),goodsHead.getOnlineTime(),goodsHead.getStandardPrice());
                updateHead.setStandardPrice(price);
                updateHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
                if (!tempFlag) {
                    updateHead.setPublishStatus(statusByEdit);
                }

                //line
                listingEbayLine.setSellDay(ObjectUtils.isEmpty(listing.getPublicationDay()) ? "GTC" : (listing.getPublicationDay().contains(",") ? listing.getPublicationDay().split(",")[0] : listing.getPublicationDay()));
                listingEbayLine.setStartSellingCount(ObjectUtils.isEmpty(listing.getStartSellingCount()) ? "1" : String.valueOf(listing.getStartSellingCount()));
                listingEbayLine.setBestOfferFlag(ObjectUtils.isEmpty(listing.getBestOfferFlag()) ? "0" : String.valueOf(listing.getBestOfferFlag()));

                //描述
                getGoodsDescription(listing, goodsDescription);

                //物流
                getGoodsSpecification(listing, goodsSpecification);
                goodsSpecification.setPackageLength(ObjUtil.isEmpty(listing.getItemLength()) ? BigDecimal.valueOf(0) : listing.getItemLength().setScale(2, RoundingMode.HALF_UP));
                goodsSpecification.setPackageWidth(ObjUtil.isEmpty(listing.getItemWidth()) ? BigDecimal.valueOf(0) : listing.getItemWidth().setScale(2, RoundingMode.HALF_UP));
                goodsSpecification.setPackageHeight(ObjUtil.isEmpty(listing.getItemHeight()) ? BigDecimal.valueOf(0) : listing.getItemHeight().setScale(2, RoundingMode.HALF_UP));

                listing.setProductCategoryCode(String.valueOf(goodsHead.getCategoryId()));
                ItemDTO itemDTO = new ItemDTO();
                itemDTO.setGoodsHead(updateHead);
                itemDTO.setEbayLine(listingEbayLine);
                itemDTO.setGoodsResourceList(getResourceList(dto, listing));
                itemDTO.setGoodDescription(goodsDescription);
                itemDTO.setGoodsSpecification(goodsSpecification);
                itemDTO.setGoodsAttributeLineList(goodsIdMap.get(String.valueOf(updateHead.getId())));
                itemDTO.setUserId(userId);

                ebayPlatformListingService.saveAndUpdateEbayInfo(itemDTO);

                if (!PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改商品编码为：[" + listing.getGoodsCode() + "]listing失败", e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT), GoodsTaskSubStatusEnum.ERROR, String.valueOf(e.getMessage()));
                throw new RuntimeException(e);
            }
        }
    }

    private void setlistingInfoByDTO(BatchListingDTO dto, ListingDTO listing) {
//        listing.setCurrency(ObjectUtils.isEmpty(dto.getCurrency()) ? PlatformSiteEnum.US.getCurrency() : dto.getCurrency());
//        listing.setCountry(ObjectUtils.isEmpty(dto.getCountry()) ? PlatformSiteEnum.US.getName() : dto.getCountry());
        listing.setProductCategoryCode(ObjectUtils.isEmpty(dto.getFirstCategoryid()) ? null : dto.getFirstCategoryid());
        listing.setLocation(ObjectUtils.isEmpty(dto.getLocation()) ? null : dto.getLocation());
        listing.setFirstCategoryid(ObjectUtils.isEmpty(dto.getFirstCategoryid()) ? null : dto.getFirstCategoryid());
        listing.setSecondCategoryid(ObjectUtils.isEmpty(dto.getSecondCategoryid()) ? null : dto.getSecondCategoryid());
        listing.setFirstShopCategory(ObjectUtils.isEmpty(dto.getFirstShopCategory()) ? null : dto.getFirstShopCategory());
        listing.setSecondShopCategory(ObjectUtils.isEmpty(dto.getSecondShopCategory()) ? null : dto.getSecondShopCategory());
    }

    /**
     * 快捷更新
     * 不需要做listing状态扭转
     * getNoUpdateStatus没有过滤 草稿跟非在售数据只需要更新本地  更新中的数据 定时任务会进行一起更新
     * @param dto
     * @param goodsHead
     */
    @Override
    public void quickUpdate(ListingQuickEditDTO dto, GoodsHead goodsHead) {
        //如果是刊登中 更新中 下架中的数据 不允许修改
        if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
            throw new RuntimeException("listing状态为刊登中、更新中、下架中、非在售的数据不允许修改");
        }
        //修改本地数据
        String sellerPrice = dto.getStandardPrice().contains("(") ? dto.getStandardPrice().split("\\(")[0] : dto.getStandardPrice();
        String finalPrice = commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),goodsHead.getPdmGoodsCode(), sellerPrice, goodsHead.getPublishType(),goodsHead.getSiteCode(),goodsHead.getOnlineTime(),goodsHead.getStandardPrice());

        String standardPriceOld = goodsHead.getStandardPrice();
        BigDecimal stockOnSalesQtyOld = goodsHead.getStockOnSalesQty();

        goodsHead.setStandardPrice(finalPrice);
        goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(finalPrice)));

        goodsHead.setStockOnSalesQty(dto.getStockOnSalesQty());
        goodsHeadService.updateListingGoodsHead(goodsHead);

        List<String> recordList = getRecordList(dto.getStockOnSalesQty(), finalPrice, standardPriceOld, stockOnSalesQtyOld);

        //在售或者更新失败的的数据需要调ebay api 更新  价格库存标题
        if (goodsHead.getPublishStatus().equals(PublishStatus.SALEING.getType()) || goodsHead.getPublishStatus().equals(PublishStatus.UPDATING_FAIL.getType())) {
            AjaxResult ajaxResult = retryable.retryableToApiMsg(()->updateEbayPriceInventory(goodsHead));
            if (ajaxResult.isSuccess()){
                smcTodoBiz.updateTodoStatusByListingUpdate(goodsHead.getId(),TodoStatusEnum.FINISH_STATUS);
                intoUpdateRecord(goodsHead, recordList,0);
            }
        }

    }

    /**
     * 更新ebay价格和库存
     * @param goodsHead
     * @return
     */
    private AjaxResult updateEbayPriceInventory(GoodsHead goodsHead) {
        EbayInventoryVO vo = new EbayInventoryVO();
        vo.setAccountCode(goodsHead.getShopCode());
        EbayInventoryVO.EbayInventory ebayInventory = new EbayInventoryVO.EbayInventory();
        ebayInventory.setItemId(goodsHead.getPlatformGoodsId());
        ebayInventory.setQuantity(ObjectUtils.isEmpty(goodsHead.getStockOnSalesQty()) ? null : goodsHead.getStockOnSalesQty().intValue());
        ebayInventory.setStartPrice(ObjectUtils.isEmpty(goodsHead.getStandardPrice()) ? null : new BigDecimal(goodsHead.getStandardPrice()));
        vo.setEbayInventoryList(Collections.singletonList(ebayInventory));
        String data = HttpUtils.post(REVISE_INVENTORY_STATUS_URL, JSONObject.toJSONString(vo));
        AjaxResult ajaxResult = JSONObject.parseObject(data, AjaxResult.class);
        return ajaxResult;
    }

    private List<String> getRecordList(BigDecimal stockOnSalesQty, String finalPrice, String standardPriceOld, BigDecimal stockOnSalesQtyOld) {
        List<String> recordList = new ArrayList<>();
        if (!ObjUtil.equals(standardPriceOld, finalPrice)) {
            recordList.add(ListingModuleType.PRICE.name());
        }
        if (!ObjUtil.equals(Double.valueOf(String.valueOf(stockOnSalesQtyOld)), Double.valueOf(String.valueOf(stockOnSalesQty))) ) {
            recordList.add(ListingModuleType.INVENTORY.name());
        }
        return recordList;
    }

    /**
     * 更新图片
     *
     * @param dto
     */
    @Override
    public void batchUpdatePictures(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE, goodsIds, String.valueOf(userId));
        List<ItemDTO> itemDTOList = new ArrayList<>();
        for (ListingDTO listing : listings) {
            try {
                List<GoodsResource> resourceList = getResourceList(dto, listing);
                GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                for (GoodsResource resource : resourceList) {
                    resource.setGoodsId(goodsHead.getId());
                }
                //goodsResourceList
                if (ObjectUtils.isNotEmpty(resourceList.stream().filter(g -> ObjectUtils.isNotEmpty(g.getGoodsId())).collect(Collectors.toList()))) {
                    goodsResourceService.deleteListingGoodsResourceByHeadId(goodsHead.getId());
                    goodsResourceService.insertListingGoodsResourceBatch(resourceList);
                }
                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Arrays.asList(ListingModuleType.IMAGE.name(), ListingModuleType.DESCRIPTION.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE), GoodsTaskSubStatusEnum.NORAML, "");
                }
                //处理待办
                brandAdaptImgTodoService.confirmByGoodId(listing.getId(), userId, "2");
                if (CollectionUtils.isNotEmpty(listing.getExsitImgArrs())) {
                    String adapatLogString = listingLogService.compareImagesChanged(listing.getImgArrs(),listing.getExsitImgArrs());
                    listingLogService.insertSuccessListingLog("适配图片变更待办处理成功," + adapatLogString, String.valueOf(userId), listing.getId());
                }
            } catch (Exception e) {
                log.error("批量修改listing图片失败,listingId:{}", listing.getId(), e);
                brandAdaptImgTodoService.confirmByGoodId(listing.getId(),userId, "3");
                listingLogService.insertSuccessListingLog("适配图片变更待办处理失败", String.valueOf(userId), listing.getId());
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });
    }

    /**
     * 批量修改标题
     *
     * @param dto
     */
    @Override
    public void batchUpdateTitle(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.BATCH_UPDATE_TITLE, goodsIds, String.valueOf(userId));

        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        Map<String, Set<String>> violateWord = new HashMap<>();

        for (ListingDTO listing : listings) {
            try {
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (ObjUtil.isEmpty(goodsHead)) {
                    continue;
                }
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                goodsHead.setTitle(listing.getTitle());
                violateWordBiz.checkViolateWord(true,null, null, goodsHead);

                GoodsHead updateHead = new GoodsHead();
                updateHead.setId(goodsHead.getId());
                updateHead.setTitle(listing.getTitle());
                if (StringUtils.isNotEmpty(updateHead.getTitle())) {
                    updateHead.setTitle(updateHead.getTitle().replaceAll("“", "\""));
                    updateHead.setTitle(updateHead.getTitle().replaceAll("’", "'"));
                }
                goodsHeadService.updateListingGoodsHead(updateHead);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Collections.singletonList(ListingModuleType.TITLE.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改listing标题失败,listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
                if (e instanceof BusinessException){
                    assembleWord(violateWord, e.getMessage());
                }
            }
        }

        if ( ObjUtil.isNotEmpty(violateWord)  ){
            assembleWordBefore(violateWord);
        }

        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });
    }

    /**
     * 批量修改价格和库存
     *
     * @param dto
     */
    @Override
    public void batchUpdatePriceAndStock(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK, goodsIds, String.valueOf(userId));
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        Map<Integer, List<ListingEbayLine>> lineMap = ebayGoodsLineService.selectListingEbayLineByHeadIds(goodsIds.stream().toArray(Integer[]::new)).stream().collect(Collectors.groupingBy(ListingEbayLine::getListingHeadId));
        for (ListingDTO listing : listings) {
            try {
                //head
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                if(!Objects.equals(dto.getTag(),"STOCK")){
                    String price = commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),goodsHead.getPdmGoodsCode(), listing.getStandardPrice(), goodsHead.getPublishType(), goodsHead.getSiteCode(),goodsHead.getOnlineTime(),goodsHead.getStandardPrice());
                    goodsHead.setStandardPrice(price);
                    goodsHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
                }
                goodsHead.setStockOnSalesQty(ObjectUtils.isEmpty(listing.getStockOnSalesQty()) ? null : listing.getStockOnSalesQty());
                goodsHeadService.updateListingGoodsHead(goodsHead);

                ListingEbayLine listingEbayLine = lineMap.get(listing.getId()).get(0);
                listingEbayLine.setSellDay(ObjectUtils.isEmpty(listing.getPublicationDay()) ? "GTC" : (listing.getPublicationDay().contains(",") ? listing.getPublicationDay().split(",")[0] : listing.getPublicationDay()));
                listingEbayLine.setStartSellingCount(ObjectUtils.isEmpty(listing.getStartSellingCount()) ? "1" : String.valueOf(listing.getStartSellingCount()));
                listingEbayLine.setBestOfferFlag(ObjectUtils.isEmpty(listing.getBestOfferFlag()) ? "0" : String.valueOf(listing.getBestOfferFlag()));
                ebayGoodsLineService.updateListingEbayLine(listingEbayLine);
                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Arrays.asList(ListingModuleType.INVENTORY.name(), ListingModuleType.PRICE.name(), ListingModuleType.EBAY_LINE.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改listing价格和库存失败,listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });

        //处理待办的状态
        loseCartTodoService.confirmByGoodIds(goodsIds, userId);
        inventoryLowTodoService.confirmByGoodIds(goodsIds, userId);
    }

    /**
     * 批量修改属性
     *
     * @param dto
     */
    @Override
    public void batchUpdateAttribute(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE, goodsIds, String.valueOf(userId));
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        Map<Integer, List<ListingEbayLine>> lineMap = ebayGoodsLineService.selectListingEbayLineByHeadIds(goodsIds.stream().toArray(Integer[]::new)).stream().collect(Collectors.groupingBy(ListingEbayLine::getListingHeadId));
        Map<String, List<ListingAmazonAttributeLine>> goodsIdMap = getGoodsCodeAttributeLineMap(dto, false);
        for (ListingDTO listing : listings) {
            try {
                //去重map
                Map<String, String> repeatVerMap = new HashMap<>();
                List<ListingAmazonAttributeLine> attributeLineList = goodsIdMap.get(String.valueOf(listing.getId()));
                ListingEbayLine listingEbayLine = lineMap.get(listing.getId()).get(0);
                //处理品牌
                String brand = attributeLineList.stream()
                        .filter(attributeLine -> Objects.equals("BRAND", attributeLine.getTableName().trim().toUpperCase()))
                        .map(ListingAmazonAttributeLine::getTableValue).findFirst().orElse(null);
                GoodsHead goodsHead = headMap.get(listing.getId());

                violateWordBiz.checkViolateWord(true,null, attributeLineList, goodsHead);

                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中、非在售的数据不允许修改");
                }
                goodsHead.setBrandCode(brand);
                goodsHeadService.updateListingGoodsHead(goodsHead);

                List<ListingEbayValue> valueList = new ArrayList<>();
                //先删除再新增
                listingEbayValueService.deleteListingEbayValueByListingLineId(listingEbayLine.getId());
                for (ListingAmazonAttributeLine attributeLine : attributeLineList) {
                    if (repeatVerMap.containsKey(attributeLine.getTableName())) {
                        continue;
                    }
                    repeatVerMap.put(attributeLine.getTableName(), null);
                    ListingEbayValue line = new ListingEbayValue();
                    line.setCreateTime(DateUtils.getNowDate());
                    line.setCreateBy(goodsHead.getCreateBy());
                    line.setListingLineId(listingEbayLine.getId());
                    line.setName(attributeLine.getTableName());
                    line.setValue(attributeLine.getTableValue());

                    valueList.add(line);
                }
                if (CollUtil.isNotEmpty(valueList)) {
                    listingEbayValueService.batchInsertListingEbayValue(valueList);
                }
                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    if (listingInfoBiz.checkListingDescriptionTemplate(goodsHead.getId())) {
                        itemDTO.setModuleType(Arrays.asList(ListingModuleType.BRAND.name(), ListingModuleType.ATTRIBUTE.name(), ListingModuleType.DESCRIPTION.name()));
                    } else {
                        itemDTO.setModuleType(Arrays.asList(ListingModuleType.BRAND.name(), ListingModuleType.ATTRIBUTE.name()));
                    }
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改listing属性失败,listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }

        }
        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });
        //处理待办的状态
        loseCartTodoService.confirmByGoodIds(goodsIds, userId);
        inventoryLowTodoService.confirmByGoodIds(goodsIds, userId);
    }

    /**
     * 批量修改适配
     *
     * @param dto
     */
    @Override
    public void batchUpdateAdaptive(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        String ids = dto.getIds();
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        String[] idList = ids.split(",");
        List<Integer> goodsIds = Arrays.stream(idList).map(Integer::valueOf).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE, goodsIds, String.valueOf(userId));
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0]));
        refreshEbAdapter( goodsHeadList);
    }

    public void refreshEbAdapter(List<GoodsHead> goodsHeadList) {
        if (CollUtil.isEmpty(goodsHeadList)) {
            return;
        }
        List<Integer> goodsIds = goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList());
        //goodsHeadList 中包含了非US站点的数据 直接抛出异常
        if (goodsHeadList.stream().anyMatch(e -> !Objects.equals(e.getSiteCode(), PlatformSiteEnum.US.getSiteCodeType()))) {
            throw new BusinessException("批量修改适配只能修改US站点的数据");
        }
        Map<Integer, List<ListingEbayLine>> lineMap = ebayGoodsLineService.selectListingEbayLineByHeadIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.groupingBy(ListingEbayLine::getListingHeadId));

        //按照类目分组
        Map<Integer, List<GoodsHead>> categoryHeadMap = goodsHeadList.stream().collect(Collectors.groupingBy(GoodsHead::getCategoryId));
        List<PlatformCategory> platformCategoryList = platformCategoryService.selectPlatformCategoryListByIds(categoryHeadMap.keySet().stream().map(String::valueOf).toArray(String[]::new));

        ThreadPoolExecutor editAdaptivePool = threadPoolForMonitorManager.getThreadPoolExecutor("editAdaptivePool");
        categoryHeadMap.forEach((category,headList)->{
            GoodsHead h = headList.get(0);
            PlatformCategory platformCategory = platformCategoryList.stream().filter(e -> Objects.equals(String.valueOf(e.getId()), String.valueOf(category))).findFirst().orElse(null);
            if (ObjectUtil.isEmpty(platformCategory)||ObjectUtils.isEmpty(h)){
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(h.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE), GoodsTaskSubStatusEnum.ERROR, "平台品类不存在,请联系管理员处理！");
                return;
            }
            EbayCompatibilityProperties compatibilityProperties = retryable.retryableEps(() -> {
                if (ObjUtil.isEmpty(platformCategory)) {
                    return null;
                }
                return BuildEbayItemBiz.getEbayCompatibilityProperties(h.getShopCode(), h.getSiteCode(), platformCategory.getCategoryId());
            });

            editAdaptivePool.execute(() -> {
                try {
                    List<ItemDTO> itemDTOList = new ArrayList<>();
                    headList.forEach(head -> {
                        try {
                            if (PublishStatus.getNoUpdateStatus().contains(head.getPublishStatus())) {
                                log.error("批量修改适配,listingId:{},状态为刊登中、更新中、下架中、非在售的数据不允许修改", head.getId());
                                return;
                            }
                            ListingEbayLine listingEbayLine = lineMap.get(head.getId()).get(0);
                            if (ObjectUtils.isEmpty(listingEbayLine)) {
                                return;
                            }
                            //清空现有的适配信息 ->从ads取新的适配
                            ebayAdaptiveService.updateAdapterByAds(listingEbayLine.getId(), head);
                            if ((Objects.equals(head.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(head.getPublishStatus()))) {
                                ItemDTO itemDTO = new ItemDTO();
                                itemDTO.setGoodsHead(head);
                                itemDTO.setModuleType(Collections.singletonList(ListingModuleType.ADAPTIVE.name()));
                                itemDTO.setEbayCompatibilityProperties(compatibilityProperties);
                                itemDTOList.add(itemDTO);
                            } else {
                                //该listing不是更新中  则修改完成
                                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE), GoodsTaskSubStatusEnum.NORAML, "");
                            }
                        } catch (Exception e) {
                            log.error("批量修改适配,listingId:{}", head.getId(), e);
                            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_EDIT_ADAPTIVE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
                        }
                    });
                    listingUpdateBuilder.updateApi(itemDTOList);
                } catch (Exception e) {
                    log.error("批量修改适配失败", e);
                }
            });
        });
    }

    @Override
    public void batchUpdateDescription(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.BATCH_UPDATE_DESCRIPTION, goodsIds, String.valueOf(userId));
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        Map<Integer, List<GoodsDescription>> descMap = goodsDescriptionService.selectDescriptionListByGoodsIdList(goodsIds).stream().collect(Collectors.groupingBy(GoodsDescription::getGoodsId));
        for (ListingDTO listing : listings) {
            try {
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                GoodsDescription goodsDescription = descMap.get(listing.getId()).get(0);
                getGoodsDescription(listing, goodsDescription);
                violateWordBiz.checkViolateWord(true,goodsDescription, null, goodsHead);
                goodsDescriptionService.updateListingGoodsDescription(goodsDescription);
                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Collections.singletonList(ListingModuleType.DESCRIPTION.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_DESCRIPTION), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改listing描述失败,listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_DESCRIPTION), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });
    }

    @Override
    public Integer move2Draft(List<GoodsHead> heads) {
        if (ObjectUtils.isEmpty(heads)) {
            return 0;
        }
        //ebay只有 刊登失败 非在售的数据才能移动到草稿箱
        List<GoodsHead> moveList = heads.stream().filter(e -> PublishStatus.move2DraftStatus(PlatformTypeEnum.EB.name())
                .contains(e.getPublishStatus())).collect(Collectors.toList());

        List<GoodsHead> noMoveList = heads.stream().filter(e -> !PublishStatus.move2DraftStatus(PlatformTypeEnum.AM.name())
                .contains(e.getPublishStatus())).collect(Collectors.toList());
        Integer sum = 0;
        for (GoodsHead head : moveList) {
            //清空itemId 以及记录由什么状态移至草稿

            if (ObjUtil.isNotEmpty(head.getPlatformGoodsId())) {
                listingLogService.insertSuccessListingLog("Listing由[" + PublishStatus.getPublishStatusName(head.getPublishStatus()) + "]移至[草稿],原平台销售编码:" + head.getPlatformGoodsId(),
                        StringUtils.isBlank(head.getUpdateBy()) ? head.getCreateBy() : head.getUpdateBy(), head.getId());
            } else {
                listingLogService.insertSuccessListingLog("Listing由[" + PublishStatus.getPublishStatusName(head.getPublishStatus()) + "]移至[草稿]",
                        StringUtils.isBlank(head.getUpdateBy()) ? head.getCreateBy() : head.getUpdateBy(), head.getId());
            }
            head.setPublishStatus(PublishStatus.DRAFT.getType());
            sum += goodsHeadService.clearPlatformGoodId(head);
            ebayGoodsLineService.clearUUID(head.getId());

            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.MOVE_DRAFT), GoodsTaskSubStatusEnum.NORAML, "");
        }
        noMoveList.forEach(no -> {
            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(no.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.MOVE_DRAFT), GoodsTaskSubStatusEnum.NORAML, "");
        });
        return sum;
    }


    /**
     * 查找关键词 ebay 标题  属性  适配 描述
     * 此处需要筛选出来的链接是该店铺所有的链接
     * @param userId
     * @param keyWord
     * @param shopCodeList
     */
    @Override
    public void findKeyword(Long userId, String keyWord, List<String> shopCodeList) {
        if (ObjectUtils.isEmpty(shopCodeList) || ObjectUtils.isEmpty(keyWord)) {
            return;
        }
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setShopCodes(shopCodeList);
        //任务总数
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadList(goodsHead);
        findKeywordPoolConfig.execute(() -> {
            GoodsTask goodsTask = goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.FIND_KEYWORD,
                    goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList()), String.valueOf(userId));

            for (GoodsHead head : goodsHeadList) {
                try {
                    //检测标题
                    if (StringUtils.checkParticipleHandler(head.getTitle().toLowerCase(),keyWord.toLowerCase())) {
                        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.FIND_KEYWORD), GoodsTaskSubStatusEnum.NORAML, "");
                        listingLogService.insertSuccessListingLog("标题中包含关键词:" + keyWord, String.valueOf(userId), head.getId());
                        continue;
                    }
                    ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHead.getId());
                    if (ObjectUtils.isEmpty(listingEbayLine)) {
                        continue;
                    }
                    //检测属性
                    List<ListingEbayValue> listingEbayValues = ebayValueService.selectListingEbayValueByLineId(listingEbayLine.getId());
                    if (ObjectUtils.isEmpty(listingEbayValues)) {
                        continue;
                    }
                    if (listingEbayValues.stream().anyMatch(e -> StringUtils.checkParticipleHandler(e.getName().toLowerCase(),keyWord.toLowerCase()) ||StringUtils.checkParticipleHandler( e.getValue().toLowerCase(),keyWord.toLowerCase()))) {
                        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.FIND_KEYWORD), GoodsTaskSubStatusEnum.NORAML, "");
                        listingLogService.insertSuccessListingLog("属性中包含关键词:" + keyWord, String.valueOf(userId), head.getId());
                        continue;
                    }
                    //检测适配
                    List<ListingEbayAdaptive> listingEbayAdaptives = ebayAdaptiveService.selectListByEbayLineId(listingEbayLine.getId());
                    if (ObjectUtils.isEmpty(listingEbayAdaptives)) {
                        continue;
                    }
                    if (listingEbayAdaptives.stream().anyMatch(e -> StringUtils.checkParticipleHandler(e.getMake().toLowerCase(),keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(e.getModel().toLowerCase(),keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(e.getYear().toLowerCase(),keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(e.getTrim().toLowerCase(),keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(e.getEngine().toLowerCase(),keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(e.getSubmodel().toLowerCase(),keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(e.getNotes().toLowerCase(),keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(e.getPlatform().toLowerCase(),keyWord.toLowerCase())
                            || StringUtils.checkParticipleHandler(e.getType().toLowerCase(),keyWord.toLowerCase()))) {
                        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.FIND_KEYWORD), GoodsTaskSubStatusEnum.NORAML, "");
                        listingLogService.insertSuccessListingLog("适配中包含关键词:" + keyWord, String.valueOf(userId), head.getId());
                        continue;
                    }
                    //检测描述
                    GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsHead.getId());
                    if (ObjectUtils.isEmpty(goodsDescription)) {
                        continue;
                    }
                    if (StringUtils.checkParticipleHandler(goodsDescription.getShortDescription().toLowerCase(),keyWord.toLowerCase())
                            ||StringUtils.checkParticipleHandler(goodsDescription.getDetailDescription().toLowerCase(),keyWord.toLowerCase())) {
                        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(head.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.FIND_KEYWORD), GoodsTaskSubStatusEnum.NORAML, "");
                        listingLogService.insertSuccessListingLog("描述中包含关键词:" + keyWord, String.valueOf(userId), head.getId());
                    }
                } catch (Exception e) {
                    log.error("查找关键词失败,listingId:{}", head.getId(), e);
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.FIND_KEYWORD), GoodsTaskSubStatusEnum.ERROR, String.valueOf(e.getMessage()));

                }

            }
            //结束任务
            if (ObjUtil.isNotEmpty(goodsTask.getId())) {
                goodsTaskService.updateGoodsTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo(), goodsTask.getId());
            }

        });
    }

    @Override
    public void batchUpdatePrice(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.BATCH_UPDATE_PRICE, goodsIds, String.valueOf(userId));
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        for (ListingDTO listing : listings) {
            try {
                //head
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }

                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),goodsHead.getPdmGoodsCode(), listing.getStandardPrice(),goodsHead.getPublishType(),goodsHead.getSiteCode(),goodsHead.getOnlineTime(),goodsHead.getStandardPrice());
                goodsHead.setStandardPrice(price);
                goodsHeadService.updateListingGoodsHead(goodsHead);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Arrays.asList(ListingModuleType.INVENTORY.name(), ListingModuleType.PRICE.name(), ListingModuleType.EBAY_LINE.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                if(e instanceof BusinessException){
                    throw e;
                }
                log.error("批量修改listing价格和库存失败,listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        redLinePriceTodoService.updateStatusByHeadIdList(goodsIds,"1");
        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });
    }

    @Override
    public void editAttribute(ListingEditDTO listingEditDTO) {
        GoodsHead dbHead = goodsHeadService.selectListingGoodsHeadById(listingEditDTO.getGoodsHeadId());
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setId(listingEditDTO.getGoodsHeadId());
        //状态流转
        if (StringUtils.isNotEmpty(listingEditDTO.getPublishStatus())) {
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(Integer.valueOf(listingEditDTO.getPublishStatus()), PlatformTypeEnum.EB.name()));
        } else {
            Integer publishStatus = dbHead.getPublishStatus();
            goodsHead.setPublishStatus(PublishStatus.getStatusByEdit(publishStatus, PlatformTypeEnum.EB.name()));
        }
        violateWordBiz.checkViolateWord(true,null, listingEditDTO.getListingAmazonAttributeLinesInfo(), goodsHead);
        goodsHeadService.updateListingGoodsHead(goodsHead);
        listingInfoBiz.saveEbayAttributeTable(listingEditDTO, listingEditDTO.getListingLineId());

    }

    @Override
    public void syncTemuListingInfo(List<TemuGoodsHead> temuGoodsHeads, String userId) {

    }

    @Override
    public void batchUpdateVideo(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        String updateDescVideo = dto.getUpdateDescVideo();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.BATCH_UPDATE_VIDEO, goodsIds, String.valueOf(userId));
        Map<Integer, GoodsHead> headMap = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(GoodsHead::getId, e -> e));
        for (ListingDTO listing : listings) {
            try {
                //head
                GoodsHead goodsHead = headMap.get(listing.getId());
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }

                String videoSelect = listing.getVideoSelect();
                listingInfoBiz.saveAndUpdateVide(listing.getId(), videoSelect);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    if (ObjUtil.isNotEmpty(updateDescVideo)&&ObjUtil.equals(updateDescVideo,"1")){
                        itemDTO.setModuleType(Arrays.asList(ListingModuleType.VIDEO.name(),ListingModuleType.DESCRIPTION.name()));
                        itemDTO.setUpdateDescVideo(updateDescVideo);
                    }else {
                        itemDTO.setModuleType(Arrays.asList(ListingModuleType.VIDEO.name()));
                    }
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_VIDEO), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                if(e instanceof BusinessException){
                    throw e;
                }
                log.error("批量修改listing视频失败,listingId:{}", listing.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_UPDATE_VIDEO), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });

    }

    @Override
    public void excludeListing(List<Integer> idList, Map<String, Object> extendMap, Long userId) {
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(idList.toArray(new Integer[0]));

        Map<String, List<GoodsHead>> goodsHeadsMap = goodsHeads.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));

        goodsHeadsMap.forEach((shopCode, goodList) -> {
            List<Integer> goodsId = goodList.stream().map(GoodsHead::getId).collect(Collectors.toList());

            Map<Integer, GoodsHead> goodsHeadMap = goodList.stream().collect(Collectors.toMap(GoodsHead::getId, Function.identity()));

            List<InventoryUpdateBlack> inventoryUpdateBlacks = inventoryUpdateBlackService.selectInventoryUpdateBlackListByShopCode(shopCode);
            if (ObjUtil.isNotEmpty(inventoryUpdateBlacks)) {
                //从goodsId移除inventoryUpdateBlackList存在的主键id
                Set<Integer> blackListedGoodIds = inventoryUpdateBlacks.stream()
                        .map(i -> i.getHeadId().intValue())
                        .collect(Collectors.toSet());
                goodsId.removeIf(blackListedGoodIds::contains);
            }

            if (ObjUtil.isNotEmpty(goodsId)) {
                List<InventoryUpdateBlack> insertList = new ArrayList<>();
                for (Integer goodId : goodsId) {
                    GoodsHead goodsHead = goodsHeadMap.get(goodId);
                    InventoryUpdateBlack add = new InventoryUpdateBlack();
                    add.setPlatform(goodsHead.getPlatform());
                    add.setSite(goodsHead.getSiteCode());
                    add.setShopCode(shopCode);
                    add.setHeadId(Long.valueOf(goodId));
                    add.setPlatformGoodsCode(goodsHead.getPlatformGoodsCode());
                    add.setDelFlag(0L);
                    add.setCreateTime(DateUtils.getNowDate());
                    add.setUpdateTime(DateUtils.getNowDate());
                    add.setCreateBy(String.valueOf(userId));
                    add.setUpdateBy(String.valueOf(userId));
                    insertList.add(add);
                }
                Lists.partition(insertList, 100).forEach(part -> inventoryUpdateBlackService.batchInsert(part));
                if (MapUtil.isEmpty(extendMap) || !extendMap.containsKey("stockZero") || Constants.YesOrNo.NO.equals(extendMap.get("stockZero"))) {
                    inventoryExcludeBiz.addListingLog(insertList, String.valueOf(userId));
                }
            }

            if (MapUtil.isNotEmpty(extendMap) && extendMap.containsKey("stockZero") && Constants.YesOrNo.YES.equals(extendMap.get("stockZero"))) {
                // Ebay链接库存调0
                ebayPullPoolConfig.execute(() -> {
                    for (GoodsHead head : goodList) {
                        updateZeroStock(head);
                        listingLogService.insertSuccessListingLog("加入库存更新黑名单,库存由" + head.getStockOnSalesQty().intValue() + "更为0", head.getCreateBy(), head.getId());
                    }
                });
            }

        });
    }

    @Override
    public void delExcludeListing(List<Integer> idList, Long userId) {
        if (ObjUtil.isEmpty(idList)) {
            return;
        }
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(idList.toArray(new Integer[0]));
        if (ObjUtil.isEmpty(goodsHeads)) {
            return;
        }
        Map<String, List<GoodsHead>> goodsHeadsMap = goodsHeads.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));
        goodsHeadsMap.forEach((shopCode, goodList) -> {
            List<Long> headIds = goodList.stream().map(GoodsHead::getId).map(Integer::longValue).collect(Collectors.toList());
            List<InventoryUpdateBlack> inventoryUpdateBlacks = inventoryUpdateBlackService.selectInventoryUpdateBlackListByHeadIds(headIds, shopCode);
            for (InventoryUpdateBlack inventoryUpdateBlack : inventoryUpdateBlacks) {
                inventoryUpdateBlack.setDelFlag(1L);
                inventoryUpdateBlackService.updateInventoryUpdateBlack(inventoryUpdateBlack);
                listingLogService.insertSuccessListingLog("从库存更新黑名单移除", String.valueOf(userId), Math.toIntExact(inventoryUpdateBlack.getHeadId()));
            }
        });
    }

    @Override
    public void listingCopy(ListingCopyDTO listingCopyDTO) {

    }

    /**
     * 主要对比状态、标题、价格、库存、图片、属性、视频
     * @param savaFlag flase 不保存更新记录
     * @param itemDTO
     * @return false表示对比一致，true表示数据不一致 需要插入更新记录表
     */
    @Override
    public List<String> comparativeData(Boolean savaFlag,ItemDTO itemDTO) {
        if (ObjUtil.isEmpty(itemDTO)) {
            return null;
        }
        ItemType ebayItem = itemDTO.getEbayItem();
        if (ObjUtil.isEmpty(ebayItem)) {
            return null;
        }
        //smc不存在
        GoodsHead goodsHead = itemDTO.getGoodsHead();
        if (ObjUtil.isEmpty(goodsHead)) {
            return null;
        }
        itemDTO.setModuleType(new ArrayList<>());
        List<String> comparativeMoudlList = new ArrayList<>();
        comparativeMoudlList.add(ListingModuleType.TITLE.name());
        comparativeMoudlList.add(ListingModuleType.PRICE.name());
        comparativeMoudlList.add(ListingModuleType.INVENTORY.name());
        comparativeMoudlList.add(ListingModuleType.ATTRIBUTE.name());
        comparativeMoudlList.add(ListingModuleType.IMAGE.name());
        comparativeMoudlList.add(ListingModuleType.VIDEO.name());
        comparativeMoudlList.add(ListingModuleType.PUBLISH_STATUS.name());

        for (String mould : comparativeMoudlList) {
            try {
                ListingUpdateModuleResolver impl = handlerListingUpdateModuleComposite.getListingUpdateResolverByModule(PlatformTypeEnum.EB.name() + mould);
                impl.compareData(itemDTO);
            } catch (Exception e) {
                log.error("平台为:{},店铺为:{},头表id:{},webhook对比数据异常", goodsHead.getPlatform(), goodsHead.getShopCode(), goodsHead.getId(), e);
            }
        }
        List<String> updateMoudleList = itemDTO.getModuleType();
        if (ObjUtil.isEmpty(updateMoudleList)||!savaFlag) {
            return null;
        }

        intoUpdateRecord(goodsHead, updateMoudleList,1);

        return updateMoudleList;
    }

    @Override
    public void batchUpdateShipping(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        String ids = dto.getIds();
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        String[] idList = ids.split(",");
        List<Integer> goodsIds = Arrays.stream(idList).map(Integer::valueOf).collect(Collectors.toList());
        List<ItemDTO> itemDTOList = new ArrayList<>();
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.SYNC_SHIPPING_LISTING, goodsIds, String.valueOf(userId));
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0]));
        for (GoodsHead goodsHead : goodsHeadList) {
            try {
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHead.getId());
                if (ObjectUtils.isEmpty(listingEbayLine)) {
                    throw new BusinessException("listing信息不完整,行信息缺适,无法更新物流信息");
                }
                Integer ebayLineId = listingEbayLine.getId();

                ebayShippingHeadService.deleteListingShippingByLineId(ebayLineId);

                buildEbayItemBiz.compensationShippingData(ebayLineId, goodsHead,null);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Collections.singletonList(ListingModuleType.EBAY_SHIPPING.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.SYNC_SHIPPING_LISTING), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                log.error("批量修改listing物流信息失败,listingId:{}", goodsHead.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.SYNC_SHIPPING_LISTING), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
                if(e instanceof BusinessException){
                    throw e;
                }
            }
        }
        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });

    }

    @Override
    public void batchUpdatePolicy(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        String ids = dto.getIds();
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        String[] idList = ids.split(",");
        List<Integer> goodsIds = Arrays.stream(idList).map(Integer::valueOf).collect(Collectors.toList());
        List<ItemDTO> itemDTOList = new ArrayList<>();
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.SYNC_POLILY_LISTING, goodsIds, String.valueOf(userId));
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0]));
        for (GoodsHead goodsHead : goodsHeadList) {
            try {
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }
                ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHead.getId());
                if (ObjectUtils.isEmpty(listingEbayLine)) {
                    throw new BusinessException("listing信息不完整,行信息缺适,无法更新物流信息");
                }
                Integer ebayLineId = listingEbayLine.getId();
                ListingEbayPolicy   policy=new ListingEbayPolicy();
                policy.setListingLineId(ebayLineId);
                //清空原有数据
                ebayPolicyService.deleteListingEbayPolicyByLineId(policy);
                //取模板数据
                buildEbayItemBiz.compensationPolicyData(ebayLineId, goodsHead);

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Arrays.asList(ListingModuleType.EBAY_POLICY.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.SYNC_POLILY_LISTING), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                if(e instanceof BusinessException){
                    throw e;
                }
                log.error("批量修改listing付款退货信息失败,listingId:{}", goodsHead.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.SYNC_POLILY_LISTING), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });

    }

    @Override
    public void batchUpdateDescriptionTemplate(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        String ids = dto.getIds();
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        String[] idList = ids.split(",");
        List<Integer> goodsIds = Arrays.stream(idList).map(Integer::valueOf).collect(Collectors.toList());
        List<ItemDTO> itemDTOList = new ArrayList<>();
        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.SYNC_DESCRIPTION_LISTING, goodsIds, String.valueOf(userId));
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.toArray(new Integer[0]));
        for (GoodsHead goodsHead : goodsHeadList) {
            try {
                if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                    throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
                }

                if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.EB.name()) && PublishStatus.getSaleStatus().contains(goodsHead.getPublishStatus()))) {
                    ItemDTO itemDTO = new ItemDTO();
                    itemDTO.setGoodsHead(goodsHead);
                    itemDTO.setModuleType(Arrays.asList(ListingModuleType.DESCRIPTION.name()));
                    itemDTOList.add(itemDTO);
                } else {
                    //该listing不是更新中  则修改完成
                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.SYNC_DESCRIPTION_LISTING), GoodsTaskSubStatusEnum.NORAML, "");
                }
            } catch (Exception e) {
                if(e instanceof BusinessException){
                    throw e;
                }
                log.error("批量修改listing描述失败,listingId:{}", goodsHead.getId(), e);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.SYNC_DESCRIPTION_LISTING), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        eidtPoolConfig.execute(() -> {
            listingUpdateBuilder.updateApi(itemDTOList);
        });

    }

    @Override
    public void batchUpdateAdapterText(BatchListingDTO dto) {
        Long userId = ShiroUtils.getUserId();
        List<ListingDTO> listings = dto.getListings();
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }

        ListingDTO listing = listings.get(0);
        Integer goodsId = listing.getId();
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<Integer> goodsIds = listings.stream().map(ListingDTO::getId).collect(Collectors.toList());
        Map<Integer, List<ListingEbayLine>> lineMap = ebayGoodsLineService.selectListingEbayLineByHeadIds(goodsIds.stream().toArray(Integer[]::new)).stream().collect(Collectors.groupingBy(ListingEbayLine::getListingHeadId));
        List<ListingAmazonAttributeLine> attributeLineList = dto.getListingAmazonAttributeLinesInfo();

        //创建任务
        GoodsTask goodsTask = goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.TODO_TEXT_UPDATE, Lists.newArrayList(goodsId), String.valueOf(userId));

        //查询数据
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(goodsId);
        if (ObjUtil.isEmpty(goodsHead)) {
            throw new BusinessException("listing数据不存在，请重新确认.");
        }
        //整理出需要更新的模块
        List<String> modules = getModules(listing,goodsHead.getTitle(), attributeLineList);
        if (CollectionUtils.isEmpty(modules)){
            throw new BusinessException("文案没有修改,请重新编辑,如需不用处理请联系管理员.");
        }
        if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
            throw new BusinessException("listing状态为刊登中、更新中、下架中的数据不允许修改");
        }
        //更新待办状态
        SmcAdaptTodo update = new SmcAdaptTodo();
        update.setId(listing.getTodoId());
        update.setStatus(5);

        Map<String, Set<String>> violateWord = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        try {
            //记日志  改了啥
            sb.append(modules.contains(ListingModuleType.TITLE.name())?"修改了标题:"+goodsHead.getTitle()+"->"+listing.getTitle()+";":"");
            if (CollUtil.isNotEmpty(attributeLineList)) {
                sb.append(modules.contains(ListingModuleType.ATTRIBUTE.name()) ? "修改了属性" : "");
            }
            //去重map
            Map<String, String> repeatVerMap = new HashMap<>();
            List<ListingEbayLine> listingEbayLines = lineMap.get(listing.getId());
            if (CollUtil.isEmpty(listingEbayLines)) {
                log.error("listingEbayLines为空,listingId:{}", listing.getId());
                throw new BusinessException("数据异常,listingId:"+listing.getId());
            }
            ListingEbayLine listingEbayLine = listingEbayLines.get(0);
            String brand = null;
            if (CollUtil.isNotEmpty(attributeLineList)) {
                //处理品牌
                brand = attributeLineList.stream()
                        .filter(attributeLine -> Objects.equals("BRAND", attributeLine.getTableName().trim().toUpperCase()))
                        .map(ListingAmazonAttributeLine::getTableValue).findFirst().orElse(null);
            }
            //放标题
            goodsHead.setTitle(listing.getTitle());
            violateWordBiz.checkViolateWord(true,null, attributeLineList, goodsHead);
            
            GoodsHead updateBrand = new GoodsHead();
            updateBrand.setId(goodsHead.getId());
            if (StrUtil.isNotEmpty(brand)) {
                updateBrand.setBrandCode(brand);
            }
            goodsHeadService.updateListingGoodsHead(updateBrand);

            List<ListingEbayValue> valueList = new ArrayList<>();
            if (CollUtil.isNotEmpty(attributeLineList)) {
                //先删除再新增
                listingEbayValueService.deleteListingEbayValueByListingLineId(listingEbayLine.getId());
                for (ListingAmazonAttributeLine attributeLine : attributeLineList) {
                    if (repeatVerMap.containsKey(attributeLine.getTableName())) {
                        continue;
                    }
                    repeatVerMap.put(attributeLine.getTableName(), null);
                    ListingEbayValue line = new ListingEbayValue();
                    line.setCreateTime(DateUtils.getNowDate());
                    line.setCreateBy(goodsHead.getCreateBy());
                    line.setListingLineId(listingEbayLine.getId());
                    line.setName(attributeLine.getTableName());
                    line.setValue(attributeLine.getTableValue());

                    valueList.add(line);
                }
                if (CollUtil.isNotEmpty(valueList)) {
                    listingEbayValueService.batchInsertListingEbayValue(valueList);
                }
            }

            //更新入库
            if (modules.contains(ListingModuleType.TITLE.name())) {
                GoodsHead updateHead = new GoodsHead();
                updateHead.setId(goodsHead.getId());
                updateHead.setTitle(listing.getTitle());
                updateHead.setTitle(updateHead.getTitle().replaceAll("“", "\"").replaceAll("’", "'"));
                goodsHeadService.updateListingGoodsHead(updateHead);
            }

            //加入更新后续流程
            if ((!PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus()) && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                ItemDTO itemDTO = new ItemDTO();
                itemDTO.setGoodsHead(goodsHead);
                itemDTO.setModuleType( modules );
                itemDTOList.add(itemDTO);
            } else {
                goodsTaskInfoService.updateGoodsStatusTaskInfo(goodsTask.getId(), String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.TODO_TEXT_UPDATE), GoodsTaskSubStatusEnum.NORAML, "");
            }
            //回写日志
            listingLogService.insertSuccessListingLog("适配文案变更待办处理,"+sb, String.valueOf(userId), Integer.valueOf(goodsHead.getId()));
        } catch (Exception e) {
            log.error("适配文案变更待办处理listing文案失败,listingId:{}", listing.getId(), e);
            goodsTaskInfoService.updateGoodsStatusTaskInfo(goodsTask.getId(), String.valueOf(listing.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.TODO_TEXT_UPDATE), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            //更新待办状态
            update.setStatus(3);
            //回写日志
            listingLogService.insertErrorListingLog("适配文案变更待办处理,"+sb, String.valueOf(userId), Integer.valueOf(goodsHead.getId()), e.getMessage());
            if (e instanceof BusinessException) {
                assembleWord(violateWord, e.getMessage());
            }
        }
        //更新待办状态
        smcAdaptTodoService.updateSmcAdaptTodo(update);

        if (ObjUtil.isNotEmpty(violateWord)) {
            assembleWordBefore(violateWord);
        }

        if(CollUtil.isNotEmpty(itemDTOList)){
            listingUpdateBuilder.updateApi(itemDTOList);
        }
    }


    private List<String> getModules(ListingDTO listing,String title, List<ListingAmazonAttributeLine> attributeLineList) {
        List<String> modules = new ArrayList<>();
        if ( StringUtils.isNotEmpty(listing.getTitle()) && !Objects.equals(listing.getTitle().replaceAll("“", "\"").replaceAll("’", "'"),title) ){
            modules.add(ListingModuleType.TITLE.name());
        }
        if (CollUtil.isNotEmpty(attributeLineList)) {
            modules.addAll(Arrays.asList(ListingModuleType.BRAND.name(), ListingModuleType.ATTRIBUTE.name()));
        }
        if (CollUtil.isNotEmpty(modules)){
            modules=modules.stream().distinct().collect(Collectors.toList());
        }
        return modules;
    }
 

    /**
     * 批量停售
     * @param goodsHeadList
     */
    public void batchStopSold(List<GoodsHead> goodsHeadList, Long userId) {
        //按照店铺分组
        Map<String, List<GoodsHead>> shopMap = goodsHeadList.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));

        shopMap.forEach((shop, onlineList) -> {
            ebayPullPoolConfig.execute(()->{
                for (GoodsHead head : onlineList) {
                    updateZeroStock(head);
                    listingLogService.insertSuccessListingLog("批量停售  库存更新为0", head.getCreateBy(), head.getId());
                }
            });
            //加入库存黑名单
            this.excludeListing(onlineList.stream().map(GoodsHead::getId).collect(Collectors.toList()), null, userId);
        });


    }

    /**
     * 更新库存为0
     * @param head
     */
    private void updateZeroStock(GoodsHead head) {
        //如果是刊登中 更新中 下架中的数据 不允许修改
        if (PublishStatus.getNoUpdateStatus().contains(head.getPublishStatus())) {
            throw new RuntimeException("listing状态为刊登中、更新中、下架中、非在售的数据不允许修改");
        }
        GoodsHead updatedHead = new GoodsHead();
        updatedHead.setId(head.getId());
        updatedHead.setStockOnSalesQty(BigDecimal.ZERO);
        goodsHeadService.updateListingGoodsHead(updatedHead);
        if (head.getPublishStatus().equals(PublishStatus.SALEING.getType()) || head.getPublishStatus().equals(PublishStatus.UPDATING_FAIL.getType())) {
            EbayInventoryVO vo = new EbayInventoryVO();
            vo.setAccountCode(head.getShopCode());
            EbayInventoryVO.EbayInventory ebayInventory = new EbayInventoryVO.EbayInventory();
            ebayInventory.setItemId(head.getPlatformGoodsId());
            ebayInventory.setQuantity(0);
            vo.setEbayInventoryList(Collections.singletonList(ebayInventory));
            String data = HttpUtils.post(REVISE_INVENTORY_STATUS_URL, JSONObject.toJSONString(vo));
            AjaxResult ajaxResult = JSONObject.parseObject(data, AjaxResult.class);
            if (ajaxResult.isSuccess()){
                smcTodoBiz.updateTodoStatusByListingUpdate(head.getId(),TodoStatusEnum.FINISH_STATUS);
                intoUpdateRecord(head, Lists.newArrayList(ListingModuleType.INVENTORY.name()),0);
            }
        }
    }
}