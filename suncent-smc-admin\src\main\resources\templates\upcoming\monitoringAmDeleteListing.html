<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<th:block th:include="include :: virtual-select-css"/>
<th:block th:include="include :: select2-css"/>

<style>
    .div-select {
        height: 30px;
        width: 222px;
        display: inline-block;
    }
    .select-list li.show-4 label:not(.radio-box){
        float: left;
        width: 92px;
        margin: 5px 0px 0px 0px;
        text-align:right;
    }
    .select-list .show-4 li {
        margin: 0px;
        width: 300px
    }
    .select-list li p, .select-list li label:not(.radio-box){
        width: 80px;
    }
    .child-class {
        width:110px !important
    }
</style>
<div class="ibox float-e-margins" th:fragment="monitoringBelongShop">
    <div class="ibox-title ibox-title-gray dashboard-header">
        <h5>AM待删除</h5>
    </div>
    <div class="ibox-content">
        <div class="nav-tabs-custom">
            <ul class="nav nav-tabs">
                <li class="active"><a href="#detail" data-toggle="tab" aria-expanded="true">待办明细</a></li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane active" id="detail">

                    <div class="col-sm-12 search-collapse">
                        <form id="formId">
                            <div class="select-list">
                                <ul>
                                    <li class="show-4">
                                        <label>编码：</label>
                                        <select data-none-selected-text="" title="请选择编码" style="width: 110px"
                                                class="child-class noselect2 selectpicker" name="codeType"
                                                id="codeType">
                                            <option value="" style="width: 112px;">请选择编码</option>
                                            <option value="1" style="width: 112px;">主键</option>
                                            <option value="3">平台商品编码</option>
                                            <option value="4">平台销售编码</option>
                                        </select>
                                        <input style="width: 300px" type="text" name="codeValue" id="codeValue"
                                               placeholder="编码，支持多个查询空格分开" width="500px"/>
                                    </li>
                                    <li class="show-4">
                                        <label>店铺：</label>
                                        <div class="div-select">
                                            <div name="shopCode" id="shop-select"></div>
                                        </div>
                                    </li>
                                    <li class="show-4">
                                        <label>运营：</label>
                                        <div class="div-select">
                                            <div name="createBy" id="createBy-select"></div>
                                        </div>
                                    </li>
                                    <li class="show-4">
                                        <label style="width: 90px">产品分类：</label>
                                        <div class="div-select">
                                            <div name="pdmCategoryIds" id="pdm-category-select"></div>
                                        </div>
                                    </li>
                                    <li class="show-4">
                                        <label>刊登类型：</label>
                                        <div class="div-select">
                                            <select name="publishType" id="publishType" class="form-control">
                                                <option value="">请选择</option>
                                                <option value="0">FBM</option>
                                                <option value="1">FBA</option>
                                                <option value="5">VCDF</option>
                                                <option value="6">VCPO</option>
                                            </select>
                                        </div>
                                    </li>
                                    <li class="show-4">
                                        <label >处理状态：</label>
                                        <div class="div-select">
                                            <select name="status" id="status" class="form-control">
                                                <option value="">请选择</option>
                                                <option value="2">待处理</option>
                                                <option value="5">已处理</option>
                                                <option value="4">无需处理</option>
                                            </select>
                                        </div>
                                    </li>
                                    <li>
                                        <label>无效链接：</label>
                                        <select name="fromInvalidFlag" id="fromInvalidFlag" class="form-control" >
                                            <option value="">请选择</option>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </li>
                                    <li>
                                        <label>品牌收缩：</label>
                                        <select name="fromBrandLimitFlag" id="fromBrandLimitFlag" class="form-control" >
                                            <option value="">请选择</option>
                                            <option value="1">是</option>
                                            <option value="0">否</option>
                                        </select>
                                    </li>
                                    <li class="select-time">
                                        <label>更新时间： </label>
                                        <input style="width: 212px;" type="text" class="time-input" id="startTime" placeholder="开始时间"
                                               name="params[beginTime]"/>
                                        <span style="padding: 0px 8px;">-</span>
                                        <input style="width: 212px;" type="text" class="time-input" id="endTime" placeholder="结束时间"
                                               name="params[endTime]"/>
                                    </li>
                                    <li class="show-4">
                                        <a class="btn btn-primary btn-rounded btn-sm"
                                           id="search-btn">
                                            <i class="fa fa-search"></i> 搜索
                                        </a>
                                        <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i
                                                class="fa fa-refresh"></i> 重置</a>
                                    </li>
                                </ul>
                            </div>
                        </form>
                    </div>
                    <div class="btn-group-sm" id="detail-toolbar" role="group">
                        <a class="btn btn-warning" style="margin-top:5px" onclick="exportSelected(table.options.exportUrl)">
                            <i class="fa fa-download"></i> 导出
                        </a>
                        <a class="btn btn-success" style="margin-top:5px" onclick="batchConfirm()" shiro:hasPermission="batch:amTodo:noHandle">
                            <i class="fa fa-check"></i> 批量标记无需处理
                        </a>
                        <a class="btn btn-danger" style="margin-top:5px" onclick="batchDelete()">
                            <i class="fa fa-remove"></i> 批量删除
                        </a>
                    </div>
                    <table id="table-schedule"></table>
                </div>
            </div>
        </div>
    </div>
</div>


<script th:src="@{/ruoyi/js/common.js?v=4.7.52}"></script>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: virtual-select-js"/>
<script th:src="@{/extend/js/todo/todoCommon.js?version=20240606}"></script>
<script th:inline="javascript">
    var prefix = ctx + "upcoming";
    var userDatas = [[${@sysBaseConfig.getSysUserNameAllKVList()}]];
    var shopCodeData=[[${@cdpBaseConfig.getShopAllKVList()}]]
    var shopCode = shopCodeData.map(o => {
        return {label: o.value, value: o.key}
    });
    var publishType = [[${@dict.getType('publication_goods_publish_type')}]];
    var categoryIdsData=[[${@smcBaseConfig.getPlatformCategoryByPlatformCode('null')}]]
    var categoryId = categoryIdsData.map(o => {

        return {label: o.value, value: o.key}
    });
    var pdmCategoryIdsData=[[${@cdpBaseConfig.getCateProductAllKVList()}]]

    var pdmCategoryIds = pdmCategoryIdsData.map(o => {

        return {label: o.value, value: o.key}
    });
    var createByList = userDatas.map(o => {
        return {label: o.value, value: o.key}
    });
    function initDetailTable(){
        $.table.destroy();
        var options = {
            id: "table-schedule",
            url: prefix+"/monitoringAmDeleteListing/list",
            exportUrl: prefix+"/monitoringAmDeleteListing/export",
            modalName: "待办明细",
            showSearch: false,
            queryParams: function(params) {
                var search = $.table.queryParams(params);
                search.status = $("#status").val();
                return search;
            },
            showRefresh: false,
            // 全选
            onCheckAll: function (rowsAfter,rowsBefore){
                disableVCHandle(rowsAfter.map(o => o.shopCode));
            },
            // 全反选
            onUncheckAll: function (rowsAfter,rowsBefore){
                disableVCHandle(rowsAfter.map(o => o.shopCode));
            },
            // 单选
            onCheck: function (row,$element) {
                let rows = $("#" + table.options.id).bootstrapTable('getSelections')
                disableVCHandle(rows.map(o => o.shopCode));
            },
            // 反选
            onUncheck: function (row,$element) {
                let rows = $("#" + table.options.id).bootstrapTable('getSelections')
                disableVCHandle(rows.map(o => o.shopCode));
            },
            showToggle: false,
            showColumns: false,
            columns: [
                {
                    checkbox: true
                },
                {
                  field: 'dataBatch',
                  title: '数据批次'
                },

                {
                    field: 'headId',
                    title: '主键ID'
                },
                {
                    field: 'shopCode',
                    title: '店铺'
                },
                {
                    field: 'brandCode',
                    title: '品牌'
                },
                {
                    field: 'pdmGoodsCode',
                    title: '商品编码',
                    formatter: function (value, row) {
                        if (value == null) {
                            return "-";
                        }
                        return  value;
                    }
                },
                {
                    field: 'platformSku',
                    title: '平台商品编码',
                    formatter: function (value, row) {
                        if (value == null) {
                            return "-";
                        }
                        let tooltip = '掉购物车链接(平台商品编码)';
                        return `<span title="${tooltip}">${value}</span>`;
                    }
                },
                {
                    field: 'asin',
                    title: 'ASIN',
                    formatter: function (value, row, index) {
                        var platformPrefix = "https://www.amazon.com/dp/";
                        var value = $.common.isEmpty(value) ? "-" : value;
                        var href = $.common.isEmpty(value) ? "" : platformPrefix + value;
                        return $.common.sprintf('<a href="%s" target="_blank" class="tooltip-show" data-toggle="tooltip" >%s</a>', href, value)
                    }
                },
                {
                    field: 'publishType',
                    title: '刊登类型',
                    formatter: function (value, row) {
                        if (value == null) {
                            return "-";
                        }
                        return $.table.selectDictLabel(publishType, value)
                    }
                },
                {
                    field: 'productCategoryName',
                    title: '产品分类'
                },
                {
                    field: 'fromBrandLimitFlag',
                    title: '品牌收缩',
                    formatter: function (value, row) {
                        if (value == null) {
                            return "-";
                        }
                        return value == 1 ? "是" : "否";
                    }
                },
                {
                    field: 'fromInvalidFlag',
                    title: '无效链接',
                    formatter: function (value, row) {
                        if (value == null) {
                            return "-";
                        }
                        return value == 1 ? "是" : "否";
                    }
                },

                {
                    field: 'onlineTime',
                    title: '上架时间'
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function (value, row, index) {
                        if (value == '2') {
                            return '<span class="label label-primary">待处理</span>';
                        }else if (value == '4') {
                            return '<span class="label label-info">无需处理</span>';
                        } else if(value =='5') {
                            return '<span class="label label-info">已处理</span>';
                        }
                        return '-'
                    }
                },
                {
                    field: 'operator',
                    title: '运营',
                    formatter: function (value, row, index) {
                        let operator = $.common.isEmpty(value)?"-":value;
                        var label = '';
                        $(userDatas).each(function(i,site){
                            if(site.key == operator){
                                label = site.value;
                                return false;
                            }
                        });
                        label = operator == '-1' ?  'system' : label;
                        return label;
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间'
                }
            ]
        };
        $.table.init(options);
    }

    function batchDelete() {
        table.set();
        let selectRows = $.table.selectRows();
        if (selectRows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        let headIds = selectRows.map(o => o.headId);
        $.modal.confirm("确认要删除选中的" + headIds.length + "条数据吗?", function () {
            var url = ctx + "publication/listing/remove"
            var data = {"ids": headIds.join()};
            $.operate.submit(url, "post", "json", data,function(result){
                if (result.code == web_status.SUCCESS) {
                    $.modal.msgSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        });
    }

    function batchConfirm(id) {
        table.set();
        var rows = $.table.selectColumns("id");
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认标记无需处理？", function () {
            $.operate.post(prefix + "/monitoringAmDeleteListing/confirm/", {ids: rows.join()}, function (result) {
                if (result.code == web_status.SUCCESS) {
                    $.modal.msgSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        });
    }

    $('#search-btn').click(function() {
        $('#table-schedule').bootstrapTable('refresh', {
            url: prefix + "/monitoringAmDeleteListing/list"
        })
    });

    function reset() {
        $("#formId")[0].reset();
        $('#shopCode').val("").trigger('change');
        $('#createBy').val("").trigger('change');
        $('#pdmCategoryIds').val("").trigger('change');
        $('#publishType').val("").trigger('change');
        $('#status').val("").trigger('change');
        $('#fromInvalidFlag').val("").trigger('change');
        $('#fromBrandLimitFlag').val("").trigger('change');
    }

    // 获取当前日期的前一天，并格式化为 yyyy-mm-dd
    function getYesterdayDate() {
        var date = new Date();
        date.setDate(date.getDate() - 1);  // 设置为前一天
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');  // 月份从0开始，需要加1
        var day = String(date.getDate()).padStart(2, '0');  // 日期补零
        return year + '-' + month + '-' + day;  // 返回格式化后的日期
    }

    $(function() {
        $("#status").select2("val",["2"]);
        initDetailTable();
        virtualSelectRender(document.querySelectorAll("div#createBy-select"), createByList);
        virtualSelectRender(document.querySelectorAll("div#shop-select"), shopCode);
        virtualSelectRender(document.querySelectorAll("div#pdm-category-select"), pdmCategoryIds);

    });

</script>
