package com.suncent.smc.persistence.publication.service;

import com.suncent.smc.persistence.publication.domain.entity.AiGenerationTask;

import java.util.List;

/**
 * AI内容生成任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface IAiGenerationTaskService {
    
    /**
     * 查询AI内容生成任务
     * 
     * @param id AI内容生成任务主键
     * @return AI内容生成任务
     */
    AiGenerationTask selectAiGenerationTaskById(String id);

    /**
     * 根据任务关联ID查询AI内容生成任务
     * 
     * @param taskCorrelationId 任务关联ID
     * @return AI内容生成任务
     */
    AiGenerationTask selectAiGenerationTaskByCorrelationId(String taskCorrelationId);

    /**
     * 查询AI内容生成任务列表
     * 
     * @param aiGenerationTask AI内容生成任务
     * @return AI内容生成任务集合
     */
    List<AiGenerationTask> selectAiGenerationTaskList(AiGenerationTask aiGenerationTask);

    /**
     * 新增AI内容生成任务
     * 
     * @param aiGenerationTask AI内容生成任务
     * @return 结果
     */
    int insertAiGenerationTask(AiGenerationTask aiGenerationTask);

    /**
     * 修改AI内容生成任务
     * 
     * @param aiGenerationTask AI内容生成任务
     * @return 结果
     */
    int updateAiGenerationTask(AiGenerationTask aiGenerationTask);

    /**
     * 根据任务关联ID更新任务状态和结果
     * 
     * @param taskCorrelationId 任务关联ID
     * @param taskStatus 任务状态
     * @param aiResult AI生成结果
     * @param beforeStatus 任务处理前状态
     * @return 结果
     */
    int updateTaskStatusAndResult(String taskCorrelationId, String taskStatus, String aiResult, String beforeStatus);

    /**
     * 批量删除AI内容生成任务
     * 
     * @param ids 需要删除的AI内容生成任务主键集合
     * @return 结果
     */
    int deleteAiGenerationTaskByIds(String ids);

    /**
     * 删除AI内容生成任务信息
     * 
     * @param id AI内容生成任务主键
     * @return 结果
     */
    int deleteAiGenerationTaskById(String id);

    /**
     * 查询待处理的任务列表
     * 
     * @param taskType 任务类型
     * @return 待处理任务列表
     */
    List<AiGenerationTask> selectPendingTasks(String taskType);

    /**
     * 创建AI生成任务
     *
     * @param taskCorrelationId 任务关联ID
     * @param taskType 任务类型
     * @param taskId 商品编码列表
     * @param shopCode 店铺编码
     * @param brandCode 品牌编码
     * @param createBy 创建人
     * @return 创建的任务
     */
    AiGenerationTask createAiGenerationTask(String taskCorrelationId, String taskType,
                                           Integer taskId, String shopCode, List<String> goodsCodes,
                                           String brandCode, String createBy);

    /**
     * 更新任务处理状态
     *
     * @param taskCorrelationId 任务关联ID
     * @param processStatus 处理状态
     * @return 结果
     */
    int updateProcessStatus(String taskCorrelationId, Integer processStatus);

    
    int countTaskByStatus(Integer taskId, int processStatus);
}
