package com.suncent.smc.provider.biz.publication;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ebay.soap.eBLBaseComponents.ItemType;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.suncent.smc.common.config.SpringTaskRetry;
import com.suncent.smc.common.constant.Constants;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysDictData;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.framework.web.service.DictService;
import com.suncent.smc.persistence.ads.domain.AdsFitmentDataVIO;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.api.domain.EbayShippingDetailsType;
import com.suncent.smc.persistence.api.domain.EbayShippingExcludeLocation;
import com.suncent.smc.persistence.api.service.IEbayShippingDetailsTypeService;
import com.suncent.smc.persistence.api.service.IEbayShippingExcludeLocationService;
import com.suncent.smc.persistence.aplus.domain.APlusAsin;
import com.suncent.smc.persistence.aplus.service.IAPlusAsinService;
import com.suncent.smc.persistence.cdp.domain.entity.Brand;
import com.suncent.smc.persistence.cdp.domain.entity.Shop;
import com.suncent.smc.persistence.cdp.service.IBrandService;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.cdp.service.ISiteService;
import com.suncent.smc.persistence.common.CommonUtilsSmc;
import com.suncent.smc.persistence.common.domain.GoodsRedLinePrice;
import com.suncent.smc.persistence.common.service.IGoodsRedLinePriceService;
import com.suncent.smc.persistence.configuration.category.domain.entity.CategoryInfo;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigEbayCategory;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigRequiredField;
import com.suncent.smc.persistence.configuration.category.service.ICategoryInfoService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.pdm.domain.dto.GetGoodsDetailQueryDTO;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.dto.ThirdpartyFbmDTO;
import com.suncent.smc.persistence.pdm.domain.entity.Goods;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsImage;
import com.suncent.smc.persistence.pdm.service.IGoodsService;
import com.suncent.smc.persistence.product.domain.common.TitleFieldQuery;
import com.suncent.smc.persistence.product.domain.entity.PublicationDay;
import com.suncent.smc.persistence.publication.domain.dto.*;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.domain.vo.DynamicListingDetailVO;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO;
import com.suncent.smc.persistence.publication.domain.vo.ListingPerformanceDetailVO;
import com.suncent.smc.persistence.publication.domain.vo.ListingTagDetailVO;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.template.domain.entity.TemplateEbayDescription;
import com.suncent.smc.persistence.template.domain.entity.TemplateEbayShippingHead;
import com.suncent.smc.persistence.template.service.ITemplateEbayDescriptionService;
import com.suncent.smc.persistence.template.service.ITemplateEbayShippingHeadService;
import com.suncent.smc.persistence.template.service.ITemplateShopRelationService;
import com.suncent.smc.persistence.temu.domain.entity.TemuGoodsHead;
import com.suncent.smc.persistence.temu.domain.entity.TemuListingSpecs;
import com.suncent.smc.persistence.temu.service.ITemuGoodsHeadService;
import com.suncent.smc.persistence.temu.service.ITemuListingSpecsService;
import com.suncent.smc.persistence.todo.domain.dto.AdapterTodoQueryDTO;
import com.suncent.smc.persistence.todo.domain.entity.SmcAdaptTodo;
import com.suncent.smc.persistence.todo.service.ISmcAdaptTodoService;
import com.suncent.smc.provider.biz.common.CommonListingInfoBiz;
import com.suncent.smc.provider.biz.configuration.CategoryInfoHandleBiz;
import com.suncent.smc.provider.biz.configuration.InventoryExcludeBiz;
import com.suncent.smc.provider.biz.configuration.ViolateWordBiz;
import com.suncent.smc.provider.biz.image.ImageHandleBiz;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.pdm.CreatePlatformCode;
import com.suncent.smc.provider.biz.publication.domain.ListingCopyToShopDTO;
import com.suncent.smc.provider.biz.publication.domain.ListingsEditVO;
import com.suncent.smc.provider.biz.publication.service.IBaseListingService;
import com.suncent.smc.provider.biz.publication.service.ListingInfoService;
import com.suncent.smc.provider.biz.publication.service.impl.AmazonPlatformListingServiceImpl;
import com.suncent.smc.provider.biz.publication.service.impl.EbayPlatformListingServiceImpl;
import com.suncent.smc.provider.biz.template.TemplateEbayDescriptionBiz;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import com.suncent.smc.system.service.impl.SysBaseConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.suncent.smc.common.enums.AmazonAttributeEnum.EXTERNAL_PRODUCT_ID;
import static com.suncent.smc.common.enums.AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE;
import static com.suncent.smc.common.enums.PublishStatus.*;
import static com.suncent.smc.persistence.publication.service.impl.GoodsHeadServiceImpl.TIME_PUBLISHING;

/**
 * listing 业务逻辑
 */
@Slf4j
@Component
public class ListingInfoBiz  extends CommonListingInfoBiz {

    @Autowired
    IGoodsHeadService goodsHeadService;
    @Autowired
    IGoodsResourceService goodsResourceService;
    @Autowired
    IGoodsDescriptionService goodsDescriptionService;
    @Autowired
    IGoodsSpecificationService goodsSpecificationService;
    @Autowired
    IListingEbayLineService ebayGoodsLineService;
    @Autowired
    @Qualifier("ebayPlatformListingServiceImpl")
    @Lazy
    EbayPlatformListingServiceImpl ebayPlatformListingService;
    @Autowired
    SpringTaskRetry retry;
    @Autowired
    CreatePlatformCode createPlatformCode;
    @Autowired
    IShopService shopService;
    @Autowired
    DictService dictService;
    @Autowired
    IBrandService brandService;
    @Autowired
    ISiteService siteService;
    @Autowired
    IListingEbayValueService ebayValueService;
    @Autowired
    IListingEbayAdaptiveService adaptiveService;
    @Autowired
    ListingTemplateService listingTemplateService;
    @Autowired
    private IListingAmazonAttributeLineService listingAmazonAttributeLineService;
    @Autowired
    private IListingEbayPolicyService ebayPolicyService;
    @Autowired
    private IListingEbayShippingHeadService ebayShippingHeadService;
    @Autowired
    private IListingShippingLocationLineService shippingLocationLineService;
    @Autowired
    private IListingShippingTypeLineService shippingTypeLineService;
    @Autowired
    private IEbayShippingDetailsTypeService ebayShippingDetailsTypeService;

    @Autowired
    private IEbayShippingExcludeLocationService ebayShippingExcludeLocationService;
    @Autowired
    private ICategoryInfoService categoryInfoService;
    @Autowired
    private IListingEbayValueService listingEbayValueService;
    @Autowired
    @Lazy
    private GoodsInfoBiz goodsInfoBiz;
    @Autowired
    private IListingLogService listingLogService;
    @Autowired
    private ThirdpartyInventoryBiz inventoryBiz;

    @Autowired
    private IListingEbayPolicyService listingEbayPolicyService;
    @Autowired
    public RedisService redisService;
    @Autowired
    ITemplateShopRelationService templateShopRelationService;

    @Autowired
    private SysBaseConfigService sysBaseConfig;
    @Autowired
    private ImageHandleBiz imageHandleBiz;
    @Autowired
    private ListingInfoService listingInfoService;

    @Autowired
    private ITaskConfigurationService taskConfigurationService;
    @Autowired
    IListingEbayLineService listingEbayLineService;

    @Autowired
    private IGoodsService goodsService;

    @Autowired
    private ThirdpartyInventoryBiz thirdpartyInventoryBiz;

    @Autowired
    private ITemplateEbayShippingHeadService templateEbayShippingHeadService;
    @Resource
    private ITemplateEbayDescriptionService templateEbayDescriptionService;
    @Resource
    private CommonInfoBiz commonInfoBiz;
    @Autowired
    private IGoodsTaskService goodsTaskService;
    @Autowired
    private IGoodsTaskInfoService goodsTaskInfoService;
    @Autowired
    private IListingEbayAdaptiveService listingEbayAdaptiveService;
    @Autowired
    private BaseAmazonProductDeleteV2Task amazonProductDeleteV2Task;
    @Autowired
    private IGoodsHeadBackupService goodsHeadBackupService;
    @Autowired
    private IPlatformCategoryService platformCategoryService;
    @Autowired
    private ICirculateTimedTaskListingPoolService circulateTimedTaskListingPoolService;
    @Autowired
    @Lazy
    private TemplateEbayDescriptionBiz templateEbayDescriptionBiz;
    @Value("${sys-file.file-template-path_new}")
    protected String fileTemplatePathNew;
    @Autowired
    private IAPlusAsinService aPlusAsinService;
    @Autowired
    private PlatformListingFactory platformListingFactory;

    @Autowired
    private IGoodsRedLinePriceService goodsRedLinePriceService;
    ThreadPoolExecutor copyConfig = new ThreadPoolExecutor(
            4,
            4,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(2000));

    @Autowired
    @Lazy
    private AmazonPlatformListingServiceImpl amazonPlatformListingService;

    @Resource
    PDMHttpRequestBiz pdmHttpRequestBiz;

    @Autowired
    IListingLabelService listingLabelService;

    @Autowired
    ITemuGoodsHeadService temuGoodsHeadService;
    @Autowired
    private IAmCategoryTemplatePrivateValueService amCategoryTemplatePrivateValueService;
    @Autowired
    IListingEbayVideoService listingEbayVideoService;
    @Autowired
    private ITemuListingSpecsService temuListingSpecsService;
    @Autowired
    private ViolateWordBiz violateWordBiz;
    @Autowired
    private IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;
    @Resource
    private ISmcAdaptTodoService smcAdaptTodoService;
    @Autowired
    private PDMHttpRequestBiz PDMHttpRequestBiz;
    @Resource
    private AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;
    @Autowired
    private AmazonProductBiz amazonProductBiz;
    @Autowired
    private IAmCategoryTemplateFieldService amCategoryTemplateFieldService;
    @Autowired
    private CategoryInfoHandleBiz categoryInfoHandleBiz;
    @Autowired
    private IAdsService adsService;
    @Autowired
    private InventoryExcludeBiz inventoryExcludeBiz;

    @Autowired
    private IListingGoodsHeadDetailService listingGoodsHeadDetailService;

    @Autowired
    private IAmUpdateAvailabilityTaskService amUpdateAvailabilityTaskService;

    @Autowired
    private ThreadPoolForMonitorManager threadPoolForMonitorManager;

    /**
     * 修改Listing
     *
     * @param id ListingID
     * @return Listing数据
     */
    public ListingEditDTO queryEditListing(Integer id) {
        ListingEditDTO listingEditDTO = new ListingEditDTO();
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(id);
        if (goodsHead.getDelFlag().equals(2)) {
            throw new BusinessException("该链接已删除");
        }

        Integer goodsId = goodsHead.getId();

        // 商品头表基础数据
        BeanUtil.copyProperties(goodsHead, listingEditDTO);
        listingEditDTO.setGoodsCode(goodsHead.getPdmGoodsCode());
        listingEditDTO.setGoodsHeadId(goodsId);
        listingEditDTO.setStockOnSalesQty(BigDecimal.valueOf(listingEditDTO.getStockOnSalesQty().intValue()));
        // 兑换属性名
        String shopName = shopService.selectShopNameByShopCode(goodsHead.getShopCode());
        listingEditDTO.setShopName(shopName);

        // 商品图片视频数据
        List<GoodsResource> goodsResourceList = goodsResourceService.selectListingGoodsResourceByHeadId(goodsId);
        listingEditDTO.setGoodsResourceList(goodsResourceList);

        Map<String, List<GoodsResource>> resource = goodsResourceList.stream().filter(p -> ObjectUtils.isNotEmpty(p.getResourceType())).collect(Collectors.groupingBy(GoodsResource::getResourceType));
        if (!resource.isEmpty()) {
            if (Objects.equals(PlatformTypeEnum.AM.name(), listingEditDTO.getPlatform())) {
                //图片数据
                List<GoodsResource> imgResource = resource.get("1");
                if (!imgResource.isEmpty()) {
                    Map<Integer, List<GoodsResource>> map = imgResource.stream().collect(Collectors.groupingBy(GoodsResource::getIsMain));
                    if (map.get(1) != null && !map.get(1).isEmpty()) {
                        //处理主图字段赋值
                        listingEditDTO.setMasterImgFile(imgResource.stream().collect(Collectors.groupingBy(GoodsResource::getIsMain)).get(1).get(0).getResourceUrl());
                        listingEditDTO.setMasterImgFileId(imgResource.stream().collect(Collectors.groupingBy(GoodsResource::getIsMain)).get(1).get(0).getId());
                    }
                    if (map.get(0) != null && !map.get(0).isEmpty()) {
                        //处理副图12张赋值
//                        listingEditDTO.setImgDataArrList(imgResource.stream().collect(Collectors.groupingBy(GoodsResource::getIsMain)).get(0));
                        List<GoodsResource> resourceListSort = map.get(0).stream().sorted(Comparator.comparing(GoodsResource::getSortNumber)).collect(Collectors.toList());
                        listingEditDTO.setImgDataArrList(resourceListSort);
                    }
                }
            } else {
                //视频数据
                if(Objects.equals(PlatformTypeEnum.EB.name(), listingEditDTO.getPlatform())){
                    ListingEbayVideo  listingEbayVideo = listingEbayVideoService.selectListingEbayVideoByGoodsId(goodsId);
                    if (ObjectUtils.isNotEmpty(listingEbayVideo)){
                        listingEditDTO.setVideoSelect(listingEbayVideo.getVideoId());
                    }
                }
                //图片数据
                List<GoodsResource> imgResource = resource.get("1");
                if (ObjectUtils.isNotEmpty(imgResource)) {
                    if (!imgResource.isEmpty()) {
                        Map<Integer, List<GoodsResource>> map = imgResource.stream().collect(Collectors.groupingBy(GoodsResource::getIsMain));
                        if (map.get(1) != null && !map.get(1).isEmpty()) {
                            //处理主图字段赋值
                            listingEditDTO.setMasterImgFile(imgResource.stream().collect(Collectors.groupingBy(GoodsResource::getIsMain)).get(1).get(0).getResourceUrl());
                            listingEditDTO.setMasterImgFileId(imgResource.stream().collect(Collectors.groupingBy(GoodsResource::getIsMain)).get(1).get(0).getId());
                        }

                        if (map.get(0) != null && !map.get(0).isEmpty()) {
                            //处理副图12张赋值
                            //如果sortNumber为空，按照id排序
                            List<GoodsResource> resourceListSort;
                            if (map.get(0).get(0).getSortNumber() == null) {
                                resourceListSort = map.get(0).stream().sorted(Comparator.comparing(GoodsResource::getId)).collect(Collectors.toList());
                            } else {
                                resourceListSort = map.get(0).stream().sorted(Comparator.comparing(GoodsResource::getSortNumber)).collect(Collectors.toList());
                            }
//                            listingEditDTO.setImgDataArrList(imgResource.stream().collect(Collectors.groupingBy(GoodsResource::getIsMain)).get(0));
//                            List<GoodsResource> resourceListSort = map.get(0).stream().sorted(Comparator.comparing(GoodsResource::getSortNumber)).collect(Collectors.toList());
                            listingEditDTO.setImgDataArrList(resourceListSort);

                        }
                    }
                }

            }
        }

        // 商品描述数据
        GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsId);
        BeanUtil.copyProperties(goodsDescription, listingEditDTO, "id");
        if (ObjectUtils.isNotEmpty(goodsDescription)) {
            listingEditDTO.setGoodsDescriptionId(goodsDescription.getId());
            listingEditDTO.setTemplateSelect(goodsDescription.getDescriptionId());
        }

        // 商品规格数据
        GoodsSpecification goodsSpecification = goodsSpecificationService.selectSpecificationListByGoodsId(goodsId);
        listingEditDTO.setPlatform(goodsHead.getPlatform());
        if (goodsSpecification != null) {
            listingEditDTO.setGoodsSpecificationId(goodsSpecification.getId());
            if (goodsSpecification.getItemLength() != null) {
                goodsSpecification.setItemLength(goodsSpecification.getItemLength().setScale(2, RoundingMode.HALF_UP));
            }
            if (goodsSpecification.getItemWidth() != null) {
                goodsSpecification.setItemWidth(goodsSpecification.getItemWidth().setScale(2, RoundingMode.HALF_UP));
            }
            if (goodsSpecification.getItemHeight() != null) {
                goodsSpecification.setItemHeight(goodsSpecification.getItemHeight().setScale(2, RoundingMode.HALF_UP));
            }
            if (goodsSpecification.getPackageLength() != null) {
                goodsSpecification.setPackageLength(goodsSpecification.getPackageLength().setScale(2, RoundingMode.HALF_UP));
            }
            if (goodsSpecification.getPackageWidth() != null) {
                goodsSpecification.setPackageWidth(goodsSpecification.getPackageWidth().setScale(2, RoundingMode.HALF_UP));
            }
            if (goodsSpecification.getPackageHeight() != null) {
                goodsSpecification.setPackageHeight(goodsSpecification.getPackageHeight().setScale(2, RoundingMode.HALF_UP));
            }
            if (goodsSpecification.getPackageWeight() != null) {
                goodsSpecification.setPackageWeight(goodsSpecification.getPackageWeight().setScale(2, RoundingMode.HALF_UP));
            }
            if (goodsSpecification.getPackageTypeLbs() != null) {
                goodsSpecification.setPackageTypeLbs(goodsSpecification.getPackageTypeLbs().setScale(2, RoundingMode.HALF_UP));
            }
            if (goodsSpecification.getPackageTypeOz() != null) {
                goodsSpecification.setPackageTypeOz(goodsSpecification.getPackageTypeOz().setScale(2, RoundingMode.HALF_UP));
            }

            BeanUtil.copyProperties(goodsSpecification, listingEditDTO);
            //ebay页面中直接塞对象
            listingEditDTO.setGoodsSpecification(goodsSpecification);

            listingEditDTO.setIsIrregularity(dictService.getLabel("sys_yes_no", goodsSpecification.getIsIrregularity()));
        }

        //Amazon Listing数据获取
        if (Objects.equals(PlatformTypeEnum.AM.name(), listingEditDTO.getPlatform())) {
            amazonPageData(listingEditDTO, goodsHead, goodsId);
        } else {
            // eBay Listing数据获取
            ebayPageData(listingEditDTO, goodsHead);
        }

        //类目
        listingEditDTO.setProductCategoryCode(String.valueOf(goodsHead.getCategoryId()));
        listingEditDTO.setFirstCategoryid(String.valueOf(goodsHead.getCategoryId()));
        //处理退货率标签
        listingEditDTO.setListingRateLabel(RefundRateLabelEnum.getDictCode(null,"listing",listingEditDTO.getListingRateLabel()));
        listingEditDTO.setSkuRateLabel(RefundRateLabelEnum.getDictCode(listingEditDTO.getPlatform(),"sku",listingEditDTO.getSkuRateLabel()));

        return listingEditDTO;
    }

    /**
     * ebay -lisitng详情页面的展示数据封装
     * @param listingEditDTO
     * @param goodsHead
     */
    private void ebayPageData(ListingEditDTO listingEditDTO, GoodsHead goodsHead) {
        ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHead.getId());
        if (ObjUtil.isEmpty(listingEbayLine)) {
            log.error("ebay Listing缺失行表数据，请检查数据来源,headId:{}", goodsHead.getId());
            return;
        }
        listingEditDTO.setPublicationDay(listingEbayLine.getSellDay());
        BeanUtil.copyProperties(listingEbayLine, listingEditDTO, true);
        listingEditDTO.setBestOfferFlag(listingEbayLine.getBestOfferFlag());

        List<PublicationDay> publicationDays = getPublicationDays();

        listingEditDTO.setPublicationDayList(publicationDays);
        publicationDays.stream().forEach(s -> {
            if (s.getDay().equals(listingEbayLine.getSellDay())) {
                s.setFlag(true);
            }
        });
        String publishTypeName = dictService.getLabel("publication_ebay_goods_publish_type", String.valueOf(goodsHead.getPublishType()));
        listingEditDTO.setPublishTypeName(publishTypeName);
        listingEditDTO.setLocation(listingEbayLine.getLocation());
        listingEditDTO.setStandardPrice(goodsHead.getStandardPrice());
        listingEditDTO.setStartStockQty(listingEbayLine.getStartSellingCount());
//        listingEditDTO.setBestOfferFlag(dictService.getLabel("sys_yes_no", listingEbayLine.getBestOfferFlag()));
        listingEditDTO.setListingLineId(listingEbayLine.getId());
        listingEditDTO.setStartSellingCount(listingEbayLine.getStartSellingCount());

        //查询付款政策模板
        ListingEbayPolicy listingEbayPolicy = ebayPolicyService.selectListingEbayPolicyByLineId(listingEbayLine.getId());
        listingEditDTO.setListingEbayPolicy(listingEbayPolicy);

        //查询物流模板
        ListingEbayShippingHead listingEbayShippingHead = ebayShippingHeadService.selectListingEbayShippingHeadByLineId(listingEbayLine.getId());
        if (ObjectUtils.isNotEmpty(listingEbayShippingHead)) {
            //处理排除地区信息展示
            List<ListingShippingLocationLine> listingShippingLocationLines = shippingLocationLineService.selectByShippingHeadId(listingEbayShippingHead.getId());
            //获取运输方式信息展示
            List<ListingShippingTypeLine> listingShippingTypeLines = shippingTypeLineService.selectListingShippingTypeLineByHeadId(listingEbayShippingHead.getId());
            listingEbayShippingHead.setShippingLocationLineList(listingShippingLocationLines);
            listingEbayShippingHead.setShippingTypeLineList(listingShippingTypeLines);
            listingEbayShippingHead.setDomesticLocations(getLocations(listingShippingLocationLines, "Domestic Location"));
            listingEbayShippingHead.setAfricas(getLocations(listingShippingLocationLines, "Africa"));
            listingEbayShippingHead.setAsias(getLocations(listingShippingLocationLines, "Asia"));
            listingEbayShippingHead.setCentralAmericaAndCaribbeans(getLocations(listingShippingLocationLines, "Central America and Caribbean"));
            listingEbayShippingHead.setEuropes(getLocations(listingShippingLocationLines, "Europe"));
            listingEbayShippingHead.setMiddleEasts(getLocations(listingShippingLocationLines, "Middle East"));
            listingEbayShippingHead.setNorthAmericas(getLocations(listingShippingLocationLines, "North America"));
            listingEbayShippingHead.setOceanias(getLocations(listingShippingLocationLines, "Oceania"));
            listingEbayShippingHead.setSoutheastAsias(getLocations(listingShippingLocationLines, "Southeast Asia"));
            listingEbayShippingHead.setSouthAmericas(getLocations(listingShippingLocationLines, "South America"));

            listingEbayShippingHead.setPoBox(getLocations(listingShippingLocationLines, "PO Box"));

            listingEditDTO.setListingEbayShippingHead(listingEbayShippingHead);
        }
        //查询描述模板
//        ListingEbayDescription listingEbayDescription = ebayDescriptionService.selectListingEbayDescriptionByLineId(listingEbayLine.getId());
    }


    private String[] getLocations(List<ListingShippingLocationLine> shippingLocationLineList, String region) {
        List<String> list = shippingLocationLineList.stream().filter(domesticLocation -> region.equals(domesticLocation.getRegion())).map(ListingShippingLocationLine::getLocation).collect(Collectors.toList());
        return list.toArray(new String[list.size()]);
    }

    public void amazonPageData(ListingEditDTO listingEditDTO, GoodsHead goodsHead, Integer goodsId) {
        String vcFlag = goodsHead.getShopCode().contains("VC") ? "Y" : "N";
        String publishTypeName = dictService.getLabel("publication_goods_publish_type", String.valueOf(goodsHead.getPublishType()));
        listingEditDTO.setPublishTypeName(publishTypeName);
        listingEditDTO.setStandardPrice(StrUtil.isBlank(goodsHead.getStandardPrice()) ? "0" : goodsHead.getStandardPrice());
        String brandName = brandService.selectBrandNameByBrandCode(goodsHead.getBrandCode());
        listingEditDTO.setBrandName(brandName);

        List<ListingAmazonAttributeLine> attributeLines = listingAmazonAttributeLineService.selectByGoodsIdAndType(goodsId, 4);
        if (!CollectionUtils.isEmpty(attributeLines)) {
            for (ListingAmazonAttributeLine attributeLine : attributeLines) {
                if (Objects.equals(attributeLine.getTableName(), AmazonAttributeEnum.FULFILLMENT_LATENCY.getInfo())){
                    listingEditDTO.setFulfillmentLatency(Convert.toNumber(attributeLine.getTableValue()) == null ? null : Convert.toNumber(attributeLine.getTableValue()).intValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getTableName(), AmazonAttributeEnum.FN_SKU.getInfo())){
                    listingEditDTO.setFnSku(attributeLine.getTableValue());
                    continue;
                }
                //处理平台编码类型
                if (Objects.equals(attributeLine.getTableName(), EXTERNAL_PRODUCT_ID_TYPE.getInfo())){
                    listingEditDTO.setExternalProductIdType(attributeLine.getTableValue());
                    continue;
                }
                //处理平台编码类型的值
                if (Objects.equals(attributeLine.getTableName(), EXTERNAL_PRODUCT_ID.getInfo())){
                    listingEditDTO.setExternalProductId(attributeLine.getTableValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getTableName(), AmazonAttributeEnum.SALE_PRICE.getInfo())){
                    listingEditDTO.setSalePrice(attributeLine.getTableValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getTableName(), AmazonAttributeEnum.SALE_FROM_DATE.getInfo())){
                    listingEditDTO.setSaleBeginDate(attributeLine.getTableValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getTableName(), AmazonAttributeEnum.SALE_END_DATE.getInfo())){
                    listingEditDTO.setSaleEndDate(attributeLine.getTableValue());
                }
            }
        }

        List<ListingAmazonAttributeLineV2> attributeLinesV2 = listingAmazonAttributeLineV2Service.selectExtendAttrByGoodsId(goodsId);
        if (!CollectionUtils.isEmpty(attributeLinesV2)) {
            for (ListingAmazonAttributeLineV2 attributeLine : attributeLinesV2) {
                if (Objects.equals(attributeLine.getPropNodePath(), AmazonAttributeEnum.FULFILLMENT_LATENCY.getInfoV2(vcFlag))) {
                    listingEditDTO.setFulfillmentLatency(Convert.toNumber(attributeLine.getTableValue()) == null ? null : Convert.toNumber(attributeLine.getTableValue()).intValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getPropNodePath(), AmazonAttributeEnum.FN_SKU.getInfoV2(vcFlag))) {
                    listingEditDTO.setFnSku(attributeLine.getTableValue());
                    continue;
                }
                //处理平台编码类型
                if (Objects.equals(attributeLine.getPropNodePath(), EXTERNAL_PRODUCT_ID_TYPE.getInfoV2(vcFlag)) && Objects.equals(attributeLine.getTableType(), 4)) {
                    listingEditDTO.setExternalProductIdType(attributeLine.getTableValue().toUpperCase());
                    continue;
                }
                //处理平台编码类型的值
                if (Objects.equals(attributeLine.getPropNodePath(), EXTERNAL_PRODUCT_ID.getInfoV2(vcFlag)) && Objects.equals(attributeLine.getTableType(), 4)) {
                    listingEditDTO.setExternalProductId(attributeLine.getTableValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getPropNodePath(), EXTERNAL_PRODUCT_ID.getInfoV2(vcFlag)) && Objects.equals(attributeLine.getTableType(), 5)) {
                    listingEditDTO.setExternalAsin(attributeLine.getTableValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getPropNodePath(), AmazonAttributeEnum.SALE_PRICE.getInfoV2(vcFlag))) {
                    listingEditDTO.setSalePrice(attributeLine.getTableValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getPropNodePath(), AmazonAttributeEnum.SALE_FROM_DATE.getInfoV2(vcFlag))) {
                    listingEditDTO.setSaleBeginDate(attributeLine.getTableValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getPropNodePath(), AmazonAttributeEnum.SALE_END_DATE.getInfoV2(vcFlag))) {
                    listingEditDTO.setSaleEndDate(attributeLine.getTableValue());
                    continue;
                }
                if (Objects.equals(attributeLine.getPropNodePath(), AmazonAttributeEnum.LIST_PRICE.getInfoV2(vcFlag))) {
                    listingEditDTO.setListPrice(attributeLine.getTableValue());
                    continue;
                }
            }
        }

    }


    public void saveUpdateEbayShip(ListingEditDTO listingEditDTO, Integer listingLineId) {
        ListingEbayShippingHead shippingHead = listingEditDTO.getListingEbayShippingHead();
        if (ObjectUtils.isEmpty(shippingHead.getId())) {
            // 无物流模板——>新增
            shippingHead.setListingLineId(listingLineId);
            shippingHead.setCreateBy(listingEditDTO.getUpdateBy());
            shippingHead.setUpdateBy(listingEditDTO.getUpdateBy());
            ebayShippingHeadService.insertListingEbayShippingHead(shippingHead);
            shippingHead.getShippingTypeLineList().stream().forEach(type -> {
                type.setShippingHeadId(Long.valueOf(shippingHead.getId()));
            });
            shippingHead.getShippingLocationLineList().stream().forEach(locationLine -> {
                locationLine.setShippingHeadId(Long.valueOf(shippingHead.getId()));
            });
            shippingTypeLineService.insertList(shippingHead.getShippingTypeLineList());
            shippingLocationLineService.insertList(shippingHead.getShippingLocationLineList());
        } else {
            //修改
            shippingTypeLineService.deleteByShippingHeadId(shippingHead.getId());
            shippingLocationLineService.deleteByShippingHeadId(shippingHead.getId());
            shippingTypeLineService.insertList(shippingHead.getShippingTypeLineList());
            if (CollUtil.isEmpty(shippingHead.getShippingLocationLineList())) {
                throw new RuntimeException("排除地区国家不能为空.");
            }
            shippingLocationLineService.insertList(shippingHead.getShippingLocationLineList());

        }
    }

    public void saveUpdateEbayPolicy(ListingEditDTO listingEditDTO, Integer listingLineId) {
        ListingEbayPolicy listingEbayPolicy = listingEditDTO.getListingEbayPolicy();
        if (ObjectUtils.isEmpty(listingEbayPolicy.getId())) {
            // 无付款模板——>新增
            ListingEbayPolicy add = new ListingEbayPolicy();
            BeanUtil.copyProperties(listingEbayPolicy, add);
            listingEbayPolicy.setListingLineId(listingLineId);
            listingEbayPolicy.setCreateBy(listingEditDTO.getUpdateBy());
            listingEbayPolicy.setUpdateBy(listingEditDTO.getUpdateBy());
            listingEbayPolicyService.insertListingEbayPolicy(listingEbayPolicy);
        } else {
            //修改
            listingEbayPolicy.setUpdateBy(listingEditDTO.getUpdateBy());
            listingEbayPolicyService.updateListingEbayPolicy(listingEbayPolicy);
        }

    }

    public void saveEbayAttributeTable(ListingEditDTO listingEditDTO, Integer listingLineId) {
        //去重map
        Map<String, String> repeatVerMap = new HashMap<>();
        //保存新的数据
        List<ListingAmazonAttributeLine> info = listingEditDTO.getListingAmazonAttributeLinesInfo();
        if (CollectionUtils.isEmpty(info)) {
            return;
        }
        //删除原来的数据
        listingEbayValueService.deleteListingEbayValueByListingLineId(listingLineId);

        for (ListingAmazonAttributeLine attributeLine : info) {
            //属性去重 ->防止重复添加
            if (repeatVerMap.containsKey(attributeLine.getTableName())) {
                continue;
            }
            repeatVerMap.put(attributeLine.getTableName(), null);

            ListingEbayValue line = new ListingEbayValue();
            line.setCreateTime(DateUtils.getNowDate());
            line.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
            line.setListingLineId(listingLineId);
            line.setName(attributeLine.getTableName());
            line.setValue(attributeLine.getTableValue());
            listingEbayValueService.insertListingEbayValue(line);
        }
    }

    public List<ListingAmazonAttributeLine> getAttributeTable(ListingEditDTO listingEditDTO) {
        List<ListingAmazonAttributeLine> amazonAttributeLines = new ArrayList<>();

        List<ListingAmazonAttributeLine> info = listingEditDTO.getListingAmazonAttributeLinesInfo();
        List<ListingAmazonAttributeLine> detail = listingEditDTO.getListingAmazonAttributeLinesDetail();
        List<ListingAmazonAttributeLine> safety = listingEditDTO.getListingAmazonAttributeLinesSafety();

        Integer fulfillmentLatency = listingEditDTO.getFulfillmentLatency();
        if (ObjectUtils.isNotEmpty(fulfillmentLatency)) {
            ListingAmazonAttributeLine line = new ListingAmazonAttributeLine();
            line.setTableName(AmazonAttributeEnum.FULFILLMENT_LATENCY.getInfo());
            line.setTableValue(String.valueOf(fulfillmentLatency));
            line.setTableType(4);
            amazonAttributeLines.add(line);
        }
        //平台商品编码
        String externalProductId = listingEditDTO.getExternalProductId();
        //平台商品编码类型
        String externalProductIdType = listingEditDTO.getExternalProductIdType();
        if (ObjectUtils.isNotEmpty(externalProductIdType)) {
            //类型
            ListingAmazonAttributeLine lineType = new ListingAmazonAttributeLine();
            lineType.setPlatform(PlatformTypeEnum.AM.name());
            lineType.setCategoryId(listingEditDTO.getCategoryId());
            lineType.setTableName(EXTERNAL_PRODUCT_ID_TYPE.getInfo());
            lineType.setTableValue(externalProductIdType);
            lineType.setTableType(4);
            amazonAttributeLines.add(lineType);
            //值
            ListingAmazonAttributeLine lineValue = new ListingAmazonAttributeLine();
            lineValue.setPlatform(PlatformTypeEnum.AM.name());
            lineValue.setCategoryId(listingEditDTO.getCategoryId());
            lineValue.setTableName(EXTERNAL_PRODUCT_ID.getInfo());
            lineValue.setTableValue(externalProductId);
            lineValue.setTableType(4);
            amazonAttributeLines.add(lineValue);
        }

        String salePrice = listingEditDTO.getSalePrice();
        String saleBeginDate = listingEditDTO.getSaleBeginDate();
        String saleEndDate = listingEditDTO.getSaleEndDate();
        if (ObjectUtils.isNotEmpty(salePrice) && ObjectUtils.isNotEmpty(saleBeginDate) && ObjectUtils.isNotEmpty(saleEndDate)) {
            ListingAmazonAttributeLine salePriceLine = new ListingAmazonAttributeLine();
            salePriceLine.setPlatform(PlatformTypeEnum.AM.name());
            salePriceLine.setCategoryId(listingEditDTO.getCategoryId());
            salePriceLine.setTableName(AmazonAttributeEnum.SALE_PRICE.getInfo());
            salePriceLine.setTableValue(salePrice);
            salePriceLine.setTableType(4);
            amazonAttributeLines.add(salePriceLine);

            ListingAmazonAttributeLine saleBeginDateLine = new ListingAmazonAttributeLine();
            saleBeginDateLine.setPlatform(PlatformTypeEnum.AM.name());
            saleBeginDateLine.setCategoryId(listingEditDTO.getCategoryId());
            saleBeginDateLine.setTableName(AmazonAttributeEnum.SALE_FROM_DATE.getInfo());
            saleBeginDateLine.setTableValue(saleBeginDate);
            saleBeginDateLine.setTableType(4);
            amazonAttributeLines.add(saleBeginDateLine);

            ListingAmazonAttributeLine saleEndDateLine = new ListingAmazonAttributeLine();
            saleEndDateLine.setPlatform(PlatformTypeEnum.AM.name());
            saleEndDateLine.setCategoryId(listingEditDTO.getCategoryId());
            saleEndDateLine.setTableName(AmazonAttributeEnum.SALE_END_DATE.getInfo());
            saleEndDateLine.setTableValue(saleEndDate);
            saleEndDateLine.setTableType(4);
            amazonAttributeLines.add(saleEndDateLine);
        }

        if (!CollectionUtils.isEmpty(info)) {
            amazonAttributeLines.addAll(info.stream().map(f -> {
                f.setTableType(0);
                return f;
            }).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(detail)) {
            amazonAttributeLines.addAll(detail.stream().map(f -> {
                f.setTableType(1);
                return f;
            }).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(safety)) {
            amazonAttributeLines.addAll(safety.stream().map(f -> {
                f.setTableType(2);
                return f;
            }).collect(Collectors.toList()));
        }

        return amazonAttributeLines;
    }

    public void saveGoodsResourceList(ListingEditDTO listingDTO) {
        ArrayList<GoodsResource> goodsResourceList = getGoodsResources(listingDTO);
        goodsResourceService.deleteListingGoodsResourceByGoodsId(listingDTO.getGoodsHeadId(), 0);
        if (goodsResourceList.size() > 0) {
            goodsResourceService.insertListingGoodsResourceBatch(goodsResourceList);
        }
    }

    public ArrayList<GoodsResource> getGoodsResources(ListingEditDTO listingDTO) {
        ArrayList<GoodsResource> goodsResourceList = new ArrayList<>();
        //处理附图12张
        JSONArray imgJsonArray = JSONArray.parseArray(listingDTO.getImgDataArr());
        for (int i = 0; i < imgJsonArray.size(); i++) {
            GoodsResource goodsResource = new GoodsResource();
            goodsResource.setResourceUrl(imgJsonArray.getJSONObject(i).getString("url"));
            goodsResource.setResourceName(imgJsonArray.getJSONObject(i).getString("name"));
            goodsResource.setResourceType("1");
            // 图片来源类型 1：默认白底图 2 amazon 套图
            goodsResource.setSourceType(imgJsonArray.getJSONObject(i).getInteger("sourceType"));
            goodsResource.setIsMain(0);
            goodsResource.setSortNumber(i);
            goodsResource.setGoodsId(listingDTO.getGoodsHeadId());
            goodsResource.setDelFlag("0");
            goodsResourceList.add(goodsResource);
        }
        return goodsResourceList;
    }
    public void handleVideoUrlResource(ListingEditDTO listingDTO, GoodsResource videoUrlResource) {
        if (StrUtil.isBlank(JSONObject.parseObject(listingDTO.getVideoFile()).getString("urls"))) {
            return;
        }
        videoUrlResource.setId(listingDTO.getVideoFileId());
        //新增时会返回json字符串，不做修改只会返回url
        if (StringUtils.isJSON(listingDTO.getVideoFile()) && !"null".equals(listingDTO.getVideoFile())) {
            videoUrlResource.setResourceUrl(JSONObject.parseObject(listingDTO.getVideoFile()).getString("urls"))
                    .setResourceName(JSONObject.parseObject(listingDTO.getVideoFile()).getString("originalFilenames")).
                    setResourceType("0").setSortNumber(1);
        } else {
            videoUrlResource.setResourceUrl(listingDTO.getVideoFile().replace("\"", ""));
        }
    }

    public GoodsResource saveMasterImgUrl(ListingEditDTO listingDTO) {
        //获取主图的地址数据
        GoodsResource masterImgUrlResource = new GoodsResource();
        if (ObjectUtils.isNotEmpty(listingDTO.getMasterImgFile())) {
            handleMasterImgUrlResource(listingDTO, masterImgUrlResource);
            if (ObjectUtils.isNotEmpty(masterImgUrlResource.getId())) {
                goodsResourceService.updateListingGoodsResource(masterImgUrlResource);
            } else {
                //先删主图
                goodsResourceService.deleteListingGoodsResourceByGoodsId(listingDTO.getGoodsHeadId(), 1);
                masterImgUrlResource.setGoodsId(listingDTO.getGoodsHeadId());
                masterImgUrlResource.setDelFlag("0");
                if (StringUtils.isNotBlank(masterImgUrlResource.getResourceUrl())) {
                    goodsResourceService.insertListingGoodsResource(masterImgUrlResource);
                }
            }
        }
        return masterImgUrlResource;
    }

    public void handleMasterImgUrlResource(ListingEditDTO listingDTO, GoodsResource masterImgUrlResource) {
        masterImgUrlResource.setId(listingDTO.getMasterImgFileId());
        //新增时会返回json字符串，不做修改只会返回url
        if (StringUtils.isJSON(listingDTO.getMasterImgFile()) && StringUtils.isNotBlank(listingDTO.getMasterImgFile())) {
            JSONObject jsonObject = JSONObject.parseObject(listingDTO.getMasterImgFile());
            if (jsonObject != null) {
                masterImgUrlResource
                        .setResourceUrl(StringUtils.isNotBlank(jsonObject.getString("urls")) ? jsonObject.getString("urls") : jsonObject.getString("url"))
                        .setResourceName(StringUtils.isNotBlank(jsonObject.getString("originalFilenames")) ? jsonObject.getString("originalFilenames") : jsonObject.getString("originalFilename"))
                        .setResourceType("1")
                        .setSourceType(jsonObject.getInteger("sourceType"))
                        .setIsMain(1)
                        .setSortNumber(1);
                //给ebay主图加上水印
                if (PlatformTypeEnum.EB.name().equals(listingDTO.getPlatform())) {
                    // this.addWatermarking(masterImgUrlResource, listingDTO);
                }
            }
        } else {
            masterImgUrlResource.setResourceUrl(listingDTO.getMasterImgFile().replace("\"", ""));
        }
    }


    /**
     * @description: TODO ebay-商品新增页面到listing--相关信息的入库操作
     * @param: [listingDTO, goodsHeadIdList, goodsResource, goodsDescription, goodsSpecification]
     * @return: void
     * <AUTHOR>
     * @date: 2023/1/13 16:28
     */
    public void eBayFixData(ListingEditDTO listingEditDTO, Integer listingLineId) {
        //处理不同商铺添加不同的数据
        ListingEbayLine listingEbayLine = new ListingEbayLine();
        //EBay商品基础数据
        BeanUtil.copyProperties(listingEditDTO, listingEbayLine);
        //设置行中的刊登天数转换
        listingEbayLine.setSellDay(listingEditDTO.getPublicationDay().split(",")[0]);
        //设置行中的起卖数量转换
        listingEbayLine.setStartSellingCount(listingEditDTO.getStartSellingCount());
        //插入ebay行表信息,需要返回行id
        listingEbayLine.setId(listingLineId);
        ebayGoodsLineService.updateListingEbayLine(listingEbayLine);

        //修改付款方式
        ebayPolicyService.updateListingEbayPolicy(listingEditDTO.getListingEbayPolicy());

        //修改物流方式
        ebayPolicyService.updateListingEbayPolicy(listingEditDTO.getListingEbayPolicy());

        updateTemplateEbayShipping(listingEditDTO.getListingEbayShippingHead());

    }

    public AjaxResult updateTemplateEbayShipping(ListingEbayShippingHead listingEbayShippingHead) {
        listingEbayShippingHead.setUpdateBy(ShiroUtils.getSysUser().getUserName());
        listingEbayShippingHead.setUpdateTime(new Date());

        handleListingEbayShippingHead(listingEbayShippingHead);

        editTemplateEbayShipping(listingEbayShippingHead);
        return AjaxResult.success();
    }

    public void handleListingEbayShippingHead(ListingEbayShippingHead listingEbayShippingHead) {
        //处理物流方式
        List<ListingShippingTypeLine> shippingTypeLineList = listingEbayShippingHead.getShippingTypeLineList();
        if (!CollectionUtils.isEmpty(shippingTypeLineList)) {
            for (ListingShippingTypeLine listingShippingTypeLine : shippingTypeLineList) {
                //运输方式值集转换
                EbayShippingDetailsType ebayShippingDetailsType = ebayShippingDetailsTypeService.getShippingDetailsTypeByShippingService(listingShippingTypeLine.getShippingService());
                listingShippingTypeLine.setShippingServiceId(Convert.toStr(ebayShippingDetailsType.getShippingServiceID()));
                listingShippingTypeLine.setShippingTimeMin(Convert.toStr(ebayShippingDetailsType.getShippingTimeMin()));
                listingShippingTypeLine.setShippingTimeMax(Convert.toStr(ebayShippingDetailsType.getShippingTimeMax()));
                listingShippingTypeLine.setShippingCategory(ebayShippingDetailsType.getShippingCategory());
                //当前默认改种类
                listingShippingTypeLine.setShippingType("Flat:same cost to all buyers");
            }
            listingEbayShippingHead.setShippingTypeLineList(shippingTypeLineList);
        }
        //排除地区
        List<ListingShippingLocationLine> listingShippingLocationLines = new ArrayList<>();
        //处理排除区域
        List<ListingShippingLocationLine> list = getShippingLocationLineList(listingEbayShippingHead.getAfricas(), "africa");
        listingShippingLocationLines.addAll(list);
        //亚洲 Asia
        list = getShippingLocationLineList(listingEbayShippingHead.getAsias(), "Asia");
        listingShippingLocationLines.addAll(list);
        //中美洲和加勒比地区 Central America and Caribbean
        list = getShippingLocationLineList(listingEbayShippingHead.getCentralAmericaAndCaribbeans(), "Central America and Caribbean");
        listingShippingLocationLines.addAll(list);
        //国内位置 Domestic Location
        list = getShippingLocationLineList(listingEbayShippingHead.getDomesticLocations(), "Domestic Location");
        listingShippingLocationLines.addAll(list);
        //欧洲
        list = getShippingLocationLineList(listingEbayShippingHead.getEuropes(), "Europe");
        listingShippingLocationLines.addAll(list);
        //中东
        list = getShippingLocationLineList(listingEbayShippingHead.getMiddleEasts(), "Middle East");
        listingShippingLocationLines.addAll(list);
        //北美洲
        list = getShippingLocationLineList(listingEbayShippingHead.getNorthAmericas(), "North America");
        listingShippingLocationLines.addAll(list);
        //大洋洲
        list = getShippingLocationLineList(listingEbayShippingHead.getOceanias(), "Oceania");
        listingShippingLocationLines.addAll(list);
        //东南亚
        list = getShippingLocationLineList(listingEbayShippingHead.getSoutheastAsias(), "Southeast Asia");
        listingShippingLocationLines.addAll(list);
        //南美洲
        list = getShippingLocationLineList(listingEbayShippingHead.getSouthAmericas(), "South America");
        listingShippingLocationLines.addAll(list);


        //其他位置PO box
        if (listingEbayShippingHead.getPoBox() != null && listingEbayShippingHead.getPoBox().length > 0) {
            ListingShippingLocationLine listingShippingLocationLine = new ListingShippingLocationLine();
            listingShippingLocationLine.setRegion("PO Box");
            listingShippingLocationLine.setLocation("PO Box");
            listingShippingLocationLine.setDescription("PO Box");
            listingShippingLocationLines.add(listingShippingLocationLine);
        }
        listingEbayShippingHead.setShippingLocationLineList(listingShippingLocationLines);
    }

    private void editTemplateEbayShipping(ListingEbayShippingHead listingEbayShippingHead) {
        if (Objects.isNull(listingEbayShippingHead.getId())) {
            return;
        }

        ebayShippingHeadService.updateListingEbayShippingHead(listingEbayShippingHead);
        List<ListingShippingLocationLine> shippingLocationLineList = listingEbayShippingHead.getShippingLocationLineList();
        shippingLocationLineService.deleteByShippingHeadId(listingEbayShippingHead.getId().intValue());
        if (!CollectionUtils.isEmpty(shippingLocationLineList)) {
            for (ListingShippingLocationLine listingShippingLocationLine : shippingLocationLineList) {
                listingShippingLocationLine.setShippingHeadId(listingEbayShippingHead.getId().longValue());
            }
            shippingLocationLineService.insertList(shippingLocationLineList);
        }


        shippingTypeLineService.deleteByShippingHeadId(listingEbayShippingHead.getId().intValue());
        List<ListingShippingTypeLine> shippingTypeLineList = listingEbayShippingHead.getShippingTypeLineList();
        if (!CollectionUtils.isEmpty(shippingTypeLineList)) {
            for (ListingShippingTypeLine listingShippingTypeLine : shippingTypeLineList) {
                listingShippingTypeLine.setShippingHeadId(listingEbayShippingHead.getId().longValue());
            }
            shippingTypeLineService.insertList(shippingTypeLineList);
        }

    }

    /**
     * 获取选中排除地区信息
     *
     * @param strs
     * @param region
     * @return
     */
    private List<ListingShippingLocationLine> getShippingLocationLineList(String[] strs, String region) {
        if (strs == null || strs.length == 0) {
            return new ArrayList<>();
        }
        List<ListingShippingLocationLine> listingShippingLocationLines = new ArrayList<>();
        List<EbayShippingExcludeLocation> ebayShippingExcludeLocationList = null;
        if (Arrays.asList(strs).contains("9999")) {
            ebayShippingExcludeLocationList = ebayShippingExcludeLocationService.getEbayShippingExcludeListByRegion(region);
        } else {
            ebayShippingExcludeLocationList = ebayShippingExcludeLocationService.getEbayShippingExcludeListByRegionAndLocations(region, strs);
        }
        for (EbayShippingExcludeLocation ebayShippingExcludeLocation : ebayShippingExcludeLocationList) {
            ListingShippingLocationLine listingShippingLocationLine = new ListingShippingLocationLine();
            BeanUtil.copyProperties(ebayShippingExcludeLocation, listingShippingLocationLine);
            listingShippingLocationLines.add(listingShippingLocationLine);
        }
        return listingShippingLocationLines;
    }

    /**
     * 复制Listing数据
     *
     * @param dto
     */
    public List<Integer> listingCopy(ListingCopyToShopDTO dto,Boolean isFollowSold) {
        List<Integer> headIdsNew = new ArrayList<>();
        String ids = dto.getIds();
        String shopCode = dto.getShopCode();
        Integer descriptionId = dto.getDescriptionId();
        String brandCode = dto.getBrandCode();
        Long userId = dto.getUserId();
        String userName = dto.getUserName();
        Shop shop = shopService.selectShopByShopCode(shopCode);
        String siteCode = shop.getSiteCode();
        Integer standardPriceType = dto.getStandardPriceType();
        Integer oldPriceType = dto.getOldPriceType();
        String oldPrice = dto.getOldPrice();
        String costPrice = dto.getCostPrice();
        String listPrice = dto.getListPrice();
        String gtin = dto.getGtin();
        Map<String, String> attributeMap = dto.getAttributeMap();
        AtomicReference<Integer> publishType = new AtomicReference<>(dto.getPublishType());
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(Convert.toIntArray(ids));
        if (ObjectUtils.isEmpty(descriptionId)) {
            TemplateEbayDescription descriptionTemplate = templateShopRelationService.selectTemplateDescriptionInfo(shopCode, "description", userName);
            descriptionId = ObjectUtils.isEmpty(descriptionTemplate) ? null : descriptionTemplate.getId().intValue();
        }
        Integer finalDescriptionId = descriptionId;
        goodsHeadList.forEach(goodsHead -> {
            String oldShopCode = goodsHead.getShopCode();
            Integer oldPublishType = goodsHead.getPublishType();
            //不能由VC复制到SC
            if (oldShopCode.startsWith( "VC") && !shopCode.startsWith( "VC")) {
                throw new BusinessException("不能从VC链接复制到SC链接,id:" + goodsHead.getId());
            }
            dto.setItemIdOld(goodsHead.getPlatformGoodsId());
            //不处理 asin为空的listing跟卖
            if (isFollowSold&&ObjUtil.isEmpty(dto.getItemIdOld())){
                    return;
            }
            Integer goodsHeadId = goodsHead.getId();

            if (StringUtils.isNotBlank(shopCode)) {
                goodsHead.setShopCode(shopCode);
            }
            if (!ObjectUtils.isEmpty(publishType)) {
                String standardPrice = ObjectUtils.isEmpty(dto.getStandardPrice()) ? goodsHead.getStandardPrice() : dto.getStandardPrice();
                if (isFollowSold){
                    if (StrUtil.isNotBlank(costPrice) || StrUtil.isNotBlank(listPrice)) {
                        if (shopCode.contains("VC")) {
                            if (StrUtil.isNotBlank(costPrice)) {
                                standardPrice = costPrice;
                            }
                        }else {
                            if (StrUtil.isNotBlank(listPrice)) {
                                standardPrice = listPrice;
                            }
                        }
                    }else {
                        if (standardPriceType != null ) {
                            standardPrice = calculatePrice(goodsHead, standardPriceType, standardPrice);
                        }
                    }


                    //如果操作人跟链接创建人不一致，不允许修改原链接价格
                    if ((Objects.equals(userId,goodsHead.getCreateBy()) ||Objects.equals(userId,1L))
                            &&ObjectUtil.isNotEmpty(oldPrice)&& ObjectUtil.isNotEmpty(oldPriceType)){

                        String updatePrice = calculatePrice(goodsHead, oldPriceType, oldPrice);
                        ListingQuickEditDTO quickEditDTO = new ListingQuickEditDTO();
                        quickEditDTO.setPlatform(PlatformTypeEnum.AM.name());
                        quickEditDTO.setShopCode(shopCode);
                        quickEditDTO.setPublishType(String.valueOf(publishType));
                        quickEditDTO.setStandardPrice(updatePrice);
                        amazonPlatformListingService.quickUpdate(quickEditDTO,goodsHead);
                    }
                }else {
                    if (StrUtil.isNotBlank(costPrice) && shopCode.contains("VC")) {
                        standardPrice = costPrice;
                    }
                }
                goodsHead.setPublishType(publishType.get());
                //处理价格
                goodsHead.setStandardPrice(standardPrice);
                if (isFollowSold) {
                    goodsHead.setSettlementPrice(null);
                }
                //库存
                if (PublishType.CHINESE.getType().equals(publishType)) {
                    goodsHead.setStockOnSalesQty(BigDecimal.valueOf(1));
                }
            }
            goodsHead.setPlatformGoodsId(null);
            //复制 设置适配数据
            String adaptationStatus = checkListingAdaptationBySku(goodsHead.getPdmGoodsCode());
            if (ObjUtil.isNotEmpty(adaptationStatus)){
                goodsHead.setAdaptationStatus(adaptationStatus);
            }
            goodsHead.setCreateBy(String.valueOf(userId));
            goodsHead.setUpdateBy(String.valueOf(userId));
            goodsHead.setCreateTime(DateUtils.getNowDate());
            goodsHead.setOnlineTime(null);
            goodsHead.setOffTime(null);
            goodsHead.setUpdateTime(DateUtils.getNowDate());
            goodsHead.setSmcFlag(shopCode.startsWith("VC")?3:0);
            //统一放到草稿中
            goodsHead.setPublishStatus(PublishStatus.DRAFT.getType());
            if (PlatformTypeEnum.AM.name().equals(goodsHead.getPlatform())) {
                String platformCode = createPlatformCode.getPlatformCodeAync(goodsHead.getPdmGoodsCode(), goodsHead.getSiteCode(), String.valueOf(userId));
                goodsHead.setPlatformGoodsCode(platformCode);
            }
            goodsHead.setSiteCode(Objects.isNull(siteCode) ? goodsHead.getSiteCode() : siteCode);

            // 商品图片视频数据
            List<GoodsResource> goodsResourceList = goodsResourceService.selectListingGoodsResourceByHeadId(goodsHeadId);

            // 商品描述数据
            GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsHeadId);
            if (ObjectUtils.isEmpty(goodsDescription)) {
                throw new BusinessException("商品描述数据为空，无法复制,id:" + goodsHead.getId());
            }

            // 商品规格数据
            GoodsSpecification goodsSpecification = goodsSpecificationService.selectSpecificationListByGoodsId(goodsHeadId);

            //品牌替换
            String oldBrandOld = brandCodeReplace(brandCode, goodsHead, goodsDescription);

            // 复制商品主表数据
            goodsHeadService.insertListingGoodsHead(goodsHead);

            // 复制后的商品主表ID
            Integer copiedHeadId = goodsHead.getId();
            headIdsNew.add(copiedHeadId);

            // 复制商品图片视频数据
            if (CollUtil.isNotEmpty(goodsResourceList)) {
                goodsResourceService.insertListingGoodsResourceBatch(goodsResourceList.stream().map(goodsResource -> {
                    goodsResource.setGoodsId(copiedHeadId);
                    return goodsResource;
                }).collect(Collectors.toList()));
            }

            // 复制商品描述数据
            goodsDescription.setGoodsId(copiedHeadId);
            goodsDescription.setDescriptionId(finalDescriptionId);
            Boolean isClean = true;
            if (ObjectUtils.isNotEmpty(finalDescriptionId) && !ObjUtil.equals("0", String.valueOf(finalDescriptionId))) {
                StringBuffer descBuffer = new StringBuffer();
                isClean = templateEbayDescriptionBiz.cleaningShortDescription(goodsDescription.getDetailDescription(), goodsDescription.getShortDescription(), descBuffer);
                goodsDescription.setShortDescription(String.valueOf(descBuffer));
                goodsDescription.setDetailDescription(null);
            }
            if (ObjUtil.equals("0", String.valueOf(finalDescriptionId))) {
                goodsDescription.setDescriptionId(null);
                goodsDescription.setDetailDescription(ObjectUtils.isEmpty(goodsDescription.getDetailDescription())
                        ? goodsDescription.getShortDescription() : goodsDescription.getDetailDescription());
                goodsDescription.setShortDescription(null);
            }

            // 复制商品规格数据
            goodsSpecification.setGoodsId(copiedHeadId);
            goodsSpecificationService.insertListingGoodsSpecification(goodsSpecification);

            if (PlatformTypeEnum.EB.name().equals(goodsHead.getPlatform())) {
                Integer lineId = copyEbayData(goodsHead, goodsHeadId, copiedHeadId, dto);
                // 处理店铺对应的模板数据
                goodsInfoBiz.saveTemplateInfo(userId, copiedHeadId, lineId, goodsDescription);
            } else {
                goodsDescriptionService.insertListingGoodsDescription(goodsDescription);
                copyAmazonData(goodsHeadId, copiedHeadId, goodsHead, oldBrandOld, String.valueOf(userId),isFollowSold,dto.getItemIdOld(),
                        listPrice, oldShopCode, oldPublishType,gtin,attributeMap);
            }

            if (!isClean) {
                listingLogService.insertSuccessListingLog((isFollowSold?"跟卖":"复制" )+"原主键Id为：[" + goodsHeadId + "],富文本描述清洗失败.", String.valueOf(userId), Integer.valueOf(copiedHeadId));
            }
            listingLogService.insertSuccessListingLog((isFollowSold?"跟卖":"复制" )+"[" + goodsHeadId + "]新建listing草稿成功", String.valueOf(userId), Integer.valueOf(copiedHeadId));

        });
        //处理库存黑名单
        if (ObjUtil.isNotEmpty(headIdsNew)) {
            inventoryExcludeBiz.addListingBySkuBlack(shop.getPlatformCode(), shop.getSiteCode(), shop.getShopCode(), headIdsNew, userId);
        }
        return headIdsNew;
    }

    /**
     * 根据页面计算当前售价
     * @param goodsHead
     * @param standardPriceType
     * @param standardPrice
     * @return
     */
    private static String calculatePrice(GoodsHead goodsHead, Integer standardPriceType, String standardPrice) {
        switch (standardPriceType){
            //固定值
            case 1:
                break;
                //加
            case 2:
                standardPrice = String.valueOf(new BigDecimal(standardPrice).add(new BigDecimal(goodsHead.getStandardPrice())));
                break;
                //减
            case 3:
                standardPrice = String.valueOf(new BigDecimal(standardPrice).subtract(new BigDecimal(goodsHead.getStandardPrice())));
                break;
                //乘
            case 4:
                standardPrice = String.valueOf(new BigDecimal(standardPrice).multiply(new BigDecimal(goodsHead.getStandardPrice())));
                break;
                //除
            case 5:
                standardPrice = String.valueOf(new BigDecimal(standardPrice).divide(new BigDecimal(goodsHead.getStandardPrice()), 2, RoundingMode.HALF_UP));
            default:
                break;
        }
        return standardPrice;
    }


    private String brandCodeReplace(String brandCode, GoodsHead goodsHead, GoodsDescription goodsDescription) {
        String brandCodeOld = goodsHead.getBrandCode();
        if (StrUtil.isBlank(brandCode) || StrUtil.isBlank(brandCodeOld)) {
            return brandCodeOld;
        }
        //品牌编码
        goodsHead.setBrandCode(brandCode);
        //标题
        goodsHead.setTitle(getNewBrandCodeInfo(goodsHead.getTitle(), brandCode, brandCodeOld));

        //描述
        goodsDescription.setItemDescription1(getNewBrandCodeInfo(goodsDescription.getItemDescription1(), brandCode, brandCodeOld));
        goodsDescription.setItemDescription2(getNewBrandCodeInfo(goodsDescription.getItemDescription2(), brandCode, brandCodeOld));
        goodsDescription.setItemDescription3(getNewBrandCodeInfo(goodsDescription.getItemDescription3(), brandCode, brandCodeOld));
        goodsDescription.setItemDescription4(getNewBrandCodeInfo(goodsDescription.getItemDescription4(), brandCode, brandCodeOld));
        goodsDescription.setItemDescription5(getNewBrandCodeInfo(goodsDescription.getItemDescription5(), brandCode, brandCodeOld));
        goodsDescription.setDetailDescription(getNewBrandCodeInfo(goodsDescription.getDetailDescription(), brandCode, brandCodeOld));
        return brandCodeOld;
    }


    private void copyAmazonData(Integer goodsHeadId, Integer copiedHeadId, GoodsHead goodsHead, String oldBrandOld,
                                String userId, Boolean isFollowSold, String oldItemId, String listPrice, String oldShopCode,
                                Integer oldPublishType,String gtin,Map<String,String> attributeMap) {
        //copy旧版属性
        copyToLineReturnPn(goodsHeadId, copiedHeadId, goodsHead, oldBrandOld, userId, isFollowSold, listPrice);

        //copy新版属性
        List<ListingAmazonAttributeLineV2> listingAmazonAttributeLineV2s = listingAmazonAttributeLineV2Service.listByGoodsId(goodsHeadId);
        String type = goodsHead.getShopCode().contains("VC") ? "VC" : "SC";

        String productType = copyToLineV2(copiedHeadId, goodsHead, oldBrandOld, userId, isFollowSold, oldItemId, listingAmazonAttributeLineV2s, type, oldShopCode, oldPublishType);
        // 补全属性，将属性值为空的属性从品类配置获取默认值进行补全
        amazonProductBiz.complementAttribute(copiedHeadId, type);


        //增加跟卖属性
        if(isFollowSold){
            addFollowAttribute(copiedHeadId, goodsHead, oldItemId, type, productType, gtin);
        }else {
            if (Objects.equals(type, "VC") && StrUtil.isNotEmpty(gtin)) {
                saveExternalAttributeKey(copiedHeadId, goodsHead, productType,"gtin",4);
                saveExternalAttributeValue(copiedHeadId, goodsHead, productType,gtin,4);
            }
        }


        List<ListingAmazonAttributeLineV2> listingAmazonAttributeLineV2new = listingAmazonAttributeLineV2Service.listByGoodsId(copiedHeadId);

        //赋值
        if (ObjUtil.isNotEmpty(attributeMap)) {
            for (String attKey : attributeMap.keySet()) {
                String value = attributeMap.get(attKey);
                if (StrUtil.isBlank(value)){
                    continue;
                }
                ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2 = listingAmazonAttributeLineV2new.stream().filter(f -> f.getPropNodePath().equals(attKey)).findFirst().orElse(null);
                if (ObjUtil.isNotEmpty(listingAmazonAttributeLineV2)){
                    listingAmazonAttributeLineV2.setTableValue(value);
                    listingAmazonAttributeLineV2Service.updateListingAmazonAttributeLineV2(listingAmazonAttributeLineV2);
                    continue;
                }
                ListingAmazonAttributeLineV2 kV2 = new ListingAmazonAttributeLineV2();
                kV2.setHeadId(Long.valueOf(copiedHeadId));
                kV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
                kV2.setProductType(productType);
                kV2.setCategoryId(goodsHead.getCategoryId());
                kV2.setPropNodePath(attKey);
                kV2.setTableValue(value);
                kV2.setTableName("");
                kV2.setTableType(0);
                kV2.setVcFlag(Objects.equals(type, "VC") ? "Y" : "N");
                kV2.setCreateBy(goodsHead.getCreateBy());
                kV2.setCreateTime(DateUtils.getNowDate());
                listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(kV2);
            }
        }

        if (StrUtil.isNotEmpty(listPrice)) {
            ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2 = listingAmazonAttributeLineV2new.stream().filter(f -> "list_price.value".equals(f.getPropNodePath())).findFirst().orElse(null);
            if (ObjUtil.isNotEmpty(listingAmazonAttributeLineV2)){
                listingAmazonAttributeLineV2.setTableValue(listPrice);
                listingAmazonAttributeLineV2Service.updateListingAmazonAttributeLineV2(listingAmazonAttributeLineV2);
            } else {
                ListingAmazonAttributeLineV2 kV2 = new ListingAmazonAttributeLineV2();
                kV2.setHeadId(Long.valueOf(copiedHeadId));
                kV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
                kV2.setProductType(productType);
                kV2.setCategoryId(goodsHead.getCategoryId());
                kV2.setPropNodePath("list_price.value");
                kV2.setTableValue(listPrice);
                kV2.setTableName("");
                kV2.setTableType(4);
                kV2.setVcFlag(Objects.equals(type, "VC") ? "Y" : "N");
                kV2.setCreateBy(goodsHead.getCreateBy());
                kV2.setCreateTime(DateUtils.getNowDate());
                listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(kV2);
            }
            // 单位
            ListingAmazonAttributeLineV2 listingAmazonAttributeLineV2Unit = listingAmazonAttributeLineV2new.stream().filter(f -> "list_price.currency".equals(f.getPropNodePath())).findFirst().orElse(null);
            if(ObjUtil.isEmpty(listingAmazonAttributeLineV2Unit)){
                ListingAmazonAttributeLineV2 kV2 = new ListingAmazonAttributeLineV2();
                kV2.setHeadId(Long.valueOf(copiedHeadId));
                kV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
                kV2.setProductType(productType);
                kV2.setCategoryId(goodsHead.getCategoryId());
                kV2.setPropNodePath("list_price.currency");
                kV2.setTableValue("USD");
                kV2.setTableName("");
                kV2.setTableType(4);
                kV2.setVcFlag(Objects.equals(type, "VC") ? "Y" : "N");
                kV2.setCreateBy(goodsHead.getCreateBy());
                kV2.setCreateTime(DateUtils.getNowDate());
                listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(kV2);
            }
        }
    }

    /**
     * 增加跟卖特有属性
     *
     * @param copiedHeadId
     * @param goodsHead
     * @param oldItemId
     * @param type
     * @param productType
     * @param gtin
     */
    private void addFollowAttribute(Integer copiedHeadId, GoodsHead goodsHead, String oldItemId, String type, String productType, String gtin) {
        ListingAmazonAttributeLine k = new ListingAmazonAttributeLine();
        k.setGoodsId(copiedHeadId);
        k.setCategoryId(goodsHead.getCategoryId());
        k.setTableType(4);
        k.setTableName("external_product_id_type");
        k.setTableValue("ASIN");
        k.setCreateBy(goodsHead.getCreateBy());
        k.setCreateTime(DateUtils.getNowDate());
        listingAmazonAttributeLineService.insertListingAmazonAttributeLine(k);

        ListingAmazonAttributeLine v = new ListingAmazonAttributeLine();
        v.setGoodsId(copiedHeadId);
        v.setCategoryId(goodsHead.getCategoryId());
        v.setTableType(4);
        v.setTableName("external_product_id");
        v.setTableValue(oldItemId);
        v.setCreateBy(goodsHead.getCreateBy());
        v.setCreateTime(DateUtils.getNowDate());
        listingAmazonAttributeLineService.insertListingAmazonAttributeLine(v);

        saveExternalAttributeKey(copiedHeadId, goodsHead, productType, "ASIN", 5);
        saveExternalAttributeValue(copiedHeadId, goodsHead, productType, oldItemId, 5);

        if (Objects.equals(type, "VC") && StrUtil.isNotEmpty(gtin)) {
            saveExternalAttributeKey(copiedHeadId, goodsHead, productType,"gtin",4);
            saveExternalAttributeValue(copiedHeadId, goodsHead, productType,gtin,4);
        }
    }

    private void saveExternalAttributeKey(Integer copiedHeadId, GoodsHead goodsHead, String productType,String tableValue,Integer tableType) {
        String vcFlag = goodsHead.getShopCode().contains("VC") ? "Y":"N";
        ListingAmazonAttributeLineV2 kV2 = new ListingAmazonAttributeLineV2();
        kV2.setHeadId(Long.valueOf(copiedHeadId));
        kV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        kV2.setProductType(productType);
        kV2.setCategoryId(goodsHead.getCategoryId());
        kV2.setPropNodePath(EXTERNAL_PRODUCT_ID_TYPE.getInfoV2(vcFlag));
        kV2.setTableValue(tableValue);
        kV2.setTableName("External Product ID");
        kV2.setTableType(tableType);
        kV2.setCreateBy(goodsHead.getCreateBy());
        kV2.setCreateTime(DateUtils.getNowDate());
        kV2.setVcFlag(vcFlag);
        listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(kV2);
    }

    private void saveExternalAttributeValue(Integer copiedHeadId, GoodsHead goodsHead, String productType,String tableValue,Integer tableType) {
        String vcFlag = goodsHead.getShopCode().contains("VC") ? "Y":"N";
        ListingAmazonAttributeLineV2 kV2 = new ListingAmazonAttributeLineV2();
        kV2.setHeadId(Long.valueOf(copiedHeadId));
        kV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        kV2.setProductType(productType);
        kV2.setCategoryId(goodsHead.getCategoryId());
        kV2.setPropNodePath(EXTERNAL_PRODUCT_ID.getInfoV2(vcFlag));
        kV2.setTableValue(tableValue);
        kV2.setTableName("External Product ID");
        kV2.setTableType(tableType);
        kV2.setVcFlag(vcFlag);
        kV2.setCreateBy(goodsHead.getCreateBy());
        kV2.setCreateTime(DateUtils.getNowDate());
        listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(kV2);
    }


    private String copyToLineV2(Integer copiedHeadId, GoodsHead goodsHead, String oldBrandOld, String userId, Boolean isFollowSold, String oldItemId,
                                List<ListingAmazonAttributeLineV2> listingAmazonAttributeLineV2s, String type, String oldShopCode, Integer oldPublishType) {

        String productType = "";
        PlatformCategory category = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
        List<AmCategoryTemplateField> amCategoryTemplateFields = amCategoryTemplateFieldService.selectAmAllCategoryTemplateFieldList(category.getProductType(), goodsHead.getSiteCode(), "VC".equals(type) ? Constants.YesOrNo.YES : Constants.YesOrNo.NO);
        List<String> propNodePaths = amCategoryTemplateFields.stream().map(AmCategoryTemplateField::getPropNodePath).distinct().collect(Collectors.toList());


        List<AmCategoryTemplatePrivateValue> privateValues = amCategoryTemplatePrivateValueService.list(goodsHead.getSiteCode(), goodsHead.getCategoryId() +"", goodsHead.getShopCode(),
                category.getProductType(), Objects.equals(type, "VC") ? Constants.YesOrNo.YES : Constants.YesOrNo.NO, userId, Arrays.asList("item_type_keyword.value"));
        Map<String, String> privateValueMap = privateValues.stream().collect(Collectors.toMap(AmCategoryTemplatePrivateValue::getPropNodePath, AmCategoryTemplatePrivateValue::getValue));

        //如果是vc 并且 属性为空，则从接口实时获取一下
        if (CollUtil.isEmpty(listingAmazonAttributeLineV2s) && Objects.equals(type, "VC")) {
            try {
                if(StrUtil.isBlank(oldItemId) && !isFollowSold){
                    PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
                    productType = platformCategory.getProductType();
                    createPartNumber(copiedHeadId, goodsHead, userId, productType);
                    return productType;
                }
                AjaxResult detailResult = amazonApiHttpRequestBiz.getBackendDetailResult(oldShopCode, oldPublishType, oldItemId, null);
                if (!detailResult.isSuccess()) {
                    throw new BusinessException(String.valueOf(detailResult.get(AjaxResult.MSG_TAG)));
                }
                JSONObject data = JSONObject.parseObject(JSON.toJSONString(detailResult.get(AjaxResult.DATA_TAG)));
                JSONArray items = data.getJSONArray("items");
                if (CollUtil.isEmpty(items)) {
                    throw new BusinessException("复制失败，未获取到原链接属性, 主键ID为：" + copiedHeadId);
                }
                JSONObject itemInfo = items.getJSONObject(0);
                JSONObject attributes = amazonPlatformListingService.getAttributes(itemInfo);
                String partNumber = attributes.containsKey("part_number") ? attributes.getJSONArray("part_number").getJSONObject(0).getString("value") : "";

                listingAmazonAttributeLineV2s = amazonProductBiz.getAmAttributeLinesV2(attributes, goodsHead);

                if (CollUtil.isNotEmpty(listingAmazonAttributeLineV2s)) {
                    listingAmazonAttributeLineV2s = listingAmazonAttributeLineV2s.stream().filter(x -> !"part_number.value".equals(x.getPropNodePath())).collect(Collectors.toList());

                    productType = listingAmazonAttributeLineV2s.get(0).getProductType();
                    // part_number
                    ListingAmazonAttributeLineV2 attributeLine = new ListingAmazonAttributeLineV2();
                    attributeLine.setHeadId(Long.valueOf(copiedHeadId));
                    attributeLine.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
                    attributeLine.setCreateBy(Convert.toStr(userId));
                    attributeLine.setCategoryId(goodsHead.getCategoryId());
                    attributeLine.setProductType(productType);
                    attributeLine.setPropNodePath("part_number.value");
                    if (isFollowSold) {
                        attributeLine.setTableValue(partNumber);
                    } else {
                        String platformCode = createPlatformCode.getPlatformCodeAync(goodsHead.getPdmGoodsCode(), goodsHead.getSiteCode(), String.valueOf(userId));
                        attributeLine.setTableValue(platformCode);
                    }
                    attributeLine.setTableType(0);
                    attributeLine.setCreateTime(new Date());
                    listingAmazonAttributeLineV2s.add(attributeLine);
                }
            }catch (Exception e){
                log.error("复制Amazon商品信息失败",e);
                if (isFollowSold) {
                    throw e;
                }
            }
        }

        //productType 为空时，从分类中获取
        if (StrUtil.isEmpty(productType)) {
            PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
            productType = platformCategory.getProductType();
        }
        if (CollUtil.isEmpty(listingAmazonAttributeLineV2s)) {
            if (!isFollowSold) {
                createPartNumber(copiedHeadId, goodsHead, userId, productType);
            }
            return productType;
        }

        //保存属性入库
        boolean pnExist=false;
        for (ListingAmazonAttributeLineV2 attributeLineV2 : listingAmazonAttributeLineV2s) {
            if(!isFollowSold ) {
                if(containsCanNotCopyField(attributeLineV2.getPropNodePath())){
                    continue;
                }
            }
            if (!propNodePaths.contains(attributeLineV2.getPropNodePath())) {
                continue;
            }
            attributeLineV2.setId(null);
            attributeLineV2.setHeadId(Long.valueOf(copiedHeadId));
            if (privateValueMap.containsKey(attributeLineV2.getPropNodePath())) {
                attributeLineV2.setTableValue(privateValueMap.get(attributeLineV2.getPropNodePath()));
            }else {
                attributeLineV2.setTableValue(getNewBrandCodeInfo(attributeLineV2.getTableValue(), goodsHead.getBrandCode(), oldBrandOld));
            }
            attributeLineV2.setCreateBy(goodsHead.getCreateBy());
            attributeLineV2.setVcFlag(Objects.equals(type, "VC") ? "Y" : "N");
            if (Objects.equals(attributeLineV2.getPropNodePath(), "part_number.value")) {
                pnExist = true;
                if (!isFollowSold) {
                    String platformCode = createPlatformCode.getPlatformCodeAync(goodsHead.getPdmGoodsCode(), goodsHead.getSiteCode(), String.valueOf(userId));
                    attributeLineV2.setTableValue(platformCode);
                }
            }
            productType = attributeLineV2.getProductType();

            listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(attributeLineV2);
        }

        if (!pnExist && StrUtil.isNotEmpty(productType)){
            createPartNumber(copiedHeadId, goodsHead, userId, productType);
        }

        return productType;
    }

    private boolean containsCanNotCopyField(String propNodePath) {
        List<String> canNotCopyField = Arrays.asList("merchant_suggested_asin","external_product_id_type","external_product_id", "externally_assigned_product_identifier", "externally_assigned_product_identifier.type", "externally_assigned_product_identifier.value");
        return canNotCopyField.stream().anyMatch(propNodePath::contains);
    }

    private void createPartNumber(Integer copiedHeadId, GoodsHead goodsHead, String userId, String productType) {
        String type = goodsHead.getShopCode().contains("VC") ? "VC" : "SC";
        ListingAmazonAttributeLineV2 attributeLine = new ListingAmazonAttributeLineV2();
        attributeLine.setHeadId(Long.valueOf(copiedHeadId));
        attributeLine.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        attributeLine.setCreateBy(Convert.toStr(userId));
        attributeLine.setCategoryId(goodsHead.getCategoryId());
        attributeLine.setProductType(productType);
        attributeLine.setPropNodePath("part_number.value");
        String platformCode = createPlatformCode.getPlatformCodeAync(goodsHead.getPdmGoodsCode(), goodsHead.getSiteCode(), String.valueOf(userId));
        attributeLine.setTableValue(platformCode);
        attributeLine.setTableType(0);
        attributeLine.setCreateTime(new Date());
        attributeLine.setVcFlag(Objects.equals(type, "VC") ? "Y" : "N");
        listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(attributeLine);
    }

    private void copyToLineReturnPn(Integer goodsHeadId, Integer copiedHeadId, GoodsHead goodsHead, String oldBrandOld, String userId, Boolean isFollowSold, String listPrice) {
        List<ListingAmazonAttributeLine> lines = listingAmazonAttributeLineService.selectByGoodsId(goodsHeadId);
        if (CollectionUtils.isEmpty(lines)){
            return;
        }
        for (ListingAmazonAttributeLine listingAmazonAttributeLine : lines) {
            listingAmazonAttributeLine.setGoodsId(copiedHeadId);
            //品牌替换
            listingAmazonAttributeLine.setTableValue(getNewBrandCodeInfo(listingAmazonAttributeLine.getTableValue(), goodsHead.getBrandCode(), oldBrandOld));
            if (Objects.equals(listingAmazonAttributeLine.getTableName(), "part_number")&& !isFollowSold) {
                String platformCode = createPlatformCode.getPlatformCodeAync(goodsHead.getPdmGoodsCode(), goodsHead.getSiteCode(), userId);
                listingAmazonAttributeLine.setTableValue(platformCode);
            }
            if(Objects.equals(listingAmazonAttributeLine.getTableName(), "list_price") && StrUtil.isNotBlank(listPrice)){
                listingAmazonAttributeLine.setTableValue(listPrice);
            }
            listingAmazonAttributeLine.setCreateBy(goodsHead.getCreateBy());
            listingAmazonAttributeLineService.insertListingAmazonAttributeLine(listingAmazonAttributeLine);
        }
    }

    private Integer copyEbayData(GoodsHead goodsHead, Integer goodsHeadId, Integer copiedHeadId, ListingCopyToShopDTO dto) {

        //ebay商品独有的行表信息数据，根据头id查询行的信息
        ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHeadId);
        Integer ebayLineId = listingEbayLine.getId();

        //根据行表id查询出原有属性表中的数据
        ListingEbayValue listingEbayValue = new ListingEbayValue();
        listingEbayValue.setListingLineId(ebayLineId);
        List<ListingEbayValue> listingEbayValueList = ebayValueService.selectListingEbayValueList(listingEbayValue);

        //处理适配表数据
        List<ListingEbayAdaptive> listingEbayAdaptiveList = adaptiveService.selectListByEbayLineId(ebayLineId);

        //listing没有适配 从ads取
        if (CollectionUtils.isEmpty(listingEbayAdaptiveList)) {
            listingEbayAdaptiveList.addAll(listingEbayAdaptiveService.handleEbayAdaptive(dto.getItemIdOld(), goodsHead.getShopCode()));
        }
        //设置物品所在地
        TemplateEbayShippingHead templateEbayShippingHead = new TemplateEbayShippingHead();
        templateEbayShippingHead.setShopCodes(new String[]{goodsHead.getShopCode()});
        List<TemplateEbayShippingHead> shippingHeadList = templateEbayShippingHeadService.selectTemplateEbayShippingHeadList(templateEbayShippingHead);
        if (ObjectUtils.isNotEmpty(shippingHeadList)) {
            listingEbayLine.setLocation(shippingHeadList.get(0).getLocation());
        }
        //复制ebay独有信息行表的数据
        listingEbayLine.setCountry(goodsHead.getSiteCode());
        listingEbayLine.setCurrency(PlatformSiteEnum.getCurrency("EB", goodsHead.getSiteCode()));
        listingEbayLine.setListingHeadId(copiedHeadId);
        listingEbayLine.setFirstCategoryid(null);
        //复制时清空UUID，避免与原链接冲突
        listingEbayLine.setUuid(null);
        //处理复制的数据
        if (!ObjectUtils.isEmpty(dto.getPublishType())) {
            handleCopyLine(dto, listingEbayLine, goodsHead);
        }
        //复制了行数据后，产生新的行id
        ebayGoodsLineService.insertListingEbayLine(listingEbayLine);
        Integer copyLineId = listingEbayLine.getId();

        //复制对应ebay属性表信息数据
        if (CollUtil.isNotEmpty(listingEbayValueList)) {
            List<ListingEbayValue> ebayValues = listingEbayValueList.stream()
                    .map(p -> {
                        if (StrUtil.isBlank(p.getName()) || StrUtil.isBlank(p.getValue())) {
                            return null;
                        }
                        p.setListingLineId(copyLineId);
                        if (ObjUtil.equals(p.getName().toUpperCase(), "UPC") || ObjUtil.equals(p.getName().toUpperCase(), "EAN")) {
                            p.setValue("Does not apply");
                        }
                        return p;
                    })
                    .filter(f -> ObjUtil.isNotEmpty(f))
                    .collect(Collectors.toList());
            ebayValueService.batchInsertListingEbayValue(ebayValues);
        }
        //复制对应ebay适配表信息数据
        if (!CollectionUtils.isEmpty(listingEbayAdaptiveList)) {
            List<ListingEbayAdaptive> adaptives = listingEbayAdaptiveList.stream().map(p -> {
                p.setListingLineId(copyLineId);
                return p;
            }).collect(Collectors.toList());
            //adaptives 切片插入 1000条一次
            adaptiveService.deleteListingEbayAdaptiveByLineId(copyLineId);
            List<List<ListingEbayAdaptive>> partition = Lists.partition(adaptives, 500);
            partition.forEach(listingEbayAdaptives -> {
                adaptiveService.batchInsertListingEbayAdaptive(listingEbayAdaptives);
            });
            GoodsHead head = goodsHeadService.selectListingGoodsHeadById(copiedHeadId);
            head.setAdaptationStatus(AdaptationStatusEnum.WAIT.getStatus());
            goodsHeadService.updateListingGoodsHead(head);
        }
        return listingEbayLine.getId();
    }

    private void handleCopyLine(ListingCopyToShopDTO dto, ListingEbayLine listingEbayLine, GoodsHead goodsHead) {
        listingEbayLine.setSellDay(ObjectUtils.isEmpty(dto.getPublicationDay()) ? "GTC" :
                dto.getPublicationDay().contains(",")
                        ? dto.getPublicationDay().split(",")[0] : dto.getPublicationDay());
        listingEbayLine.setReservePrice(StrUtil.isEmpty(dto.getReservePrice()) ? listingEbayLine.getReservePrice() : dto.getReservePrice());
        listingEbayLine.setStartSellingCount(ObjectUtils.isEmpty(dto.getStartSellingCount()) ? "1" : dto.getStartSellingCount().contains(",")
                ? dto.getStartSellingCount().split(",")[0] : dto.getStartSellingCount());
        listingEbayLine.setBestOfferFlag(ObjectUtils.isEmpty(dto.getBestOfferFlag()) ? "0" : String.valueOf(dto.getBestOfferFlag()));
    }


    /**
     * 根据实际库存范围筛选
     *
     * @param listingDTO
     * @param goodsHeadParam
     * @return
     */
    public boolean getGoodHeadParamByActulStockScoop(ListingDTO listingDTO, GoodsHead goodsHeadParam) {
        BigDecimal actualStockOnSalesQtyStart = listingDTO.getActualStockOnSalesQtyStart();
        BigDecimal actualStockOnSalesQtyEnd = listingDTO.getActualStockOnSalesQtyEnd();
        String pdmGoodsCode = goodsHeadParam.getPdmGoodsCode();
        String siteCode=ObjectUtils.isEmpty(listingDTO.getSiteCode())?"US":listingDTO.getSiteCode();
        if (ObjectUtils.isNotEmpty(actualStockOnSalesQtyStart) || ObjectUtils.isNotEmpty(actualStockOnSalesQtyEnd)) {
            List<String> specialSku = getSpeciaSku();

            Boolean isVc = PublishType.isVC(goodsHeadParam.getPublishType());

            List<ThirdpartyFbmDTO> specialSkuThirdpartyFbmDTOList = thirdpartyInventoryBiz.selectStockShareAndPartGoodsCode(specialSku, siteCode, isVc);
            //过滤specialSkuThirdpartyFbmDTOList中sellableQty大于等于actualStockOnSalesQtyStart 小于等于 actualStockOnSalesQtyEnd的sku
            List<String> specialSkuList = specialSkuThirdpartyFbmDTOList.stream().filter(e -> {
                if (ObjectUtils.isNotEmpty(actualStockOnSalesQtyStart) && ObjectUtils.isNotEmpty(actualStockOnSalesQtyEnd)) {
                    return e.getSellableQty() >= actualStockOnSalesQtyStart.intValue() && e.getSellableQty() <= actualStockOnSalesQtyEnd.intValue();
                }
                if (ObjectUtils.isNotEmpty(actualStockOnSalesQtyStart)) {
                    return e.getSellableQty() >= actualStockOnSalesQtyStart.intValue();
                }
                if (ObjectUtils.isNotEmpty(actualStockOnSalesQtyEnd)) {
                    return e.getSellableQty() <= actualStockOnSalesQtyEnd.intValue();
                }
                return false;
            }).map(ThirdpartyFbmDTO::getSku).collect(Collectors.toList());

            //通过接口获取在库存范围的sku,此处因为库存api只是实物库存,所以筛出来的sku会不准确,所以需要再次筛选
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = thirdpartyInventoryBiz.selectStockByStockScope(
                    ObjectUtils.isNotEmpty(actualStockOnSalesQtyStart) ? actualStockOnSalesQtyStart.intValue() : 0,
                    ObjectUtils.isNotEmpty(actualStockOnSalesQtyEnd) ? actualStockOnSalesQtyEnd.intValue() : null,
                    siteCode, WarehouseExcludeEnum.getExcludeWarehouseCodeList(), isVc);

            //thirdpartyFbmDTOList中的sku与specialSku去差集
            List<String> skuList = thirdpartyFbmDTOList.stream().map(ThirdpartyFbmDTO::getSku).collect(Collectors.toList());
            skuList.removeAll(specialSku);
            skuList.addAll(specialSkuList);
            if (ObjectUtils.isEmpty(skuList)) {
                //没有匹配到范围内的合适库存,也不需要查数据库直接返回
                return false;
            }
            if (ObjectUtils.isEmpty(pdmGoodsCode)) {
                String skuStr = String.join(" ", skuList);
                goodsHeadParam.setPdmGoodsCode(skuStr);
            } else {
                //把pdmGoodsCode按照 分割成list
                List<String> pdmGoodsCodeList = new ArrayList<>(Arrays.asList(pdmGoodsCode.split(" ")));
                //pdmGoodsCodeList 与skuList 取交集
                pdmGoodsCodeList.retainAll(skuList);
                if (CollectionUtils.isEmpty(pdmGoodsCodeList)) {
                    //没有匹配到范围内的合适库存,也不需要查数据库直接返回
                    return false;
                } else {
                    String skuStr = String.join(" ", pdmGoodsCodeList);
                    goodsHeadParam.setPdmGoodsCode(skuStr);
                }
            }
        }
        return true;
    }

    public List<String> getSpeciaSku() {
        String redisKey="SMC:SPECIALSKU";
        if (redisService.exists(redisKey)) {
            return JSON.parseArray(redisService.getCacheObject(redisKey), String.class);
        }
        //获取所有的虚拟套件父sku和主商品编码
        List<String> allMainGoodsCodes = pdmHttpRequestBiz.getAllMainGoodsCodes();
        List<String> allParentPartList = pdmHttpRequestBiz.getAllParentPartList();
        allParentPartList.addAll(allMainGoodsCodes);
        List<String> specialSku = allParentPartList.stream().distinct().collect(Collectors.toList());
        redisService.setCacheObject(redisKey, JSON.toJSONString(specialSku), 1L, TimeUnit.DAYS);
        return specialSku;
    }


    @Deprecated
    public Map<String, List<GoodsRedLinePrice>> getRedLinePriceBySiteSku(List<GoodsHeadVO> goodsHeadVOList) {
        Map<String, List<String>> sitePdmGoodsCodeMap = goodsHeadVOList.stream().collect(Collectors.groupingBy(GoodsHeadVO::getSiteCode, Collectors.mapping(GoodsHeadVO::getPdmGoodsCode, Collectors.toList())));
        List<GoodsRedLinePrice> goodsRedLinePriceList = new ArrayList<>();
        sitePdmGoodsCodeMap.forEach((siteCode, pdmGoodsCodes) -> {
            GoodsRedLinePrice query = new GoodsRedLinePrice();
            query.setGoodsCodeList(pdmGoodsCodes);
            query.setSite(siteCode);
            goodsRedLinePriceList.addAll(goodsRedLinePriceService.selectGoodsRedLinePriceList(query));
        });
        Map<String, List<GoodsRedLinePrice>> priceBySiteSku = new HashMap<>();
        if (CollUtil.isNotEmpty(goodsRedLinePriceList)) {
            priceBySiteSku = goodsRedLinePriceList.stream().collect(Collectors.groupingBy(e -> e.getSite() + "_" + e.getGoodsCode()));
        }
        return priceBySiteSku;
    }


    public void handleExport(List<GoodsHeadVO> goodsHeadVOList, ListingDTO listingDTO) {
        //获取所有人员
        List<KeyValueEntity> sysUserNameAllKVList = sysBaseConfig.getSysUserNameAllKVList();
        Map<String, String> userNameMap = sysUserNameAllKVList.stream().collect(Collectors.toMap(KeyValueEntity::getKey, KeyValueEntity::getValue));
        userNameMap.put("-1", "system");

        // 获取属性 组成map(goodsId, List<name:value>)
        Map<String, List<String>> attributeMap = getAttributeMap(goodsHeadVOList);

        //在noSelect 去除 多余属性字段
        setAttributeMaxColumn(listingDTO, attributeMap);

        //ebay店铺分类
        setEbayShopCategory(goodsHeadVOList);

        //处理销量、订单量
        handleSaleAndOrder(goodsHeadVOList);

        //类目cache
        Map<Integer, String> categoryCache = new HashMap<>();
        //获取图片
        Map<Integer, List<String>> goodsResourceMap = getGoodsResourceMap(goodsHeadVOList);

        for (GoodsHeadVO goodsHeadVO : goodsHeadVOList) {
            if("AM".equals(goodsHeadVO.getPlatform())) {
                List<ListingAmazonAttributeLine> attributeLines = listingAmazonAttributeLineService.selectByGoodsIdAndType(goodsHeadVO.getId(), 4);
                if (!CollectionUtils.isEmpty(attributeLines)) {
                    attributeLines.stream().filter(f -> Objects.equals(f.getTableName(), AmazonAttributeEnum.FULFILLMENT_LATENCY.getInfo())).forEach(s -> {
                        goodsHeadVO.setHandlingTime(s.getTableValue());
                    });
                }
            }
            if(goodsHeadVO.getShopCode().contains("VC")) {
                String standardPrice = goodsHeadVO.getStandardPrice();
                // 当前售价使用listPrice
                goodsHeadVO.setStandardPrice(goodsHeadVO.getListPrice());
                // costPrice使用standardPrice
                goodsHeadVO.setVcCostPrice(standardPrice);
                goodsHeadVO.setSettlementPrice(null);
            }
            //创建人 发布状态
            String publishStatusName = PublishType.getTypeName(goodsHeadVO.getPublishType());
            goodsHeadVO.setPublishTypeName(publishStatusName);
            String userName = userNameMap.get(goodsHeadVO.getCreateBy());
            goodsHeadVO.setCreateByName(userName);

            //类目
            exportCategory(categoryCache, goodsHeadVO);

            //属性
            attribute(attributeMap, goodsHeadVO);
            //图片
            image(goodsResourceMap, goodsHeadVO);
            //退货率标签
            goodsHeadVO.setSkuRateLabel(RefundRateLabelEnum.getDictName(goodsHeadVO.getSkuRateLabel()));
            goodsHeadVO.setListingRateLabel(RefundRateLabelEnum.getDictName(goodsHeadVO.getListingRateLabel()));

            // 将退货率标签合并到listing表现
            String listingPerformance = goodsHeadVO.getListingPerformance();
            JSONArray jsonArray = StrUtil.isBlank(listingPerformance) ? new JSONArray() : JSON.parseArray(listingPerformance);
            if (goodsHeadVO.getSkuRateLabel() != null) {
                jsonArray.add(goodsHeadVO.getSkuRateLabel());
            }
            if (goodsHeadVO.getListingRateLabel() != null) {
                jsonArray.add(goodsHeadVO.getListingRateLabel());
            }
            // 主链接
            if (goodsHeadVO.getIsMain() != null && goodsHeadVO.getIsMain() == 1) {
                jsonArray.add("主链接");
            }
            // 独立站专卖
            if (goodsHeadVO.getIndependentSiteOnly() != null && "Y".equals(goodsHeadVO.getIndependentSiteOnly())) {
                jsonArray.add("独立站专卖");
            }
            if (goodsHeadVO.getInventoryExclude() != null && "Y".equals(goodsHeadVO.getInventoryExclude())) {
                jsonArray.add("库存更新黑名单");
            }
            if (goodsHeadVO.getRedLineBlackFlag() != null && "Y".equals(goodsHeadVO.getRedLineBlackFlag())) {
                jsonArray.add("红线价管控白名单");
            }

            goodsHeadVO.setListingPerformance(jsonArray.toJSONString());
        }

    }


    private Map<Integer, List<String>> getGoodsResourceMap(List<GoodsHeadVO> goodsHeadVOList) {
        List<GoodsResource> goodsResourceList = goodsResourceService.selectListingGoodsResourceByHeadIds(goodsHeadVOList.stream().map(GoodsHeadVO::getId).collect(Collectors.toList()));
        //按照headId分组 key:headId value:图片集合  图片排序规则先取isMain=1放在第一张,然后Ismain=0的按照sortnubmer排序
        Map<Integer, List<GoodsResource>> goodsResourceMap = goodsResourceList.stream().collect(Collectors.groupingBy(GoodsResource::getGoodsId));
        Map<Integer, List<String>> goodsResourceMap1 = new HashMap<>();
        for (Integer headId : goodsResourceMap.keySet()) {
            List<GoodsResource> goodsResources = goodsResourceMap.get(headId);
            List<String> images = goodsResources.stream().sorted(Comparator.comparing(GoodsResource::getIsMain, Comparator.nullsLast(Comparator.reverseOrder()))
                    .thenComparing(GoodsResource::getSortNumber, Comparator.nullsLast(Comparator.naturalOrder()))).map(GoodsResource::getResourceUrl).collect(Collectors.toList());
            goodsResourceMap1.put(headId, images);
        }
        return goodsResourceMap1;
    }


    private void setEbayShopCategory(List<GoodsHeadVO> goodsHeadVOList) {
        Map<String, String> shopCategoryCache = new HashMap<>();
        Map<String, Boolean> shopCategoryIsExist = new HashMap<>();
        List<Integer> ebIds = goodsHeadVOList.stream().filter(f -> ObjUtil.equals(f.getPlatform(), PlatformTypeEnum.EB.name())).map(m -> m.getId()).collect(Collectors.toList());
        if (CollUtil.isEmpty(ebIds)) {
            return;
        }
        List<ListingEbayLine> listingEbayLines = listingEbayLineService.selectListingEbayLineByHeadIds(ebIds.stream().toArray(Integer[]::new));
        if (CollUtil.isEmpty(listingEbayLines)) {
            return;
        }
        List<ListingEbayShippingHead> listingEbayShippingHeads = ebayShippingHeadService.selectListingEbayShippingByLineIds(listingEbayLines.stream().map(ListingEbayLine::getId).collect(Collectors.toList()));
        Map<Integer, ListingEbayShippingHead> shippingHeadMap = listingEbayShippingHeads.stream().collect(Collectors.toMap(ListingEbayShippingHead::getListingLineId, e -> e));

        Map<Integer, ListingEbayLine> listingEbayLineMap = listingEbayLines.stream().collect(Collectors.toMap(ListingEbayLine::getListingHeadId, e -> e));

        for (GoodsHeadVO goodsHeadInfo : goodsHeadVOList) {
            ListingEbayLine listingEbayLine = listingEbayLineMap.get(goodsHeadInfo.getId());
            if (ObjUtil.isEmpty(listingEbayLine)) {
                continue;
            }
            goodsHeadInfo.setLocation(ObjectUtils.isNotEmpty(goodsHeadInfo.getLocation()) ? goodsHeadInfo.getLocation() : listingEbayLine.getLocation());
            goodsHeadInfo.setPublicationDay(ObjectUtils.isNotEmpty(goodsHeadInfo.getPublicationDay()) ? goodsHeadInfo.getPublicationDay() : listingEbayLine.getSellDay());
            ListingEbayShippingHead shippingHead = shippingHeadMap.get(listingEbayLine.getId());
            goodsHeadInfo.setHandlingTime(ObjUtil.isEmpty(shippingHead) ? null : shippingHead.getHandlingTime());

            //如果这个店铺没用分类，就不用去查了
            boolean checkShopCategory = checkShopCategory(shopCategoryIsExist, goodsHeadInfo);
            if (checkShopCategory) {
                continue;
            }

            //ebay 店铺分类
            String key = goodsHeadInfo.getShopCode() + "_" + listingEbayLine.getFirstShopCategory();
            if (shopCategoryCache.containsKey(key)) {
                goodsHeadInfo.setShopCategoryName(shopCategoryCache.get(key));
            } else {
                ConfigEbayCategory configEbayCategory = new ConfigEbayCategory();
                configEbayCategory.setShopCode(goodsHeadInfo.getShopCode());
                if (ObjUtil.isEmpty(listingEbayLine.getFirstShopCategory()) || !listingEbayLine.getFirstShopCategory().matches("[0-9]+")) {
                    continue;
                }
                configEbayCategory.setCategoryId(Long.valueOf(listingEbayLine.getFirstShopCategory()));
                List<ConfigEbayCategory> configEbayCategories = configEbayCategoryService.selectConfigEbayCategoryList(configEbayCategory);
                if (CollUtil.isNotEmpty(configEbayCategories)) {
                    ConfigEbayCategory configEbayCategory1 = configEbayCategories.get(0);
                    goodsHeadInfo.setShopCategoryName(configEbayCategory1.getCategoryName());
                    shopCategoryCache.put(key, configEbayCategory1.getCategoryName());
                }
            }
        }
    }



    /**
     * 处理goodHeadVO属性MAP
     *
     * @param goodsHeadVOList
     * @return
     */
    private Map<String, List<String>> getAttributeMap(List<GoodsHeadVO> goodsHeadVOList) {
        Map<String, List<String>> attributes = new HashMap<>();
        for (GoodsHeadVO head : goodsHeadVOList) {
            Integer goodsId = head.getId();
            String platform = head.getPlatform();
            String shopCode = head.getShopCode();

            //Ebay 处理属性导出
            if (Objects.equals(platform, PlatformTypeEnum.EB.name())) {
                ebayAttrMappingGoodHeadVO(goodsId, attributes);
                continue;
            }
            //Amazon处理属性导出
            if (Objects.equals(platform, PlatformTypeEnum.AM.name())) {
//                if (ObjUtil.isEmpty(shopCode)) {
//                    continue;
//                }
//                boolean vcFlag = shopCode.contains("VC");
//                amazonV2AttrMappingGoodHeadVO(goodsId, attributes, vcFlag);
            }
        }
        return attributes;
    }

    /**
     * amazon属性映射到goodHeadVO
     *
     * @param goodsId
     * @param attributes
     * @param vcFlag
     */
    private void amazonV2AttrMappingGoodHeadVO(Integer goodsId, Map<String, List<String>> attributes, boolean vcFlag) {
        List<ListingAmazonAttributeLineV2> amV2Attr = listingAmazonAttributeLineV2Service.selecAttrByGoodsIds(Collections.singletonList(Long.valueOf(goodsId)));
        //vc只查V2
        if (vcFlag && ObjUtil.isEmpty(amV2Attr)) {
            return;
        }
        for (ListingAmazonAttributeLineV2 attr : amV2Attr) {
            String name = attr.getPropNodePath();
            String value = attr.getTableValue();
            if (attributes.containsKey(String.valueOf(goodsId))) {
                List<String> strings = attributes.get(String.valueOf(goodsId));
                strings.add(name + ":" + value);
            } else {
                List<String> strings = new ArrayList<>();
                strings.add(name + ":" + value);
                attributes.put(String.valueOf(goodsId), strings);
            }
        }
        //sc 先查V2 没有查V1属性
        if (!vcFlag && ObjUtil.isEmpty(amV2Attr)) {
            List<ListingAmazonAttributeLine> amLines = listingAmazonAttributeLineService.selectByGoodsIds(Collections.singletonList(goodsId));
            for (ListingAmazonAttributeLine amLine : amLines) {
                String tableName = amLine.getTableName();
                String tableValue = amLine.getTableValue();
                if (attributes.containsKey(String.valueOf(goodsId))) {
                    List<String> strings = attributes.get(String.valueOf(goodsId));
                    strings.add(tableName + ":" + tableValue);
                } else {
                    List<String> strings = new ArrayList<>();
                    strings.add(tableName + ":" + tableValue);
                    attributes.put(String.valueOf(goodsId), strings);
                }
            }
        }
    }

    /**
     * ebay属性映射到goodHeadVO
     *
     * @param goodsId
     * @param attributes
     */
    private void ebayAttrMappingGoodHeadVO(Integer goodsId, Map<String, List<String>> attributes) {
        List<ListingEbayValue> ebAttr = listingEbayValueService.selectListingEbayValueByHeadIds(Collections.singletonList(goodsId));
        for (ListingEbayValue attr : ebAttr) {
            String headId = String.valueOf(attr.getHeadId());
            String name = attr.getName();
            String value = attr.getValue();
            if (attributes.containsKey(headId)) {
                List<String> strings = attributes.get(headId);
                strings.add(name + ":" + value);
            } else {
                List<String> strings = new ArrayList<>();
                strings.add(name + ":" + value);
                attributes.put(headId, strings);
            }
        }
    }


    /**
     * 获取ebay属性表的品牌编码
     *
     * @param id
     * @return
     */
    public String getEbayBrandCode(Integer id) {
        ListingEbayLine ebayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(id);
        if (ObjectUtils.isEmpty(ebayLine)) {
            return null;
        }
        List<ListingEbayValue> listingEbayValueList = ebayValueService.selectListingEbayValueByLineId(ebayLine.getId());
        if (ObjectUtils.isEmpty(listingEbayValueList)) {
            return null;
        }
        return listingEbayValueList.stream().filter(e -> "Brand".equals(e.getName())).map(ListingEbayValue::getValue).findFirst().orElse(null);
    }

    public void filleBayDesc(List<GoodsHeadVO> goodsHeadVOList, List<Integer> goodsId, ListingDTO listingDTO) {
        if (CollUtil.isEmpty(goodsId)) {
            return;
        }
        String noSelectColumn = listingDTO.getNoSelectColumn();
        List<GoodsDescription> goodsDescriptionsList;
        if (ObjectUtils.isEmpty(noSelectColumn) || noSelectColumn.contains("EB_shortDescription")) {
            goodsDescriptionsList = goodsDescriptionService.selectDescriptionIdListByGoodsIdList(goodsId);
        } else {
            goodsDescriptionsList = goodsDescriptionService.selectDescriptionListByGoodsIdList(goodsId);
        }


        if (CollUtil.isEmpty(goodsDescriptionsList)) {
            return;
        }
        List<Integer> descriptionIds = goodsDescriptionsList.stream().map(GoodsDescription::getDescriptionId).filter(f -> ObjUtil.isNotEmpty(f)).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(descriptionIds)) {
            return;
        }
        List<TemplateEbayDescription> templateEbayDescriptions = templateEbayDescriptionService.selectTemplateEbayDescriptionByIds(descriptionIds);
        if (CollUtil.isEmpty(templateEbayDescriptions)) {
            return;
        }
        Map<Long, List<TemplateEbayDescription>> templateEbayDescriptionMap = templateEbayDescriptions.parallelStream().collect(Collectors.groupingBy(TemplateEbayDescription::getId));

        Map<Integer, List<GoodsDescription>> descriptionMap = goodsDescriptionsList.stream().collect(Collectors.groupingBy(GoodsDescription::getGoodsId));
        goodsHeadVOList.stream().forEach(goodsHeadInfo -> {
            List<GoodsDescription> goodsDescriptions = descriptionMap.get(goodsHeadInfo.getId());
            if (CollUtil.isEmpty(goodsDescriptions)) {
                return;
            }
            GoodsDescription desc = goodsDescriptions.get(0);
            if (Objects.isNull(desc)) {
                return;
            }
            goodsHeadInfo.setDetailDescription(desc.getDetailDescription());
            goodsHeadInfo.setShortDescription(desc.getShortDescription());

            Integer descriptionId = desc.getDescriptionId();
            if (Objects.isNull(descriptionId)) {
                return;
            }
            List<TemplateEbayDescription> templateEbayDescription = templateEbayDescriptionMap.get(Long.valueOf(descriptionId));
            if (CollUtil.isNotEmpty(templateEbayDescription)) {
                goodsHeadInfo.setDescribeTemplateName(templateEbayDescription.get(0).getTemplateName());
            }

        });

    }

    public void fillMainImage(List<GoodsHeadVO> goodsHeadVOList, List<Integer> goodsId) {
        List<GoodsResource> goodsResourceList = goodsResourceService.selectListingGoodsResourceByHeadIds(goodsId);

        //填充相关资源信息
        if (!CollectionUtils.isEmpty(goodsResourceList)) {
            Map<Integer, List<GoodsResource>> resourceByGoodIdMap = goodsResourceList.stream().collect(Collectors.groupingBy(GoodsResource::getGoodsId));
            goodsHeadVOList.forEach(goodsHeadInfo -> {
                //图片
                List<GoodsResource> goodsResource = resourceByGoodIdMap.get(goodsHeadInfo.getId());
                if (!CollectionUtils.isEmpty(goodsResource)) {
                    Optional<GoodsResource> first = goodsResource
                            .stream()
                            .filter(r -> Objects.equals(r.getIsMain(), 1)).findFirst();
                    if (first.isPresent()) {
                        goodsHeadInfo.setMainImageUrl(first.get().getResourceUrl());
                    }
                }

            });
        }

        /**
         * Aplus标题
         */
        List<String> platformGoodsIds = goodsHeadVOList
                .stream()
                .filter(f->Objects.equals(f.getPlatform(), PlatformTypeEnum.AM.name()))
                .map(GoodsHeadVO::getPlatformGoodsId)
                .filter(f->ObjUtil.isNotEmpty(f))
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(platformGoodsIds)){
            List<APlusAsin> aPlusAsins = aPlusAsinService.selectAplusNameByAsins(platformGoodsIds);
            Map<String, String> aplusNameMap = aPlusAsins.stream().collect(Collectors.toMap(APlusAsin::getAsin, APlusAsin::getAplusName));
            goodsHeadVOList.forEach(goodsHeadVO -> {
                String aplusName = aplusNameMap.get(goodsHeadVO.getPlatformGoodsId());
                goodsHeadVO.setAplusName(aplusName);
            });
        }
    }

    public void handleAmData(List<GoodsHeadVO> goodsHeadVOList, List<Integer> headIdAm, ListingDTO listingDTO) {
        if (CollUtil.isEmpty(headIdAm)) {
            return;
        }
        List<ListingAmazonAttributeLine> lines = listingAmazonAttributeLineService.selectByGoodsIds(headIdAm);
        Map<Integer, List<ListingAmazonAttributeLine>> PNMap = lines.stream().filter(f -> Objects.equals(f.getTableName(), "part_number")).collect(Collectors.groupingBy(e -> e.getGoodsId()));
        Map<Integer, List<ListingAmazonAttributeLine>> fnSkuMap = lines.stream().filter(f -> Objects.equals(f.getTableName(), "fn_sku")).collect(Collectors.groupingBy(e -> e.getGoodsId()));
        String noSelectColumn = listingDTO.getNoSelectColumn();
        List<GoodsDescription> goodsDescriptionsList = null;
        if ( ObjectUtils.isEmpty(noSelectColumn)||
                ((!noSelectColumn.contains("AM_detailDescription") || !noSelectColumn.contains("AM_pointDescription")))) {
            goodsDescriptionsList = goodsDescriptionService.selectDescriptionListByGoodsIdList(headIdAm);
        }else {
            goodsDescriptionsList = null;
        }
        List<ListingAmazonAttributeLineV2> attributeLineV2s = listingAmazonAttributeLineV2Service.selecAttrByGoodsIds(headIdAm.stream().map(Integer::longValue).collect(Collectors.toList()));
        Map<Integer, List<ListingAmazonAttributeLineV2>> pnMapV2 = attributeLineV2s.stream().filter(f -> Objects.equals(f.getPropNodePath(), "part_number.value")).collect(Collectors.groupingBy(e -> e.getHeadId().intValue()));
        // list_price
        Map<Integer, List<ListingAmazonAttributeLineV2>> listPriceMapV2 = attributeLineV2s.stream().filter(f -> Objects.equals(f.getPropNodePath(), "list_price.value")).collect(Collectors.groupingBy(e -> e.getHeadId().intValue()));
        Map<Integer, List<ListingAmazonAttributeLineV2>> salePriceMapV2 = attributeLineV2s.stream().filter(f -> Objects.equals(f.getPropNodePath(), AmazonAttributeEnum.SALE_PRICE.getInfoV2("Y"))).collect(Collectors.groupingBy(e -> e.getHeadId().intValue()));
        Map<Integer, List<ListingAmazonAttributeLineV2>> salePriceFormMapV2 = attributeLineV2s.stream().filter(f -> Objects.equals(f.getPropNodePath(), AmazonAttributeEnum.SALE_FROM_DATE.getInfoV2("Y"))).collect(Collectors.groupingBy(e -> e.getHeadId().intValue()));
        Map<Integer, List<ListingAmazonAttributeLineV2>> salePriceEndMapV2 = attributeLineV2s.stream().filter(f -> Objects.equals(f.getPropNodePath(), AmazonAttributeEnum.SALE_END_DATE.getInfoV2("Y"))).collect(Collectors.groupingBy(e -> e.getHeadId().intValue()));

        // 添加可用状态详情对象

        List<ListingGoodsHeadDetail> details = listingGoodsHeadDetailService.selectListingGoodsHeadDetailByHeadIds(headIdAm.stream().map(Integer::longValue).collect(Collectors.toList()));
        Map<Integer, ListingGoodsHeadDetail> detailMap = details.stream().collect(Collectors.toMap(e -> e.getHeadId().intValue(), e -> e));
        
        for (GoodsHeadVO goodsHeadVO : goodsHeadVOList) {
            Integer id = goodsHeadVO.getId();

            ListingGoodsHeadDetail detail = detailMap.get(id);
            goodsHeadVO.setAvailabilityDetail(detail);
            
            List<ListingAmazonAttributeLine> listingAmazonAttributeLinesPN = PNMap.get(id);
            if (!CollectionUtils.isEmpty(listingAmazonAttributeLinesPN)) {
                String partNumber = listingAmazonAttributeLinesPN.get(0).getTableValue();
                goodsHeadVO.setPartNumber(partNumber);
            }
            List<ListingAmazonAttributeLineV2> listingAmazonAttributeLinesPNV2 = pnMapV2.get(id);
            if (!CollectionUtils.isEmpty(listingAmazonAttributeLinesPNV2)) {
                String partNumber = listingAmazonAttributeLinesPNV2.get(0).getTableValue();
                goodsHeadVO.setPartNumber(partNumber);
            }
            List<ListingAmazonAttributeLineV2> listingAmazonAttributeLinesListPriceV2 = listPriceMapV2.get(id);
            if (!CollectionUtils.isEmpty(listingAmazonAttributeLinesListPriceV2)) {
                String listPrice = listingAmazonAttributeLinesListPriceV2.get(0).getTableValue();
                goodsHeadVO.setListPrice(listPrice);
            }

            List<ListingAmazonAttributeLineV2> salePriceLinesV2 = salePriceMapV2.get(id);
            if (!CollectionUtils.isEmpty(salePriceLinesV2)) {
                String salePrice = salePriceLinesV2.get(0).getTableValue();
                goodsHeadVO.setSalePrice(salePrice);
            }
            List<ListingAmazonAttributeLineV2> salePriceFormLinesV2 = salePriceFormMapV2.get(id);
            if (!CollectionUtils.isEmpty(salePriceFormLinesV2)) {
                String salePriceForm = salePriceFormLinesV2.get(0).getTableValue();
                goodsHeadVO.setSalePriceBegin(salePriceForm);
            }
            List<ListingAmazonAttributeLineV2> salePriceEndLinesV2 = salePriceEndMapV2.get(id);
            if (!CollectionUtils.isEmpty(salePriceEndLinesV2)) {
                String salePriceEnd = salePriceEndLinesV2.get(0).getTableValue();
                goodsHeadVO.setSalePriceEnd(salePriceEnd);
            }

            List<ListingAmazonAttributeLine> listingAmazonAttributeLinesFN = fnSkuMap.get(id);
            if (!CollectionUtils.isEmpty(listingAmazonAttributeLinesFN)) {
                String fnSku = listingAmazonAttributeLinesFN.get(0).getTableValue();
                goodsHeadVO.setFnSku(fnSku);
            }
            if (ObjectUtils.isEmpty(goodsDescriptionsList)){
                continue;
            }
            GoodsDescription goodsDescription = goodsDescriptionsList.stream().filter(f -> Objects.equals(f.getGoodsId(), id)).findFirst().orElse(null);
            if (Objects.isNull(goodsDescription)) {
                continue;
            }
            goodsHeadVO.setDetailDescription(goodsDescription.getDetailDescription());
            goodsHeadVO.setPoint1(goodsDescription.getItemDescription1());
            goodsHeadVO.setPoint2(goodsDescription.getItemDescription2());
            goodsHeadVO.setPoint3(goodsDescription.getItemDescription3());
            goodsHeadVO.setPoint4(goodsDescription.getItemDescription4());
            goodsHeadVO.setPoint5(goodsDescription.getItemDescription5());

        }
    }

    /**
     * 获取Listing字段
     *
     * @param id Listing主键ID
     * @return Listing字段集合
     */
    public Map<String, String> getListingFieldMap(Integer id) {
        // Listing头表数据
        Map<String, String> goodsHeadMap = goodsHeadService.selectGoodsHeadMapById(id);

        //库存不要显示小数
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(id);

        //价格校验
        if ( !Objects.equals(String.valueOf(goodsHeadMap.get("publish_status")), "9001") ){
            commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), goodsHead.getPublishType())
                    .headId(goodsHead.getId())
                    .siteCode(goodsHead.getSiteCode())
                    .onlineTime(goodsHead.getOnlineTime())
                    .oldPrice(goodsHead.getStandardPrice())
                    .isOnline(StrUtil.isNotBlank(goodsHead.getPlatformGoodsId()))
                    .build());
        }

        //库存为0 自动更新
        Map<String, String> partialUpdateMap = getStockPartialUpdate(goodsHead);
        if (partialUpdateMap != null) {
            return partialUpdateMap;
        }
        goodsHeadMap.put("stock_on_sales_qty", Objects.isNull(goodsHead.getStockOnSalesQty()) ? "0" : String.valueOf(goodsHead.getStockOnSalesQty().intValue()));

        //Listing类目name
        List<CategoryInfo> categoryInfoList = categoryInfoService.getCategoryInfoListByCategoryId(String.valueOf(goodsHeadMap.get("category_id")));
        if (!CollectionUtils.isEmpty(categoryInfoList)) {
            goodsHeadMap.put("category", categoryInfoList.get(0).getCategoryName());
            goodsHeadMap.put("feed_product_type", categoryInfoList.get(0).getProductType());
            goodsHeadMap.put("item_type", categoryInfoList.get(0).getItemTypeKeyword());

//            PlatformCategory platformCategory = this.judgeNewTemplateExistByCategoryId(categoryInfoList.get(0).getPlatformCategoryId());
//            if(ObjUtil.isNotEmpty(platformCategory)) {
//                //新模板
//                goodsHeadMap.put("feed_product_type", platformCategory.getProductType());
//                goodsHeadMap.put("item_type", platformCategory.getItemTypeKeyword());
//            }else {
//                goodsHeadMap.put("feed_product_type", categoryInfoList.get(0).getProductType());
//                goodsHeadMap.put("item_type", categoryInfoList.get(0).getItemTypeKeyword());
//            }
        }


        // Listing描述数据
        Map<String, String> goodsDescriptionMap = goodsDescriptionService.selectGoodsDescriptionMapById(id);

        // Listing规格数据
        Map<String, String> specificationMap = goodsSpecificationService.selectSpecificationMapByGoodsId(id);
        if (!CollectionUtils.isEmpty(specificationMap)) {
            try {
                specificationMap.put("item_length_unit_of_measure", Objects.isNull(specificationMap.get("item_length_unit")) ? "CM" : specificationMap.get("item_length_unit").toUpperCase());
                specificationMap.put("item_width_unit_of_measure", Objects.isNull(specificationMap.get("item_width_unit")) ? "CM" : specificationMap.get("item_width_unit").toUpperCase());
                specificationMap.put("item_height_unit_of_measure", Objects.isNull(specificationMap.get("item_height_unit")) ? "CM" : specificationMap.get("item_height_unit").toUpperCase());
                specificationMap.put("package_length_unit_of_measure", Objects.isNull(specificationMap.get("package_length_unit")) ? "CM" : specificationMap.get("package_length_unit").toUpperCase());
                specificationMap.put("package_width_unit_of_measure", Objects.isNull(specificationMap.get("package_width_unit")) ? "CM" : specificationMap.get("package_width_unit").toUpperCase());
                specificationMap.put("package_height_unit_of_measure", Objects.isNull(specificationMap.get("package_height_unit")) ? "CM" : specificationMap.get("package_height_unit").toUpperCase());
                specificationMap.put("package_weight_unit_of_measure", Objects.isNull(specificationMap.get("package_weight_unit")) ? "KG" : specificationMap.get("package_weight_unit").toUpperCase());

                specificationMap.put("item_length", new BigDecimal(String.valueOf(specificationMap.get("item_length"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("item_width", new BigDecimal(String.valueOf(specificationMap.get("item_width"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("item_height", new BigDecimal(String.valueOf(specificationMap.get("item_height"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("package_length", new BigDecimal(String.valueOf(specificationMap.get("package_length"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("package_width", new BigDecimal(String.valueOf(specificationMap.get("package_width"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("package_height", new BigDecimal(String.valueOf(specificationMap.get("package_height"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("package_weight", new BigDecimal(String.valueOf(specificationMap.get("package_weight"))).setScale(2, RoundingMode.HALF_UP).toString());

                String boxes = String.valueOf(specificationMap.get("number_of_boxes"));
                specificationMap.put("number_of_boxes", ObjUtil.isEmpty(boxes)|| ObjUtil.equals(boxes,"0") ? "1" : boxes );
            } catch (Exception e) {
                //长宽高重量保留两位  但是不影响主流程  所以catch住
                log.error("Listing规格数据转换异常", e);
            }
        }
        // Listing图片数据
        List<GoodsResource> goodsResourceList = goodsResourceService.selectListingGoodsResourceByHeadId(id);

        //true:不要换链  false:需要换链并传至API
        Boolean isEqual = checkImage(String.valueOf(goodsHeadMap.get("publish_status")), String.valueOf(id), goodsResourceList);
        if (isEqual) {
            log.info("amazon更新商品组装数据,图片一致,不需要换链更新,headId:{}", id);
            goodsResourceList = new ArrayList<>();
        }

        // 删除、下架 时不需要图片换链
        if (!Objects.equals(String.valueOf(goodsHeadMap.get("publish_status")), "9001")
                && !Objects.equals(String.valueOf(goodsHeadMap.get("publish_status")), "5")) {
            //对资源数据进行换链
            try {
                imageHandleBiz.replaceResourcesUrl(goodsResourceList, null);
            } catch (Exception e) {
                log.error("AM Listing图片数据换链异常", e);
            }
        }


        // Listing属性数据
        List<ListingAmazonAttributeLine> listingAmazonAttributeList = listingAmazonAttributeLineService.selectByGoodsId(id);

        // 组装Listing数据
        Map<String, String> listingMap = new HashMap<>();
        listingMap.putAll(goodsHeadMap);
        listingMap.putAll(goodsDescriptionMap);
        listingMap.putAll(specificationMap);
        assembleListingImage(goodsResourceList, listingMap);
        assembleListingAttribute(listingAmazonAttributeList, listingMap);

        //如果 删除
        if (Objects.equals(String.valueOf(goodsHeadMap.get("publish_status")), "9001")) {
            listingMap.put("update_delete", "Delete");
            return listingMap;
        }
        //如果 更新
        if (Objects.equals(String.valueOf(goodsHeadMap.get("publish_status")), "3")) {
            listingMap.put("update_delete", "PartialUpdate");
        }
        //如果 下架
        if (Objects.equals(String.valueOf(goodsHeadMap.get("publish_status")), "5")) {
            listingMap.put("update_delete", "Update");
            listingMap.put("stock_on_sales_qty", "0");
        }

        //fba就不上传库存了
        if (Objects.equals(String.valueOf(listingMap.get("publish_type")), "1")) {
            listingMap.remove("stock_on_sales_qty");
            listingMap.put("fulfillment_center_id", "AMAZON_NA");

            listingMap.put("standard_price", goodsHead.getStandardPrice());
            listingMap.put("fba_price", goodsHead.getStandardPrice());
            listingMap.put("fbm_price", goodsHead.getStandardPrice());
        } else {
            listingMap.put("fulfillment_center_id", "DEFAULT");
            listingMap.put("quantity", Objects.isNull(goodsHead.getStockOnSalesQty()) ? "0" : String.valueOf(goodsHead.getStockOnSalesQty().intValue()));

            listingMap.put("standard_price", goodsHead.getStandardPrice());
            listingMap.put("fba_price", goodsHead.getStandardPrice());
            listingMap.put("fbm_price", goodsHead.getStandardPrice());
        }

        if( !listingMap.containsKey("list_price") || ObjUtil.isEmpty(listingMap.get("list_price")) ){
            listingMap.put("list_price", goodsHead.getStandardPrice());
            listingAmazonAttributeLineService.setListPrice(id, goodsHead.getStandardPrice());
        }
        return listingMap;
    }

    public Boolean checkImage(String publishStatus, String headId, List<GoodsResource> goodsResourceList) {
        if (!Objects.equals(publishStatus, "3")) {
            return false;
        }
        GoodsHeadBackup goodsHeadBackup = goodsHeadBackupService.selectGoodsHeadBackupByGoodsId(headId);
        if (ObjUtil.isEmpty(goodsHeadBackup)) {
            return false;
        }
        ItemBackUpDTO itemBackUpDTO = JSONObject.parseObject(goodsHeadBackup.getContext(), ItemBackUpDTO.class);
        if (ObjUtil.isEmpty(itemBackUpDTO)) {
            return false;
        }
        List<GoodsResource> oldGoodsResourceList = itemBackUpDTO.getGoodsResourceList();
        Boolean isEqual = commonInfoBiz.checkResourceEqual(oldGoodsResourceList, goodsResourceList);
        return isEqual;
    }


    /**
     * 更新库存为0
     *
     * @param goodsHead
     * @return
     */
    private Map<String, String> getStockPartialUpdate(GoodsHead goodsHead) {
        if (Objects.isNull(goodsHead.getStockOnSalesQty())) {
            return null;
        }
        if (!Objects.equals(goodsHead.getStockOnSalesQty().intValue(), 0)) {
            return null;
        }
        List<ListingLog> listingLogs = listingLogService.selectListingLogListByListingIds(Arrays.asList(goodsHead.getId()).stream().map(e -> String.valueOf(e)).toArray(String[]::new));
        if (CollectionUtils.isEmpty(listingLogs)) {
            return null;
        }
        ListingLog listingLog = listingLogs.get(0);
        if (!Objects.equals(listingLog.getDetails(), "商品库存自动更新")) {
            return null;
        }
        Map<String, String> partialUpdateMap = new HashMap<>();
        partialUpdateMap.put("item_sku", String.valueOf(goodsHead.getPlatformGoodsCode()));
//            partialUpdateMap.put("standard_price", "");
        partialUpdateMap.put("quantity", "0");
        partialUpdateMap.put("update_delete", "PartialUpdate");
        return partialUpdateMap;
    }

    /**
     * 组装Listing图片数据
     *
     * @param goodsResourceList 图片集合
     * @param listingMap        Listing字段集合
     */
    public void assembleListingImage(List<GoodsResource> goodsResourceList, Map<String, String> listingMap) {
        if (CollUtil.isEmpty(goodsResourceList)) {
            return;
        }
        goodsResourceList.forEach(goodsResource -> {
            if (Objects.equals(goodsResource.getIsMain(), 1)) {

                listingMap.put("main_image_url", goodsResource.getResourceUrl());
            }
            if (Objects.equals(goodsResource.getSortNumber(), 0)) {
                listingMap.put("other_image_url1", goodsResource.getResourceUrl());
            }
            if (Objects.equals(goodsResource.getSortNumber(), 1)) {
                listingMap.put("other_image_url2", goodsResource.getResourceUrl());
            }
            if (Objects.equals(goodsResource.getSortNumber(), 2)) {
                listingMap.put("other_image_url3", goodsResource.getResourceUrl());
            }
            if (Objects.equals(goodsResource.getSortNumber(), 3)) {
                listingMap.put("other_image_url4", goodsResource.getResourceUrl());
            }
            if (Objects.equals(goodsResource.getSortNumber(), 4)) {
                listingMap.put("other_image_url5", goodsResource.getResourceUrl());
            }
            if (Objects.equals(goodsResource.getSortNumber(), 5)) {
                listingMap.put("other_image_url6", goodsResource.getResourceUrl());
            }
            if (Objects.equals(goodsResource.getSortNumber(), 6)) {
                listingMap.put("other_image_url7", goodsResource.getResourceUrl());
            }
            if (Objects.equals(goodsResource.getSortNumber(), 7)) {
                listingMap.put("other_image_url8", goodsResource.getResourceUrl());
            }
        });
    }

    /**
     * 组装Listing属性数据
     *
     * @param listingAmazonAttributeList 属性集合
     * @param listingMap                 Listing字段集合
     */
    public void assembleListingAttribute(List<ListingAmazonAttributeLine> listingAmazonAttributeList, Map<String, String> listingMap) {
        listingAmazonAttributeList.forEach(listingAmazonAttribute -> {
            String tableName = listingAmazonAttribute.getTableName();
            String tableValue = listingAmazonAttribute.getTableValue();
            if (Objects.equals(tableName, "number_of_items") && StringUtils.isNotEmpty(tableValue) && tableValue.contains(".0")) {
                tableValue = tableValue.replaceAll(".0", "");
            }
            if (Objects.equals(tableName, "number_of_boxes") && StringUtils.isNotEmpty(tableValue) && tableValue.contains(".0")) {
                tableValue = tableValue.replaceAll(".0", "");
            }
            if (Objects.equals(tableName, "model_year") && StringUtils.isNotEmpty(tableValue) && tableValue.contains(".0")) {
                tableValue = tableValue.replaceAll(".0", "");
            }
            if (Objects.equals(tableName, "external_product_id") && StringUtils.isBlank(tableValue)) {
                return;
            }
            if (Objects.equals(tableName, "included_components1") && StringUtils.isNotEmpty(tableValue) ) {
                listingMap.put("included_components", tableValue);
            }
            //处理时间
//            if (Objects.equals(tableName, AmazonAttributeEnum.FULFILLMENT_LATENCY.getInfo()) ) {
//                listingMap.put("fulfillment_availability#1.lead_time_to_ship_max_days", tableValue);
//            }
            listingMap.put(tableName, tableValue);
        });
    }

    /**
     * 兑换模板表头字段
     *
     * @param listingMap Listing字段
     * @return Listing数据
     */
    public Map<String, String> assembleListingMap(Map<String, String> listingMap) {
        Map<String, String> fillMap = new HashMap<>(listingMap.size());
        listingMap.keySet().stream().forEach(key -> {
            if (StringUtils.isEmpty(key)) {
                return;
            }
            if (Objects.isNull(listingMap.get("category_id")) || Objects.isNull(listingMap.get("site_code"))) {
                fillMap.put(key, String.valueOf(listingMap.get(key)));
                return;
            }
            TitleFieldQuery titleFieldQuery = new TitleFieldQuery();
            titleFieldQuery.setListingField(key);
            titleFieldQuery.setPlatformCategoryId(String.valueOf(listingMap.get("category_id")));
            titleFieldQuery.setSite(listingMap.get("site_code"));
            String tableField = listingTemplateService.getTitleByTableField(titleFieldQuery);

            // 组装待填充数据
            if (Objects.nonNull(tableField)) {
                fillMap.put(tableField, Objects.nonNull(listingMap.get(key)) ? String.valueOf(listingMap.get(key)) : null);
            } else {
                fillMap.put(key, String.valueOf(listingMap.get(key)));
            }
        });

        return fillMap;
    }

    /**
     * 批量刊登Listing
     *
     * @param ids Listing ID集合
     */
    public AjaxResult publishListingByIds(String ids, Long userId) {
        // 记录刊登日志
        String[] idsArr = Convert.toStrArray(ids);
        List<Integer> idList = Arrays.stream(idsArr).map(Integer::valueOf).collect(Collectors.toList());

        for (int j = 0; j < idsArr.length; j++) {
            ListingLog listingLog = new ListingLog();
            String id = idsArr[j];
            GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(Integer.valueOf(id));
            String publishStatusName = PublishStatus.getPublishStatusName(goodsHead.getPublishStatus());
            try {
                if (Objects.isNull(goodsHead)) {
                    throw new BusinessException("商品不存在.");
                }
                if (j == 0) {
                    goodsTaskService.insertGoodsPendingProcessingTask(goodsHead.getPlatform(), GoodsTaskTypeEnum.BATCH_PUBLISH, idList, String.valueOf(userId));
                }
                String price = commonInfoBiz.checkPriceAndReturnSellerPrice(goodsHead.getId(),goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), goodsHead.getPublishType(),goodsHead.getSiteCode(),goodsHead.getOnlineTime(),goodsHead.getStandardPrice());

                if (Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name())) {
                    if (ObjectUtils.isNotEmpty(goodsHead.getPlatformGoodsId())) {
                        return AjaxResult.error("存在在售的listing,主键ID:"+goodsHead.getId());
                    }
                    if(!goodsHead.getShopCode().contains("VC")) {
                        List<ListingAmazonAttributeLineV2> listingAmazonAttributeLineV2s = listingAmazonAttributeLineV2Service.listByGoodsId(Integer.valueOf(id));

                        if (ObjUtil.isEmpty(listingAmazonAttributeLineV2s)) {
                            checkSCV1Attr(id, goodsHead);
                        } else {
                            checkSCV2Attr(listingAmazonAttributeLineV2s, id, goodsHead);
                        }
                    }else {
                        List<ListingAmazonAttributeLineV2> listingAmazonAttributeLineV2s = listingAmazonAttributeLineV2Service.listByGoodsId(Integer.valueOf(id));
                        if (CollUtil.isEmpty(listingAmazonAttributeLineV2s)) {
                            return AjaxResult.error("商品id为"+id+"的listing属性为空！");
                        }
                        String listPriceValue = listingAmazonAttributeLineV2s.stream().filter(f -> Objects.equals(f.getPropNodePath(), "list_price.value")).findFirst().map(ListingAmazonAttributeLineV2::getTableValue).orElse(null);
                        commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), goodsHead.getStandardPrice(), goodsHead.getPublishType())
                                .headId(goodsHead.getId())
                                .siteCode(goodsHead.getSiteCode())
                                .onlineTime(goodsHead.getOnlineTime())
                                .oldPrice(goodsHead.getStandardPrice())
                                .isOnline(StrUtil.isNotBlank(goodsHead.getPlatformGoodsId()))
                                .listPrice(listPriceValue)
                                .build());
                    }
                    executeAmazonDeleteThenPublish(goodsHead, price, userId);
                    continue;
                }

                GoodsHead updateHead = new GoodsHead();
                updateHead.setId(goodsHead.getId());
                updateHead.setStandardPrice(price);
                updateHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
                if (ObjectUtils.isNotEmpty(goodsHead.getPlatformGoodsId())) {
                    listingEbayAdaptiveService.writerEbayAdapter(goodsHead.getPlatformGoodsId(), goodsHead.getId(), goodsHead.getShopCode());
                    //清空销售编码
                    updateHead.setAsinNullFlag(1);
                    updateHead.setSmcFlag(0);
                    updateHead.setPdmStatus(0);
                }
                //smc_flag=0
                goodsHeadService.updateListingGoodsHead(updateHead);
                // 修改Listing的状态为刊登中
                goodsHeadService.publishListingByIds(id);

                listingLog.setStatus(0);
                listingLog.setDetails("批量刊登Listing,数据由["+publishStatusName+"]移至[刊登中]");
                listingLog.setOperName(String.valueOf(userId));
                listingLog.setOperTime(new Date());
                listingLog.setListingId(Integer.valueOf(id));
                listingLogService.insertListingLog(listingLog);
            } catch (Exception e) {
                listingLog.setStatus(1);
                listingLog.setErrorMsg(e.getMessage());
                listingLog.setDetails("批量刊登Listing,数据由["+publishStatusName+"]移至[刊登中]");
                listingLog.setOperName(String.valueOf(userId));
                listingLog.setOperTime(new Date());
                listingLog.setListingId(Integer.valueOf(id));
                listingLogService.insertListingLog(listingLog);

                List<GoodsTaskTypeEnum> taskTypeEnum = new ArrayList<>();
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(id), taskTypeEnum, GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        List<String> idStrList = Arrays.stream(idsArr).map(String::valueOf).collect(Collectors.toList());
        return AjaxResult.success(idStrList);
    }

    /**
     * 异步执行Amazon删除操作，并在删除成功后执行刊登
     *
     * @param goodsHead 商品Head数据
     * @param userId
     */
    private void executeAmazonDeleteThenPublish(GoodsHead goodsHead, String price, Long userId) {
        // 如果是刊登失败的状态，且销售编码为空，则先调用删除接口，但不能影响正常业务
        if (Objects.equals(goodsHead.getPublishStatus(), PublishStatus.PUBLISH_FAIL.getType()) && StrUtil.isBlank(goodsHead.getPlatformGoodsId())
                && StrUtil.isNotBlank(goodsHead.getPlatformGoodsCode()) && goodsHead.getPublishType() != null) {
            // 获取线程池
            ThreadPoolExecutor executor = threadPoolForMonitorManager.getThreadPoolExecutor("amazon-push-upload");

            // 第一步：执行Amazon删除操作
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("执行删除Amazon SKU: {}", goodsHead.getPlatformGoodsCode());
                    // 调用删除API
                    amazonApiHttpRequestBiz.deleteSkuV2(
                            goodsHead.getShopCode(),
                            goodsHead.getPublishType(),
                            goodsHead.getPlatformGoodsCode()
                    );
                    log.info("成功删除Amazon SKU: {}", goodsHead.getPlatformGoodsCode());

                } catch (Exception e) {
                    log.error("删除Amazon SKU失败: {}, 错误: {}", goodsHead.getPlatformGoodsCode(), e.getMessage(), e);
                    // 即使删除失败，依然尝试继续刊登
                }
            }, executor).thenRunAsync(() -> {
                try {
                    // 使用提取出的通用Amazon刊登方法
                    updateAndPublishAmazonListing(goodsHead, price, userId);
                    log.info("Amazon SKU: {} 刊登操作已完成", goodsHead.getPlatformGoodsCode());
                } catch (Exception e) {
                    log.error("Amazon SKU: {} 刊登操作失败: {}", goodsHead.getPlatformGoodsCode(), e.getMessage(), e);
                }
            }, executor);
        } else{
            try {
                // 使用提取出的通用Amazon刊登方法
                updateAndPublishAmazonListing(goodsHead, price, userId);
                log.info("Amazon SKU: {} 刊登操作已完成", goodsHead.getPlatformGoodsCode());
            } catch (Exception e) {
                log.error("Amazon SKU: {} 刊登操作失败: {}", goodsHead.getPlatformGoodsCode(), e.getMessage(), e);
            }
        }
    }

    /**
     * 更新并刊登Amazon Listing
     * 
     * @param goodsHead 商品Head数据
     * @param price 价格
     * @param userId 用户ID
     */
    private void updateAndPublishAmazonListing(GoodsHead goodsHead, String price, Long userId) {
        String publishStatusName = PublishStatus.getPublishStatusName(goodsHead.getPublishStatus());
        ListingLog listingLog = new ListingLog();
        
        try {
            log.info("开始执行商品刊登, 商品ID: {}, 价格: {}", goodsHead.getId(), price);
            
            GoodsHead updateHead = new GoodsHead();
            updateHead.setId(goodsHead.getId()); 
            updateHead.setStandardPrice(price);
            updateHead.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(price)));
            
            if (ObjectUtils.isNotEmpty(goodsHead.getPlatformGoodsId())) {
                listingEbayAdaptiveService.writerEbayAdapter(goodsHead.getPlatformGoodsId(), goodsHead.getId(), goodsHead.getShopCode());
                //清空销售编码
                updateHead.setAsinNullFlag(1);
                updateHead.setSmcFlag(0);
                updateHead.setPdmStatus(0);
            }
            
            //smc_flag=0
            goodsHeadService.updateListingGoodsHead(updateHead);
            // 修改Listing的状态为刊登中
            goodsHeadService.publishListingByIds(String.valueOf(goodsHead.getId()));
            
            listingLog.setStatus(0);
            listingLog.setDetails("批量刊登Listing,数据由["+publishStatusName+"]移至[刊登中]");
            listingLog.setOperName(String.valueOf(userId));
            listingLog.setOperTime(new Date());
            listingLog.setListingId(goodsHead.getId());
            listingLogService.insertListingLog(listingLog);
            
            log.info("商品刊登执行完成, 商品ID: {}", goodsHead.getId());
        } catch (Exception e) {
            log.error("商品刊登失败, 商品ID: {}, 错误: {}", goodsHead.getId(), e.getMessage(), e);

            listingLog.setStatus(1);
            listingLog.setErrorMsg(e.getMessage());
            listingLog.setDetails("批量刊登Listing,数据由["+publishStatusName+"]移至[刊登中]");
            listingLog.setOperName(String.valueOf(userId));
            listingLog.setOperTime(new Date());
            listingLog.setListingId(goodsHead.getId());
            listingLogService.insertListingLog(listingLog);

            List<GoodsTaskTypeEnum> taskTypeEnum = new ArrayList<>();
            taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
            goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), taskTypeEnum, GoodsTaskSubStatusEnum.ERROR, e.getMessage());
        }
    }
    
    /**
     * 校验SC V2版本
     *
     * @param listingAmazonAttributeLineV2s
     * @param id
     * @param goodsHead
     */
    private void checkSCV2Attr(List<ListingAmazonAttributeLineV2> listingAmazonAttributeLineV2s, String id, GoodsHead goodsHead) {
        List<ListingAmazonAttributeLineV2> attributeLines = listingAmazonAttributeLineV2s.stream().filter(f -> Objects.equals(f.getPropNodePath(), "part_number.value")).collect(Collectors.toList());
        if (CollUtil.isEmpty(attributeLines)) {
            throw new BusinessException("商品没有填写part_number");
        }
        if (attributeLines.size() > 1) {
            log.error("请注意,该Listing存在多个part_number,headId:{},line:{}", id, JSON.toJSONString(attributeLines));
            // 有多个part_number 删掉多余的
            for (int i = 1; i < attributeLines.size(); i++) {
                listingAmazonAttributeLineV2Service.deleteListingAmazonAttributeLineV2ById(attributeLines.get(i).getId());
            }
        }

        ListingAmazonAttributeLineV2 attributeLine = attributeLines.get(0);
        if (Objects.equals(attributeLine.getTableValue(), "0") || Objects.equals(attributeLine.getTableValue(), "1") || StrUtil.isBlank(attributeLine.getTableValue())) {
            String platformCode = createPlatformCode.getPlatformCode(goodsHead.getPdmGoodsCode(), goodsHead.getSiteCode());
            attributeLine.setTableValue(platformCode);
            listingAmazonAttributeLineV2Service.updateListingAmazonAttributeLineV2(attributeLine);
        }
    }

    /**
     * 校验SC v1版本
     *
     * @param id
     * @param goodsHead
     */
    private void checkSCV1Attr(String id, GoodsHead goodsHead) {
        List<ListingAmazonAttributeLine> listingAmazonAttributeList = listingAmazonAttributeLineService.selectByGoodsId(Integer.valueOf(id));
        if (CollUtil.isEmpty(listingAmazonAttributeList)) {
            throw new BusinessException("商品没有填写part_number");
        }
        List<ListingAmazonAttributeLine> attributeLines = listingAmazonAttributeList.stream().filter(f -> Objects.equals(f.getTableName(), "part_number")).collect(Collectors.toList());
        if (CollUtil.isEmpty(attributeLines)) {
            throw new BusinessException("商品没有填写part_number");
        }
        if (attributeLines.size() > 1) {
            log.error("请注意,该Listing存在多个part_number,headId:{},line:{}", id, JSON.toJSONString(attributeLines));
            // 有多个part_number 删掉多余的
            for (int i = 1; i < attributeLines.size(); i++) {
                listingAmazonAttributeLineService.deleteListingAmazonAttributeLineById(attributeLines.get(i).getId());
            }
        }

        ListingAmazonAttributeLine attributeLine = attributeLines.get(0);
        if (Objects.equals(attributeLine.getTableValue(), "0") || Objects.equals(attributeLine.getTableValue(), "1") || StrUtil.isBlank(attributeLine.getTableValue())) {
            String platformCode = createPlatformCode.getPlatformCode(goodsHead.getPdmGoodsCode(), goodsHead.getSiteCode());
            attributeLine.setTableValue(platformCode);
            listingAmazonAttributeLineService.updateListingAmazonAttributeLine(attributeLine);
        }
    }


    /**
     * 批量刊登temu Listing
     *
     * @param ids Listing ID集合
     */
    public AjaxResult publishTemuListingByIds(String ids, Long userId) {
        // 记录刊登日志
        String[] idsArr = Convert.toStrArray(ids);
        List<Integer> idList = Arrays.stream(idsArr).map(f -> Integer.valueOf(f)).collect(Collectors.toList());

        for (int j = 0; j < idsArr.length; j++) {
            ListingLog listingLog = new ListingLog();
            listingLog.setStatus(0);
            String id = idsArr[j];
            try {
                TemuGoodsHead goodsHead = temuGoodsHeadService.selectTemuGoodsHeadById(id);
                if (Objects.isNull(goodsHead)) {
                    throw new BusinessException("temu商品不存在.");
                }
                if(ObjectUtil.isNotEmpty(goodsHead.getPlatformGoodsSpu())){
                    throw new BusinessException("不允许重复刊登");
                }
                if (j == 0) {
                    goodsTaskService.insertGoodsPendingProcessingTask(goodsHead.getPlatform(), GoodsTaskTypeEnum.BATCH_PUBLISH, idList, String.valueOf(userId));
                }
                List<TemuListingSpecs> temuListingSpecs = temuListingSpecsService.selectListByGoodsId(Long.valueOf(id));
                for (TemuListingSpecs temuListingSpec : temuListingSpecs) {
                    String price = commonInfoBiz.checkPriceAndReturnSellerPrice(null,temuListingSpec.getPdmGoodsCode(), temuListingSpec.getSupplyPrice(), null,goodsHead.getSiteCode(),null,temuListingSpec.getSupplyPrice());

                    TemuListingSpecs updateSpecs = new TemuListingSpecs();
                    updateSpecs.setId(temuListingSpec.getId());
                    updateSpecs.setSupplyPrice(price);
                    updateSpecs.setUpdateBy(userId+"");
                    temuListingSpecsService.updateTemuListingSpecs(updateSpecs);
                }
                TemuGoodsHead updateHead = new TemuGoodsHead();
                updateHead.setId(goodsHead.getId());
                updateHead.setPublishStatus(PublishStatus.PUBLISHING.getType());
                temuGoodsHeadService.updateTemuGoodsHead(updateHead);

            } catch (Exception e) {
                listingLog.setStatus(1);
                listingLog.setErrorMsg(e.getMessage());

                List<GoodsTaskTypeEnum> taskTypeEnum = new ArrayList<>();
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(id), taskTypeEnum, GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }finally {
                listingLog.setDetails("批量刊登Listing");
                listingLog.setOperName(String.valueOf(userId));
                listingLog.setOperTime(new Date());
                listingLog.setListingId(Integer.valueOf(id));
//                listingLogService.insertListingLog(listingLog);
            }
        }
        return AjaxResult.success();
    }


    private static BigDecimal getPrice(String tableValue) {
        return new BigDecimal(tableValue);
    }

    public void listingCopyToShop(ListingCopyDTO listingCopyDTO) {
        String goodsIds = listingCopyDTO.getGoodsIds();
        String shopCode = listingCopyDTO.getShopCode();
        Integer descriptionId = listingCopyDTO.getDescriptionId();
        String brandCode = listingCopyDTO.getBrandCode();
        Integer publishType = listingCopyDTO.getPublishType();
        String amazonPublishType = listingCopyDTO.getAmazonPublishType();
        Integer copyCount = listingCopyDTO.getCopyCount();
        String platform = listingCopyDTO.getPlatform();
        Integer oldPriceType = listingCopyDTO.getOldPriceType();
        String oldPrice = listingCopyDTO.getOldPrice();
        Integer standardPriceType = listingCopyDTO.getStandardPriceType();
        String standardPrice = listingCopyDTO.getStandardPrice();
        boolean isFollowSold = ObjUtil.equals(listingCopyDTO.getIsFollowSold(), "1");
        String followSoldData = listingCopyDTO.getFollowSoldData();
        Map<Integer, ListingCopyDTO.CopyInfo> copyInfoMap = new HashMap<>();
        if (StrUtil.isNotBlank(followSoldData)) {
            praseCopyInfoMap(followSoldData, copyInfoMap);
            checkPrice(copyInfoMap);
        }

        if (StringUtils.isEmpty(goodsIds)) {
            throw new BusinessException("缺少主键ID");
        }
        if (StringUtils.isEmpty(shopCode)) {
            throw new BusinessException("请选择店铺");
        }
        if (Objects.isNull(copyCount)) {
            throw new BusinessException("请输入复制次数");
        }
        //如果是跟卖或是亚马逊平台
        if (StrUtil.isNotEmpty(amazonPublishType) && (isFollowSold || ObjUtil.equals(platform, PlatformTypeEnum.AM.name()))){
                publishType= Integer.valueOf(amazonPublishType);
                platform=PlatformTypeEnum.AM.name();
        }
        if (Objects.isNull(publishType)) {
            throw new BusinessException("请输入刊登类型");
        }

        List<String> goodsIdList = new ArrayList<>();
        List<String> shopCodeList = new ArrayList<>();
        if (goodsIds.contains(",")) {
            List<String> goodIds = Arrays.stream(goodsIds.split(",")).collect(Collectors.toList());
            goodsIdList.addAll(goodIds);
        } else {
            goodsIdList.add(goodsIds);
        }
        if( isFollowSold && ( ObjUtil.equals(publishType,PublishType.VCDF.getType()) || ObjUtil.equals(publishType,PublishType.VCPO.getType())) ){
            List<String> list = goodsHeadService.listFollowSoldAsin(String.valueOf(publishType), goodsIdList);
            if (CollUtil.isNotEmpty(list)){
                throw new BusinessException("其中ASIN:"+JSON.toJSONString(list)+",已经被跟卖至:"+PublishType.getTypeName(publishType));
            }
        }

        if (shopCode.contains(",")) {
            List<String> shopCodes = Arrays.stream(shopCode.split(",")).collect(Collectors.toList());
            shopCodeList.addAll(shopCodes);
        } else {
            shopCodeList.add(shopCode);
        }
        
        // AM平台
        if (ObjUtil.equals(platform, PlatformTypeEnum.AM.name())) {
            Map<String, GoodsDetailDTO> goodsDetailMap = new HashMap<>();
            // 判断是否可刊登
            for (String goodsId : goodsIdList) {
                GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(Integer.valueOf(goodsId));
                if (Objects.isNull(goodsHead)) {
                    throw new BusinessException("商品不存在");
                }
                String brand = StrUtil.isBlank(brandCode) ? goodsHead.getBrandCode() : brandCode;
                String goodsCode = goodsHead.getPdmGoodsCode();
               
                // 判断是否可刊登
                for (String subCode : shopCodeList) {
                    amazonProductBiz.canPutListing(brand, goodsCode, goodsDetailMap, subCode);
                }
            }
        }
    

        //开始复制
        Long userId = ShiroUtils.getUserId();
        String userName = ShiroUtils.getSysUser().getUserName();

        //初始化任务
        int taskNum = shopCodeList.size() * goodsIdList.size() * copyCount;
        GoodsTask goodsTask = goodsTaskService.insertListingTotalTask(isFollowSold?GoodsTaskTypeEnum.ONE_KEY_FOLLOW:GoodsTaskTypeEnum.LISTING_COPY, platform, taskNum, String.valueOf(userId));

        GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
        goodsTaskInfo.setTaskId(goodsTask.getId());
        goodsTaskInfo.setPlatform(platform);
        goodsTaskInfo.setCreateBy(String.valueOf(userId));

        Integer finalPublishType = publishType;
        String finalPlatform = platform;
        copyConfig.execute(() -> {
            for (String shop : shopCodeList) {
                for (String goodsId : goodsIdList) {
                    ListingCopyDTO.CopyInfo copyInfo = copyInfoMap.get(Integer.valueOf(goodsId));
                    for (Integer i = 0; i < copyCount; i++) {
                        String headFlag = shop + "_" + goodsId + "_" + (i + 1);
                        try {
                            ListingCopyToShopDTO dto = ListingCopyToShopDTO.builder()
                                    .ids(goodsId)
                                    .shopCode(shop)
                                    .descriptionId(descriptionId)
                                    .brandCode(brandCode)
                                    .publicationDay(listingCopyDTO.getPublicationDay())
                                    .standardPrice(standardPrice)
                                    .standardPriceType(standardPriceType)
                                    .oldPrice(oldPrice)
                                    .oldPriceType(oldPriceType)
                                    .startSellingCount(listingCopyDTO.getStartSellingCount())
                                    .bestOfferFlag(listingCopyDTO.getBestOfferFlag())
                                    .publishType(finalPublishType)
                                    .reservePrice(listingCopyDTO.getReservePrice())
                                    .userId(userId)
                                    .userName(userName)
                                    .platform(finalPlatform)
                                    .costPrice(copyInfo == null || copyInfo.getCostPrice() == null ? null : copyInfo.getCostPrice().toPlainString())
                                    .listPrice(copyInfo == null || copyInfo.getListPrice() == null ? null : copyInfo.getListPrice().toPlainString())
                                    .gtin(copyInfo == null || copyInfo.getGtin() == null ? null : copyInfo.getGtin().trim())
                                    .attributeMap(copyInfo == null || copyInfo.getAttributeMap() == null ? null : copyInfo.getAttributeMap())
                                    .build();
                            List<Integer> ids = this.listingCopy(dto,isFollowSold);
                            goodsTaskInfo.setListingHeadId(String.valueOf(ids.get(0)));
                            goodsTaskService.addSuccessNum(goodsTask.getId());
                            goodsTaskInfo.setStatus(GoodsTaskSubStatusEnum.NORAML.getSubStatus());
                            goodsTaskInfoService.insertGoodsTaskInfo(goodsTaskInfo);
                        } catch (Exception e) {
                            log.error("Listing复制失败", e);
                            goodsTaskInfo.setListingHeadId(null);
                            goodsTaskInfo.setStatus(GoodsTaskSubStatusEnum.ERROR.getSubStatus());
                            goodsTaskInfo.setErrorMsg(headFlag + ",复制失败:" + e.getMessage());
                            goodsTaskInfoService.insertGoodsTaskInfo(goodsTaskInfo);
                        }
                    }
                }
            }
            //复制/跟卖直接任务完成
            if (ObjUtil.isNotEmpty(goodsTask.getId())) {
                goodsTaskService.updateGoodsTaskStatus(GoodsTaskStatusEnum.COMPLETED.getInfo(), goodsTask.getId());
            }
        });

    }

    private void checkPrice(Map<Integer, ListingCopyDTO.CopyInfo> copyInfoMap) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<Integer, ListingCopyDTO.CopyInfo> entry : copyInfoMap.entrySet()) {
            ListingCopyDTO.CopyInfo copyInfo = entry.getValue();
            if (copyInfo.getCostPrice() != null && copyInfo.getListPrice() != null && copyInfo.getCostPrice().compareTo(copyInfo.getListPrice()) > 0) {
                sb.append("商品ID:").append(entry.getKey()).append(", Cost Price不能大于List Price;");
            }
        }
        if (sb.length() > 0) {
            throw new BusinessException(sb.toString());
        }
    }

    private static void praseCopyInfoMap(String followSoldData, Map<Integer, ListingCopyDTO.CopyInfo> copyInfoMap) {
        JSONArray jsonArray = JSON.parseArray(followSoldData);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Integer id = jsonObject.getInteger("id");
            ListingCopyDTO.CopyInfo copyInfo = copyInfoMap.get(id);
            if (Objects.isNull(copyInfo)) {
                copyInfo = new ListingCopyDTO.CopyInfo();
                copyInfo.setId(id);
                copyInfoMap.put(id, copyInfo);
            }
            String propNodePath = jsonObject.getString("propNodePath");
            String tableValue = jsonObject.getString("tableValue");
            if (StrUtil.isBlank(propNodePath) || StrUtil.isBlank(tableValue)) {
                continue;
            }
            tableValue = tableValue.trim();

            switch (propNodePath) {
                case "Cost Price":
                    copyInfo.setCostPrice(getPrice(tableValue));
                    break;
                case "List Price":
                    copyInfo.setListPrice(getPrice(tableValue));
                    break;
                case "GTIN":
                    copyInfo.setGtin(tableValue);
                    break;
                default:
                    Map<String, String> attributeMap = copyInfo.getAttributeMap();
                    if (Objects.isNull(attributeMap)) {
                        attributeMap = new HashMap<>();
                    }
                    attributeMap.put(propNodePath, tableValue);
                    copyInfo.setAttributeMap(attributeMap);
                    break;
            }
        }
    }


    private BigDecimal getPrice(JSONObject jsonObject,String col) {
//        if (!jsonObject.containsKey(col)){
//            return null;
//        }
        try {
            String string = jsonObject.getString(col);
            return StringUtils.isNotEmpty(string) ? new BigDecimal(string.trim()) : null;
        } catch (Exception e) {
            log.error("Listing复制失败,col:{},jsonObject:{}",col,JSON.toJSONString(jsonObject), e);
            BigDecimal bigDecimal = jsonObject.getBigDecimal(col);
            return bigDecimal;
        }
    }

    public List<ConfigRequiredField> getListingAttributeByEbayHeadIds(List<Integer> listingIds) {
        List<ConfigRequiredField> fieldAll = new ArrayList<>();
        List<ListingEbayLine> ebayLines = ebayGoodsLineService.selectListingEbayLineByHeadIds(listingIds.stream().toArray(Integer[]::new));
        Map<Integer, List<ListingEbayLine>> lineMap = ebayLines.stream().collect(Collectors.groupingBy(ListingEbayLine::getListingHeadId));

        Map<Integer, List<ListingEbayValue>> ebayAttribute = ebayValueService.selectListingEbayValueByLineIds(ebayLines.stream().map(ListingEbayLine::getId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(ListingEbayValue::getListingLineId));

        Map<String, String> hashMap = new HashMap<>();

        for (Integer goodsId : listingIds) {
            List<ListingEbayValue> listingEbayValues = ebayAttribute.get(lineMap.get(goodsId).get(0).getId());
            for (ListingEbayValue listingEbayValue : listingEbayValues) {
                if (hashMap.containsKey(listingEbayValue.getName())) {
                    continue;
                }
                hashMap.put(listingEbayValue.getName(), listingEbayValue.getValue());
                ConfigRequiredField info = new ConfigRequiredField();
                info.setAttributeCode(listingEbayValue.getName());
                info.setAttributeName(listingEbayValue.getName());
//                info.setAttributeMemo();
                info.setAttributeValue(listingEbayValue.getValue());
                fieldAll.add(info);
            }
        }
        return fieldAll;
    }


    /**
     * Eb  获取已填的和必填的属性
     *
     * @param requiredFieldList
     * @param listingIds
     * @return
     */
    public List<ConfigRequiredField> getListingAttributeByEbayHeadIds(List<ConfigRequiredField> requiredFieldList, List<Integer> listingIds) {
        if (CollUtil.isEmpty(listingIds)) {
            return requiredFieldList;
        }
        List<ConfigRequiredField> fieldAll = new ArrayList<>();
        List<ListingEbayLine> ebayLines = ebayGoodsLineService.selectListingEbayLineByHeadIds(listingIds.stream().toArray(Integer[]::new));
        Map<Integer, List<ListingEbayLine>> lineMap = ebayLines.stream().collect(Collectors.groupingBy(ListingEbayLine::getListingHeadId));

        Map<Integer, List<ListingEbayValue>> ebayAttribute = ebayValueService.selectListingEbayValueByLineIds(ebayLines.stream().map(ListingEbayLine::getId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(ListingEbayValue::getListingLineId));

        Map<String, String> hashMap = new HashMap<>();

        // 用户手动输的
        for (Integer goodsId : listingIds) {
            List<ListingEbayValue> listingEbayValues = ebayAttribute.get(lineMap.get(goodsId).get(0).getId());
            if (CollUtil.isEmpty(listingEbayValues)) {
                continue;
            }
            for (ListingEbayValue listingEbayValue : listingEbayValues) {
                if (hashMap.containsKey(listingEbayValue.getName())) {
                    continue;
                }
                hashMap.put(listingEbayValue.getName(), listingEbayValue.getValue());
                ConfigRequiredField info = new ConfigRequiredField();
                info.setAttributeCode(listingEbayValue.getName());
                info.setAttributeName(listingEbayValue.getName());
//                info.setAttributeMemo();
                info.setAttributeValue(listingEbayValue.getValue());
                fieldAll.add(info);
            }
        }

        // 品类配置中的
        for (ConfigRequiredField configRequiredField : requiredFieldList) {
            if (hashMap.containsKey(configRequiredField.getAttributeName())) {
                continue;
            }
            hashMap.put(configRequiredField.getAttributeName(), configRequiredField.getAttributeValue());
            ConfigRequiredField info = new ConfigRequiredField();
            info.setAttributeCode(configRequiredField.getAttributeName());
            info.setAttributeName(configRequiredField.getAttributeName());
//                info.setAttributeMemo();
            info.setAttributeValue(configRequiredField.getAttributeValue());
            fieldAll.add(info);
        }
        return fieldAll;
    }


    /**
     * AM 获取已填的和必填的属性
     *
     * @param requiredFieldList
     * @param listingIds
     * @return
     */
    public List<ConfigRequiredField> getAMListingAttributeByEbayHeadIds(List<ConfigRequiredField> requiredFieldList, List<Integer> listingIds) {
        if (CollUtil.isEmpty(listingIds)) {
            return requiredFieldList;
        }
        List<ConfigRequiredField> fieldAll = new ArrayList<>();

        List<ListingAmazonAttributeLine> lines = listingAmazonAttributeLineService.selectByGoodsIds(listingIds);
        Map<Integer, List<ListingAmazonAttributeLine>> linesMap = lines.stream().collect(Collectors.groupingBy(ListingAmazonAttributeLine::getGoodsId));


        Map<String, String> hashMap = new HashMap<>();

        // 品类配置中的
        for (ConfigRequiredField configRequiredField : requiredFieldList) {
            if (hashMap.containsKey(configRequiredField.getAttributeName())) {
                continue;
            }
            hashMap.put(configRequiredField.getAttributeName(), configRequiredField.getAttributeValue());
            ConfigRequiredField info = new ConfigRequiredField();
            info.setAttributeType(Long.valueOf(configRequiredField.getAttributeType()));
            info.setIsRequire(String.valueOf(1));
            info.setAttributeCode(configRequiredField.getAttributeName());
            info.setAttributeName(configRequiredField.getAttributeName());
            info.setAttributeValue(configRequiredField.getAttributeValue());
            fieldAll.add(info);
        }

        for (Integer goodsId : listingIds) {
            List<ListingAmazonAttributeLine> lineList = linesMap.get(goodsId);
            if (CollUtil.isEmpty(lineList)) {
                continue;
            }
            for (ListingAmazonAttributeLine lineValue : lineList) {
                if (hashMap.containsKey(lineValue.getTableName())) {
                    continue;
                }
                hashMap.put(lineValue.getTableName(), lineValue.getTableValue());
                ConfigRequiredField info = new ConfigRequiredField();
                info.setAttributeCode(lineValue.getTableName());
                info.setAttributeName(lineValue.getTableName());
                info.setAttributeValue(lineValue.getTableValue());
                info.setAttributeType(Long.valueOf(lineValue.getTableType()));
                info.setCategoryId(String.valueOf(lineValue.getCategoryId()));
                info.setIsRequire(String.valueOf(0));
                fieldAll.add(info);
            }
        }
        return fieldAll;
    }


    public boolean cancelScheduledPublish(String ids, Long userId) {
        List<String> idList = CommonUtilsSmc.toStringList(ids);
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        //根据idList查询数据
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(Convert.toIntArray(ids));
        TaskConfiguration taskConfiguration = new TaskConfiguration();
        taskConfiguration.setHeadIdList(goodsHeads.stream().map(GoodsHead::getId).collect(Collectors.toList()));
        taskConfiguration.setIsSuccess(String.valueOf(SMCCommonEnum.DEFEAT.getValue()));
        List<TaskConfiguration> taskConfigurations = taskConfigurationService.selectTaskConfigurationList(taskConfiguration);
        List<String> relistIdList = new ArrayList<>();
        for (GoodsHead goodsHead : goodsHeads) {
            cancelByPublishStatus(userId, taskConfigurations, relistIdList, goodsHead);
        }
        //删除定时重上架任务表
        if (ObjectUtils.isNotEmpty(relistIdList)) {
            taskConfigurationService.deleteTaskConfigurationByHeadId(relistIdList);
        }
        return true;
    }

    /**
     * 根据刊登状态取消定时刊登任务
     * 兼容定时刊登和定时重上架
     *
     * @param userId
     * @param taskConfigurations
     * @param relistIdList
     * @param goodsHead
     */
    private void cancelByPublishStatus(Long userId, List<TaskConfiguration> taskConfigurations, List<String> relistIdList, GoodsHead goodsHead) {
        if (!PublishStatus.getTimeStatus().contains(goodsHead.getPublishStatus())) {
            throw new BusinessException("主键为:" + goodsHead.getId() + "的数据状态不是定时刊登状态，请重新选择");
        }
        //定时刊登
        if (PublishStatus.TOBEPUBLISHING.getType().equals(goodsHead.getPublishStatus())) {
            String key = TIME_PUBLISHING + goodsHead.getId();
            String cacheObjectStr = redisService.getCacheObject(key);
            ScheduledPublishDTO scheduledPublishDTO = JSON.parseObject(cacheObjectStr, ScheduledPublishDTO.class);
            //时间到了 不能取消
            if (ObjectUtils.isNotEmpty(scheduledPublishDTO) && System.currentTimeMillis() > DateUtils.parseDate(scheduledPublishDTO.getScheduledBeginTime()).getTime()) {
                throw new BusinessException("主键为:" + goodsHead.getId() + "的数据,已经到了刊登的时候,请重新选择");
            }
            redisService.deleteObject(key);
            listingLogService.insertSuccessListingLog("定时任务取消，数据由[定时刊登]移至[草稿]", String.valueOf(userId), goodsHead.getId());
            goodsHead.setPublishStatus(PublishStatus.getCancelStatus(goodsHead.getPublishStatus()));
        }
        //定时重上架
        if (PublishStatus.TIMED_RE_LISTING.getType().equals(goodsHead.getPublishStatus())) {
            TaskConfiguration task = taskConfigurations.stream().filter(f -> Objects.equals(f.getHeadId(), String.valueOf(goodsHead.getId()))).findFirst()
                    .orElseThrow(() -> new BusinessException("主键为:" + goodsHead.getId() + "的数据,已经取消定时重上架任务,请重新选择"));

            //时间到了 不能取消
            if (ObjectUtils.isNotEmpty(task) && System.currentTimeMillis() > task.getBeginTime().getTime()) {
                throw new BusinessException("主键为:" + goodsHead.getId() + "的数据,已经到了刊登的时候,请重新选择");
            }
            listingLogService.insertSuccessListingLog("定时任务取消，数据由[定时重上架]移至[草稿]", String.valueOf(userId), goodsHead.getId());
            goodsHead.setPublishStatus(getRelistCancelStatus(goodsHead));
            relistIdList.add(String.valueOf(goodsHead.getId()));
        }
        //循环定时刊登
        if (PublishStatus.TIMED_CIRCULATE_LISTING.getType().equals(goodsHead.getPublishStatus())) {
            Integer headId = goodsHead.getId();

            listingLogService.insertSuccessListingLog("定时任务取消，数据由[循环定时刊登]移至[草稿]", String.valueOf(userId), headId);
            goodsHead.setPublishStatus(PublishStatus.getCancelStatus(goodsHead.getPublishStatus()));

            //删除定时刊登任务池  通过headId查询
            CirculateTimedTaskListingPool pool = circulateTimedTaskListingPoolService.selectCirculateTimedTaskListingPoolByHeadId(Long.valueOf(headId));
            circulateTimedTaskListingPoolService.deleteCirculateTimedTaskListingPoolById(pool.getId());
        }

        List<GoodsTaskTypeEnum> goodsTaskTypeEnums = CollUtil.newArrayList(GoodsTaskTypeEnum.TIMING_PUBLISH, GoodsTaskTypeEnum.TIMING_RESURFACE, GoodsTaskTypeEnum.CIRCULATE_TIMED_TASK);
        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), goodsTaskTypeEnums, GoodsTaskSubStatusEnum.NORAML, "任务已取消");
        goodsHeadService.updateListingGoodsHead(goodsHead);
    }

    /**
     * 根据平台商品id获取定时重上架的状态
     *
     * @param goodsHead
     * @return
     */
    private Integer getRelistCancelStatus(GoodsHead goodsHead) {
        if (ObjectUtils.isEmpty(goodsHead) || StringUtils.isEmpty(goodsHead.getPlatformGoodsId())) {
            return PublishStatus.OFF_SALE.getType();
        }
        AjaxResult itemResp = retry.retryableToApiMsg(() -> ebayPlatformListingService.getEbayItem(goodsHead));
        if (!itemResp.isSuccess()) {
            return PublishStatus.OFF_SALE.getType();
        }
        ItemType item = JSONObject.toJavaObject((JSONObject)itemResp.get(AjaxResult.DATA_TAG) , ItemType.class);
        if (ObjectUtils.equals(item.getSellingStatus().getListingStatus().value(), "Active")) {
            return PublishStatus.SALEING.getType();
        }
        return PublishStatus.OFF_SALE.getType();
    }

    public void handleRelistItem(TaskConfiguration taskConfiguration) {
        String model = taskConfiguration.getModel();
        if (StringUtils.isBlank(model)) {
            throw new BusinessException("请选择上架模式");
        }
        if (StringUtils.isBlank(taskConfiguration.getHeadId())) {
            throw new BusinessException("请选择要上架的商品");
        }
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(Convert.toIntArray(taskConfiguration.getHeadId()));
        //只取在售、更新失败、非在售、已关闭、已结束的商品   ,并六个小时内刊登或者重上架的listing不能再次重上架
        goodsHeadList = goodsHeadList.stream()
                .filter(f -> PublishStatus.isRePublishStatus(f.getPublishStatus()) && f.getPlatform().equals(PlatformTypeEnum.EB.name()))
                .map(f -> redisService.exists(RedisKeyEnum.EBAY_PUSH_Listing.getKey() + f.getId()) ? null : f)
                .filter(f -> ObjUtil.isNotEmpty(f))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsHeadList)) {
            throw new BusinessException("只有在售、更新失败、非在售、已关闭、已结束的EB平台的商品才能重上架,并六个小时内刊登或重上架的listing不能再次重上架,请重新选择.");
        }
        List<Integer> headIds = goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList());

        //直接重上架
        if (ObjUtil.equals(String.valueOf(SMCCommonEnum.DIRECT_RE_LISTING.getValue()), model)) {
            Map<String, List<GoodsHead>> platformMap = goodsHeadList.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform));
            goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.DIRECT_RESURFACE, headIds, taskConfiguration.getCreateBy());

            listingInfoService.relistItem(taskConfiguration.getCreateBy(), new HashMap<>(), platformMap);
        } else {
            //定时重上架或者间隔重上架
//            handleRelistItem(taskConfiguration, goodsHeadList);
            handleRelistItem(taskConfiguration, headIds);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleRelistItem(TaskConfiguration taskConfiguration, List<Integer> idsArr) {
        //处理定时重上架的时区
        handleUSAtime(taskConfiguration);

        String model = taskConfiguration.getModel();
        Integer intervalTime = taskConfiguration.getIntervalTime();
        Integer intervalSize = taskConfiguration.getIntervalSize();
        Date beginTime = taskConfiguration.getBeginTime();
        Date endTime = taskConfiguration.getEndTime();
        Date offTime = taskConfiguration.getOffTime();
        List<TaskConfiguration> taskConfigurationList = new ArrayList<TaskConfiguration>();
        //保存日志
        List<ListingLog> logList = new ArrayList<>();
        //保存信息到定时任务配置表
        for (int i = 0; i < idsArr.size(); i++) {
            Integer headId = idsArr.get(i);
            TaskConfiguration task = new TaskConfiguration();
            task.setIsSuccess(String.valueOf(SMCCommonEnum.DEFEAT.getValue()));
            task.setBeginTime(beginTime);
            task.setEndTime(endTime);
            if (!ObjectUtils.isEmpty(offTime)) {
                if (beginTime.before(offTime)) {
                    throw new BusinessException("下架时间不能晚于开始刊登时间");
                }
                task.setOffTime(offTime);
                task.setIsOff(String.valueOf(SMCCommonEnum.DEFEAT.getValue()));
            }
            task.setModel(model);
            task.setPlatform(PlatformTypeEnum.EB.name());
            task.setHeadId(String.valueOf(headId));
            task.setCreateTime(DateUtils.getNowDate());
            task.setCreateBy(taskConfiguration.getCreateBy());
            task.setUpdateTime(DateUtils.getNowDate());
            task.setUpdateBy(taskConfiguration.getUpdateBy());
            //间隔刊登
            if (SMCCommonEnum.INTERVAL_RE_LISTING.getValue().toString().equals(model)) {
                // 间隔刊登
                if (i % intervalSize == 0) {
                    beginTime = DateUtils.addMinutes(beginTime, intervalTime);
                }
            }
            taskConfigurationList.add(task);
            ListingLog listingLog = new ListingLog();
            listingLog.setListingId(headId);
            listingLog.setOperName(task.getCreateBy());
            listingLog.setOperTime(new Date());
            listingLog.setCreateTime(new Date());
            listingLog.setStatus(0);
            listingLog.setDetails("商品定时重上架,开始时间:" + DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", task.getBeginTime()));
            logList.add(listingLog);
        }
        //插入定时任务表
        taskConfigurationService.insertList(taskConfigurationList);
        //插入日志
        listingLogService.insertListingLogList(logList);

        //更新头表状态
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setIdList(idsArr.stream().map(f -> String.valueOf(f)).collect(Collectors.toList()));
        goodsHead.setPublishStatus(PublishStatus.TIMED_RE_LISTING.getType());
        goodsHead.setUpdateBy(StringUtils.isNotBlank(taskConfiguration.getUpdateBy()) ? taskConfiguration.getUpdateBy() : taskConfiguration.getCreateBy());
        goodsHeadService.updateListingGoodsHeadByIdList(goodsHead);

        goodsTaskService.insertGoodsPendingProcessingTask(PlatformTypeEnum.EB.name(), GoodsTaskTypeEnum.TIMING_RESURFACE, idsArr, taskConfiguration.getCreateBy());
    }




    /**
     * 根据模块id获取模块字段
     *
     * @param id
     * @param amazonMap
     * @return
     */
    public Map<String, String> getListingFieldMapByModule(Integer id, Map<Integer, Map<String, String>> amazonMap) {
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(id);
        if (ObjectUtils.isEmpty(goodsHead)) {
            throw new BusinessException("根据id:{}查询商品头表数据为空" + id);
        }
        if (ObjectUtils.isEmpty(amazonMap)) {
            throw new BusinessException("listing数据未做任何变动,无需更新,id:{}" + id);
        }
        Map<String, String> amazonListingMap = amazonMap.get(id);
        if (ObjectUtils.isEmpty(amazonListingMap)) {
            throw new BusinessException("listing数据未做任何变动,无需更新,id:{}" + id);
        }
        amazonListingMap.put("update_delete", "PartialUpdate");
        amazonListingMap.put("publish_type", String.valueOf(goodsHead.getPublishType()));
        amazonListingMap.put("site_code", String.valueOf(goodsHead.getSiteCode()));
        //Listing类目name
        List<CategoryInfo> categoryInfoList = categoryInfoService.getCategoryInfoListByCategoryId(String.valueOf(goodsHead.getCategoryId()));
        if (!CollectionUtils.isEmpty(categoryInfoList)) {
            amazonListingMap.put("category_id", String.valueOf(goodsHead.getCategoryId()));
            amazonListingMap.put("category", categoryInfoList.get(0).getCategoryName());
            amazonListingMap.put("feed_product_type", categoryInfoList.get(0).getProductType());
            amazonListingMap.put("item_type", categoryInfoList.get(0).getItemTypeKeyword());

//            PlatformCategory platformCategory = this.judgeNewTemplateExistByCategoryId(categoryInfoList.get(0).getCategoryId());
//            if(ObjUtil.isNotEmpty(platformCategory)) {
//                //新模板
//                amazonListingMap.put("feed_product_type", platformCategory.getProductType());
//                amazonListingMap.put("item_type", platformCategory.getItemTypeKeyword());
//            }else {
//                amazonListingMap.put("feed_product_type", categoryInfoList.get(0).getProductType());
//                amazonListingMap.put("item_type", categoryInfoList.get(0).getItemTypeKeyword());
//            }
        }
        amazonListingMap.put("platform_goods_id", goodsHead.getPlatformGoodsId());
        amazonListingMap.put("platform_goods_code", goodsHead.getPlatformGoodsCode());
        //fba就不上传库存了
        if (Objects.equals(String.valueOf(amazonListingMap.get("publish_type")), "1")) {
            amazonListingMap.remove("stock_on_sales_qty");
            amazonListingMap.put("fulfillment_center_id", "AMAZON_NA");

        } else {
            amazonListingMap.put("fulfillment_center_id", "DEFAULT");
        }
        if (amazonListingMap.containsKey("sale_price") && amazonListingMap.containsKey("sale_from_date")  && amazonListingMap.containsKey("sale_end_date")){
            amazonListingMap.put("standard_price", goodsHead.getStandardPrice());
        }
        return amazonListingMap;
    }

    /**
     * 批量修改listing 支持修改本地数据 以及线上数据指定内容 更新到平台
     *
     * @param batchInfo
     * @return
     */
    public List<ItemDTO> batchListing(ListingBatchEditDTO batchInfo) {
        Long userId = ShiroUtils.getSysUser().getUserId();
        Integer[] idArray = Arrays.stream(batchInfo.getIds().split(",")).map(Integer::valueOf).toArray(Integer[]::new);
        if (CollUtil.isEmpty(Arrays.asList(idArray))) {
            return null;
        }
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(idArray);
        goodsHeads.forEach(e -> {
            if (PublishStatus.getNoUpdateStatus().contains(e.getPublishStatus())) {
                throw new BusinessException("刊登中,更新中,下架中的Listing不允许批量修改");
            }
        });

        if (ListingBatchOptions.COVER_RED_LINE_PRICE.name().equals(batchInfo.getPriceType())) {
            boolean anyRedLinePriceInvalid = goodsHeads.stream()
                    .anyMatch(goodsHead -> goodsHead.getRedLinePrice() == null
                            || goodsHead.getRedLinePrice().compareTo(BigDecimal.ZERO) <= 0);
            if (anyRedLinePriceInvalid) {
                throw new BusinessException("刊登中,更新中,红线价为0或不存在，覆盖失败");
            }
        }

        if (CollUtil.isEmpty(goodsHeads)) {
            return null;
        }

        //简单校验  and 是否包含EB
        boolean EB = isEbAndCheck(goodsHeads, batchInfo.getItemList(), batchInfo);

        List<Integer> idList = Arrays.stream(idArray).collect(Collectors.toList());
        goodsTaskService.insertGoodsPendingProcessingTask(goodsHeads.get(0).getPlatform(), GoodsTaskTypeEnum.BATCH_MODIFY, idList, String.valueOf(userId));

        //组装相关数据
        ListingBatchEditSaveDTO listingBatchEditSaveDTO = packageGoodsInfo(batchInfo, idArray, goodsHeads, EB);

        //入库
        batchEditIntoDB(batchInfo, listingBatchEditSaveDTO);

        return listingBatchEditSaveDTO.getItemDTOList();
    }

    /**
     * 批量修改入库
     *
     * @param batchInfo
     * @param listingBatchEditSaveDTO
     */
    private void batchEditIntoDB(ListingBatchEditDTO batchInfo, ListingBatchEditSaveDTO listingBatchEditSaveDTO) {
        String location = batchInfo.getLocation();  //物品所在地
        String descTemplate = batchInfo.getDescTemplate();  //描述模板
        String category = batchInfo.getCategory();  //分类
        String shopCategory = batchInfo.getShopCategory();  //店铺分类

        List<GoodsHead> goodsUpdateList = listingBatchEditSaveDTO.getGoodsUpdateList();
        List<ListingEbayValue> updateList = listingBatchEditSaveDTO.getUpdateList();
        List<ListingEbayValue> addList = listingBatchEditSaveDTO.getAddList();
        List<ListingEbayValue> deleteList = listingBatchEditSaveDTO.getDeleteList();
        Set<Integer> headIdList = listingBatchEditSaveDTO.getLocationHeadIdList();
        List<Integer> descTemplateHeadIdList = listingBatchEditSaveDTO.getDescTemplateHeadIdList();
        List<Integer> shopCategoryHeadIdList = listingBatchEditSaveDTO.getShopCategoryHeadIdList();
        List<ItemDTO> itemDTOList = listingBatchEditSaveDTO.getItemDTOList();

        // 更新商品明细信息
        if (!goodsUpdateList.isEmpty()) {
            for (GoodsHead goodsHead : goodsUpdateList) {
                goodsHeadService.updateListingGoodsHead(goodsHead);
            }
        }
        // 更新属性信息
        if (!updateList.isEmpty()) {
            for (ListingEbayValue v : updateList) {
                listingEbayValueService.updateListingEbayValue(v);
            }
        }
        if (!deleteList.isEmpty()) {
            for (ListingEbayValue v : deleteList) {
                listingEbayValueService.deleteListingEbayValueById(v.getId());
            }
        }
        // 更新描述模板
        if (!CollectionUtils.isEmpty(descTemplateHeadIdList)) {
            //首先查询是否存在描述模板
            for (Integer headId : descTemplateHeadIdList) {
                GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(headId);
                GoodsDescription desc = new GoodsDescription();
                desc.setGoodsIdList(Collections.singletonList(headId));
                desc.setDescriptionId(Integer.valueOf(descTemplate));
                if (ObjUtil.isEmpty(goodsDescription)) {
                    //插入
                    desc.setGoodsId(headId);
                    goodsDescriptionService.insertListingGoodsDescription(desc);
                } else {
                    StringBuffer descBuffer = new StringBuffer();
                    Boolean isClean = templateEbayDescriptionBiz.cleaningShortDescription(goodsDescription.getDetailDescription(), goodsDescription.getShortDescription(), descBuffer);
                    if (isClean) {
                        desc.setShortDescription(String.valueOf(descBuffer));
                    }
                    //更新
                    goodsDescriptionService.updateByGoodIds(desc);
                }
            }

        }
        // 更新eb物品所在地和ebay行分类
        if (!CollectionUtils.isEmpty(headIdList)) {
            ListingEbayLine listingEbayLine = new ListingEbayLine();
            listingEbayLine.setHeadList(new ArrayList<>(headIdList));
            listingEbayLine.setLocation(location);
            listingEbayLine.setFirstCategoryid(category);
            listingEbayLine.setFirstShopCategory(shopCategory);
            listingEbayLineService.updateListingEbayLineByHeadIdList(listingEbayLine);
        }
        //更新ebay店铺分类
        if (!CollectionUtils.isEmpty(shopCategoryHeadIdList)) {
            ListingEbayLine listingEbayLine = new ListingEbayLine();
            listingEbayLine.setHeadList(new ArrayList<>(shopCategoryHeadIdList));
            listingEbayLine.setFirstShopCategory(shopCategory);
            listingEbayLineService.updateListingEbayLineByHeadIdList(listingEbayLine);
        }

        if (!addList.isEmpty()) {
            listingEbayValueService.batchInsertListingEbayValue(addList);
        }
        itemDTOList.stream().forEach(itemDTO -> {
            //重新设置新的head数据
            itemDTO.setGoodsHead(goodsHeadService.selectListingGoodsHeadById(itemDTO.getGoodsHead().getId()));
        });
    }

    /**
     * 组装批量修改的数据
     *
     * @param batchInfo
     * @param idArray
     * @param goodsHeads
     * @param EB
     * @return
     */
    private ListingBatchEditSaveDTO packageGoodsInfo(ListingBatchEditDTO batchInfo, Integer[] idArray, List<GoodsHead> goodsHeads, boolean EB) {
        List<ListingEbayLine> listingEbayLines = new ArrayList<>();
        List<ListingEbayValue> listingEbayValues = new ArrayList<>();
        List<GoodsHead> goodsUpdateList = new ArrayList<>();
        List<ListingEbayValue> deleteList = new ArrayList<>();
        List<ListingEbayValue> updateList = new ArrayList<>();
        List<ListingEbayValue> addList = new ArrayList<>();
        Set<Integer> headIdList = new HashSet<>();// 需要更新的物品所在地头id
        List<Integer> descTemplateHeadIdList = new ArrayList<>();//需要更新描述模板的头表id
        List<Integer> shopCategoryHeadIdList = new ArrayList<>();//需要更新店铺分类的头表id

        List<ListingItem> itemList = batchInfo.getItemList();
        String location = batchInfo.getLocation(); //物品所在地
        String descTemplate = batchInfo.getDescTemplate(); //描述模板
        String category = batchInfo.getCategory(); //分类
        String shopCategory = batchInfo.getShopCategory(); //店铺分类

        //更新框架所需要指定更新模块
        List<String> moduleType = new ArrayList<>();
        List<ItemDTO> itemDTOList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(batchInfo.getItemList())) {
            moduleType.add(ListingModuleType.ATTRIBUTE.name());
        }
        if (ObjectUtils.isNotEmpty(batchInfo.getStockOnSalesQty())) {
            moduleType.add(ListingModuleType.INVENTORY.name());
        }
        if (ObjectUtils.isNotEmpty(batchInfo.getTitle())) {
            moduleType.add(ListingModuleType.TITLE.name());
        }
        if (ObjectUtils.isNotEmpty(batchInfo.getPlatformGoodsCode())) {
            moduleType.add(ListingModuleType.PLATFORM_PRODUCT_CODE.name());
        }
        if (ObjectUtils.isNotEmpty(batchInfo.getPrice()) || ListingBatchOptions.COVER_RED_LINE_PRICE.name().equals(batchInfo.getPriceType())) {
            moduleType.add(ListingModuleType.PRICE.name());
        }
        if (ObjectUtils.isNotEmpty(batchInfo.getDescTemplate())) {
            moduleType.add(ListingModuleType.DESCRIPTION.name());
        }
        if (ObjectUtils.isNotEmpty(batchInfo.getLocation())) {
            if (!moduleType.contains(ListingModuleType.EBAY_LINE.name())) {
                moduleType.add(ListingModuleType.EBAY_LINE.name());
            }
        }
        if (ObjectUtils.isNotEmpty(batchInfo.getShopCategory())) {
            if (!moduleType.contains(ListingModuleType.EBAY_LINE.name())) {
                moduleType.add(ListingModuleType.EBAY_LINE.name());
            }
        }
        if (ObjectUtils.isNotEmpty(batchInfo.getCategory())) {
            if (!moduleType.contains(ListingModuleType.EBAY_LINE.name())) {
                moduleType.add(ListingModuleType.EBAY_LINE.name());
            }
        }

        GoodsHead goodsItem;
        ListingEbayValue valueItem;

        if (!itemList.isEmpty() && EB) {
            listingEbayLines.addAll(listingEbayLineService.selectListingEbayLineByHeadIds(idArray));
            if (!listingEbayLines.isEmpty()) {
                listingEbayValues.addAll(
                        listingEbayValueService.selectListingEbayValueByLineIds(listingEbayLines
                                .stream()
                                .map(ListingEbayLine::getId)
                                .collect(Collectors.toList()))
                );
            }
        }

        for (GoodsHead goodsHead : goodsHeads) {
            List<ListingEbayValue> updateListSingle = new ArrayList<>();
            List<ListingEbayValue> addListSingle = new ArrayList<>();

            String platform = goodsHead.getPlatform();
            BigDecimal qty = batchInfo.getStockOnSalesQty();
            BigDecimal price = batchInfo.getPrice();
            String title = batchInfo.getTitle();
            String platformGoodsCode = batchInfo.getPlatformGoodsCode();
            String platformGoodsCodeType = batchInfo.getPlatformGoodsCodeType();
            String replaceChar = StringUtils.isBlank(batchInfo.getReplaceChar()) ? "" : batchInfo.getReplaceChar();
            goodsItem = new GoodsHead();
            goodsItem.setId(goodsHead.getId());
            goodsItem.setUpdateBy(String.valueOf(ShiroUtils.getUserId()));
            boolean isUpdateField = false;
            List<ListingAmazonAttributeLine> checkValueAttributes=new ArrayList<>();

            // 库存信息处理
            if (null != qty) {
                if (goodsHead.getShopCode().contains("VC")) {
                    throw new BusinessException("VC链接不允许修改库存");
                }
                String qtyType = batchInfo.getQtyType();
                BigDecimal currQty = goodsHead.getStockOnSalesQty();
                // 跳过库存为0的SKU
                if (!"Y".equals(batchInfo.getSkipZero()) || goodsHead.getStockOnSalesQty().compareTo(BigDecimal.ZERO) == 1) {
                    // set Value
                    goodsItem.setStockOnSalesQty(getValueByOptions(qtyType, currQty, qty));
                    isUpdateField = true;
                }
            }
            if (StrUtil.isNotEmpty(platformGoodsCode)||StrUtil.isNotEmpty(platformGoodsCodeType)) {
                goodsItem.setPlatformGoodsCode(getValueByOptionStr(platformGoodsCodeType, goodsHead.getPlatformGoodsCode(), platformGoodsCode,replaceChar));
                isUpdateField = true;
            }

            // 价格信息处理
            String priceType = batchInfo.getPriceType();
            // 是否红线价覆盖的
            boolean isCoverRedLinePrice = ListingBatchOptions.COVER_RED_LINE_PRICE.name().equals(priceType) && goodsHead.getRedLinePrice() != null && goodsHead.getRedLinePrice().compareTo(BigDecimal.ZERO) > 0;
            if (null != price || isCoverRedLinePrice) {
                BigDecimal newPrice;
                if (isCoverRedLinePrice) {
                    newPrice = goodsHead.getRedLinePrice();
                } else {
                    String currPriceStr = StrUtil.isBlank(goodsHead.getStandardPrice()) ? "0" : goodsHead.getStandardPrice();
                    newPrice = getValueByOptions(priceType, new BigDecimal(currPriceStr), price);
                }
                String listPrice = listingAmazonAttributeLineV2Service.getValueByPropNodePath(goodsHead.getId(), "list_price.value");
                String finalPrice = commonInfoBiz.checkPriceAndReturnSellerPriceV2(new PriceCheckRequest.Builder(goodsHead.getPdmGoodsCode(), String.valueOf(newPrice), goodsHead.getPublishType())
                        .headId(goodsHead.getId())
                        .siteCode(goodsHead.getSiteCode())
                        .onlineTime(goodsHead.getOnlineTime())
                        .isOnline(StrUtil.isNotBlank(goodsHead.getPlatformGoodsId()))
                        .listPrice(listPrice)
                        .oldPrice(goodsHead.getStandardPrice())
                        .build());
                goodsItem.setStandardPrice(finalPrice);
                goodsItem.setSettlementPrice(BigDecimal.valueOf(Double.parseDouble(finalPrice)));
                isUpdateField = true;
            }

            // 标题信息 处理
            if (StringUtils.isNotBlank(title)) {
                String titleType = batchInfo.getTitleType();
                String currTitle = StringUtils.isBlank(goodsHead.getTitle()) ? "" : goodsHead.getTitle();
                // set Value
                goodsItem.setTitle(getValueByOptions(titleType, currTitle, title, replaceChar));
                isUpdateField = true;
            }
            //分类
            if (StringUtils.isNotBlank(category)) {
                goodsItem.setCategoryId(Integer.valueOf(category));
                if (PlatformTypeEnum.EB.name().equals(platform)) {
                    headIdList.add(goodsHead.getId());
                }
                if (PlatformTypeEnum.AM.name().equals(platform)) {
                    //amazon 修改平台品类 后修改属性中的品类
                    handleAMCategory(category, goodsHead);
                }
                isUpdateField = true;
            }
            //店铺分类
            if (StringUtils.isNotBlank(shopCategory)) {
                shopCategoryHeadIdList.add(goodsHead.getId());
                isUpdateField = true;
            }
            //物品所在地
            if (StringUtils.isNotBlank(location) && PlatformTypeEnum.EB.name().equals(platform)) {
                headIdList.add(goodsHead.getId());
                isUpdateField = true;
            }
            //描述模板
            if (StringUtils.isNotBlank(descTemplate) && PlatformTypeEnum.EB.name().equals(platform)) {
                descTemplateHeadIdList.add(goodsHead.getId());
                isUpdateField = true;
            }
            // 属性列表处理
            if (!itemList.isEmpty() && EB && !listingEbayValues.isEmpty()) {
                String itemType = batchInfo.getItemType();
                Map<Integer, List<ListingEbayValue>> valuesMap = listingEbayValues.stream().collect(Collectors.groupingBy(ListingEbayValue::getListingLineId));
                ImmutableMap<Integer, ListingEbayLine> linesMap = Maps.uniqueIndex(listingEbayLines, ListingEbayLine::getListingHeadId);
                ListingEbayLine line = linesMap.get(goodsHead.getId());
                // 不存在商品行 - 跳过属性
                if (null == line) {
                    continue;
                }
                Integer lineId = line.getId();
                // 删除
                if (ListingBatchOptions.DEL.name().equals(itemType)) {
                    //itemList中包含了品牌的话，需要把品牌设置给头表
                    if (itemList.stream().map(o -> o.getItemKey().trim().toUpperCase()).collect(Collectors.toList()).contains("BRAND")) {
                        goodsItem.setBrandCode("");
                    }
                    //从valuesMap拿到对于的lineid的数据,与itemlist比较,如果itemlist中包含valuesmap中的name,则需要添加到deleteList里
                    valuesMap.get(lineId).stream().filter(o -> itemList.stream().map(e -> e.getItemKey().trim().toUpperCase())
                            .collect(Collectors.toList()).contains(o.getName().trim().toUpperCase())).forEach(o -> {
                        deleteList.add(o);
                    });

                } else {
                    // 新增、修改逻辑
                    for (ListingItem item : itemList) {
                        boolean existValue = false;
                        for (ListingEbayValue val : valuesMap.get(lineId)) {
                            // exist
                            //设置品牌给头表
                            if (StringUtils.equalsIgnoreCase(item.getItemKey(), "Brand")) {
                                goodsItem.setBrandCode(item.getItemValue());
                            }
                            if (StringUtils.equalsIgnoreCase(item.getItemKey(), val.getName())) {
                                valueItem = new ListingEbayValue();
                                valueItem.setListingLineId(lineId);
                                valueItem.setName(item.getItemKey());
                                valueItem.setValue(item.getItemValue());
                                valueItem.setUpdateBy(String.valueOf(ShiroUtils.getUserId()));
                                valueItem.setId(val.getId());
                                updateListSingle.add(valueItem);

                                ListingAmazonAttributeLine attributeLine = new ListingAmazonAttributeLine();
                                attributeLine.setGoodsId(goodsHead.getId());
                                attributeLine.setTableName(item.getItemKey());
                                attributeLine.setTableValue(item.getItemValue());
                                checkValueAttributes.add(attributeLine);

                                existValue = true;
                            }
                        }
                        if (!existValue) {
                            valueItem = new ListingEbayValue();
                            valueItem.setListingLineId(lineId);
                            valueItem.setName(item.getItemKey());
                            valueItem.setValue(item.getItemValue());
                            valueItem.setCreateBy(String.valueOf(ShiroUtils.getUserId()));
                            valueItem.setCreateTime(DateUtils.getNowDate());

                            ListingAmazonAttributeLine attributeLine = new ListingAmazonAttributeLine();
                            attributeLine.setGoodsId(goodsHead.getId());
                            attributeLine.setTableName(item.getItemKey());
                            attributeLine.setTableValue(item.getItemValue());
                            checkValueAttributes.add(attributeLine);

                            addListSingle.add(valueItem);
                        }
                    }
                }
                if (checkListingDescriptionTemplate(goodsHead.getId())) {
                    //判断moduleType 是否包含描述模板
                    if (!moduleType.contains(ListingModuleType.DESCRIPTION.name())) {
                        moduleType.add(ListingModuleType.DESCRIPTION.name());
                    }
                }
                isUpdateField = true;
            }

            try {
                goodsHead.setTitle(ObjUtil.isEmpty(goodsItem.getTitle())?goodsHead.getTitle() : goodsItem.getTitle());
                if (batchInfo.getTitleType() != null) {
                    violateWordBiz.checkViolateWord(true, null, checkValueAttributes, goodsHead);
                }
                updateList.addAll(updateListSingle);
                addList.addAll(addListSingle);
            } catch (Exception e) {
                listingLogService.insertErrorListingLog("批量修改失败", goodsHead.getCreateBy(), goodsHead.getId(), e.getMessage());
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_MODIFY), GoodsTaskSubStatusEnum.ERROR, e.getMessage());
                continue;
            }

            if (isUpdateField) {
                goodsUpdateList.add(goodsItem);
            }

            //状态不是更新中
            if (!ObjUtil.equals(PublishStatus.UPDATING.getType(), PublishStatus.getStatusByEdit(goodsHead.getPublishStatus(), goodsHead.getPlatform()))) {
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), CollUtil.newArrayList(GoodsTaskTypeEnum.BATCH_MODIFY), GoodsTaskSubStatusEnum.NORAML, "");
            }
            //状态需要扭转更新中的数据,直接加入到itemDTOList的列表中
            if (ObjUtil.equals(PublishStatus.UPDATING.getType(), PublishStatus.getStatusByEdit(goodsHead.getPublishStatus(), goodsHead.getPlatform()))) {
                ItemDTO itemDTO = new ItemDTO();
                itemDTO.setGoodsHead(goodsHead);
                itemDTO.setModuleType(moduleType);
                itemDTOList.add(itemDTO);
            }
        }

        ListingBatchEditSaveDTO listingBatchEditSaveDTO = new ListingBatchEditSaveDTO();
        listingBatchEditSaveDTO.setListingEbayLines(listingEbayLines);
        listingBatchEditSaveDTO.setListingEbayValues(listingEbayValues);
        listingBatchEditSaveDTO.setGoodsUpdateList(goodsUpdateList);
        listingBatchEditSaveDTO.setDeleteList(deleteList);
        listingBatchEditSaveDTO.setUpdateList(updateList);
        listingBatchEditSaveDTO.setAddList(addList);
        listingBatchEditSaveDTO.setLocationHeadIdList(headIdList);
        listingBatchEditSaveDTO.setDescTemplateHeadIdList(descTemplateHeadIdList);
        listingBatchEditSaveDTO.setShopCategoryHeadIdList(shopCategoryHeadIdList);
        listingBatchEditSaveDTO.setItemDTOList(itemDTOList);
        return listingBatchEditSaveDTO;
    }

    /**
     * 通过选项获取值
     * @param type 操作类型
     * @param originValue 原值
     * @param targetValue 输入值
     * @param targetValue2 需要替换的值
     */
    private String getValueByOptionStr(String type, String originValue, String targetValue,String targetValue2) {
        if (ListingBatchOptions.ADD_SUFFIX.name().equals(type)) {
            return originValue + targetValue;
        } else if (ListingBatchOptions.ADD_PREFIX.name().equals(type)) {
            return targetValue + originValue;
        } else if (ListingBatchOptions.REPLACECHAR.name().equals(type)) {
            return originValue.replace(targetValue2, targetValue);
        }else if (ListingBatchOptions.FIXEDVALUE.name().equals(type)) {
            return targetValue;
        }else {
            return originValue;
        }
    }

    private boolean isEbAndCheck(List<GoodsHead> goodsHeads, List<ListingItem> itemList, ListingBatchEditDTO batchInfo) {
        //物品所在地
        String location = batchInfo.getLocation();
        //分类
        String category = batchInfo.getCategory();

        //判断是否需要更新属性列表
        //判断是否包含EB
        boolean EB = goodsHeads.stream().anyMatch(o -> PlatformTypeEnum.EB.name().equals(o.getPlatform()));
        //判断是否包含AM
        boolean AM = goodsHeads.stream().anyMatch(o -> PlatformTypeEnum.AM.name().equals(o.getPlatform()));
        //判断修改了分类是否含有平台尾亚马逊的
        if (StringUtils.isNotBlank(category) && AM && EB) {
            throw new BusinessException("不支持同时修改EB和AM的平台品类");
        }
        //不支持同时修改分类和属性
        if (StringUtils.isNotBlank(category) && !itemList.isEmpty()) {
            throw new BusinessException("不支持同时修改平台品类和属性");
        }
        if (StringUtils.isNotBlank(category)) {
            VerificationSite(goodsHeads);
        }
        if (StringUtils.isNotBlank(location)) {
            VerificationSite(goodsHeads);
        }
        return EB;
    }

    private void VerificationSite(List<GoodsHead> goodsHeads) {
        List<String> siteCodeList = goodsHeads.stream().map(e -> {
            return e.getSiteCode();
        }).collect(Collectors.toList());
        Set<String> set = siteCodeList.stream().collect(Collectors.toSet());
        //只允许修改同平台同站点的分类
        //只允许修改同平台同站点的物品所在地
        if (set.size() > 1) {
            throw new BusinessException("只允许同平台和同站点修改物品所在地和平台品类");
        }
    }


    /**
     * 处理AM 批量修改后，属性的变化
     *
     * @param category
     * @param goodsHead
     */
    private void handleAMCategory(String category, GoodsHead goodsHead) {
        Integer headId = goodsHead.getId();
        if (ObjUtil.isEmpty(goodsHead)) {
            return;
        }
        List<ListingAmazonAttributeLine> allLines = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            AttributeQueryDTO attributeQueryDTO = new AttributeQueryDTO();
            attributeQueryDTO.setCategoryId(Integer.valueOf(category));
            attributeQueryDTO.setAttributeType(i);
            attributeQueryDTO.setPlatform(goodsHead.getPlatform());
            attributeQueryDTO.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            attributeQueryDTO.setGoodsId(goodsHead.getId());
            attributeQueryDTO.setShopCode(goodsHead.getShopCode());
            attributeQueryDTO.setUserId(Long.valueOf(goodsHead.getCreateBy()));
            List<ListingAmazonAttributeLine> lines = listingAmazonAttributeLineService.selectRequireCategoryAttribute(attributeQueryDTO);
            allLines.addAll(lines);
        }

        //去重map
        Map<String, String> repeatVerMap = new HashMap<>();
        //先批量删除
        listingAmazonAttributeLineService.deleteListingAmazonAttributeLineByGoodId(Integer.valueOf(headId));
        //后批量新增

        List<ListingAmazonAttributeLine> oldAttributeList = allLines.stream().filter(a -> !Objects.equals(a.getCategoryId(), Integer.valueOf(category))).collect(Collectors.toList());
        List<ListingAmazonAttributeLine> newAttributeList = allLines.stream().filter(a -> Objects.equals(a.getCategoryId(), Integer.valueOf(category))).collect(Collectors.toList());

        for (ListingAmazonAttributeLine newAttribute : newAttributeList) {
            ListingAmazonAttributeLine oldAttribute = oldAttributeList.stream().
                    filter(a -> StringUtils.equalsIgnoreCase(a.getTableName(), newAttribute.getTableName())).findFirst().orElse(null);
            //找到相同的属性名,值使用旧的值，否则用新的值
            if (ObjUtil.isNotEmpty(oldAttribute)) {
                newAttribute.setTableValue(oldAttribute.getTableValue());
            }
            if (repeatVerMap.containsKey(newAttribute.getTableName())) {
                continue;
            }
            repeatVerMap.put(newAttribute.getTableName(), null);

            ListingAmazonAttributeLine line = new ListingAmazonAttributeLine();
            line.setCreateTime(DateUtils.getNowDate());
            line.setCreateBy(goodsHead.getCreateBy());
            line.setGoodsId(Integer.valueOf(headId));
            line.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            line.setPlatform(goodsHead.getPlatform());
            line.setCategoryId(Integer.valueOf(category));
            line.setTableValue(newAttribute.getTableValue());
            line.setTableName(newAttribute.getTableName());
            line.setTableType(newAttribute.getTableType());
            listingAmazonAttributeLineService.insertListingAmazonAttributeLine(line);


        }
    }


    /**
     * 根据批量操作类型计算数值
     *
     * @param optionType  操作类型
     * @param currValue   当前数值
     * @param targetValue 目标值
     * @return
     */
    private BigDecimal getValueByOptions(String optionType, BigDecimal currValue, BigDecimal targetValue) {
        if (ListingBatchOptions.FIXEDVALUE.name().equals(optionType)) {
            return targetValue;
        }
        if (ListingBatchOptions.PLUS.name().equals(optionType)) {
            return currValue.add(targetValue);
        }
        if (ListingBatchOptions.SUBTRACT.name().equals(optionType)) {
            return currValue.subtract(targetValue);
        }
        return currValue.multiply(targetValue);
    }

    /**
     * @param optionType
     * @param currValue
     * @param targetValue
     * @param targetValue2 需要替换的字符串
     * @return
     */
    private String getValueByOptions(String optionType, String currValue, String targetValue, String targetValue2) {

        // 添加前缀
        if (ListingBatchOptions.ADD_PREFIX.name().equals(optionType)) {
            return targetValue + " " + currValue;
        }
        // 添加后缀
        if (ListingBatchOptions.ADD_SUFFIX.name().equals(optionType)) {
            return currValue + " " + targetValue;
        }
        // 替换单词
        if (ListingBatchOptions.REPLACECHAR.name().equals(optionType)) {
            StringBuilder newValue = new StringBuilder("");
            String[] titles = currValue.split(" ");
            for (String s : titles) {
                if (s.equals(targetValue2)) {
                    newValue.append(targetValue);
                } else {
                    newValue.append(s);
                }
                newValue.append(" ");
            }
            return newValue.toString().trim();

        }
        return targetValue;

    }


    public void handlePublishType(ListingEditDTO listingEditDTO, GoodsHead goodsHead) {
        Integer goodsId = goodsHead.getId();
        ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHead.getId());
        if (listingEbayLine != null) {
            //刊登天数
            listingEditDTO.setPublicationDay(listingEbayLine.getSellDay());
            //起卖数量
            listingEditDTO.setStartSellingCount(listingEbayLine.getStartSellingCount());
            //起卖数量
            listingEditDTO.setBestOfferFlag(listingEbayLine.getBestOfferFlag());
            //一口价
            listingEditDTO.setReservePrice(listingEbayLine.getReservePrice());
        }
        //价格
        listingEditDTO.setStandardPrice(goodsHead.getStandardPrice());

        listingEditDTO.setPublishType(goodsHead.getPublishType());

    }


//
//    /**
//     * 校验该sku是否需要适配信息
//     * 目前支持ebay 新建listing(包含单个新建 批量新建 复制 自动生成 自动刊登)
//     *
//     * @param goodsHead
//     * @return
//     */
//    public GoodsHead checkListingAdaptationBySku(GoodsHead goodsHead) {
//        if(ObjUtil.isEmpty(goodsHead.getPdmGoodsCode())){
//            return goodsHead;
//        }
//        SaleGoodsDTO query = new SaleGoodsDTO();
//        query.setGoodsCode(goodsHead.getPdmGoodsCode());
//        Goods pdmGood = goodsService.selectGoodsByGoodCode(query);
//        //如果pdm中没有数据,从ads中查询数据
//        if (ObjectUtils.isEmpty(pdmGood)) {
//            return goodsHead;
//        }
//        //如果pdm中有数据且填写了适配标识,则根据适配标识设置适配状态
//        //暂时设置待适配 后续保存适配时,判断有无适配数据再设置适配状态
//        goodsHead.setAdaptationStatus(Objects.equals(pdmGood.getAdaptFlag(), SMCCommonEnum.N) ? AdaptationStatusEnum.NO.getStatus() : AdaptationStatusEnum.WAIT.getStatus());
//        return goodsHead;
//    }


    public boolean judgeNewTemplateExistByProductType(String productType){
        String templateFileName = fileTemplatePathNew + productType + ".xlsm";  //模板
        //判断新模板是否存在
        File file = new File(templateFileName);
        if (!file.exists()) {
            return false;
        }
        return true;
    }

    public PlatformCategory judgeNewTemplateExistByCategoryId(String platformCategoryId){
        PlatformCategory categoryInfo = platformCategoryService.selectPlatformCategoryById(Long.valueOf(platformCategoryId));
        String templateFileName = fileTemplatePathNew + categoryInfo.getProductType() + ".xlsm";  //模板
        //判断新模板是否存在
        File file = new File(templateFileName);
        if (!file.exists()) {
            return null;
        }
        return categoryInfo;
    }


    /**
     * v2版本的listing查询   适用于  Amazon、Ebay 查询
     * @param listingDTO
     * @param exportFlag
     * @return
     */
    public List<Integer> selectListingVoListV2(ListingDTO listingDTO, boolean exportFlag) {
        // 基础头部数据
        GoodsHead goodsHeadParam = goodsHeadService.getGoodsHeadParam(listingDTO);
        //处理实际库存
        if (!getGoodHeadParamByActulStockScoop(listingDTO, goodsHeadParam)) {
            return null;
        }

        List<Integer> ids = goodsHeadService.selectListIds(goodsHeadParam);

        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        if (exportFlag && ids.size() > 10000) {
            throw new BusinessException("导出数据不能超过10000条。如需大批量导出,请联系管理员！");
        }
        return ids;
    }

    @Override
    public void handleQueryNew(ListingDTO listingDTO) {
       goodsHeadService.handleQueryNew(listingDTO);
    }

    public List<GoodsHeadVO> selectListByIds(List<Integer> ids, ListingDTO listingDTO, boolean exportFlag) {
        GoodsHead query = new GoodsHead();
        query.setIds(ids.stream().map(String::valueOf).collect(Collectors.joining(" ")));
        List<GoodsHeadVO> goodsHeadVOList = goodsHeadService.selectListingGoodsHeadVOList(query);
        List<GoodsHeadVO> goodsHeadVOS = getGoodsHeadVOS(exportFlag, goodsHeadVOList, listingDTO,false);
        for (GoodsHeadVO goodsHeadVO : goodsHeadVOList) {
            // 将退货率标签合并到listing表现
            String listingPerformance = goodsHeadVO.getListingPerformance();
            JSONArray jsonArray = StrUtil.isBlank(listingPerformance) ? new JSONArray() : JSON.parseArray(listingPerformance);
            if (goodsHeadVO.getSkuRateLabel() != null) {
                String value = getOriginValue(goodsHeadVO.getSkuRateLabel());
                if (value == null) {
                    continue;
                }
                jsonArray.add("sku_rate_label_" + value);
            }
            if (goodsHeadVO.getListingRateLabel() != null) {
                String value = getOriginValue(goodsHeadVO.getListingRateLabel());
                if (value == null) {
                    continue;
                }
                jsonArray.add("listing_rate_label_" + value);
            }
            // 主链接
            if (goodsHeadVO.getIsMain() != null && goodsHeadVO.getIsMain() == 1) {
                jsonArray.add("self_主链接");
            }
            // 黑名单
            if (goodsHeadVO.getIndependentSiteOnly() != null && "Y".equals(goodsHeadVO.getIndependentSiteOnly())) {
                jsonArray.add("self_独立站专卖");
            }
            if (goodsHeadVO.getInventoryExclude() != null && "Y".equals(goodsHeadVO.getInventoryExclude())) {
                jsonArray.add("self_库存更新黑名单");
            }
            if (goodsHeadVO.getRedLineBlackFlag() != null && "Y".equals(goodsHeadVO.getRedLineBlackFlag())) {
                jsonArray.add("self_红线价管控白名单");
            }
            goodsHeadVO.setListingPerformance(jsonArray.toJSONString());
        }
        return goodsHeadVOS;
    }


    /**
     * 跟卖链接的 excel map写入
     * @param goodsHead
     * @return
     */
    public Map<String, String> getFlowSoldListingFieldMap(GoodsHead goodsHead) {

        Map<String, String> goodsHeadMap = new HashMap<>();
        goodsHeadMap.put("sku",goodsHead.getPlatformGoodsCode());
        goodsHeadMap.put("price",goodsHead.getStandardPrice());
        BigDecimal stockOnSalesQty = goodsHead.getStockOnSalesQty();
        DecimalFormat df = new DecimalFormat("#.##");
        String quantityStr = df.format(stockOnSalesQty);
        goodsHeadMap.put("quantity", quantityStr);

        List<ListingAmazonAttributeLine> listingAmazonAttributeLines = listingAmazonAttributeLineService.selectByGoodsId(goodsHead.getId());
        if (ObjUtil.isNotEmpty(listingAmazonAttributeLines)) {
            listingAmazonAttributeLines.forEach(listingAmazonAttributeLine -> {
                if ("external_product_id".equals(listingAmazonAttributeLine.getTableName())){
                    goodsHeadMap.put("product-id", listingAmazonAttributeLine.getTableValue());

                }
            });
        }
        goodsHeadMap.put("product-id-type", "ASIN");
        goodsHeadMap.put("condition-type", goodsHead.getCondition());
        goodsHeadMap.put("condition-note", "In original package");
        goodsHeadMap.put("fulfillment-center-id", Objects.equals(goodsHead.getPublishType(),PublishType.FBM.getType())?"DEFAULT":"AMAZON_NA");

        return goodsHeadMap;
    }

    /**
     * 新增或者修改视频
     * @param goodsId
     * @param videoId
     */
    public void saveAndUpdateVide(Integer goodsId, String videoId) {
        ListingEbayVideo listingEbayVideo = listingEbayVideoService.selectListingEbayVideoByGoodsId(goodsId);
        if (ObjUtil.isEmpty(listingEbayVideo)){
            ListingEbayVideo video = new ListingEbayVideo();
            video.setGoodsId(String.valueOf(goodsId));
            video.setVideoId(videoId);
            video.setCreateBy("1");
            video.setUpdateBy("1");
            listingEbayVideoService.insertListingEbayVideo(video);
            return;
        }
        listingEbayVideo.setVideoId(videoId);
        listingEbayVideoService.updateListingEbayVideo(listingEbayVideo);

    }


    /**
     * 适配文案变更快捷更新
     * @param listingId
     * @return
     */
    public ListingEditDTO getListingsEditVOAdapterText(Integer listingId) {
        if (ObjUtil.isEmpty(listingId)) {
            throw new BusinessException("listingId为空,请重新选择.");
        }
        AdapterTodoQueryDTO adapterTodoQueryDTO= new AdapterTodoQueryDTO();
        adapterTodoQueryDTO.setHeadIds(String.valueOf(listingId));
        List<SmcAdaptTodo> smcAdaptTodos = smcAdaptTodoService.selectAdaptationTodoList(adapterTodoQueryDTO);
        if (CollUtil.isEmpty(smcAdaptTodos)){
            throw new BusinessException("listingId:"+listingId+"没有适配待办.");
        }

        ListingsEditVO listingsEditVO = listingInfoService.getListingsEditDTOSByListingIds(Lists.newArrayList(listingId));

        //将待办中的数据也放进去
        ListingEditDTO listingEditDTO = listingsEditVO.getListingEditDTOS().get(0);
        if (ObjUtil.isEmpty(listingEditDTO)){
            return listingsEditVO.getListingEditDTOS().get(0);
        }

        SmcAdaptTodo smcAdaptTodo = smcAdaptTodos.get(0);
        // 如果是Ebay，需要对productDescriptionFit进行处理,{'Universal Fitment': 'No', 'Fitment': '12371381, 12471375, 15295861, 19149619, 265921, 894375 K8964', 'Fitment 2': '12471376, 19149617, 2659226 15472, 15864153 12471379, 19168944', 'Fitment 3': '1999 2000 2001 2002 2003 2004 2005 2006', 'Fitment 4': 'For Cadillac Escalade ESV EXT Chevrolet Avalanche 1500 Tahoe', 'Fitment 5': 'For Silverado Suburban 1500 GMC Sierra 1500 Yukon XL 1500'}，如果以{开头，}结尾，只取后面的值
        if (Objects.equals(listingEditDTO.getPlatform(), PlatformTypeEnum.EB.name())) {
            String productDescriptionFit = smcAdaptTodo.getProductDescriptionFit();
            if (StrUtil.isNotBlank(productDescriptionFit)) {
                if (JSONUtil.isJson(productDescriptionFit)){
                    // 解析json
                    JSONObject jsonObject = JSONObject.parseObject(productDescriptionFit);
                    // 对listingEditDTO的lines过滤，只显示jsonObject中的key
                    List<ListingEbayValue> listingEbayValues = listingEditDTO.getListingEbayValues();
                    if (CollUtil.isNotEmpty(listingEbayValues)) {
                         // 移除jsonObject中不存在的key
                         listingEbayValues.removeIf(listingEbayValue -> !jsonObject.containsKey(listingEbayValue.getName()));
                    }

                    // 获取所有的value
                    List<String> values = jsonObject.values().stream().map(String::valueOf).collect(Collectors.toList());
                    smcAdaptTodo.setProductDescriptionFit(values.stream().map(String::trim).collect(Collectors.joining(", ")));
                }
            }
        }
        
        String bulletPoint1Fit = smcAdaptTodo.getBulletPoint1Fit();
        String bulletPoint2Fit = smcAdaptTodo.getBulletPoint2Fit();
        String bulletPoint3Fit = smcAdaptTodo.getBulletPoint3Fit();
        String bulletPoint4Fit = smcAdaptTodo.getBulletPoint4Fit();
        String bulletPoint5Fit = smcAdaptTodo.getBulletPoint5Fit();
        listingEditDTO.setFiveAdapterEdit(true);
        if (StrUtil.isBlank(bulletPoint1Fit) && StrUtil.isBlank(bulletPoint2Fit)
                && StrUtil.isBlank(bulletPoint3Fit) && StrUtil.isBlank(bulletPoint4Fit) && StrUtil.isBlank(bulletPoint5Fit)){
            listingEditDTO.setFiveAdapterEdit(false);
        }
        listingEditDTO.setSmcAdaptTodo(smcAdaptTodo);

        listingEditDTO.setPdmGoodsCode(smcAdaptTodo.getPdmGoodsCode());

        SaleGoodsDTO query = new SaleGoodsDTO();
        query.setGoodsCode(smcAdaptTodo.getPdmGoodsCode());
        Goods pdmGood = goodsService.selectGoodsByGoodCode(query);
        if(ObjUtil.isNotEmpty(pdmGood)){
            List<AdsFitmentDataVIO> adsList = adsService.selectFitmentDataVIO(Lists.newArrayList(pdmGood.getProductCode()));
            List<Object> subDatas = adsService.handleAdsDataForExportV2(adsList, Lists.newArrayList("modelName","yearName","liter"), ", ");
            listingEditDTO.setSubDatas(subDatas);
        }

        return listingEditDTO;
    }

    /**
     * 适配图片变更快捷更新
     * @param listingIds
     * @return
     */
    public ListingsEditVO getListingsEditVOAdapterImage(List<Integer> listingIds) {
        if (Objects.isNull(listingIds)) {
            throw new BusinessException("该选择有效Listing.");
        }
        List<GoodsHead> heads = goodsHeadService.selectListingGoodsHeadByIds(listingIds.stream().toArray(Integer[]::new));
        if (Objects.isNull(heads)) {
            throw new BusinessException("该选择有效Listing.");
        }
        // 按平台分组
        Map<String, List<GoodsHead>> groupedByPlatform = heads.stream()
                .collect(Collectors.groupingBy(GoodsHead::getPlatform));

        // 初始化返回对象
        ListingsEditVO resultVO = new ListingsEditVO();
        List<ListingEditDTO> mergedEditDTOs = new ArrayList<>();

        // 遍历每个平台，获取其对应的 ListingsEditDTO 数据
        for (Map.Entry<String, List<GoodsHead>> entry : groupedByPlatform.entrySet()) {
            String platform = entry.getKey();
            List<Integer> platformListingIds = entry.getValue().stream()
                    .map(GoodsHead::getId)
                    .collect(Collectors.toList());

            // 根据平台类型获取服务
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platform);
            ListingsEditVO platformListingsVO = listingServiceByPlatformType.getListingsEditDTOSByListingIds(platformListingIds);

            // 合并当前平台的 ListingsEditDTO 数据
            if (platformListingsVO != null && platformListingsVO.getListingEditDTOS() != null) {
                for (ListingEditDTO dto : platformListingsVO.getListingEditDTOS()) {
                    dto.setPlatform(platform);
                    mergedEditDTOs.add(dto);
                }
            }
        }
        resultVO.setListingEditDTOS(mergedEditDTOs);
        getListingsPDMAdapterImage(resultVO);
        return resultVO;
    }

    /**
     * 获取PDM的适配品牌图
     * @param listingsEditVO
     * @return
     */
    private void getListingsPDMAdapterImage(ListingsEditVO listingsEditVO) {
        // 获取所有的 goodsCode 列表
        List<String> goodsCodeList = listingsEditVO.getListingEditDTOS()
                .stream()
                .map(ListingEditDTO::getGoodsCode)
                .collect(Collectors.toList());

        // 构造查询参数
        GetGoodsDetailQueryDTO goodsDetailQueryDTO = new GetGoodsDetailQueryDTO();
        goodsDetailQueryDTO.setGoodsCodes(goodsCodeList);
        goodsDetailQueryDTO.setModels(Collections.singletonList("GoodsImage"));

        // 调用接口获取商品详情
        List<SaleGoodsDTO> saleGoodsDTOList = PDMHttpRequestBiz.listGoodsDetail(goodsDetailQueryDTO);

        // 如果商品详情为空，抛出异常
        if (CollectionUtils.isEmpty(saleGoodsDTOList)) {
            throw new BusinessException("商品不存在");
        }
        // 此处用的是brandCode 实际上是 brandName, 需要转化下
        List<String> brandNameList = listingsEditVO.getListingEditDTOS().stream().map(ListingEditDTO::getBrandCode).collect(Collectors.toList());
        List<Brand> brandList = brandService.selectBrandListByNameList(brandNameList);
        Map<String, Brand> brandMap = Optional.ofNullable(brandList).orElse(Collections.emptyList()).stream()
                .collect(Collectors.toMap(Brand::getBrandName, brand -> brand, (a, b) -> a));
        // 提取符合条件的 GoodsImage（type = "12"） 适配图
        List<GoodsImage> imageNameList = saleGoodsDTOList.stream()
                .flatMap(saleGoodsDTO -> saleGoodsDTO.getGoodsImageList().stream())
                .filter(goodsImage -> Objects.equals(goodsImage.getType(), "12"))
                .collect(Collectors.toList());

        // 如果没有符合条件的图片，直接返回
        if (CollectionUtils.isEmpty(imageNameList)) {
            return;
        }

        // 将 GoodsImage 按照 goodsCode 分组
        Map<String, List<GoodsImage>> goodsImageMap = imageNameList.stream()
                .collect(Collectors.groupingBy(GoodsImage::getGoodsCode));

        // 更新 ListingEditDTO 中的图片信息
        listingsEditVO.getListingEditDTOS().forEach(listingEditDTO -> {
            List<GoodsImage> subImageList = goodsImageMap.get(listingEditDTO.getGoodsCode());
            Brand brand = brandMap.get(listingEditDTO.getBrandCode());
            if (subImageList != null) {
                List<GoodsImage> resultImageList = new ArrayList<>();
                for (GoodsImage subGoodsImage : subImageList) {
                    if (brand != null && Objects.equals(subGoodsImage.getBrandCode(), brand.getBrandCode())) {
                        resultImageList.add(subGoodsImage);
                    }
                }
                if (!CollectionUtils.isEmpty(resultImageList)) {
                    listingEditDTO.setAdpatImageList(resultImageList);
                } else {
                    listingEditDTO.setAdpatImageList(subImageList);
                }
            }
        });
    }


    /**
     * 批量保存适配图片更新的数据
     * @param dto 来源的数据
     * @return
     */
    public void adapteBatchUpdateImage(BatchListingDTO dto) {
        if (dto == null || dto.getListings() == null || dto.getListings().isEmpty()) {
            throw new BusinessException("列表数据为空，无法更新图片.");
        }

        // 按平台分组
        Map<String, List<ListingDTO>> groupedByPlatform = dto.getListings().stream()
                .collect(Collectors.groupingBy(ListingDTO::getPlatform));

        // 遍历每个平台的数据分组
        for (Map.Entry<String, List<ListingDTO>> entry : groupedByPlatform.entrySet()) {
            String platformType = entry.getKey();
            List<ListingDTO> platformListings = entry.getValue();

            // 获取对应的平台服务
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            if (listingServiceByPlatformType == null) {
                throw new BusinessException("未找到对应平台服务: " + platformType);
            }

            // 构造单个平台的 BatchListingDTO
            BatchListingDTO platformBatchDTO = new BatchListingDTO();
            platformBatchDTO.setPlatform(platformType);
            platformBatchDTO.setListings(platformListings);

            // 调用对应平台的方法
            listingServiceByPlatformType.batchUpdatePictures(platformBatchDTO);
        }
    }




    @Transactional(rollbackFor = Exception.class)
    public List<ItemDTO> batchEditAttrByExcel(BatchListingDTO dto, Long userId) {
        // 对属性进行处理
        List<String> listingAttributeLine = dto.getListingAttributeLine();
        if (CollUtil.isEmpty(listingAttributeLine)) {
            throw new BusinessException("属性不能为空");
        }
        Map<Integer, GoodsHead> goodsHeadMap = new HashMap<>();

        List<ListingAmazonAttributeLineV2> attributeLines = parseLines(listingAttributeLine, goodsHeadMap);
        attributeLines.removeIf(e -> Arrays.asList("part_number.value").contains(e.getPropNodePath()));


        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<String> moduleType = new ArrayList<>();
        moduleType.add(ListingModuleType.ATTRIBUTE_V2.name());
        Map<Long, List<ListingAmazonAttributeLineV2>> attributeLineMap = attributeLines.stream().collect(Collectors.groupingBy(ListingAmazonAttributeLineV2::getHeadId));
        attributeLineMap.forEach((k, v) -> {
            GoodsHead goodsHead = goodsHeadMap.get(k.intValue());
            if (goodsHead == null) {
                return;
            }
            boolean updatePlatform = amazonProductBiz.comparativeAttribute(goodsHead, v);
            if (!updatePlatform) {
                return;
            }
            // 对比属性，是否需要更新到平台
            if ((Objects.equals(goodsHead.getPlatform(), PlatformTypeEnum.AM.name()) && !PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())
                    && ObjUtil.isNotEmpty(goodsHead.getPlatformGoodsId()))) {
                ItemDTO itemDTO = new ItemDTO();
                itemDTO.setGoodsHead(goodsHead);
                itemDTO.setModuleType(moduleType);
                itemDTO.setOperationFlag("AM_BATCH_EDIT");
                itemDTOList.add(itemDTO);
            }

            // 显示的时候只显示0的和为4的GTIN，删除只删除为0的，GTIN当前请求会携带
            listingAmazonAttributeLineV2Service.deleteListingAmazonAttributeLineV2ByGoodId(k.intValue(), Collections.singletonList("part_number.value"), Arrays.asList(0));
            listingAmazonAttributeLineV2Service.deleteGTINByGoodId(k.intValue());

            v.forEach(e -> {
                e.setUpdateBy(userId.toString());
                e.setUpdateTime(DateUtils.getNowDate());
                listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(e);
            });
        });
        return itemDTOList;
    }

    private List<ListingAmazonAttributeLineV2> parseLines(List<String> listingAttributeLine, Map<Integer, GoodsHead> goodsHeadMap) {
        List<ListingAmazonAttributeLineV2> amazonAttributeLines = new ArrayList<>();

        if (CollUtil.isEmpty(listingAttributeLine)) {
            return amazonAttributeLines;
        }


        for (String listingAmazonAttributeLineStr : listingAttributeLine) {

            ListingAmazonAttributeLineV2 attributeLine = JSON.parseObject(listingAmazonAttributeLineStr, ListingAmazonAttributeLineV2.class);
            if (ObjectUtils.isEmpty(attributeLine)) {
                continue;
            }
            if(attributeLine.getHeadId() == null){
                continue;
            }
            GoodsHead goodsHead = goodsHeadMap.containsKey(attributeLine.getHeadId().intValue()) ? goodsHeadMap.get(attributeLine.getHeadId().intValue()) : goodsHeadService.selectListingGoodsHeadById(attributeLine.getHeadId().intValue());
            if(!goodsHeadMap.containsKey(attributeLine.getHeadId().intValue())) {
                goodsHeadMap.put(attributeLine.getHeadId().intValue(), goodsHead);
            }
            String vcFlag = goodsHead.getShopCode().contains("VC") ? "Y" : "N";

            List<String> attributeV2Names = AmazonAttributeEnum.getEnumValuesByFlag(vcFlag);

            PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));

            if (StringUtils.isBlank(attributeLine.getTableValue())) {
                continue;
            }
            //判断是否是平台商品编码类型
            if (amazonPlatformListingService.isSpecialAttr(attributeLine.getPropNodePath())) {
                if("ASIN".equalsIgnoreCase(attributeLine.getPropNodePath())) {
                    saveExternal(goodsHead, "ASIN",  5, platformCategory, attributeLine.getTableValue(), amazonAttributeLines);
                }else {
                    saveExternal(goodsHead, attributeLine.getPropNodePath().toUpperCase(), 4, platformCategory, attributeLine.getTableValue(), amazonAttributeLines);
                }
                continue;
            }
            ListingAmazonAttributeLineV2 line = new ListingAmazonAttributeLineV2();
            line.setCategoryId(goodsHead.getCategoryId());
            line.setHeadId(Long.valueOf(goodsHead.getId()));
            line.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            line.setProductType(platformCategory.getProductType());
            line.setTableValue(attributeLine.getTableValue());
            line.setTableName(attributeLine.getTableName());
            line.setTableType(getTableType(attributeLine.getPropNodePath(), attributeV2Names));
            line.setPropNodePath(attributeLine.getPropNodePath());
            amazonAttributeLines.add(line);
        }
        return amazonAttributeLines;
    }

    private void saveExternal(GoodsHead goodsHead, String listingItem, int tableType, PlatformCategory categoryInfo, String value, List<ListingAmazonAttributeLineV2> amazonAttributeLines) {
        String vcFlag = goodsHead.getShopCode().contains("VC")?"Y":"N";
        //类型
        ListingAmazonAttributeLineV2 lineType = new ListingAmazonAttributeLineV2();
        lineType.setCategoryId(goodsHead.getCategoryId());
        lineType.setHeadId(Long.valueOf(goodsHead.getId()));
        lineType.setPropNodePath(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE.getInfoV2(vcFlag));
        lineType.setTableValue(listingItem);
        lineType.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        lineType.setTableType(tableType);
        lineType.setProductType(categoryInfo.getProductType());
        amazonAttributeLines.add(lineType);
        //值
        ListingAmazonAttributeLineV2 lineValue = new ListingAmazonAttributeLineV2();
        lineValue.setCategoryId(goodsHead.getCategoryId());
        lineValue.setHeadId(Long.valueOf(goodsHead.getId()));
        lineValue.setPropNodePath(AmazonAttributeEnum.EXTERNAL_PRODUCT_ID.getInfoV2(vcFlag));
        lineValue.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        lineValue.setTableValue(value);
        lineValue.setProductType(categoryInfo.getProductType());
        lineValue.setTableType(tableType);
        amazonAttributeLines.add(lineValue);
    }

    private boolean isMultiValue(String itemKey) {
        if (StrUtil.isBlank(itemKey)) {
            return false;
        }
//        external_product_id.type-1	external_product_id.type-2	external_product_id.value-1	external_product_id.value-2
        // 使用正则表达式判断是否是多值
        return itemKey.matches(".*-\\d+$");
    }

    private Integer getTableType(String propNodePath, List<String> attributeV2Names) {
        return  attributeV2Names.contains(propNodePath) ? 4 : 0;
    }

    public Map<String, Object> listBatchEditData(Integer[] ids, String source) {
        return categoryInfoHandleBiz.listGoodsHeadListAttr(Stream.of(ids).collect(Collectors.toList()), source);
    }

    public void handlerUpdate(List<GoodsHead> needUpdateList) {
        if (CollUtil.isEmpty(needUpdateList)) {
            return;
        }
        List<Integer> ids = needUpdateList.stream().map(GoodsHead::getId).collect(Collectors.toList());
        List<ListingLabel> cartList = listingLabelService.selectHeadIdByListingPerformance(ids, "有购物车", null);
        // 有购物车的listing
        List<Integer> cartIds = cartList.stream().map(ListingLabel::getHeadId).collect(Collectors.toList());
        needUpdateList.forEach(goodsHead -> {
            GoodsHead update = new GoodsHead();
            update.setId(goodsHead.getId());
            if (PublishStatus.getNoUpdateStatus().contains(goodsHead.getPublishStatus())) {
                return;
            }
            if(goodsHead.getPublishStatus().equals(UPDATING_FAIL.getType()) || goodsHead.getPublishStatus().equals(DELETE_FAIL.getType())){
                return;
            }
            if (cartIds.contains(goodsHead.getId())) {
                update.setPublishStatus(SALEING.getType());
            } else {
                update.setPublishStatus(OFF_SALE.getType());
            }
            goodsHeadService.updateListingGoodsHead(update);
        });
    }

    /**
     * 检查listing的描述模板是否包含了属性组件
     *
     * @param goodsId
     * @return
     */
    public Boolean checkListingDescriptionTemplate(Integer goodsId) {
        if (ObjUtil.isEmpty(goodsId)) {
            throw new BusinessException("检测ebay描述模板是否包含属性组件,ebay头数据Id为空!");
        }
        GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsId);
        if (ObjUtil.isEmpty(goodsDescription)) {
            throw new BusinessException("检测ebay描述模板是否包含属性组件,ebay描述数据为空!");
        }
        Integer descriptionId = goodsDescription.getDescriptionId();
        if (ObjUtil.isEmpty(descriptionId)) {
            return false;
        }
        TemplateEbayDescription templateEbayDescription = templateEbayDescriptionService.selectTemplateEbayDescriptionById(Long.valueOf(descriptionId));
        if (ObjUtil.isEmpty(templateEbayDescription)) {
            throw new BusinessException("检测ebay描述模板是否包含属性组件,ebay描述模板数据为空!");
        }
        String widgetConfig = templateEbayDescription.getWidgetConfig();
        if (ObjUtil.isEmpty(widgetConfig)) {
            throw new BusinessException("检测ebay描述模板是否包含属性组件,ebay描述模板模板配置异常!");
        }
        return widgetConfig.contains(BasicWidgetEnum.COMMON_ATTRIBUTE.getWidgetType());

    }

    /**
     * 提交批量修改可用状态任务
     *
     * @param listingGoodsHeadDetail 商品可用状态详情
     * @param userId 用户ID
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult submitUpdateAvailabilityTask(ListingGoodsHeadDetail listingGoodsHeadDetail, String userId) {
        if (StringUtils.isEmpty(listingGoodsHeadDetail.getIds())) {
            return AjaxResult.error("请选择需要修改可用状态的商品");
        }
        
        // 处理目标状态
        Integer targetAvailability = listingGoodsHeadDetail.getTargetAvailability();
        if (targetAvailability == null) {
            return AjaxResult.error("请选择目标可用状态");
        }
        
        // 对于临时不可用状态，需要设置开始日期和下一个可用日期
        if (targetAvailability == 2) {
            if (StringUtils.isEmpty(listingGoodsHeadDetail.getStartDate())) {
                return AjaxResult.error("临时不可用状态必须设置不可用开始日期");
            }
            if (StringUtils.isEmpty(listingGoodsHeadDetail.getNextDate())) {
                return AjaxResult.error("临时不可用状态必须设置下一个可用日期");
            }
        }
        
        // 对于永久不可用状态，需要设置开始日期
        if (targetAvailability == 3) {
            if (StringUtils.isEmpty(listingGoodsHeadDetail.getStartDate())) {
                return AjaxResult.error("永久不可用状态必须设置不可用开始日期");
            }
        }
        
        // 查询商品信息
        String[] idArray = listingGoodsHeadDetail.getIds().split(",");

        Date now = DateUtils.getNowDate();

        // 将ID数组转换为Long类型列表
        List<Long> goodsIds = Arrays.stream(idArray)
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf).distinct()
                .collect(Collectors.toList());

        if (goodsIds.isEmpty()) {
            return AjaxResult.error("没有有效的商品ID");
        }

        // 批量查询处理中的RPA任务
        AmUpdateAvailabilityTask queryTask = new AmUpdateAvailabilityTask();
        queryTask.setStatus(1); // 处理中状态
        List<AmUpdateAvailabilityTask> processingTasks = amUpdateAvailabilityTaskService.selectAmUpdateAvailabilityTaskList(queryTask);

        // 检查是否有处理中的任务与当前商品相关
        Set<Long> processingGoodsIds = processingTasks.stream()
                .map(AmUpdateAvailabilityTask::getHeadId)
                .collect(Collectors.toSet());

        for (Long goodsId : goodsIds) {
            if (processingGoodsIds.contains(goodsId)) {
                return AjaxResult.error("商品ID: " + goodsId + " 已有处理中的RPA任务，请等待任务完成后再操作");
            }
        }

        // 批量查询商品信息
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.stream().map(e -> e.intValue()).toArray(Integer[]::new));

        if (goodsHeads.isEmpty()) {
            return AjaxResult.error("未找到有效的商品信息");
        }

        // 批量查询现有的商品详情记录
        List<ListingGoodsHeadDetail> existingDetails = listingGoodsHeadDetailService.selectListingGoodsHeadDetailByHeadIds(goodsIds);
        Map<Long, ListingGoodsHeadDetail> existingDetailsMap = existingDetails.stream()
                .collect(Collectors.toMap(ListingGoodsHeadDetail::getHeadId, Function.identity()));

        // 准备新增和更新的详情记录列表
        List<ListingGoodsHeadDetail> detailsToInsert = new ArrayList<>();
        List<ListingGoodsHeadDetail> detailsToUpdate = new ArrayList<>();
        List<AmUpdateAvailabilityTask> rpaTasks = new ArrayList<>();

        // 处理每个商品
        for (GoodsHead goodsHead : goodsHeads) {
            Long goodsId = Long.valueOf(goodsHead.getId());

            // 处理商品详情记录
            ListingGoodsHeadDetail detail = existingDetailsMap.get(goodsId);
            if (detail == null) {
                detail = new ListingGoodsHeadDetail();
                detail.setHeadId(goodsId);
                detail.setCurrentAvailability(null); // 初始不设置当前状态，由RPA在处理时获取
                detail.setStartDate(listingGoodsHeadDetail.getStartDate());
                detail.setNextDate(listingGoodsHeadDetail.getNextDate());
                detail.setDelFlag(0);
                detail.setCreateBy(userId);
                detail.setCreateTime(now);
                detail.setRpaStatus(0);

                detailsToInsert.add(detail);
            } else {
                detail.setStartDate(listingGoodsHeadDetail.getStartDate());
                detail.setNextDate(listingGoodsHeadDetail.getNextDate());
                detail.setRpaStatus(0);

                detailsToUpdate.add(detail);
            }

            // 创建RPA任务记录
            AmUpdateAvailabilityTask rpaTask = new AmUpdateAvailabilityTask();
            rpaTask.setHeadId(goodsId);
            rpaTask.setAsin(goodsHead.getPlatformGoodsId());
            rpaTask.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            rpaTask.setPlatformGoodsCode(goodsHead.getPlatformGoodsCode());
            rpaTask.setVcCode(Objects.equals(goodsHead.getPublishType(), PublishType.VCDF.getType()) ? "WM741" : "IH75B");
            rpaTask.setStatus(0); // 待处理状态
            rpaTask.setTargetAvailability(targetAvailability);
            // 转换一下格式，从2025-04-18转为04/13/2025
            String startDate = listingGoodsHeadDetail.getStartDate();
            if (StrUtil.isNotBlank(startDate)) {
                startDate = DateUtils.parseDateToStr("MM/dd/yyyy", DateUtils.parseDate(startDate));
            }
            rpaTask.setStartDate(startDate);
            String nextDate = listingGoodsHeadDetail.getNextDate();
            if (StrUtil.isNotBlank(nextDate)) {
                nextDate = DateUtils.parseDateToStr("MM/dd/yyyy", DateUtils.parseDate(nextDate));
            }
            rpaTask.setNextDate(nextDate);
            rpaTask.setDelFlag(0);
            rpaTask.setCreateBy(userId);
            rpaTask.setCreateTime(now);

            amUpdateAvailabilityTaskService.delUnProcessedTask(goodsId);

            rpaTasks.add(rpaTask);
        }

        // 批量插入和更新数据
        if (!detailsToInsert.isEmpty()) {
            listingGoodsHeadDetailService.batchInsertListingGoodsHeadDetail(detailsToInsert);
        }

        if (!detailsToUpdate.isEmpty()) {
            listingGoodsHeadDetailService.batchUpdateListingGoodsHeadDetail(detailsToUpdate, null);
        }

        // 批量插入RPA任务
        for (AmUpdateAvailabilityTask rpaTask : rpaTasks) {
            amUpdateAvailabilityTaskService.insertAmUpdateAvailabilityTask(rpaTask);
        }

        return AjaxResult.success("任务创建成功，RPA将在后台执行更新操作");
    }

    /**
     * 转换为标签详情VO列表
     *
     * @param goodsHeadVOList 原始数据列表
     * @return 标签详情VO列表
     */
    public List<ListingTagDetailVO> convertToTagDetailVOList(List<GoodsHeadVO> goodsHeadVOList) {
        if (goodsHeadVOList == null || goodsHeadVOList.isEmpty()) {
            return new ArrayList<>();
        }
        return goodsHeadVOList.stream().map(this::convertToTagDetailVO).collect(Collectors.toList());
    }

    /**
     * 转换为表现详情VO列表
     *
     * @param goodsHeadVOList 原始数据列表
     * @return 表现详情VO列表
     */
    public List<ListingPerformanceDetailVO> convertToPerformanceDetailVOList(List<GoodsHeadVO> goodsHeadVOList) {
        if (goodsHeadVOList == null || goodsHeadVOList.isEmpty()) {
            return new ArrayList<>();
        }
        return goodsHeadVOList.stream().map(this::convertToPerformanceDetailVO).collect(Collectors.toList());
    }

    /**
     * 转换为动态标签详情VO列表
     *
     * @param goodsHeadVOList 原始数据列表
     * @return 动态标签详情VO列表
     */
    public List<DynamicListingDetailVO> convertToDynamicTagDetailVOList(List<GoodsHeadVO> goodsHeadVOList) {
        if (goodsHeadVOList == null || goodsHeadVOList.isEmpty()) {
            return new ArrayList<>();
        }
        return goodsHeadVOList.stream().map(vo -> convertToDynamicTagDetailVO(vo, true)).collect(Collectors.toList());
    }

    /**
     * 转换为动态表现详情VO列表
     *
     * @param goodsHeadVOList 原始数据列表
     * @return 动态表现详情VO列表
     */
    public List<DynamicListingDetailVO> convertToDynamicPerformanceDetailVOList(List<GoodsHeadVO> goodsHeadVOList) {
        if (goodsHeadVOList == null || goodsHeadVOList.isEmpty()) {
            return new ArrayList<>();
        }
        return goodsHeadVOList.stream().map(vo -> convertToDynamicTagDetailVO(vo, false)).collect(Collectors.toList());
    }

    /**
     * 转换单个GoodsHeadVO为TagDetailVO
     */
    private ListingTagDetailVO convertToTagDetailVO(GoodsHeadVO goodsHeadVO) {
        if (goodsHeadVO == null) {
            return new ListingTagDetailVO();
        }

        ListingTagDetailVO tagDetailVO = new ListingTagDetailVO();

        // 基础字段
        tagDetailVO.setId(goodsHeadVO.getId());
        tagDetailVO.setPdmGoodsCode(goodsHeadVO.getPdmGoodsCode());
        tagDetailVO.setShopCode(goodsHeadVO.getShopCode());
        tagDetailVO.setPlatformGoodsCode(goodsHeadVO.getPlatformGoodsCode());
        tagDetailVO.setPlatformGoodsId(goodsHeadVO.getPlatformGoodsId());

        // 解析标签数据
        String listingPerformance = goodsHeadVO.getListingPerformance();
        JSONArray jsonArray = StrUtil.isBlank(listingPerformance) ? new JSONArray() : JSON.parseArray(listingPerformance);

        // 退货率等级
        tagDetailVO.setRefundRateLevel(extractRefundRateLevel(goodsHeadVO, jsonArray));

        // 是否主链接
        tagDetailVO.setIsMainListing(goodsHeadVO.getIsMain() != null && goodsHeadVO.getIsMain() == 1 ? "是" : "否");

        // 是否独立站专卖
        tagDetailVO.setIsIndependentSiteOnly("Y".equals(goodsHeadVO.getIndependentSiteOnly()) ? "是" : "否");

        // 是否库存黑名单
        tagDetailVO.setIsInventoryBlacklist("Y".equals(goodsHeadVO.getInventoryExclude()) ? "是" : "否");

        // 是否红线价白名单
        tagDetailVO.setIsRedLineWhitelist("Y".equals(goodsHeadVO.getRedLineBlackFlag()) ? "是" : "否");

        // 是否Coupon黑名单
        tagDetailVO.setIsCouponBlacklist(goodsHeadVO.getIsCouponBlack() != null && goodsHeadVO.getIsCouponBlack() == 1 ? "是" : "否");

        // 完整标签列表
        tagDetailVO.setAllTags(formatTagsForDisplay(jsonArray, true));

        return tagDetailVO;
    }

    /**
     * 转换单个GoodsHeadVO为PerformanceDetailVO
     */
    private ListingPerformanceDetailVO convertToPerformanceDetailVO(GoodsHeadVO goodsHeadVO) {
        if (goodsHeadVO == null) {
            return new ListingPerformanceDetailVO();
        }

        ListingPerformanceDetailVO performanceDetailVO = new ListingPerformanceDetailVO();

        // 基础字段
        performanceDetailVO.setId(goodsHeadVO.getId());
        performanceDetailVO.setPdmGoodsCode(goodsHeadVO.getPdmGoodsCode());
        performanceDetailVO.setShopCode(goodsHeadVO.getShopCode());
        performanceDetailVO.setPlatformGoodsCode(goodsHeadVO.getPlatformGoodsCode());
        performanceDetailVO.setPlatformGoodsId(goodsHeadVO.getPlatformGoodsId());

        // 解析表现数据
        String listingPerformance = goodsHeadVO.getListingPerformance();
        JSONArray jsonArray = StrUtil.isBlank(listingPerformance) ? new JSONArray() : JSON.parseArray(listingPerformance);

        // 解析各种表现指标
        performanceDetailVO.setNoImpression(containsPerformance(jsonArray, "无曝光") ? "是" : "否");
        performanceDetailVO.setLowConversion(containsPerformance(jsonArray, "低转化") ? "是" : "否");
        performanceDetailVO.setHighSpend(containsPerformance(jsonArray, "高花费") ? "是" : "否");
        performanceDetailVO.setDependsOnAds(containsPerformance(jsonArray, "依赖广告") ? "是" : "否");

        // VCPO标签和其他标签
        performanceDetailVO.setVcpoLabel(extractVcpoLabel(jsonArray));
        performanceDetailVO.setStockLabel(extractStockLabel(jsonArray));
        performanceDetailVO.setListingQualityLabel(extractListingQualityLabel(jsonArray));

        // 完整表现列表
        performanceDetailVO.setAllPerformance(formatTagsForDisplay(jsonArray, false));

        return performanceDetailVO;
    }

    /**
     * 提取退货率等级
     */
    private String extractRefundRateLevel(GoodsHeadVO goodsHeadVO, JSONArray jsonArray) {
        // 优先从sku退货率标签获取
        if (goodsHeadVO.getSkuRateLabel() != null) {
            String value = getOriginValue(goodsHeadVO.getSkuRateLabel());
            if (value != null) {
                return value; // 高/中/低
            }
        }

        // 从listing退货率标签获取
        if (goodsHeadVO.getListingRateLabel() != null) {
            String value = getOriginValue(goodsHeadVO.getListingRateLabel());
            if (value != null) {
                return value; // 高/中/低
            }
        }

        return "无";
    }

    /**
     * 检查是否包含指定表现
     */
    private boolean containsPerformance(JSONArray jsonArray, String performance) {
        for (int i = 0; i < jsonArray.size(); i++) {
            String item = jsonArray.getString(i);
            if (item != null && item.contains(performance)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 提取VCPO标签
     */
    private String extractVcpoLabel(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.size(); i++) {
            String item = jsonArray.getString(i);
            if (item != null && item.contains("VCPO")) {
                return item;
            }
        }
        return "-";
    }

    /**
     * 提取库存标签
     */
    private String extractStockLabel(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.size(); i++) {
            String item = jsonArray.getString(i);
            if (item != null && (item.contains("库存") || item.contains("stock"))) {
                return item;
            }
        }
        return "-";
    }

    /**
     * 提取链接质量标签
     */
    private String extractListingQualityLabel(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.size(); i++) {
            String item = jsonArray.getString(i);
            if (item != null && (item.contains("质量") || item.contains("链接"))) {
                return item;
            }
        }
        return "-";
    }

    /**
     * 格式化标签用于显示
     *
     * @param jsonArray 标签数组
     * @param isTag     是否为标签（true）还是表现（false）
     * @return 格式化后的字符串
     */
    private String formatTagsForDisplay(JSONArray jsonArray, boolean isTag) {
        if (jsonArray == null || jsonArray.isEmpty()) {
            return "-";
        }

        List<String> displayTags = new ArrayList<>();

        // 获取字典数据
        List<SysDictData> labelDict = dictService.getType("listing_label_list");
        List<SysDictData> performanceDict = dictService.getType("listing_performance_list");

        // 防止空指针
        if (labelDict == null) labelDict = new ArrayList<>();
        if (performanceDict == null) performanceDict = new ArrayList<>();

        Set<String> labelValues = labelDict.stream().map(SysDictData::getDictValue).collect(Collectors.toSet());
        Set<String> performanceValues = performanceDict.stream().map(SysDictData::getDictValue).collect(Collectors.toSet());

        for (int i = 0; i < jsonArray.size(); i++) {
            String item = jsonArray.getString(i);
            if (item != null) {
                if (isTag) {
                    // 标签：使用字典判断或包含特定关键词
                    if (labelValues.contains(item) || item.contains("rate_label") || item.contains("self_")) {
                        displayTags.add(formatSingleTag(item, labelDict));
                    }
                } else {
                    // 表现：使用字典判断
                    if (performanceValues.contains(item)) {
                        displayTags.add(formatSingleTag(item, performanceDict));
                    }
                }
            }
        }

        return displayTags.isEmpty() ? "-" : String.join(",", displayTags);
    }

    /**
     * 格式化单个标签
     */
    private String formatSingleTag(String tag, List<SysDictData> dictList) {
        // 首先尝试从字典中获取显示名称
        for (SysDictData dict : dictList) {
            if (dict.getDictValue().equals(tag)) {
                return dict.getDictLabel();
            }
        }

        // 如果字典中没有，使用原有的格式化逻辑
        if (tag.startsWith("sku_rate_label_")) {
            return "sku退货率" + tag.replace("sku_rate_label_", "");
        } else if (tag.startsWith("listing_rate_label_")) {
            return "listing退货率" + tag.replace("listing_rate_label_", "");
        } else if (tag.startsWith("self_")) {
            return tag.replace("self_", "");
        }
        return tag;
    }

    /**
     * 处理主要标签概览
     */
    public void processMainTags(List<GoodsHeadVO> goodsHeadVOList) {
        for (GoodsHeadVO goodsHeadVO : goodsHeadVOList) {
            List<String> mainTags = new ArrayList<>();

            // 退货率标签
            String refundRate = extractRefundRateLevel(goodsHeadVO, null);
            if (!"无".equals(refundRate)) {
                mainTags.add(refundRate + "退货率");
            }

            // 主链接
            if (goodsHeadVO.getIsMain() != null && goodsHeadVO.getIsMain() == 1) {
                mainTags.add("主链接");
            }

            // 独立站专卖
            if ("Y".equals(goodsHeadVO.getIndependentSiteOnly())) {
                mainTags.add("独立站专卖");
            }

            // 库存黑名单
            if ("Y".equals(goodsHeadVO.getInventoryExclude())) {
                mainTags.add("库存黑名单");
            }

            goodsHeadVO.setMainTags(mainTags.isEmpty() ? "-" : String.join(",", mainTags));
        }
    }

    /**
     * 转换为动态详情VO
     *
     * @param goodsHeadVO 原始数据
     * @param isTag       是否为标签（true）还是表现（false）
     * @return 动态详情VO
     */
    private DynamicListingDetailVO convertToDynamicTagDetailVO(GoodsHeadVO goodsHeadVO, boolean isTag) {
        if (goodsHeadVO == null) {
            return new DynamicListingDetailVO();
        }

        DynamicListingDetailVO dynamicVO = new DynamicListingDetailVO();

        // 添加基础字段
        dynamicVO.addBaseField("id", goodsHeadVO.getId());
        dynamicVO.addBaseField("pdmGoodsCode", goodsHeadVO.getPdmGoodsCode());
        dynamicVO.addBaseField("shopCode", goodsHeadVO.getShopCode());
        dynamicVO.addBaseField("platformGoodsCode", goodsHeadVO.getPlatformGoodsCode());
        dynamicVO.addBaseField("platformGoodsId", goodsHeadVO.getPlatformGoodsId());

        if (isTag) {
            // 对于标签，先添加基础标签字段
            addBasicTagFields(dynamicVO, goodsHeadVO);
        }

        // 解析listingPerformance字段，动态添加标签或表现字段
        String listingPerformance = goodsHeadVO.getListingPerformance();
        if (StringUtils.isNotEmpty(listingPerformance)) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(listingPerformance);
                addDynamicFields(dynamicVO, jsonArray, isTag, goodsHeadVO);
            } catch (Exception e) {
                // 解析失败时添加默认字段
                dynamicVO.addDynamicField("完整" + (isTag ? "标签" : "表现") + "列表", "-");
            }
        } else {
            addDynamicFields(dynamicVO, new JSONArray(), isTag, goodsHeadVO);
        }

        return dynamicVO;
    }

    /**
     * 添加基础标签字段
     *
     * @param dynamicVO   动态VO
     * @param goodsHeadVO 原始数据
     */
    private void addBasicTagFields(DynamicListingDetailVO dynamicVO, GoodsHeadVO goodsHeadVO) {
        // 主链接 - 只有当isMain=1时才添加标签
        if (goodsHeadVO.getIsMain() != null && goodsHeadVO.getIsMain() == 1) {
            dynamicVO.addDynamicField("主链接", "主链接");
        }

        // 独立站专卖 - 只有当independentSiteOnly="Y"时才添加标签
        if ("Y".equals(goodsHeadVO.getIndependentSiteOnly())) {
            dynamicVO.addDynamicField("独立站专卖", "独立站专卖");
        }

        // 库存黑名单 - 只有当inventoryExclude="Y"时才添加标签
        if ("Y".equals(goodsHeadVO.getInventoryExclude())) {
            dynamicVO.addDynamicField("库存黑名单", "库存黑名单");
        }

        // Coupon黑名单 - 只有当isCouponBlack=1时才添加标签
        if (goodsHeadVO.getIsCouponBlack() != null && goodsHeadVO.getIsCouponBlack() == 1) {
            dynamicVO.addDynamicField("Coupon黑名单", "Coupon黑名单");
        }

        // 红线价白名单 - 只有当redLineBlackFlag="Y"时才添加标签
        if ("Y".equals(goodsHeadVO.getRedLineBlackFlag())) {
            dynamicVO.addDynamicField("红线价白名单", "红线价白名单");
        }
    }

    /**
     * 动态添加字段
     *
     * @param dynamicVO   动态VO
     * @param jsonArray   数据数组
     * @param isTag       是否为标签
     * @param goodsHeadVO 原始数据（用于获取基础标签）
     */
    private void addDynamicFields(DynamicListingDetailVO dynamicVO, JSONArray jsonArray, boolean isTag, GoodsHeadVO goodsHeadVO) {
        // 获取字典数据
        List<SysDictData> labelDict = dictService.getType("listing_label_list");
        List<SysDictData> performanceDict = dictService.getType("listing_performance_list");

        // 防止空指针
        if (labelDict == null) labelDict = new ArrayList<>();
        if (performanceDict == null) performanceDict = new ArrayList<>();

        Set<String> labelValues = labelDict.stream().map(SysDictData::getDictValue).collect(Collectors.toSet());
        Set<String> performanceValues = performanceDict.stream().map(SysDictData::getDictValue).collect(Collectors.toSet());

        // 收集匹配的项目
        Set<String> matchedItems = new HashSet<>();
        List<String> displayItems = new ArrayList<>();

        // 如果是标签，先收集基础标签到完整列表中
        if (isTag) {
            collectBasicTagsForCompleteList(goodsHeadVO, displayItems);
        }

        for (int i = 0; i < jsonArray.size(); i++) {
            String item = jsonArray.getString(i);
            if (item != null) {
                boolean shouldInclude = false;
                String displayName = item;

                if (isTag) {
                    // 标签：使用字典判断或包含特定关键词
                    if (labelValues.contains(item) || item.contains("rate_label") || item.contains("self_")) {
                        shouldInclude = true;
                        displayName = formatSingleTag(item, labelDict);
                    }
                } else {
                    // 表现：使用字典判断
                    if (performanceValues.contains(item)) {
                        shouldInclude = true;
                        displayName = formatSingleTag(item, performanceDict);
                    }
                }

                if (shouldInclude) {
                    matchedItems.add(item);
                    displayItems.add(displayName);
                    // 为匹配的项目添加标签
                    dynamicVO.addDynamicField(displayName, displayName);
                }
            }
        }

        // 添加完整列表字段
        String completeList = displayItems.isEmpty() ? "-" : String.join(",", displayItems);
        dynamicVO.addDynamicField("完整" + (isTag ? "标签" : "表现") + "列表", completeList);
    }

    /**
     * 收集基础标签到完整列表中
     *
     * @param goodsHeadVO  原始数据
     * @param displayItems 显示项目列表
     */
    private void collectBasicTagsForCompleteList(GoodsHeadVO goodsHeadVO, List<String> displayItems) {
        // 主链接
        if (goodsHeadVO.getIsMain() != null && goodsHeadVO.getIsMain() == 1) {
            displayItems.add("主链接");
        }

        // 独立站专卖
        if ("Y".equals(goodsHeadVO.getIndependentSiteOnly())) {
            displayItems.add("独立站专卖");
        }

        // 库存黑名单
        if ("Y".equals(goodsHeadVO.getInventoryExclude())) {
            displayItems.add("库存黑名单");
        }

        // Coupon黑名单
        if (goodsHeadVO.getIsCouponBlack() != null && goodsHeadVO.getIsCouponBlack() == 1) {
            displayItems.add("Coupon黑名单");
        }

        // 红线价白名单
        if ("Y".equals(goodsHeadVO.getRedLineBlackFlag())) {
            displayItems.add("红线价白名单");
        }
    }

    /**
     * 获取动态表头 - 标签
     *
     * @return 表头映射（字段名 -> 显示名）
     */
    public LinkedHashMap<String, String> getDynamicTagHeaders() {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();

        // 基础表头
        headers.put("id", "主键");
        headers.put("pdmGoodsCode", "商品编码");
        headers.put("shopCode", "店铺编码");
        headers.put("platformGoodsCode", "平台商品编码");
        headers.put("platformGoodsId", "平台销售编码");

        // 基础标签字段（只有有数据时才会出现）
        headers.put("主链接", "主链接");
        headers.put("独立站专卖", "独立站专卖");
        headers.put("库存黑名单", "库存黑名单");
        headers.put("Coupon黑名单", "Coupon黑名单");
        headers.put("红线价白名单", "红线价白名单");

        // 动态标签表头（从字典获取，排除基础标签）
        List<SysDictData> labelDict = dictService.getType("listing_label_list");
        if (labelDict != null) {
            for (SysDictData dict : labelDict) {
                // 排除已经作为基础字段处理的标签
                String dictValue = dict.getDictValue();
                if (!dictValue.equals("self_独立站专卖") &&
                        !dictValue.equals("self_主链接") &&
                        !dictValue.equals("self_库存更新黑名单") &&
                        !dictValue.equals("self_红线价管控白名单")) {
                    headers.put(dict.getDictLabel(), dict.getDictLabel());
                }
            }
        }

        // 特殊标签（退货率相关）
        headers.put("sku退货率高", "sku退货率高");
        headers.put("sku退货率中", "sku退货率中");
        headers.put("sku退货率低", "sku退货率低");
        headers.put("listing退货率高", "listing退货率高");
        headers.put("listing退货率中", "listing退货率中");
        headers.put("listing退货率低", "listing退货率低");

        // 完整列表
        headers.put("完整标签列表", "完整标签列表");

        return headers;
    }

    /**
     * 获取动态表头 - 表现
     *
     * @return 表头映射（字段名 -> 显示名）
     */
    public LinkedHashMap<String, String> getDynamicPerformanceHeaders() {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();

        // 基础表头
        headers.put("id", "主键");
        headers.put("pdmGoodsCode", "商品编码");
        headers.put("shopCode", "店铺编码");
        headers.put("platformGoodsCode", "平台商品编码");
        headers.put("platformGoodsId", "平台销售编码");

        // 动态表现表头
        List<SysDictData> performanceDict = dictService.getType("listing_performance_list");
        if (performanceDict != null) {
            for (SysDictData dict : performanceDict) {
                headers.put(dict.getDictLabel(), dict.getDictLabel());
            }
        }

        // 完整列表
        headers.put("完整表现列表", "完整表现列表");

        return headers;
    }
}

