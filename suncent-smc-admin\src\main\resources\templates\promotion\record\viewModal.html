<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header(${pageTitle ?: 'BD促销详情'})" />
    <style>
        /* 详情页面样式 */
        .view-form .form-control[readonly] {
            background-color: #f9f9f9;
            cursor: default;
        }
        
        .view-form .form-control[disabled] {
            background-color: #f9f9f9;
            cursor: default;
        }
        
        .asin-detail-table {
            margin-top: 20px;
        }
        
        .asin-detail-table .table {
            font-size: 12px;
        }
        
        .asin-detail-table .table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }
        
        .asin-detail-table .table td {
            text-align: center;
            vertical-align: middle;
        }
        
        .price-cell {
            text-align: right;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status-draft { background-color: #d1ecf1; color: #0c5460; }
        .status-attention { background-color: #fff3cd; color: #856404; }
        .status-approved { background-color: #d4edda; color: #155724; }
        .status-canceled { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <!-- 基本信息 -->
        <form class="form-horizontal m view-form" id="form-record-view">
            <input name="id" type="hidden" th:value="${record?.id}">
            
            <div class="form-group">
                <label class="col-sm-3 control-label">促销名称：</label>
                <div class="col-sm-8">
                    <input name="promotionName" class="form-control" type="text" readonly th:value="${record?.promotionName}">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label">站点：</label>
                <div class="col-sm-8">
                    <select name="site" class="form-control" disabled>
                        <option value="US" th:selected="${record?.site == 'US' or record?.site == null}">美国站</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label">刊登类型：</label>
                <div class="col-sm-8">
                    <select name="publishType" class="form-control" disabled>
                        <option value="5" th:selected="${record?.publishType == 5 or record?.publishType == null}">VCDF</option>
                        <option value="6" th:selected="${record?.publishType == 6}">VCPO</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label">事件类型：</label>
                <div class="col-sm-8">
                    <select name="eventType" class="form-control" disabled>
                        <option value="1" th:selected="${record?.eventType == 1 or record?.eventType == null}">自定义日期</option>
                        <option value="2" th:selected="${record?.eventType == 2}">会员日</option>
                        <option value="3" th:selected="${record?.eventType == 3}">黑五</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label">开始时间：</label>
                <div class="col-sm-8">
                    <input name="startDateUtc" class="form-control" type="text" readonly th:value="${record?.startDateUtc}">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label">结束时间：</label>
                <div class="col-sm-8">
                    <input name="endDateUtc" class="form-control" type="text" readonly th:value="${record?.endDateUtc}">
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="form-control-static">
                        <span class="status-badge" th:classappend="${'status-' + (#strings.toLowerCase(record?.status ?: 'draft'))}"
                              th:text="${record?.status}">
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="3" readonly th:text="${record?.remark}"></textarea>
                </div>
            </div>
        </form>



        <!-- ASIN详情表格 -->
        <div class="asin-detail-table">
            <h4 style="margin-bottom: 15px; color: #495057;">
                <i class="fa fa-list"></i> ASIN详情列表
            </h4>
            <div class="table-responsive">
                <table id="asin-detail-bootstrap-table"></table>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    
    <script th:inline="javascript">
        var prefix = ctx + "promotion/record";
        var recordData = /*[[${record}]]*/ null;

        $(function() {
            // 初始化ASIN详情表格
            initAsinDetailTable();
        });

        // 初始化ASIN详情表格
        function initAsinDetailTable() {
            var asinList = recordData && recordData.asinList ? recordData.asinList : [];

            var options = {
                id: "asin-detail-bootstrap-table",
                data: asinList,
                pagination: true,
                showSearch: false,
                showColumns: false,
                showRefresh: false,
                showToggle: false,
                pageSize: 20,
                pageList: [10, 20, 50, 100],
                sortName: "id",
                sortOrder: "asc",
                sidePagination: "client",
                columns: [{
                    field: 'index',
                    title: '序号',
                    width: 60,
                    align: 'center',
                    formatter: function(value, row, index) {
                        return index + 1;
                    }
                }, {
                    field: 'platformGoodsId',
                    title: 'ASIN',
                    width: 120,
                    align: 'center',
                }, {
                    field: 'platformGoodsCode',
                    title: '平台商品编码',
                    width: 120,
                    align: 'center',
                }, {
                    field: 'pdm_goods_code',
                    title: '商品编码',
                    width: 120,
                }, {
                    field: 'standardPrice',
                    title: 'Cost Price',
                    width: 80,
                    align: 'right',
                    formatter: function(value, row, index) {
                        return value ? '$' + parseFloat(value).toFixed(2) : '-';
                    }
                }, {
                    field: 'referencePrice',
                    title: 'Reference price',
                    width: 80,
                    align: 'right',
                    formatter: function(value, row, index) {
                        return value ? '$' + parseFloat(value).toFixed(2) : '-';
                    }
                }, {
                    field: 'dealPrice',
                    title: 'Deal price',
                    width: 80,
                    align: 'right',
                    formatter: function(value, row, index) {
                        return value ? '$' + parseFloat(value).toFixed(2) : '-';
                    }
                }, {
                    field: 'discount',
                    title: 'Discount',
                    width: 80,
                    align: 'right',
                    formatter: function(value, row, index) {
                        if (row.referencePrice && row.dealPrice && row.referencePrice > 0) {
                            var discount = ((row.referencePrice - row.dealPrice) / row.referencePrice) * 100;
                            return discount.toFixed(1) + '%';
                        }
                        return '-';
                    }
                }, {
                    field: 'perUnitFunding',
                    title: 'Per unit funding',
                    width: 80,
                    align: 'right',
                    formatter: function(value, row, index) {
                        return value ? '$' + parseFloat(value).toFixed(2) : '-';
                    }
                }, {
                    field: 'committedUnits',
                    title: 'Committed units',
                    width: 80,
                    align: 'center',
                    formatter: function(value, row, index) {
                        return value ? parseInt(value).toLocaleString() : '-';
                    }
                }, {
                    field: 'expectedDemand',
                    title: 'Expected demand',
                    width: 80,
                    align: 'center',
                    formatter: function(value, row, index) {
                        return value ? parseInt(value).toLocaleString() : '-';
                    }
                }]
            };

            $.table.init(options);
        }
    </script>
</body>
</html>
