<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suncent.smc.persistence.ads.mapper.AdsMapper">

    <resultMap type="com.suncent.smc.persistence.ads.domain.FitmentDemand" id="adsFitmentDemand" >
        <result property="id" column="id"/>
        <result property="goodsCode" column="goods_code"/>
        <result property="productCode" column="product_code"/>
        <result property="partNumber" column="part_number"/>
        <result property="asin" column="asin"/>
        <result property="platformCode" column="platform_code"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <insert id="insertItDemandA">
        INSERT INTO `ads_fitmentdata`.`it_demand` (`申请日期`, `品牌`, `brand_code`, `shop_code`, `browser_name`, `SKU`,`product_code`, `Part Number`, `ASIN`, `类型`, `需求类型`, `status`,`create_time`, `country`)
        VALUES (#{time}, #{brandEnName}, #{brandAaiaId}, #{shopCode}, #{browserName}, #{sku},#{productCode}, #{pn}, #{asin}, #{type}, #{demandType}, '0', now(), #{country});
    </insert>


    <insert id="insertItDemandCompare">
        INSERT INTO `ads_fitmentdata`.`it_demand` (`申请日期`, `品牌`, `brand_code`, `shop_code`, `browser_name`, `SKU`,`product_code`, `Part Number`, `ASIN`, `类型`, `需求类型`, `status`,`create_time`, `country`, `up_data_version`)
        VALUES (#{time}, #{brandEnName}, #{brandAaiaId}, #{shopCode}, #{browserName}, #{sku},#{productCode}, #{pn}, #{asin}, #{type}, #{demandType}, '2', now(), #{country}, #{upDataVersion});
    </insert>

    <select id="countByPnAndAsin" resultType="Integer" >
        SELECT count(*) FROM `ads_fitmentdata`.`it_demand` WHERE `status` IN ('1', '0') AND  `ASIN` = #{asin}
    </select>


    <select id="countByPnAndAsinCompare" resultType="Integer" >
        SELECT count(*) FROM `ads_fitmentdata`.`it_demand` WHERE `需求类型` = '2'  AND  `ASIN` = #{asin}
    </select>

    <select id="selectByAsinList" resultType="java.util.Map">
        select `SKU`, `Part Number`, `ASIN`, `status` from `ads_fitmentdata`.`it_demand` where `ASIN` in
        <foreach collection="list" open="(" separator="," close=")" item="asin">
            #{asin}
        </foreach>
    </select>


    <select id="selectAdapterErrorList" resultType="java.util.Map">
        select id,shop_code shopCode,`SKU`, `product_code`,`Part Number`, `ASIN`, `status`,`需求类型` as demandType,up_data_version,currentNumber from `ads_fitmentdata`.`it_demand` where `status` = '2'
            AND STR_TO_DATE(SUBSTRING_INDEX(up_data_version, '-', 1), '%y%m%d') &lt; DATE_SUB(CURDATE(), INTERVAL 5 DAY)
            <if test="currentNumber != null  and currentNumber != ''">
              and currentNumber = #{currentNumber}
            </if>
    </select>

    
    <insert id="insertItDemandByCopyId" >
        INSERT INTO `ads_fitmentdata`.`it_demand`
            (`申请日期`, `品牌`, `brand_code`, `shop_code`, `browser_name`, `SKU`,`product_code`, `Part Number`, `ASIN`, `Link`, `类型`, `对应产品`, `具体问题备注`,`负责产品经理`, `batch`,`upload_time`,`需求类型`,`status`,`error_reason`,`create_time`,`country`,`up_data_version`,`up_data_type`,`currentNumber`)
        SELECT
                DATE_FORMAT(CURDATE(), '%Y%m%d') AS `申请日期`,
               `品牌`,`brand_code`,`shop_code`,`browser_name`,`SKU`,`product_code`,
               #{partNumber} AS `Part Number`,
               `ASIN`,`Link`,`类型`,`对应产品`,`具体问题备注`,`负责产品经理`,`batch`, `upload_time`, '1' as `需求类型`,
               '0' AS `status`,
               `error_reason`,
               NOW() AS `create_time`,
               `country`,
               '' as `up_data_version`,
               '' as `up_data_type`,
               2 AS `currentNumber`
        FROM
            `ads_fitmentdata`.`it_demand`
        WHERE
            `id` = #{itDemandId};
    </insert>

    <select id="selectByStatus" resultType="java.util.Map">
        select `SKU`, `Part Number`, `ASIN`, `status`,`需求类型` as demandType from `ads_fitmentdata`.`it_demand` where
        `status` IN
        <foreach item="status" collection="statusList" open="(" separator="," close=")">
            #{status}
        </foreach>
        <if test="createTime != null  and createTime != ''"> and DATE(create_time) = #{createTime}</if>
    </select>

    <update id="updateItDemandStatusByAsin">
        UPDATE `ads_fitmentdata`.`it_demand` SET `status` = #{status} ,`error_reason` = #{errorReason} WHERE  `ASIN` IN
        <foreach collection="asinList" open="(" separator="," close=")" item="asin">
            #{asin}
        </foreach>
        and `status` = 1
    </update>



    <update id="updateItDemandStatusById">
        UPDATE `ads_fitmentdata`.`it_demand` SET `status` = #{status} ,`error_reason` = #{errorReason} WHERE  `id`=#{id}
    </update>

    <select id="selectFitmentDataEbayByProductCode" resultType="com.suncent.smc.persistence.ads.domain.AdsFitmentDataEbay">
    select  `product_code`, `make`,`year`,`model`,`submodel`,`Engine`,`Trim`, `notes`
       from `ads_fitmentdata`.`ads_fitmentdata_ebay` where  `status` = '1'
       and `product_code` = #{productCode} limit 10000
    </select>


    <select id="selectEbayDataRequirementByProductCode" resultType="com.suncent.smc.persistence.ads.domain.AdsEbayDataRequirement">
    select  `product_code`, `goods_code`, `isfilled`, `isupload`,  `universality`
       from `ads_fitmentdata`.`ebay_data_requirement` where  `goods_code` = #{goodsCode}
       and `product_code` = #{productCode}
    </select>

   <insert id="insertEbayDataRequirement">
    INSERT INTO `ads_fitmentdata`.`ebay_data_requirement` (`product_code`, `goods_code`, `isfilled`, `isupload`)
    VALUES (#{productCode}, #{goodsCode},  '0', '0');
   </insert>
    <insert id="insertAmzJudgeCategoryMap">
        INSERT INTO `ads_amazon`.`amz_judge_category_map` (`department`, `platform`, `country`, `operation_classification`, `product_category`, `categoryID`, `categoryName`, `create_time`, `source`, `del_flag`, category_code)
        VALUES (#{department}, #{platform}, #{country}, #{operationClassification}, #{productCategory}, #{categoryID}, #{categoryName}, now(), #{source}, 0,  #{categoryCode});
    </insert>


    <update id="updateEbayFitmentIsUpload">
        UPDATE `ads_fitmentdata`.`ebay_data_requirement` SET `isupload` = 1  WHERE  `product_code` IN
        <foreach item="productCode" collection="productCodeList" open="(" separator="," close=")">
            #{productCode}
        </foreach>
    </update>
    <update id="updateCaseIdByNodeCorrect">
        UPDATE `ads_amazon`.`amz_judge_category_info` SET `case_id` = #{caseId}  WHERE  asin=#{platformGoodsId} and platform_sku=#{platformGoodsCode} and shop_code=#{shopCode} and sit_code=#{siteCode}
    </update>

    <update id="updateAmzJudgeCategoryInfoStatus">
        UPDATE `ads_amazon`.`amz_judge_category_info` SET `status` = 1  WHERE   asin=#{platformGoodsId} and platform_sku=#{platformGoodsCode} and shop_code=#{shopCode} and sit_code=#{siteCode} and status=0
    </update>

    <select id="selectDicPartnumberByProductCode" resultType="String">
        select `original_partnumber` from `ads_fitmentdata`.`dic_partnumber` where `pdm_partnumber` = #{productCode} limit 1
    </select>

  <sql id="selectadsFitmentdataAmazonVo">
        select
        product_code,
        refNo,
        Position,
        position_origin,
        notes,
        MakeName,
        ModelName,
        SubModelName,
        RegionName ,
        VehicleTypeName,
        YearName,
        Liter,
        BodyTypeName,
        BlockType,
        Cylinders,
        FuelTypeName,
        DriveTypeName,
        AspirationName,
        CylinderHeadTypeName,
        TransmissionControlTypeName,
        vio,
        application,
        PartTerminologyName
    from `ads_fitmentdata`.`ads_fitmentdata_simplify_vio`
    </sql>

     <resultMap type="com.suncent.smc.persistence.ads.domain.AdsFitmentDataVIO" id="adsFitmentResult" >
         <result column="product_code" property="productCode" jdbcType="VARCHAR" />
         <result column="refNo" property="refNo" jdbcType="VARCHAR" />
         <result column="Position" property="position" jdbcType="VARCHAR" />
         <result column="position_origin" property="positionOrigin" jdbcType="VARCHAR" />
         <result column="notes" property="notes" jdbcType="VARCHAR" />
         <result column="MakeName" property="makeName" jdbcType="VARCHAR" />
         <result column="ModelName" property="modelName" jdbcType="VARCHAR" />
         <result column="SubModelName" property="subModelName" jdbcType="VARCHAR" />
         <result column="RegionName" property="regionName" jdbcType="VARCHAR" />
         <result column="VehicleTypeName" property="vehicleTypeName" jdbcType="VARCHAR" />
         <result column="YearName" property="yearName" jdbcType="VARCHAR" />
         <result column="Liter" property="liter" jdbcType="VARCHAR" />
         <result column="BodyTypeName" property="bodyTypeName" jdbcType="VARCHAR" />
         <result column="BlockType" property="blockType" jdbcType="VARCHAR" />
         <result column="Cylinders" property="cylinders" jdbcType="VARCHAR" />
         <result column="FuelTypeName" property="fuelTypeName" jdbcType="VARCHAR" />
         <result column="DriveTypeName" property="driveTypeName" jdbcType="VARCHAR" />
         <result column="AspirationName" property="aspirationName" jdbcType="VARCHAR" />
         <result column="CylinderHeadTypeName" property="cylinderHeadTypeName" jdbcType="VARCHAR" />
         <result column="TransmissionControlTypeName" property="transmissionControlTypeName" jdbcType="VARCHAR" />
         <result column="vio" property="vio" jdbcType="FLOAT" />
         <result column="application" property="application" jdbcType="VARCHAR" />
         <result column="PartTerminologyName" property="partTerminologyName" jdbcType="VARCHAR" />
    </resultMap>


    <select id="selectFitmentDataVIO" resultMap="adsFitmentResult">
        <include refid="selectadsFitmentdataAmazonVo"/>
        where status = '1' and  product_code IN
        <foreach collection="list" open="(" separator="," close=")" item="productCode">
            #{productCode}
        </foreach> and RegionName = 'United States'
    </select>



    <sql id="selectadsFitmentGPTDataVo">
        select
            product_code,
            标题,
            标题指标,
            五点,
            五点指标,
            详情,
            详情指标,
            原始,
            原始指标,
            create_time,
            status
        from `ads_fitmentdata`.`ads_fitmentdata_detail`
    </sql>

    <resultMap type="com.suncent.smc.persistence.ads.domain.AdsFitmentGPTData" id="adsFitmentGPTResult" >
        <result property="productCode" column="product_code"/>
        <result property="title" column="标题"/>
        <result property="titleIndex" column="标题指标"/>
        <result property="description" column="五点"/>
        <result property="descriptionIndex" column="五点指标"/>
        <result property="detail" column="详情"/>
        <result property="detailIndex" column="详情指标"/>
        <result property="original" column="原始"/>
        <result property="originalIndex" column="原始指标"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <select id="selectFitmentGPTData" resultMap="adsFitmentGPTResult">
        <include refid="selectadsFitmentGPTDataVo"/>
        where status =1 and  product_code IN
        <foreach collection="list" open="(" separator="," close=")" item="productCode">
            #{productCode}
        </foreach>
    </select>

    <select id="getDataRedLineMaxBatch" resultType="java.lang.String">
        select max(batch) from `ads_amazon`.redline_price
    </select>

    <select id="getRedLinePriceList" resultType="java.util.Map">
        select sku,operation_classification,batch,platform, redline_price from `ads_amazon`.redline_price where batch = #{dataMaxBatch}
        and platform = #{platform}
        and sku in
        <foreach collection="goodsCodeList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getDataRedLineGoodsByMaxBatch" resultType="java.lang.String">
        select sku from `ads_amazon`.redline_price where batch = #{dataMaxBatch} group by sku
    </select>

    <resultMap type="com.suncent.smc.persistence.ads.domain.AdsListingLabel" id="adsListingLabelResult" >
        <result property="platformCode" column="platform_code"/>
        <result property="platformSku" column="platform_sku"/>
        <result property="productCode" column="product_code"/>
        <result property="asin" column="asin"/>
        <result property="sku" column="sku"/>
        <result property="sitCode" column="sit_code"/>
        <result property="shopCode" column="shop_code"/>
        <result property="operatorsName" column="operators_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="classificationName" column="classification_name"/>
        <result property="operationClassification" column="operation_classification"/>
        <result property="salePrice" column="sale_price"/>
        <result property="marketPrice" column="market_price"/>
        <result property="priceLabel" column="price_label"/>
        <result property="aplus" column="aplus"/>
        <result property="amazonfit" column="amazonfit"/>
        <result property="video" column="video"/>
        <result property="lowImg" column="low_img"/>
        <result property="badReview" column="bad_review"/>
        <result property="problemNum" column="problem_num"/>
        <result property="listingLabel" column="listing_label"/>
        <result property="noImpression" column="no_impression"/>
        <result property="lowCr" column="low_cr"/>
        <result property="highSpend" column="high_spend"/>
        <result property="highAdsRate" column="high_ads_rate"/>
        <result property="adsPerformanceLabel" column="ads_performance_label"/>
        <result property="listingStatus" column="listing_status"/>
        <result property="stockLabel" column="stock_label"/>
        <result property="vcLabel" column="vc_label"/>
        <result property="saleStatus" column="sale_status"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getListingLabelList" resultMap="adsListingLabelResult">
     where  shop_code = #{shopCode}
    order by id asc
    </select>

    <select id="getListingLabelListPaged" resultMap="adsListingLabelResult">
        select platform_code,
               platform_sku,
               product_code,
               asin,
               sku,
               sit_code,
               shop_code,
               operators_name,
               dept_name,
               classification_name,
               operation_classification,
               sale_price,
               market_price,
               price_label,
               aplus,
               amazonfit,
               video,
               low_img,
               bad_review,
               problem_num,
               listing_label,
               no_impression,
               low_cr,
               high_spend,
               high_ads_rate,
               ads_performance_label,
               listing_status,
               stock_label,
               vc_label,
               sale_status,
               update_time
        from `ads_amazon`.listing_label
        where shop_code = #{shopCode}
        order by id asc
            LIMIT #{limit}
        OFFSET #{offset}
    </select>

    <select id="getListingLabelByAsin"  resultMap="adsListingLabelResult">
        select   platform_code,platform_sku,product_code,asin,sku,sit_code,shop_code,operators_name,dept_name,classification_name,operation_classification,sale_price,market_price,price_label,aplus,amazonfit,video,low_img,bad_review,problem_num,listing_label,no_impression,low_cr,high_spend,high_ads_rate,ads_performance_label,listing_status,stock_label,vc_label,
                 sale_status,update_time
        from `ads_amazon`.listing_label
        where shop_code =#{shopCode} and asin = #{asin} limit 1
    </select>
    <select id="countByProductCodes" resultType="java.util.Map">
        select product_code, count(*) as count  from `ads_fitmentdata`.`ads_fitmentdata_simplify_vio`
        where status = '1' and product_code in <foreach collection="productCodes" open="(" separator="," close=")" item="productCode">#{productCode}</foreach>
        group by product_code
    </select>

    <resultMap type="com.suncent.smc.persistence.ads.domain.AdsRecordData" id="adsListingCartData" >
        <result property="date" column="date"/>
        <result property="parentAsin" column="parentAsin"/>
        <result property="cart" column="cart"/>
        <result property="pageAsin" column="page_asin"/>
        <result property="shipment" column="shipment"/>
        <result property="merchant" column="merchant"/>
        <result property="country" column="country"/>
        <result property="shopCode" column="shop_code"/>
        <result property="price" column="price"/>
    </resultMap>

    <select id="selectTodayCartList" resultMap="adsListingCartData">
        select date,parentAsin,cart,page_asin,shipment,country,shop_code,price from `ads_amazon`.`suncent_records_data`
        where cart = 1
        <if test="date != null and date != ''">
            and date = #{date}
        </if>
        <if test="shopCode != null and shopCode != ''">
            and shop_code = #{shopCode}
        </if>
        <if test="asinList != null and asinList.size() > 0">
            and page_asin in
            <foreach collection="asinList" open="(" separator="," close=")" item="asin">#{asin}</foreach>
        </if>
    </select>

    <select id="countNodeIncorrectByCoreListing" resultType="java.lang.Integer">
        SELECT count(*)
        FROM  `ads_amazon`.amz_judge_category_info info
                  LEFT JOIN `ads_amazon`.`amazon_listing_data` listing_data ON info.asin = listing_data.asin
        WHERE
            listing_data.`listing` = '核心链接'
          AND info.date = #{nowDate}
          AND info.STATUS = 0
          AND info.judge_flag NOT IN ( "路径错误" )
        ORDER BY
            info.shop_code ASC
    </select>

    <select id="listNodeIncorrectByCoreListing" resultType="com.suncent.smc.persistence.ads.domain.AmzJudgeCategoryInfoVO">
        SELECT info.product_category AS productCategory, info.cat_map AS catMap,info.categoryID AS categoryID, info.categoryName AS categoryName,info.asin, info.platform_code AS platformCode,info.platform_sku AS platformSku, info.shop_code AS shopCode,info.sit_code AS sitCode, info.operators_name AS operatorsName,info.classification_name AS classificationName, info.operation_classification AS operationClassification,info.date, info.node_name AS nodeName,info.node_id AS nodeId, info.is_category AS isCategory,info.judge_flag AS judgeFlag, info.shipment,info.is_vc AS isVc, info.STATUS, info.case_id AS caseId
        FROM  `ads_amazon`.amz_judge_category_info info
            LEFT JOIN `ads_amazon`.`amazon_listing_data` listing_data ON info.asin = listing_data.asin
        WHERE
            listing_data.`listing` = '核心链接'
          AND info.date = #{nowDate}
          AND info.STATUS = 0
          AND info.judge_flag NOT IN ( "路径错误" )
        ORDER BY
            info.shop_code ASC
    </select>

    <select id="countAmzJudgeCategoryMap" resultType="java.lang.Integer">
        select count(*) from `ads_amazon`.amz_judge_category_map
                        where  operation_classification=#{operationClassification}
                          and category_code=#{categoryCode} and categoryID=#{newPlatformCategoryId} and  categoryName=#{newCategoryName}
    </select>

    <select id="selectFitmentDemandList" resultMap="adsFitmentDemand">
        select id,goods_code,product_code,part_number,asin,platform_code,status,create_time,update_time
        from `ads_fitmentdata`.`fitment_demand` where status = 0
    </select>

    <select id="selectFitmentDemandListByType" resultMap="adsFitmentDemand">
        select id,goods_code,product_code,part_number,asin,platform_code,status,create_time,update_time
        from `ads_fitmentdata`.`fitment_demand`
        where status = 0
        <if test="type == 1">
            AND goods_code IS NOT NULL
            AND asin IS NOT NULL
            AND part_number IS NOT NULL
        </if>
        <if test="type == 2">
            AND goods_code IS NOT NULL
            AND asin IS NOT NULL
            AND part_number IS NULL
        </if>
        <if test="type == 3">
            AND goods_code IS NOT NULL
            AND asin IS NULL
        </if>
        ORDER BY id
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countByPnAndAsinBatch" resultType="java.util.Map">
        SELECT platform_goods_id as asin, COUNT(*) as count
        FROM `ads_fitmentdata`.`it_demand`
        WHERE platform_goods_id IN
        <foreach collection="asinList" open="(" separator="," close=")" item="asin">
            #{asin}
        </foreach>
        AND 需求类型 = '0'
        GROUP BY platform_goods_id
    </select>

    <update id="updateFitmentDemandStatus">
        UPDATE `ads_fitmentdata`.`fitment_demand` SET `status` = #{status} ,  `update_time` = now()  WHERE  `id` = #{id}
    </update>



    <resultMap type="com.suncent.smc.persistence.ads.domain.FitExamineDay" id="adsFitExamineDay" >
        <result property="date" column="date"/>
        <result property="platformSku" column="platform_sku"/>
        <result property="productCode" column="product_code"/>
        <result property="sku" column="sku"/>
        <result property="asin" column="asin"/>
        <result property="sitCode" column="sit_code"/>
        <result property="platformCode" column="platform_code"/>
        <result property="shopCode" column="shop_code"/>
        <result property="fitStatus" column="fit_status"/>
        <result property="fitErrowReason" column="fit_errow_reason"/>
    </resultMap>


    <resultMap type="com.suncent.smc.persistence.publication.domain.entity.AdapterManage" id="adapterManage" >
        <result property="id" column="id"/>
        <result property="classificationName" column="classification_name"/>
        <result property="operationClassification" column="operation_classification"/>
        <result property="categoryName" column="category_name"/>
        <result property="productName" column="product_name"/>
        <result property="productCode" column="product_code"/>
        <result property="pdmGoodsCode" column="sku"/>
        <result property="platformGoodsId" column="asin"/>
        <result property="platform" column="platform_code"/>
        <result property="isCoreAsin" column="is_core_asin"/>
        <result property="mpn" column="mpn"/>
        <result property="userName" column="operator"/>
        <result property="deptName" column="dept"/>
        <result property="shopCode" column="shop_code"/>
        <result property="brand" column="brand"/>
        <result property="isControlAsin" column="is_control_asin"/>
        <result property="platformSku" column="platform_sku"/>
        <result property="fitStatus" column="fit_status"/>
        <result property="fitErrowReason" column="fit_errow_reason"/>
        <result property="delStatus" column="del_status"/>
    </resultMap>


    <select id="getFitExamineDayListByAsins" resultMap="adsFitExamineDay">
<!--        select-->
<!--            ANY_VALUE(date) AS date,-->
<!--            ANY_VALUE(platform_sku) AS platform_sku,-->
<!--            ANY_VALUE(product_code) AS product_code,-->
<!--            ANY_VALUE(sku) AS sku,-->
<!--            asin,-->
<!--            ANY_VALUE(sit_code) AS sit_code,-->
<!--            ANY_VALUE(platform_code) AS platform_code,-->
<!--            ANY_VALUE(shop_code) AS shop_code,-->
<!--            ANY_VALUE(fit_status) AS fit_status,-->
<!--            ANY_VALUE(fit_errow_reason) AS fit_errow_reason-->
<!--        from `ads_amazon`.fit_examine_day where-->
<!--        asin in-->
<!--            <foreach collection="asins" open="(" separator="," close=")" item="item">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        GROUP BY asin-->
<!--        ORDER BY shop_code-->
        WITH LatestDates AS (
            SELECT
                date,
                platform_sku,
                product_code,
                sku,
                asin,
                sit_code,
                platform_code,
                shop_code,
                fit_status,
                fit_errow_reason,
                mpn,
                ROW_NUMBER() OVER ( PARTITION BY asin ORDER BY date DESC ) AS rn
            FROM
                `ads_amazon`.fit_examine_day
            WHERE
                asin in
                <foreach collection="asins" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
        ) SELECT
            date,
            platform_sku,
            product_code,
            sku,
            asin,
            sit_code,
            platform_code,
            shop_code,
            fit_status,
            fit_errow_reason,
            mpn
        FROM LatestDates
        WHERE rn = 1
        ORDER BY shop_code;
    </select>
    <select id="listDemandByAsins" resultType="java.util.Map">
        select `SKU`, `Part Number`, `ASIN`, `status` from `ads_fitmentdata`.`it_demand` where `ASIN` in
        <foreach collection="notExistsAsin" open="(" separator="," close=")" item="asin">
            #{asin}
        </foreach>
        and 需求类型 = '0'
    </select>
    <select id="countByAsin" resultType="java.lang.Integer">
        select count(*) from `ads_fitmentdata`.`it_demand` where `ASIN` = #{platformGoodsId} and 需求类型 = '0'
    </select>
    <select id="listAmazonListingChangeMonitor"
            resultType="com.suncent.smc.persistence.ads.domain.AmazonListingChangeMonitorVO">
            WITH LatestDates AS (
                select id,asin,date,change_index as changeIndex,content,spider_time as spiderTime,is_change as isChange,is_suncent as isSuncent,type,product_code as productCode,sku,is_week_lastday as isWeekLastday,is_month_lastday as isMonthLastday,is_week_change as isWeekChange,is_month_change as isMonthChange,change_num_day as changeNumDay,change_num_week as changeNumWeek,change_num_month as changeNumMonth,week,month,
                ROW_NUMBER() OVER ( PARTITION BY asin,change_index ORDER BY spider_time DESC ) AS rn, brand
         from `ads_amazon`.amazon_listing_change_monitor <where>
         <if test="pdmGoodsCodeList != null and pdmGoodsCodeList.size() > 0">
            and sku in
            <foreach collection="pdmGoodsCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            and is_suncent = 0 and date =#{date}
        </if>
        <if test="asinList != null and asinList.size() > 0">
            and asin in
            <foreach collection="asinList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
            and is_suncent = 1 and date =#{date}
        </if>
    </where>
        )
        select * from LatestDates where rn = 1
    </select>


    <select id="getByProduct" resultType="com.suncent.smc.persistence.ads.domain.AdsFitmentDataBi">
        select `product_code` as productCode,`notes` as notes,`position` as position,`MakeName` as makeName,`ModelName` as modelName,`YearName` as yearName,`BlockType` as block,`Cylinders` as cylinders,`Liter` as liter,`aspiration` as aspiration,`RegionName` as regionName,`vio` as vio,`application` as application,`BodyTypeName` as bodyTypeName,`SubModelName` as subModelName,`DriveTypeName` as driveTypeName,`AspirationName` as aspirationName,`FuelTypeName` as fuelTypeName,`EngineVINName` as engineVINName,`status` as status,`create_time` as createTime
        from `ads_fitmentdata`.ads_fitmentdata_bi
        where `product_code` = #{productCode}    and  status = '1' and RegionName = 'United States'
    </select>

    <select id="getDataUpdateLinks" resultType="java.lang.String">
        SELECT product_code
        FROM `ads_fitmentdata`.`ads_fitmentdata_ebay_item`
        GROUP BY product_code
    </select>
    <select id="selectFitmentDataEbayItemByProductCode"
            resultType="com.suncent.smc.persistence.ads.domain.AdsFitmentDataEbayItem">
        select `product_code`,
               `make`,
               `year`,
               `model`,
               `submodel`,
               `Engine`,
               `Trim`,
               `notes`
        from `ads_fitmentdata`.`ads_fitmentdata_ebay_item`
        where `status` = '1'
          and `product_code` = #{productCode}
    </select>



    <select id="listAdapterManage" resultMap="adapterManage">
        SELECT * FROM `ads_amazon`.`fit_examine_day` WHERE
             `date` = #{maxDate}
             and (
                 `is_control_asin` = 1
                 or(
                     `listing_status`  &lt;&gt; 1
                     AND `status` IN (0, 6)
                     AND `product_valid` = 1
                     AND `is_control_asin` = 0
                 )
             )
            and (del_status is null or del_status='')
            and (follow_status is null or follow_status='')
        <if test="categoryName != null and categoryName != ''">
            and `category_name`  in
            <foreach item="code" collection="categoryName.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="shopCode != null and shopCode != ''">
            and `shop_code` in
            <foreach item="code" collection="shopCode.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platform != null and platform != ''">
            and `platform_code` = #{platform}
        </if>
        <if test="isCoreAsin != null and isCoreAsin != ''">
            and `is_core_asin` = #{isCoreAsin}
        </if>
        <if test="platformGoodsId != null and platformGoodsId != ''">
            and `asin`   in
            <foreach item="code" collection="platformGoodsId.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="productCode != null and productCode != ''">
            and `product_code`  in
            <foreach item="code" collection="productCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="pdmGoodsCode != null and pdmGoodsCode != ''">
            and `sku`  in
            <foreach item="code" collection="pdmGoodsCode.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="mpn != null and mpn != ''">
            and `mpn`  in
            <foreach item="code" collection="mpn.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="platformSku != null and platformSku != ''">
            and `platform_sku`  in
            <foreach item="code" collection="platformSku.split(' ')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="createTimeSite != null and createTimeSite != ''">
            and `create_time_site`  =  #{createTimeSite}
        </if>
        <if test="classificationName != null and classificationName != ''">
            and `classification_name` in
            <foreach item="code" collection="classificationName.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="userName != null and userName != ''">
            and `operator` in
            <foreach item="code" collection="userName.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and `id` = #{id}
        </if>
        <if test="ids != null and ids != ''">
            and `id` in
            <foreach item="code" collection="ids.split(',')" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

    </select>


    <update id="updateAdapterManage" >
        <foreach collection="collection" item="adapterManage"  separator=";" >
            update `ads_amazon`.`fit_examine_day` set  `is_control_asin` = #{adapterManage.isControlAsin}
            <if test="adapterManage.delStatus != null and adapterManage.delStatus != ''">
                 ,`del_status` = #{adapterManage.delStatus}
            </if>
            <if test="adapterManage.delStatus == null or adapterManage.delStatus == ''">
                ,`del_status` = ''
            </if>
            where
            `shop_code` = #{adapterManage.shopCode}
            and `asin` = #{adapterManage.platformGoodsId}
            and `platform_sku` = #{adapterManage.platformSku}
        </foreach>
    </update>


    <select id="getMaxDate" resultType="string">
        SELECT MAX(date) FROM `ads_amazon`.`fit_examine_day`
    </select>

    <select id="getSecondDate" resultType="java.lang.String">
        SELECT MAX(date) AS second_max_date FROM ads_amazon.fit_examine_day
            WHERE date &lt; ( SELECT MAX(date) FROM ads_amazon.fit_examine_day );
    </select>

    <select id="getAdapterManageDifferenceSetByDate" resultType="java.lang.String">
        SELECT DISTINCT platform_sku  FROM `ads_amazon`.`fit_examine_day`
            WHERE  `date` =  #{fromDate}
                AND (
                        `is_control_asin` = 1
                        OR (
                            `listing_status`  &lt;&gt; 1
                            AND `status` IN (0, 6)
                            AND `product_valid` = 1
                            AND `is_control_asin` IN (0, 1)
                        )
                    )
                AND platform_sku NOT IN (
                    SELECT platform_sku FROM `ads_amazon`.`fit_examine_day`
                    WHERE `date` = #{toDate}
                    AND (
                        `is_control_asin` = 1
                        OR (
                            `listing_status` &lt;&gt; 1
                            AND `status` IN (0, 6)
                            AND `product_valid` = 1
                            AND `is_control_asin` IN (0, 1)
                            )
                        )
                    )
                limit #{pageSize} offset #{offset} ;
    </select>


    <select id="listBlockRelinePriceSku" resultType="java.lang.String">
        SELECT DISTINCT SKU  FROM `ads_amazon`.`redline_price_new` WHERE redline_white_list = 1
    </select>
    <select id="countCartListByDate" resultType="java.lang.Integer">
        select count(*) from `ads_amazon`.`suncent_records_data`
        where cart = 1
            and date = #{date}
        
            and shop_code = #{shopCode}
        
        <if test="asinList != null and asinList.size() > 0">
            and page_asin in
            <foreach collection="asinList" open="(" separator="," close=")" item="asin">#{asin}</foreach>
        </if>
    </select>

    <!-- AdsFitmentDataOe resultMap -->
    <resultMap type="com.suncent.smc.persistence.ads.domain.AdsFitmentDataOe" id="adsFitmentDataOeResult">
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="qty" column="qty" jdbcType="VARCHAR"/>
        <result property="partnumberRaw" column="partnumber_raw" jdbcType="VARCHAR"/>
        <result property="brand" column="brand" jdbcType="VARCHAR"/>
        <result property="oeNumber" column="oe_number" jdbcType="VARCHAR"/>
        <result property="oeBrand" column="oe_brand" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="partType" column="partType" jdbcType="VARCHAR"/>
        <result property="rate" column="rate" jdbcType="FLOAT"/>
        <result property="siteRevenue" column="site_revenue" jdbcType="FLOAT"/>
        <result property="importance" column="Importance" jdbcType="FLOAT"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 根据产品编码列表查询ads_fitmentdata_oe表数据 -->
    <select id="selectFitmentDataOeList" resultMap="adsFitmentDataOeResult">
        select
        `product_code`,
        `qty`,
        `partnumber_raw`,
        `brand`,
        `oe_number`,
        `oe_brand`,
        `category`,
        `partType`,
        `rate`,
        `site_revenue`,
        `Importance`,
        `status`,
        `create_time`
        from `ads_fitmentdata`.`ads_fitmentdata_oe`
        where `status` = '1'
        and `product_code` IN
        <foreach collection="list" open="(" separator="," close=")" item="productCode">
            #{productCode}
        </foreach>
    </select>

</mapper>