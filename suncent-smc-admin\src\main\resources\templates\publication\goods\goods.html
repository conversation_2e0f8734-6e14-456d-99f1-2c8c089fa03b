<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('商品信息列表')"/>
    <th:block th:include="include :: ztree-css"/>
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: select2-css"/>
    <th:block th:include="include :: bootstrap-select-css"/>
    <th:block th:include="include :: virtual-select-css"/>

    <style>
        .bck_color {
            background-color: rgba(74, 165, 234, 1);
            color: white;
        }
        .div-select {
            height: 30px;
            width: 235px;
            display: inline-block;
        }
        .select_bck_color {
            background-color: rgba(70, 121, 178, 1);
            color: white;
        }
        .select-list li.show-4 label:not(.radio-box) {
            float: left;
            width: 95px;
            margin: 5px 0px 0px 0px;
            text-align: right;
        }

        .select-list .show-4 li {
            margin: 0px;
            width: 300px
        }

        .select-list .show-4 .select2-container--bootstrap {
            width: 300px !important;
        }

        .select-list .show-4.multiple .select2-container--bootstrap {
            width: 300px !important;
        }

        .select-list .show-4.multiple-max .select2-container--bootstrap {
            width: 300px !important;
        }

        .select-list li.show-4 input {
            width: 300px;
        }

        .btn-group-sm .btn {
            height: 34px;
            padding: 5px 10px;
            font-size: 14px;
        }

        .dropdown-menu a {
            font-size: 14px;
            color: #333;
        }

        .dropdown-menu {
            min-width: 180px;
        }

        .badge-show {
            background-color: #8B91A0;
            color: #fff;
        }
        .refund-label div {
            margin-bottom: 5px;
        }
        .select-list li.show-4 label:not(.radio-box){
            float: left;
            width: 92px;
            margin: 5px 0px 0px 0px;
            text-align:right;
        }
        .select-list .show-4 li {
            margin: 0px;
            width: 300px
        }
        .select-list .show-4 .select2-container--bootstrap{
            width: 300px !important;
        }
        .select-list .show-4.multiple .select2-container--bootstrap {
            width: 300px !important;
        }
        .select-list .show-4.multiple-max .select2-container--bootstrap {
            width: 300px !important;
        }
        .select-list li.show-4 input {
            width: 300px;
        }
        .select-list li p, .select-list li label:not(.radio-box){
            width: 80px;
        }
        .select-list .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
            width: 235px;
        }
        .option-select {
            width:210px
        }
        .child-class {
            width:110px !important
        }
    </style>

</head>

<body class="gray-bg">
<div class="container-div">
    <div style="text-align: center; color: red; font-weight: bold; margin-bottom: 10px;" th:if="${isRadio}">
        <span style='color:red;'>商品编码变更，关联</span>
        <strong style='color:red;'>平台商品编码、平台品类、适配文案、价格、标题、属性、描述、图片</strong>
        <span style='color:red;'>数据注意需同步更新！</span><br>
        <span style='color:red;'>链接库存、适配框、规格将由系统自动更新！</span>
    </div>
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>商品编码：</label><input type="text" name="goodsCode" id="goodsCode" th:value="${goodsCode}"
                                            placeholder="商品编码，支持多个查询空格分开" style="width:565px;"/>
                        </li>
                        <li>
                            <label>商品名称：</label><input type="text" name="goodsName" placeholder="商品名称" />
                        </li>
                        <li>
                            <label >未刊登店铺：</label>
                            <div class="div-select">
                                <div name="shopCode" id="notPublishShopCode"></div>
                            </div>
                        </li>
                        <li class="show-4 multiple-max" style="height: 30px">
                            <label>商品类别：</label>
                            <div class="div-select">
                                <div name="productCategoryCode" id="productCategoryCode"></div>
                            </div>
                        </li>
                        <li class="show-4 multiple-max" style="height: 30px">
                            <label>业务分类：</label>
                            <div class="div-select">
                                <div name="operationClassificationList" id="operation-classification-select"></div>
                            </div>
                        </li>
                        <li>
                            <label >是否需适配：</label>
                            <div class="div-select">
                                <select name="isAdapt" id="adaptFlag" class="noselect2 selectpicker"
                                        title="请选择" >
                                    <option class="option-select" value="">全部</option>
                                    <option class="option-select" value="1">需适配</option>
                                    <option class="option-select" value="0">无需适配</option>
                                </select>
                            </div>
                        </li>
                        <li>
                            <label >销售状态：</label>
                            <div class="div-select">
                                <select name="salesStatus" id="salesStatus" class="noselect2 selectpicker"
                                        title="请选择">
                                    <option class="option-select" value="">全部</option>
                                    <option class="option-select" value="1">在售</option>
                                    <option class="option-select" value="2">停售</option>
                                    <option class="option-select" value="3">清库</option>
                                    <option class="option-select" value="4">维稳</option>
                                    <option class="option-select" value="5">开发中</option>
                                    <option class="option-select" value="6">交付中</option>
                                    <option class="option-select" value="7">风险</option>
                                    <option class="option-select" value="8">无状态</option>
                                </select>
                            </div>
                        </li>
                        <li class="select-time">
                            <label>创建时间： </label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="beginTime"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="endTime"/>
                        </li>
                        <li style="height: 30px;">
                            <label >有白底图：</label>
                            <div class="div-select">
                                <select name="isExistWhiteBackImage" id="whiteBackgroundFlag" class="noselect2 selectpicker"
                                        title="请选择" >
                                    <option class="option-select" value="">请选择</option>
                                    <option class="option-select" value="1">有白底图</option>
                                    <option class="option-select" value="0">无白底图</option>
                                </select>
                            </div>
                        </li>
                        <li>
                            <label >实际库存：</label>
                            <input style="width: 104px;" type="number"name="actualStockOnSalesQtyStart"  id="actualStockOnSalesQtyStart" placeholder="请输入库存"
                            />
                            <span style="padding: 0px 8px;">-</span>
                            <input style="width: 104px;" type="number"name="actualStockOnSalesQtyEnd"  id="actualStockOnSalesQtyEnd" placeholder="请输入库存"
                            />
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group" th:if="${searchBar}">
            <!-- Amazon Operations -->
            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-primary btn-sm dropdown-toggle ">Amazon操作 <span
                        class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="font-bold " href="#" onclick="addAmazonV2('SC')"
                           shiro:hasPermission="publication:goods:addAmazon">新建单个Amazon SC刊登</a>
                    </li>
                    <li>
                        <a class="font-bold " href="#" onclick="addAmazonV2('VC')"
                           shiro:hasPermission="publication:goods:addAmazon">新建单个Amazon VC刊登</a>
                    </li>
                    <li>
                        <a class="font-bold " href="#" onclick="batchAddAmazon('SC')"
                           shiro:hasPermission="publication:goods:batchAddPublishAmazon">批量新建Amazon SC刊登</a>
                    </li>
                    <li>
                        <a class="font-bold " href="#" onclick="batchAddAmazon('VC')"
                           shiro:hasPermission="publication:goods:batchAddPublishAmazon">批量新建Amazon VC刊登</a>
                    </li>
                    <li>
                        <a href="#" class="font-bold " onclick="autoCreateListingAmazon()"
                           shiro:hasPermission="publication:goods:autoCreateListingAmazon">自动生成AmazonListing</a>
                    </li>
                </ul>
            </div>

            <!-- Ebay Operations -->
            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-success btn-sm dropdown-toggle">
                    Ebay操作 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="#" class="font-bold" onclick="addEbay('V1')"
                           shiro:hasPermission="publication:goods:addEbay">新建单个Ebay刊登</a></li>
                    <li><a href="#" class="font-bold" onclick="addEbay('V2')"
                           shiro:hasPermission="publication:goods:addEbay">新建单个Ebay刊登(多属性)</a></li>
                    <li><a href="#" class="font-bold" onclick="batchAddEbay()"
                           shiro:hasPermission="publication:goods:batchAddPublishEbay">批量新建Ebay刊登</a></li>
                    <li><a href="#" class="font-bold" onclick="batchAddEbayAI()"
                           shiro:hasPermission="publication:goods:batchAddPublishEbay">批量新建Ebay刊登(AI生成)</a></li>
                    <li><a href="#" class="font-bold" onclick="autoCreateListingEbay()"
                           shiro:hasPermission="publication:goods:autoCreateListingEbay">自动生成EbayListing</a></li>
                    <li><a href="#" class="font-bold" onclick="addEbayDistribution()"
                           shiro:hasPermission="publication:goods:addEbayDistribution">新建Ebay刊登(分销)</a></li>
                </ul>
            </div>

            <!-- Temu Operations -->
            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-info btn-sm dropdown-toggle">
                    Temu操作 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="#" class="font-bold" onclick="addTemu()"
                           shiro:hasPermission="publication:goods:addTemu">新建单个Temu刊登</a></li>
                </ul>
            </div>

            <!-- Export Operations -->
            <div class="btn-group">
                <button data-toggle="dropdown" class="btn btn-warning btn-sm dropdown-toggle">
                    导出 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="#" class="font-bold" onclick="exportSelected(table.options.exportUrl)"
                           shiro:hasPermission="publication:goods:export">导出</a></li>
                    <li><a href="#" class="font-bold" onclick="exportDiy()"
                           shiro:hasPermission="publication:goods:export">导出适配保有量</a></li>
                </ul>
            </div>

            <!-- Stock Update Blacklist -->
            <a class="btn btn-danger btn-sm" onclick="stockUpdateBlackList()" id="stockUpdateBlackList"
               shiro:hasPermission="publication:goods:stockUpdateBlackList">
                <i class="fa fa-plus"></i> 库存更新黑名单
            </a>
            <!-- 批量加入竞对监控 -->
            <a class="btn btn-success btn-sm" onclick="batchAddMonitor()" id="batchAddMonitor"
               shiro:hasPermission="publication:goods:batchAddMonitor">
                <i class="fa fa-plus"></i> 批量加入竞对监控
            </a>
            <!-- 批量移除竞对监控 -->
            <a class="btn btn-danger btn-sm" onclick="batchRemoveMonitor()" id="batchRemoveMonitor"
               shiro:hasPermission="publication:goods:batchRemoveMonitor">
                <i class="fa fa-remove"></i> 批量移除竞对监控
            </a>
            <!-- 批量测算VC价格 -->
            <a class="btn btn-warning btn-sm" onclick="batchVcCalculation()" id="batchVcCalculation"
               shiro:hasPermission="publication:goods:vcCalculation">
                <i class="fa fa-calculator"></i> 批量测算VC价格
            </a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-select-js"/>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: virtual-select-js"/>

<script th:inline="javascript">
    var productData = [[${@cdpBaseConfig.getCateProductAllKVList()}]];
    var refundRateLabelData = [[${@dict.getType('refund_rate_label')}]]
    var prefix = ctx + "publication/goods";
    var jumpSkus = [[${jumpSkus}]];
    var isRadio = [[${isRadio}]];
    var pdmCategoryIdsData=[[${@cdpBaseConfig.getCateProductAllKVList()}]]
    var shopCodeData=[[${@cdpBaseConfig.getShopAllKVList()}]]
    var operationClassificationData = [[${operationClassificationList}]]
    let shopCode = shopCodeData.map(o => {

        return {label: o.value, value: o.key}
    });
    var pdmCategoryIds = pdmCategoryIdsData.map(o => {

        return {label: o.value, value: o.key}
    });
    let operationClassification = operationClassificationData.map(o => {

        return {label: o.value, value: o.key}
    });

    window.jump = function(shopCode, platform){
        //创建一个新的标签页
        if ($.common.isEmpty(shopCode) || $.common.isEmpty(platform)) {
            return;
        }
        $.modal.openTab('Listing管理', "publication/listing/jump/" + platform + "/goods-listing/"+shopCode );
    }


    $(function () {
        virtualSelectRender(document.querySelectorAll("div#productCategoryCode"), pdmCategoryIds);
        virtualSelectRender(document.querySelectorAll("div#notPublishShopCode"), shopCode);
        virtualSelectRender(document.querySelectorAll("div#operation-classification-select"), operationClassification);

        var options = {
            url: prefix + "/list",
            detailUrl: prefix + "/addEbay/{id}",
            updateUrl: prefix + "/add/{id}",
            exportUrl: prefix + "/export",
            exportAdsUrl: prefix + "/exportAds",
            exportGPTUrl: prefix + "/exportGPT",
            sortName: "createTime",
            sortOrder: "desc",
            modalName: "商品管理",
            pageList: [10, 25, 50, 100, 200, 250],
            pageSize: 25,
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.productCategoryCode = $.common.join($('#productCategoryCode').val());
                // 添加业务分类筛选条件
                var operationClassificationSelect = document.querySelector('#operation-classification-select');
                if (operationClassificationSelect && operationClassificationSelect.value) {
                    search.operationClassificationList = operationClassificationSelect.value;
                }
                return search;
            },
            columns:
                [
                    isRadio ?
                        {
                            radio: true
                        } : {
                            checkbox: true
                        },
                    {
                        field: 'id',
                        title: '主键',
                        visible: false
                    },
                    {
                        field: 'goodsLogo',
                        title: '商品图片',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            if ($.common.isNotEmpty(value)) {
                                return $.common.sprintf("<img class='scale-img' style='width: 64px;height: 64px;border-radius: 6px;' data-height='%s' data-width='%s' data-target='%s' src='%s'/>", "auto", "auto", "_self", value);
                            } else {
                                return $.common.sprintf("<img class='scale-img' style='width: 64px;height: 64px;border-radius: 6px;' data-height='%s' data-width='%s' data-target='%s' src='https://oss.cloud.suncentgroup.com/badbe99ebfbe4dc2ad90a756bacd9d7c.jpg' />", "auto", "auto", "_self");
                            }
                        },
                        colspan: 1
                    },
                    {
                        field: 'goodsCode',
                        title: '商品编码'
                    },
                    {
                        field: 'goodsName',
                        title: '商品名称',
                        width: 400
                    },
                    {
                        field: 'productCategoryName',
                        title: '商品类别',
                        formatter: function (value, row, index) {
                            if ($.common.isEmpty(value)) {
                                return '-';
                            }
                            return value;
                        }
                    },
                    {
                        title: '价格信息',
                        width: 380,
                        formatter: function (value, row, index) {
                            var html = '<div class="price-info">';
                            html += '<div ><span>EB红线价:</span><span>' + (row.ebayRedLinePrice == null ? '-' : row.ebayRedLinePrice) + '</span></div>';
                            html += '<div ><span>FBA红线价:</span><span>' + (row.fbaRedLinePrice == null ? '-' : row.fbaRedLinePrice) + '</span></div>';
                            html += '<div ><span>FBM红线价:</span><span>' + (row.fbmRedLinePrice == null ? '-' : row.fbmRedLinePrice) + '</span></div>';
                            html += '<div ><span>VC红线价:</span><span>' + (row.vcRedLinePrice == null ? '-' : row.vcRedLinePrice) + '</span></div>';
                            html += '<div ><span>TEMU红线价:</span><span>' + (row.temuRedLinePrice == null ? '-' : row.temuRedLinePrice) + '</span></div>';
                            html += '</div>';
                            return html;
                        }
                    },
                    // {
                    //     title: '建议价格',
                    //     width: 280,
                    //     formatter: function (value, row, index) {
                    //         var html = '<div class="price-info">';
                    //         html += '<div ><span>低建议价:</span><span>' + (row.adviceLowPrice == null ? '-' : row.adviceLowPrice) + '</span></div>';
                    //         html += '<div ><span>中建议价:</span><span>' + (row.adviceMediumPrice == null ? '-' : row.adviceMediumPrice) + '</span></div>';
                    //         html += '<div ><span>高建议价:</span><span>' + (row.adviceHighPrice == null ? '-' : row.adviceHighPrice) + '</span></div>';
                    //         html += '</div>';
                    //         return html;
                    //     }
                    // },
                    {
                        title: '库存信息',
                        width: 260,
                        formatter: function (value, row, index) {
                            var html = '';
                            html += '<div>可售库存: ' + (row.stockOnSalesQty == null ? '-' : row.stockOnSalesQty) + '</div>';
                            // html += '<div>在途库存: ' + (row.stockOnWayQty == null ? '-' : row.stockOnWayQty) + '</div>';
                            // html += '<div >其他: ' +  '-' + '</div>';
                            return html;
                        }
                    }, {
                    title: '图片信息',
                    width: 260,
                    formatter: function (value, row, index) {
                        var html = '';
                        html += '<div> ' + (row.isExistWhiteBackImage ? '<span class="badge badge-primary">有白底图</span>' : '<span class="badge badge-danger">无白底图</span>') + '</div>';
                        return html;
                    }
                }, {
                    title: '适配信息',
                    width: 260,
                    formatter: function (value, row, index) {
                        var html = '';
                        html += '<div> '
                        if (row.smcAdaptFlag == 'Y') {
                            html += '<span class="badge badge-primary">已适配</span>';
                            if (!isRadio) {
                                html += '<div style="margin-top: 5px"><a class="" href="javascript:void(0)" onclick="watchFitmentVIO(\'' + row.goodsCode + '\')">适配信息</a></div>';
                            }
                        } else {
                            if (row.isAdapt ) {
                                html += '<span class="badge badge-danger">未适配</span>';
                            } else {
                                html += '<span class="badge badge-show">无需适配</span>';
                            }
                        }

                        html += '</div>';
                        return html;
                    }
                },

                    isRadio ? {} : {
                    title: '链接信息',
                    width: 260,
                    formatter: function (value, row, index) {
                        let listingDetail = row.countOnlineListing;
                        if($.common.isEmpty(listingDetail)) {
                            return '<div>0</div>';
                        }

                        let count = 0
                        listingDetail.forEach(function (item) {
                            count += Number(item.onlineCount);
                        });

                        var html = '<div>';
                        html += '<a class="" href="javascript:void(0)" data-listing-detail=\'' + JSON.stringify(listingDetail) + '\' onclick="watchListing(this)"> ' + count + '</a>';
                        html += '</div>';
                        return html;
                    }
                }, {
                    title: '共享库存(SKU:库存)',
                    width: 260,
                    formatter: function (value, row, index) {
                        var shareInventroySkuList = "<div>";
                        let shareInventory = row.shareInventroySkuMap;
                        if (shareInventory) {
                            $.each(shareInventory, function (key, value) {
                                shareInventroySkuList += key + ":" + value + "<br>";
                            });
                        }
                        shareInventroySkuList += "</div>";
                        return shareInventroySkuList;
                    }
                },
                    {
                        field: 'partList',
                        title: '零件列表(sku*数量)',
                        formatter: function (value, row, index) {
                            //对value的所有值进行遍历把 展示的值用 所有的partSku 用+拼接起来,最后一个不拼接
                            var partSku = "";
                            $.each(value, function (index, part) {
                                partSku += part.partSku + "*" + part.quantity;
                                if (index != value.length - 1) {
                                    partSku += "<br>";

                                }
                            });
                            return partSku;
                        }
                    }, {
                    field: 'salesStatus',
                    title: '商品状态',
                    width: 320,
                    formatter: function (value, row, index) {
                        let statusHtml = '<div class="refund-label">'
                        let statusName;
                        if (value == '1') {
                            statusName = '在售';
                        } else if (value == '2') {
                            statusName = '停售';
                        } else if (value == '3') {
                            statusName = '清库';
                        } else if (value == '4') {
                            statusName = '维稳';
                        } else if (value == '5') {
                            statusName = '开发中'
                        } else if (value == '6') {
                            statusName = '交付中'
                        } else if (value == '7') {
                            statusName = '风险'
                        } else if (value == '8') {
                            statusName = '无状态'
                        }
                        statusHtml += '<div><span class="badge badge-success">' + statusName + '</span></div>';
                        // 退货率
                        let refundRateLabel = row.refundRateLabelMap
                        if (refundRateLabel) {
                            $.each(refundRateLabel, function (key, value) {
                                let refundHtml = '<div>';
                                refundHtml += key + ':';
                                $.each(value, function (index, label) {
                                    var labelObj = refundRateLabelData.find(function (item) {
                                        return item.dictValue === label;
                                    });
                                    if ($.common.isEmpty(labelObj) || $.common.isEmpty(labelObj.dictLabel) || $.common.isEmpty(labelObj.listClass)) {
                                        return;
                                    }
                                    refundHtml += '<span class="badge badge-' + labelObj.listClass + '">' + labelObj.dictLabel + '退货率</span></div>';
                                });
                                statusHtml += refundHtml;
                            });
                        }
                        statusHtml += '</div>';
                        return statusHtml;
                    }
                },
                    {
                        field: 'createTime',
                        title: '创建时间'
                    },
                    isRadio ? {} : {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="watchGoods(' + row.id + ')"><i class="fa fa-edit"></i>详情</a> ');
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="watchBasic(\'' + row.goodsCode + '\')"><i class="fa fa-edit"></i>基础数据</a> ');
                            return actions.join(' ');
                        }
                    }]
        };
        if ($.common.isNotEmpty(jumpSkus)) {
            $('#goodsCode').val(jumpSkus);
        }
        $.table.init(options);


    });

    function watchListing(element) {
        listingDetail = JSON.parse(element.getAttribute('data-listing-detail'));
        var html = '<div class="col-sm-12 select-table table-striped">'

        // 构建表格
        var table = '<table class="table table-bordered table-striped">';
        table += '<thead>';
        table += '<tr>';
        table += '<th>商品编码</th>';
        table += '<th>平台</th>';
        table += '<th>店铺</th>';
        table += '<th>链接数量</th>';
        table += '</tr>';
        table += '</thead>';
        table += '<tbody>';
        $.each(listingDetail, function (index, item) {
            table += '<tr>';
            table += '<td>' + item.goodsCode + '</td>';
            table += '<td>' + item.platform + '</td>';
            table += '<td>' + item.shopCode + '</td>';
            table += '<td>' + '<a href="javascript:void(0)" onclick="top.jump(\'' + item.shopCode + '\' , \'' + item.platform + '\')">' + item.onlineCount + '</a>' + '</td>';
            table += '</tr>';
        });
        table += '</tbody>';
        table += '</table>';

        html += table;
        html += '</div>';
        // 加入jump函数

        layer.open({
            type: 1,
            title: '商品链接明细',
            area: ['800px', '600px'],
            content: html,
            success: function(layero, index) {
                // 确保在 layer 打开后函数可用
                if (typeof top.jump !== 'function') {
                    top.jump = window.jump;
                }
            }
        });
    }


    function stockUpdateBlackList() {
        table.set();
        var rows = $.table.selectThirdColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        $.modal.open("添加库存更新黑名单", prefix + '/toAddInventoryExcludePage?goodsCodes=' + rows, '600', '600', function (index, layero) {
            var data = layero.find("iframe")[0].contentWindow.getDataInfo();
            if (data == false) {
                return;
            }
            $.modal.close(index);
            $.operate.save(ctx + "configuration/exclude/addByType", data);
        });
    }

    // 基础数据
    function watchBasic(goodsCode) {
        var url = "/system/origin/watchBasic/" + goodsCode;
        $.modal.openTab(goodsCode + "-基础数据", url);
    }


    // 适配保有量
    function watchFitmentVIO(goodsCode) {
        var url = "/ads/watchVIO/" + goodsCode;
        $.modal.openTab(goodsCode + "-适配保有量", url);
    }

    // 查看产品
    function watchGoods(id) {
        var options = {
            title: "查看商品",
            url: "/publication/goods/watch/" + id,
            width: $(window).width() - 200,
            height: $(window).height() - 50,
            yes: false,
            btn: ['<i class="fa fa-close"></i> 关闭'],
        };
        $.modal.openOptions(options);
    }

    function reset() {
        $('#productCategoryCode').val("").trigger('change');
        $("#operation-classification-select")[0].setValue("");
        $.form.reset();
    }

    function addAmazon() {
        let ids = $.table.selectFirstColumns();
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (ids.length > 1) {
            $.modal.alertWarning("只能选择一条记录");
            return;
        }

        var url = prefix + "/add/" + ids;
        $.modal.openTab("新建Amazon刊登", url);
    }

    function addAmazonV2(type) {
        let ids = $.table.selectFirstColumns();
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (ids.length > 1) {
            $.modal.alertWarning("只能选择一条记录");
            return;
        }

        var url = prefix + "/v2/add/" + type + "/" + ids;
        $.modal.openTab("新建Amazon刊登", url);
    }


    function batchAddAmazon(type) {
        let ids = $.table.selectFirstColumns();
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var uri = prefix + "/batchAddPublishAmazonByGoodCode?goodsCodes=" + $.table.selectThirdColumns();
        $.modal.open("确认生成listing数量", uri, '1000', '500', function (index, layero) {
            var data = layero.find("iframe")[0].contentWindow.getDataInfo();
            $.modal.close(index);
            // 判断 data 是否为空
            if ($.common.isEmpty(data)) {
                var url = prefix + "/v2/batchAddPublishAmazon?type=" + type + "&platformType=AM&saleGoodIds=" + $.table.selectFirstColumns() + "&data=" + data;
                $.modal.openTab("批量新建Amazon刊登", url);
                return;
            }
            // 把 data 转成字符串把 & 替换成 ,
            var dataInfo = data.replaceAll("&", ",");
            var url = prefix + "/v2/batchAddPublishAmazon?type=" + type + "&platformType=AM&saleGoodIds=" + $.table.selectFirstColumns() + "&data=" + dataInfo;
            $.modal.openTab("批量新建Amazon刊登", url);
        });
    }


    function batchAddEbay() {
        let ids = $.table.selectFirstColumns();
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/batchAddPublishEbay?platformType=EB&saleGoodIds=" + $.table.selectFirstColumns();
        $.modal.openTab("批量新建Ebay刊登", url);
    }

    function batchAddEbayAI() {
        let ids = $.table.selectFirstColumns();
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/batchAddPublishEbayAI?platformType=EB&saleGoodIds=" + $.table.selectFirstColumns();
        $.modal.openTab("批量新建Ebay刊登(AI生成)", url);
    }

    function addEbay(version) {
        let ids = $.table.selectFirstColumns();
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (ids.length > 1) {
            $.modal.alertWarning("只能选择一条记录");
            return;
        }
        var url = prefix + "/addEbay/" + $.table.selectFirstColumns()+"/"+version;
        $.modal.openTab("新建Ebay刊登", url);
    }

    function addTemu() {
        let ids = $.table.selectFirstColumns();
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (ids.length > 1) {
            $.modal.alertWarning("只能选择一条记录");
            return;
        }
        var url = ctx + "temu/goods" + "/addTemu/" + $.table.selectFirstColumns();
        $.modal.openTab("新建Temu刊登", url);
    }

    function autoCreateListingAmazon() {
        let ids = $.table.selectFirstColumns();
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/autoCreateListing?platformType=AM&saleGoodIds=" + $.table.selectFirstColumns();
        $.modal.open("自动生成Amazon Listing", url, '1000', '500', function (index, layero) {
            var data = layero.find("iframe")[0].contentWindow.getDataInfo();
            if (data == false) {
                return;
            }
            $.modal.close(index);
            $.operate.save(prefix + "/autoCreateListing", data);
        });
    }

    function batchAddMonitor() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        // 弹窗提示
        $.modal.confirm("选中的" + rows.length + "条SKU将加入竞对链接监控，是否继续操作？", function () {
            let goodsCodes = $.table.selectThirdColumns();
            let data = {
                'goodsCodes': goodsCodes.join(',')
            }
            $.operate.post(prefix + "/batchAddMonitor", data);
        });
    }

    function batchRemoveMonitor() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        // 弹窗提示
        $.modal.confirm("选中的" + rows.length + "条SKU将移除竞对链接监控，是否继续操作？", function () {
            let data = {
                goodsCodes: $.table.selectThirdColumns().join(',')
            }
            $.operate.post(prefix + "/batchRemoveMonitor", data);
        });
    }

    function autoCreateListingEbay() {
        let ids = $.table.selectFirstColumns();
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var url = prefix + "/autoCreateListing?platformType=EB&saleGoodIds=" + $.table.selectFirstColumns();
        $.modal.open("自动生成Ebay Listing", url, '1000', '600', function (index, layero) {
            var data = layero.find("iframe")[0].contentWindow.getDataInfo();
            if (data == false) {
                return;
            }
            $.modal.close(index);
            $.operate.save(prefix + "/autoCreateListing", data);
        });
    }

    function addEbayDistribution() {
        var url = prefix + "/addEbayDistribution";
        $.modal.openTab("新建Ebay刊登(分销)", url);
    }

    function jumpListing() {
        top.layer.confirm("生成草稿成功，是否跳转至Listing管理页?", {
            icon: 3,
            title: "系统提示",
            btn: ['确认', '取消']
        }, function (index) {
            $.modal.close(index)
            $.modal.openTab('Listing管理', "/publication/listing");
        }, function (index) {
            $.modal.close(index);
        });
    }

    /**
     * 渲染虚拟下拉框
     * @param elements
     * @param optionList
     * @param multiple
     */
    function virtualSelectRender(elements, optionList, multiple = true,placeholder) {
        if (!elements || !elements.length) {
            return;
        }
        elements.forEach(item => {
            VirtualSelect.init({
                ele: item,
                options: optionList,
                showValueAsTags: false,
                disableAllOptionsSelectedText: true,
                alwaysShowSelectedOptionsCount: true,
                placeholder: placeholder?placeholder:'请选择',
                noOptionsText: '没有任何选项',
                noSearchResultsText: '未找到任何结果',
                searchPlaceholderText: '输入关键字搜索',
                optionsSelectedText: '项已选中',
                optionSelectedText: '项已选中',
                allOptionsSelectedText: '全选',
                clearButtonText: '清空',
                multiple: multiple,
                moreText: '更多',
                search: true,
                zIndex: 900
            });
        })
    }
    /**
     * 自定义导出
     */
    function exportDiy() {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        $.modal.openOptions({
            title: $.common.isNotEmpty(rows) ? "导出勾选" + rows.length + "条数据" : "导出全部" + table.options.modalName + "的数据",
            url: prefix + "/exportColumns",
            shadeClose: false,
            height: 800,
            width: 1200,
            yes: function (index, layero) {
                var iframeWin = layero.find('iframe')[0];
                let selectNames = iframeWin.contentWindow.submitHandler(index, layero);
                var dataParam = [];
                if ($.common.isNotEmpty(rows)) {
                    dataParam.push({"name": "ids", "value": rows.join()});
                } else {
                    var params = $("#" + table.options.id).bootstrapTable('getOptions');
                    dataParam = $("#formId").serializeArray();
                    dataParam.push({"name": "orderByColumn", "value": params.sortName});
                    dataParam.push({"name": "isAsc", "value": params.sortOrder});

                    // 手动添加业务分类筛选条件（因为virtual-select可能不会被表单序列化捕获）
                    var operationClassificationSelect = document.querySelector('#operation-classification-select');
                    if (operationClassificationSelect && operationClassificationSelect.value) {
                        dataParam.push({
                            "name": "operationClassificationList",
                            "value": operationClassificationSelect.value
                        });
                    }
                }
                //将selectNames数组和dataParam数组合并
                if(selectNames) {
                    dataParam.push({"name": "rules", "value": JSON.stringify(selectNames)});
                }
                var newArr = dataParam;
                $.modal.loading("正在导出数据，请稍候...");
                $.modal.close(index);
                $(".layui-layer-btn0").addClass("layui-btn-disabled").attr("disabled", "disabled");
                $.post(table.options.exportAdsUrl, newArr, function (result) {
                    if (result.code == web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                });
            }
        });
    }

    // 导出数据
    function exportSelected(url) {
        table.set();
        var rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        $.modal.confirm($.common.isNotEmpty(rows) ? "确定导出勾选" + rows.length + "条数据吗？" : "确定导出全部" + table.options.modalName + "的数据吗？", function () {
            var dataParam = [];
            if ($.common.isNotEmpty(rows)) {
                dataParam.push({"name": "ids", "value": rows.join()});
            } else {
                var currentId = 'formId';
                var params = $("#" + table.options.id).bootstrapTable('getOptions');
                dataParam = $("#" + currentId).serializeArray();
                dataParam.push({"name": "orderByColumn", "value": params.sortName});
                dataParam.push({"name": "isAsc", "value": params.sortOrder});

                // 手动添加业务分类筛选条件（因为virtual-select可能不会被表单序列化捕获）
                var operationClassificationSelect = document.querySelector('#operation-classification-select');
                if (operationClassificationSelect && operationClassificationSelect.value) {
                    dataParam.push({
                        "name": "operationClassificationList",
                        "value": operationClassificationSelect.value
                    });
                }
            }
            $.modal.loading("正在导出数据，请稍候...");
            $.post(url, dataParam, function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            });
        });
    }

    function getSelections() {
        var rows = $("#bootstrap-table").bootstrapTable('getSelections');
        if(rows.length == 0){
            return $.modal.alertError("请选择商品");
        }
        if(rows.length > 1){
            return $.modal.alertError("请选择一条商品");
        }
        return rows[0];
    }


    function getMultipleSelections() {
        var rows = $("#bootstrap-table").bootstrapTable('getSelections');
        if (rows.length == 0) {
            return $.modal.alertError("请选择商品");
        }
        return rows;
    }

    // 批量测算VC价格
    function batchVcCalculation() {
        var rows = $("#bootstrap-table").bootstrapTable('getSelections');
        var skus = [];

        // 收集选中的SKU
        if (rows.length > 0) {
            skus = rows.map(function(row) {
                return row.goodsCode;
            });
        }

        // 构建URL参数
        var url = ctx + "publication/goods/vcCalculation";
        if (skus.length > 0) {
            url += "?selectedSkus=" + encodeURIComponent(skus.join(','));
        }

        // 在新Tab页面中打开VC测算页面
        $.modal.openTab("批量测算VC价格", url);
    }

</script>
</body>
</html>