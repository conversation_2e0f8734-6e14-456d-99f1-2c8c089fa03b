package com.suncent.smc.persistence.publication.domain.dto;

import com.suncent.smc.persistence.ebay.domain.EbayListingShippingV2;
import com.suncent.smc.persistence.ebay.domain.EbayListingSpecificationV2;
import com.suncent.smc.persistence.ebay.dto.SpecAttribute;
import com.suncent.smc.persistence.ebay.dto.SpecItem;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsImage;
import com.suncent.smc.persistence.product.domain.entity.PublicationDay;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.todo.domain.entity.SmcAdaptTodo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 刊登商品数据传输实体类
 *
 * <AUTHOR>
 * @since 2023-01-11 16:48:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListingEditDTO extends ListingDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 平台商品编码
     */
    private String platformGoodsCode;

    /**
     * 商品平台ID-刊登成功后平台返回的ID
     */
    private String platformGoodsId;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * 刊登类型名
     */
    private String publishTypeName;

    /**
     * 品牌名
     */
    private String brandName;

    /**
     * 商品头部基础实体类主键ID
     */
    private Integer goodsHeadId;

    /**
     * 商品资源实体类主键ID
     */
    private Integer goodsResourceId;

    /**
     * 商品描述实体类主键ID
     */
    private String goodsDescriptionId;

    /**
     * 商品规格实体类主键ID
     */
    private Integer goodsSpecificationId;

    /**
     * 商品安全与规管实体类主键ID
     */
    private Integer amazonGoodsSafetyId;

    /**
     * 商品基础实体类主键ID
     */
    private Integer amazonGoodsInfoLineId;

    /**
     * 商品详情实体类主键ID
     */
    private Integer amazonGoodsDetailId;

    //兼容ebay页面的字段新增

    /**
     * @description: TODO ebay行id
     */
    private Integer listingLineId;
    /**
     * 刊登天数
     */
    private List<PublicationDay> publicationDayList;
    /**
     * @description: TODO 商品规格
     */
    private GoodsSpecification goodsSpecification;
    /**
     * @description: TODO 副标题
     */
    private String subtitle;


    /**
     * @description: 商品图片
     */
    private List<GoodsImage> goodsImageList = new ArrayList<>();
    /**
     * @description: TODO 适配数据
     */
    private List<ListingEbayAdaptive> goodsAdapterList = new ArrayList<>();
    /**
     * @description: TODO 付款对象
     */
    private ListingEbayPolicy listingEbayPolicy;
    /**
     * @description: TODO 物流对象
     */
    private ListingEbayShippingHead listingEbayShippingHead;


    //重要信息
    private List<ListingAmazonAttributeLine> listingAmazonAttributeLinesInfo;
    //详情
    private List<ListingAmazonAttributeLine> listingAmazonAttributeLinesDetail;
    //安全与合规
    private List<ListingAmazonAttributeLine> listingAmazonAttributeLinesSafety;
    //描述模板ID
    private  Integer templateSelect;
    //视频ID
    private  String videoSelect;
    //视频下拉框
    private Map<String,String> videoSelectMap;

    //文本描述（有描述模板ID与该字段组合生成一个大描述)
    private  String shortDescription;
    /**送检标志*/
    private String inspectionMark;

    /**
     * 图片数据json
     */
    private String imgDataArr;


    private SmcAdaptTodo smcAdaptTodo;

    private Boolean fiveAdapterEdit;

    /**
     * 适配图片数据
     */
    private List<GoodsImage> adpatImageList;


    private List<Object> subDatas;

    private String pdmGoodsCode;

    private String pdmGoodsId;


    /**
     * 包裹信息
     */
    private EbayListingSpecificationV2 goodsSpecificationEbay;


    /**
     * 运输方式
     */
    private List<EbayListingShippingV2> listingShippingTypeLines;

    /**
     * 商品规格属性
     */
    private List<SpecAttribute> specAttributesList;

    /**
     * 商品规格项
     */
    private List<SpecItem> specsItemsList;

    private boolean ebayV2 = false;

    /**
     * 商品规格属性
     */
    private List<ListingEbayValue> listingEbayValues;
    
    /**
     * 是否需要同步到其他相同ASIN的链接
     */
    private Boolean syncOtherListings;

    /**
     * 需要同步的字段列表，逗号分隔
     */
    private String syncFields;


    private Integer ebayDescriptionId;
}