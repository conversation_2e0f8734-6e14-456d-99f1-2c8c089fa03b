package com.suncent.smc.quartz.task.listing.processor.impl;

import cn.hutool.core.util.ObjUtil;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.persistence.bi.entity.BiSalesAnalysis;
import com.suncent.smc.persistence.bi.service.IBiDataService;
import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.quartz.task.listing.processor.LabelProcessor;
import com.suncent.smc.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * BI标签处理器
 * 处理BI业务综合分析数据，生成相关标签
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Component
@Slf4j
public class BiLabelProcessor implements LabelProcessor {

    @Autowired
    private IBiDataService biDataService;

    @Autowired
    private IGoodsHeadService goodsHeadService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Override
    public String getLabelType() {
        return "bi";
    }

    @Override
    public List<BiSalesAnalysis> querySourceData(String shopCode, int offset, int limit) {
        return biDataService.queryTodaySalesAnalysisPaged(shopCode, offset, limit);
    }

    @Override
    public List<ListingLabel> processToLabels(List<? extends Object> sourceData, String shopCode) {
        List<BiSalesAnalysis> analysisList = (List<BiSalesAnalysis>) sourceData;

        if (ObjUtil.isEmpty(analysisList)) {
            log.debug("店铺 {} 的BI分析数据为空", shopCode);
            return Collections.emptyList();
        }

        // 获取商品头表映射
        Map<String, Integer> skuToHeadIdMap = getHeadIdMapping(analysisList, shopCode);

        if (skuToHeadIdMap.isEmpty()) {
            log.warn("店铺 {} 的BI分析数据无对应的SMC商品主数据", shopCode);
            return Collections.emptyList();
        }

        // 获取配置参数
        Map<String, String> configParams = getConfigParams();

        List<ListingLabel> labels = new ArrayList<>();

        for (BiSalesAnalysis analysis : analysisList) {
            String key = analysis.getShopCode() + analysis.getPlatformSku() + analysis.getPlatformSaleCode();
            Integer headId = skuToHeadIdMap.get(key);

            if (headId != null) {
                labels.addAll(createBiLabels(analysis, headId, configParams));
            }
        }

        return filterAndDeduplicateLabels(labels);
    }

    @Override
    public int getBatchSize() {
        return 1000;
    }

    @Override
    public int getPriority() {
        return 1; // BI标签优先级最高
    }

    @Override
    public String getDescription() {
        return "BI业务综合分析标签处理器";
    }

    /**
     * 获取商品头表ID映射
     */
    private Map<String, Integer> getHeadIdMapping(List<BiSalesAnalysis> analysisList, String shopCode) {
        // 获取所有需要查询的SKU
        Set<String> skuSet = analysisList.stream()
                .map(BiSalesAnalysis::getPlatformSku)
                .filter(ObjUtil::isNotEmpty)
                .collect(Collectors.toSet());

        if (skuSet.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, Integer> resultMap = new HashMap<>();

        for (String sku : skuSet) {
            List<GoodsHeadVO> goodsHeadVOList = goodsHeadService.selectListingGoodsByShopCodeAndSku(
                    Collections.singletonList(shopCode), sku, null);

            if (ObjUtil.isNotEmpty(goodsHeadVOList)) {
                for (GoodsHeadVO goodsHeadVO : goodsHeadVOList) {
                    String key = goodsHeadVO.getShopCode() + goodsHeadVO.getPlatformGoodsCode() + goodsHeadVO.getPlatformGoodsId();
                    resultMap.put(key, goodsHeadVO.getId());
                }
            }
        }

        return resultMap;
    }

    /**
     * 获取配置参数
     */
    private Map<String, String> getConfigParams() {
        Map<String, String> params = new HashMap<>();

        params.put("salesQty7Qoq", getConfigValue("sales_qty_7_qoq", "0.1"));
        params.put("sessions7Qoq", getConfigValue("sessions_7_qoq", "0.1"));
        params.put("adSpend7Qoq", getConfigValue("ad_spend_7_qoq", "0.1"));
        params.put("grossProfitRealRate7Qoq", getConfigValue("gross_profit_real_rate_7_qoq", "0.1"));

        return params;
    }

    /**
     * 获取配置值
     */
    private String getConfigValue(String key, String defaultValue) {
        String value = sysConfigService.selectConfigByKey(key);
        return ObjUtil.isEmpty(value) ? defaultValue : value;
    }

    /**
     * 创建BI标签
     */
    private List<ListingLabel> createBiLabels(BiSalesAnalysis analysis, Integer headId, Map<String, String> configParams) {
        List<ListingLabel> labels = new ArrayList<>();

        // 销量标签
        if (ObjUtil.isNotEmpty(analysis.getSalesQty7Qoq())) {
            String label = getBiLabel("销量", analysis.getSalesQty7Qoq(), configParams.get("salesQty7Qoq"));
            if (label != null) {
                labels.add(createLabel(headId, analysis, label));
            }
        }

        // 流量标签
        if (ObjUtil.isNotEmpty(analysis.getSessions7Qoq())) {
            String label = getBiLabel("流量", analysis.getSessions7Qoq(), configParams.get("sessions7Qoq"));
            if (label != null) {
                labels.add(createLabel(headId, analysis, label));
            }
        }

        // 广告费标签
        if (ObjUtil.isNotEmpty(analysis.getAdSpend7Qoq())) {
            String label = getBiLabel("广告费", analysis.getAdSpend7Qoq(), configParams.get("adSpend7Qoq"));
            if (label != null) {
                labels.add(createLabel(headId, analysis, label));
            }
        }

        // 毛利标签
        if (ObjUtil.isNotEmpty(analysis.getGrossProfitRealRate7Qoq())) {
            String label = getBiLabel("毛利", analysis.getGrossProfitRealRate7Qoq(), configParams.get("grossProfitRealRate7Qoq"));
            if (label != null) {
                labels.add(createLabel(headId, analysis, label));
            }
        }

        return labels;
    }

    /**
     * 生成BI标签
     */
    private String getBiLabel(String type, String current, String standard) {
        try {
            if (Double.parseDouble(current) < Double.parseDouble(standard)) {
                return null;
            }
            if (current.startsWith("-")) {
                return type + "暴跌";
            }
            return type + "暴涨";
        } catch (NumberFormatException e) {
            log.warn("BI标签数值解析失败: type={}, current={}, standard={}", type, current, standard);
            return null;
        }
    }

    /**
     * 创建标签对象
     */
    private ListingLabel createLabel(Integer headId, BiSalesAnalysis analysis, String label) {
        ListingLabel listingLabel = new ListingLabel();
        listingLabel.setHeadId(headId);
        listingLabel.setPlatform(Objects.equals("AMAZON", analysis.getPlatform()) ? "AM" : "EB");
        listingLabel.setSiteCode(analysis.getCountry());
        listingLabel.setShopCode(analysis.getShopCode());
        listingLabel.setLabelType(getLabelType());
        listingLabel.setLabel(label);
        listingLabel.setCreateTime(DateUtils.getNowDate());
        return listingLabel;
    }

    /**
     * 过滤和去重标签
     */
    private List<ListingLabel> filterAndDeduplicateLabels(List<ListingLabel> labels) {
        return new ArrayList<>(labels.stream()
                .filter(label -> ObjUtil.isNotEmpty(label.getLabel()))
                .collect(Collectors.toMap(
                        label -> label.getHeadId() + "_" + label.getLabel(),
                        label -> label,
                        (existing, replacement) -> existing
                ))
                .values());
    }
}
