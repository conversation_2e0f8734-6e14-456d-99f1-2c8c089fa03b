package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AI结果同步业务处理类
 * 负责将AI生成的结果同步到商品标题和描述
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Component
public class AiResultSyncBiz {
    @Autowired
    private IGoodsTaskService goodsTaskService;

    @Autowired
    private IAiGenerationTaskService aiGenerationTaskService;

    @Autowired
    private IGoodsHeadService goodsHeadService;

    @Autowired
    private IGoodsDescriptionService goodsDescriptionService;

    @Resource
    protected IGoodsTaskInfoService goodsTaskInfoService;
    /**
     * 异步同步标题结果到商品
     *
     * @param aiResult     AI生成结果
     * @param goodsHeads
     */
    public void syncTitleResults(AiGenerationTask task, JSONObject aiResult, List<GoodsHead> goodsHeads) {
        // 解析商品编码列表
        List<String> goodsCodes = JSON.parseArray(task.getGoodsCodes(), String.class);

        Set<Integer> alreadySyncedHeadIds = new HashSet<>();
        // 遍历AI结果，更新对应商品的标题
        for (String goodsCode : goodsCodes) {
            // 找到对应的链接
            List<GoodsHead> goodsHeadList = goodsHeads.stream().filter(e -> e.getPdmGoodsCode().equals(goodsCode) && !alreadySyncedHeadIds.contains(e.getId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(goodsHeadList)) {
                continue;
            }
            GoodsHead goodsHead = goodsHeadList.get(0);
            alreadySyncedHeadIds.add(goodsHead.getId());

            // 更新标题
            JSONObject goodsResult = aiResult.getJSONObject(goodsCode);
            if (goodsResult != null && goodsResult.containsKey("titles")) {
                List<String> titles = goodsResult.getJSONArray("titles").toJavaList(String.class);
                if (!titles.isEmpty()) {
                    // 使用第一个生成的标题
                    String newTitle = titles.get(0);

                    // 查询并更新商品标题
                    updateGoodsTitle(goodsHead, newTitle);

                    GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
                    goodsTaskInfo.setTaskId(task.getTaskId()+"");
                    goodsTaskInfo.setPlatform(PlatformTypeEnum.EB.name());
                    goodsTaskInfo.setCreateBy("-1");
                    goodsTaskInfo.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
                    goodsTaskInfo.setStatus("0");
                    goodsTaskInfo.setDetails("标题生成成功");
                    goodsTaskInfo.setListingHeadId(goodsHead.getId()+"");
                    goodsTaskInfoService.insertGoodsTaskInfo(goodsTaskInfo);
                }
            }
        }
        log.info("标题结果同步完成，taskCorrelationId: {}", task.getTaskCorrelationId());
    }

    /**
     * 异步同步描述结果到商品
     *
     * @param aiResult     AI生成结果
     */
    public void syncDescriptionResults(AiGenerationTask task, JSONObject aiResult, List<GoodsHead> goodsHeads) {
        log.info("开始同步描述结果，taskCorrelationId: {}", task.getTaskCorrelationId());
        // 解析商品编码列表
        List<String> goodsCodes = JSON.parseArray(task.getGoodsCodes(), String.class);
        Set<Integer> alreadySyncedHeadIds = new HashSet<>();
        // 遍历AI结果，更新对应商品的描述
        for (String goodsCode : goodsCodes) {
            // 找到对应的链接
            List<GoodsHead> goodsHeadList = goodsHeads.stream().filter(e -> e.getPdmGoodsCode().equals(goodsCode) && !alreadySyncedHeadIds.contains(e.getId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(goodsHeadList)) {
                continue;
            }
            GoodsHead goodsHead = goodsHeadList.get(0);
            alreadySyncedHeadIds.add(goodsHead.getId());
            // 更新描述
            JSONObject goodsResult = aiResult.getJSONObject(goodsCode);
            if (goodsResult != null && goodsResult.containsKey("pd_content")) {
                String pdContent = goodsResult.getString("pd_content");
                if (pdContent != null && !pdContent.trim().isEmpty()) {
                    // 更新商品描述
                    updateGoodsDescription(goodsHead, pdContent);


                    GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
                    goodsTaskInfo.setTaskId(task.getTaskId()+"");
                    goodsTaskInfo.setPlatform(PlatformTypeEnum.EB.name());
                    goodsTaskInfo.setCreateBy("-1");
                    goodsTaskInfo.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
                    goodsTaskInfo.setStatus("0");
                    goodsTaskInfo.setDetails("描述生成成功");
                    goodsTaskInfo.setListingHeadId(goodsHead.getId()+"");
                    goodsTaskInfoService.insertGoodsTaskInfo(goodsTaskInfo);
                }
            }
        }

        log.info("描述结果同步完成，taskCorrelationId: {}", task.getTaskCorrelationId());
    }

    /**
     * 更新商品标题
     *
     * @param newTitle 新标题
     */
    private void updateGoodsTitle(GoodsHead goodsHead, String newTitle) {
        goodsHead.setTitle(newTitle);
        goodsHeadService.updateListingGoodsHead(goodsHead);
    }

    /**
     * 更新商品描述
     *
     * @param newDescription 新描述
     */
    private void updateGoodsDescription(GoodsHead goodsHead, String newDescription) {
        // 查询并更新商品描述
        GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsHead.getId());
        if (goodsDescription != null) {
            goodsDescription.setShortDescription(newDescription);
            goodsDescriptionService.updateListingGoodsDescription(goodsDescription);
            log.info("更新商品描述成功，商品ID: {}", goodsHead.getId());
        }
    }

    /**
     * 处理单个任务
     *
     * @param task AI任务
     * @param taskType 任务类型描述
     */
    public void processTask(AiGenerationTask task, String taskType) {
        log.info("开始处理{}任务，taskCorrelationId: {}", taskType, task.getTaskCorrelationId());
        GoodsTask goodsTask = goodsTaskService.selectGoodsTaskById(task.getTaskId() +"");
        // 通过任务找到所有的链接
        GoodsTaskInfo goodsTaskInfo = new GoodsTaskInfo();
        goodsTaskInfo.setTaskId(goodsTask.getId());
        List<GoodsTaskInfo> infoList = goodsTaskInfoService.selectGoodsTaskInfoList(goodsTaskInfo);
        if (CollUtil.isEmpty(infoList)) {
            return;
        }
        List<Integer> headIds = infoList.stream().filter(e -> StrUtil.isNotBlank(e.getListingHeadId())).map(e -> Integer.parseInt(e.getListingHeadId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(headIds)) {
            return;
        }
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(headIds.toArray(new Integer[headIds.size()]));

        if ("TITLE".equals(taskType)) {
            // 处理标题任务
            syncTitleResults(task, JSON.parseObject(task.getAiResult()), goodsHeads);
        } else if ("DESCRIPTION".equals(taskType)) {
            // 处理描述任务
            syncDescriptionResults(task, JSON.parseObject(task.getAiResult()), goodsHeads);
        }

        // 更新任务处理状态为已处理
        aiGenerationTaskService.updateProcessStatus(task.getTaskCorrelationId(), 1);
        log.info("{}任务处理完成，taskCorrelationId: {}", taskType, task.getTaskCorrelationId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void processTasks(Integer taskId, List<AiGenerationTask> taskList) {
        for (AiGenerationTask task : taskList) {
            processTask(task, task.getTaskType());
        }

        int successTask = aiGenerationTaskService.countTaskByStatus(taskId, 1);
        // 如果两个任务都处理成功，则更新任务状态为已完成
        if (successTask == 2) {
            GoodsTask goodsTask = goodsTaskService.selectGoodsTaskById(taskId +"");
            GoodsTask updateTask = new GoodsTask();
            updateTask.setId(taskId+"");
            updateTask.setTaskStatus("已完成");
            updateTask.setSuccessNum(goodsTask.getTaskNum());
            goodsTaskService.updateGoodsTask(updateTask);
        }
    }


}
