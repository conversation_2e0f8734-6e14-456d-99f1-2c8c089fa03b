package com.suncent.smc.persistence.publication.mapper;

import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * listing标签关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
public interface ListingLabelMapper 
{
    /**
     * 查询listing标签关系
     * 
     * @param id listing标签关系主键
     * @return listing标签关系
     */
    public ListingLabel selectListingLabelById(Long id);

    /**
     * 查询listing标签关系列表
     * 
     * @param listingLabel listing标签关系
     * @return listing标签关系集合
     */
    public List<ListingLabel> selectListingLabelList(ListingLabel listingLabel);

    /**
     * 新增listing标签关系
     * 
     * @param listingLabel listing标签关系
     * @return 结果
     */
    public int insertListingLabel(ListingLabel listingLabel);

    /**
     * 修改listing标签关系
     * 
     * @param listingLabel listing标签关系
     * @return 结果
     */
    public int updateListingLabel(ListingLabel listingLabel);

    /**
     * 删除listing标签关系
     * 
     * @param id listing标签关系主键
     * @return 结果
     */
    public int deleteListingLabelById(Long id);

    /**
     * 批量删除listing标签关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteListingLabelByIds(String[] ids);

    int insertListingLabelBatch(List<ListingLabel> list);

    int deleteListingLabelByShopCode(@Param("labelType") String labelType,@Param("shopCode") String shopCode);

    /**
     * 根据listingPerformance查询headId
     * @param headIdList
     * @param listingPerformance
     * @return
     */
    List<ListingLabel> selectHeadIdByListingPerformance(@Param("headIdList")List<Integer> headIdList, @Param("listingPerformance")String listingPerformance,@Param("shopCodeList")List<String> shopCodeList);

    List<ListingLabel> selectCartListingLabelList(@Param("shopCode") String shopCode);

    int deleteListingLabelsByShopCode(@Param("label") List<String> label, @Param("shopCode") String shopCode);

    int deleteListingLabelsByHeadIdsAndLabelType(@Param("headIds") List<Integer> headIds, @Param("labelType") String labelType, @Param("shopCode") String shopCode);

    int delYesterdayLabel(@Param("labelType") String labelType);

    int deleteListingLabelByLabelTypeAndLabel(@Param("labelType") String labelType, @Param("label") String label);

    ListingLabel selectListingLabelByHeadIdAndLabelType(@Param("headId") Integer headId, @Param("labelType") String labelType);

    void deleteListingLabelByHeadIdAndLabel(@Param("headId") Integer headId, @Param("label") String label);

    void deleteListingLabelsByHeadIds(@Param("labelList") List<String> labelList, @Param("headIds") Set<Integer> headIds);

    int selectCountByHeadIdAndLabel(@Param("headId") Integer headId, @Param("label") String label);
}
