package com.suncent.smc.persistence.publication.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import com.suncent.smc.persistence.publication.mapper.ListingLabelMapper;
import com.suncent.smc.persistence.publication.service.IListingLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * listing标签关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Service
public class ListingLabelServiceImpl implements IListingLabelService
{
    @Autowired
    private ListingLabelMapper listingLabelMapper;

    /**
     * 查询listing标签关系
     * 
     * @param id listing标签关系主键
     * @return listing标签关系
     */
    @Override
    public ListingLabel selectListingLabelById(Long id)
    {
        return listingLabelMapper.selectListingLabelById(id);
    }

    /**
     * 查询listing标签关系列表
     * 
     * @param listingLabel listing标签关系
     * @return listing标签关系
     */
    @Override
    public List<ListingLabel> selectListingLabelList(ListingLabel listingLabel)
    {
        return listingLabelMapper.selectListingLabelList(listingLabel);
    }

    /**
     * 新增listing标签关系
     * 
     * @param listingLabel listing标签关系
     * @return 结果
     */
    @Override
    public int insertListingLabel(ListingLabel listingLabel)
    {
        listingLabel.setCreateTime(DateUtils.getNowDate());
        return listingLabelMapper.insertListingLabel(listingLabel);
    }

    /**
     * 修改listing标签关系
     * 
     * @param listingLabel listing标签关系
     * @return 结果
     */
    @Override
    public int updateListingLabel(ListingLabel listingLabel)
    {
        return listingLabelMapper.updateListingLabel(listingLabel);
    }

    /**
     * 批量删除listing标签关系
     * 
     * @param ids 需要删除的listing标签关系主键
     * @return 结果
     */
    @Override
    public int deleteListingLabelByIds(String ids)
    {
        return listingLabelMapper.deleteListingLabelByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除listing标签关系信息
     * 
     * @param id listing标签关系主键
     * @return 结果
     */
    @Override
    public int deleteListingLabelById(Long id)
    {
        return listingLabelMapper.deleteListingLabelById(id);
    }


    @Override
    public int insertListingLabelBatch(List<ListingLabel> list) {
        return listingLabelMapper.insertListingLabelBatch(list);
    }

    @Override
    public int deleteListingLabelByShopCode(String labelType, String shopCode) {
        return listingLabelMapper.deleteListingLabelByShopCode(labelType,shopCode);
    }

    @Override
    public int deleteListingLabelsByShopCode(List<String> label, String shopCode) {
        return listingLabelMapper.deleteListingLabelsByShopCode(label, shopCode);
    }

    @Override
    public int deleteListingLabelsByHeadIdsAndLabelType(List<Integer> headIds, String labelType, String shopCode) {
        return listingLabelMapper.deleteListingLabelsByHeadIdsAndLabelType(headIds, labelType, shopCode);
    }

    @Override
    public List<ListingLabel> selectHeadIdByListingPerformance(List<Integer> headIdList, String listingPerformance,List<String>shopCodeList) {
        return listingLabelMapper.selectHeadIdByListingPerformance(headIdList,listingPerformance,shopCodeList);
    }

    @Override
    public List<ListingLabel> selectCartListingLabelList(String shopCode) {
        return listingLabelMapper.selectCartListingLabelList(shopCode);
    }

    @Override
    public int delYesterdayLabel(String labelType) {
        return listingLabelMapper.delYesterdayLabel(labelType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<ListingLabel> dbLabelList) {
        if (CollUtil.isEmpty(dbLabelList)) {
            return;
        }
        // 再插入
        dbLabelList.forEach(item -> {
            listingLabelMapper.insertListingLabel(item);
        });
    }

    @Override
    public int deleteListingLabelByLabelTypeAndLabel(String labelType, String label) {
        return listingLabelMapper.deleteListingLabelByLabelTypeAndLabel(labelType, label);
    }

    @Override
    public void deleteListingLabelsByHeadIds(List<String> list, Set<Integer> headIds) {
        listingLabelMapper.deleteListingLabelsByHeadIds(list, headIds);
    }
}
