package com.suncent.smc.web.controller.publication;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.*;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.suncent.smc.common.annotation.Excel;
import com.suncent.smc.common.annotation.Log;
import com.suncent.smc.common.annotation.RecordLog;
import com.suncent.smc.common.config.RuoYiConfig;
import com.suncent.smc.common.constant.Constants;
import com.suncent.smc.common.core.controller.BaseController;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.core.page.TableDataInfo;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.EnvUtils;
import com.suncent.smc.common.utils.ExceptionUtil;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.poi.DataReorganizeStrategy;
import com.suncent.smc.common.utils.poi.ExcelUtil;
import com.suncent.smc.common.utils.poi.GlobalColumnMergeStrategy;
import com.suncent.smc.provider.biz.publication.dto.EbayDescriptionGenerationResponseDTO;
import com.suncent.smc.provider.biz.publication.dto.EbayTitleGenerationResponseDTO;
import com.suncent.smc.system.service.ISysConfigService;
import org.springframework.data.redis.core.RedisTemplate;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.util.concurrent.TimeUnit;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import com.suncent.smc.persistence.ads.domain.*;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.cdp.service.ICateProductService;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.common.CommonUtilsSmc;
import com.suncent.smc.persistence.configuration.category.domain.entity.ConfigRequiredField;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreShortDescription;
import com.suncent.smc.persistence.configuration.store.service.IShortDescriptionApiService;
import com.suncent.smc.persistence.pdm.domain.dto.*;
import com.suncent.smc.persistence.pdm.domain.dto.goodsInfoDto.GoodsInfoDTO;
import com.suncent.smc.persistence.pdm.domain.entity.*;
import com.suncent.smc.persistence.pdm.service.*;
import com.suncent.smc.persistence.product.domain.entity.PublicationDay;
import com.suncent.smc.persistence.publication.domain.dto.*;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.domain.vo.ListAttrDefaultValueVO;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.provider.base.service.ICdpBaseConfigService;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.pdm.GoodsBiz;
import com.suncent.smc.provider.biz.publication.*;
import com.suncent.smc.provider.biz.publication.domain.AutoCreateListingVO;
import com.suncent.smc.provider.biz.publication.domain.GoodsNewAttributeVO;
import com.suncent.smc.provider.biz.publication.domain.SaleGoodsVO;
import com.suncent.smc.provider.biz.publication.service.IBaseListingService;
import com.suncent.smc.provider.biz.publication.service.impl.AmazonPlatformListingServiceImpl;
import com.suncent.smc.provider.biz.publication.service.impl.ListingInfoServiceImpl;
import com.suncent.smc.provider.biz.temu.TemuBiz;
import com.suncent.smc.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 商品管理Controller
 *
 * <AUTHOR>
 * @since 2023-01-05 15:33:00
 */
@Controller
@RequestMapping("/publication/goods")
@Slf4j
public class GoodsController extends BaseController {
    private final String prefix = "publication/goods";

    private final String ebay_prefix = "ebay/goods";

    @Override
    @InitBinder  //类初始化是调用的方法注解
    public void initBinder(WebDataBinder binder) {
        //给这个controller配置接收list的长度100000，仅在这个controller有效
        binder.setAutoGrowCollectionLimit(100000);
    }

    @Autowired
    private GoodsInfoBiz goodsInfoBiz;
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private IGoodsBiPriceService goodsBiPriceService;
    @Autowired
    private IGoodsImageService goodsImageService;
    @Autowired
    private IStockWhSkuService stockWhSkuService;
    @Autowired
    private ICdpBaseConfigService cdpBaseConfig;
    @Autowired
    private IGoodsFunctionInfoService goodsFunctionInfoService;
    @Autowired
    private IGoodsPaperWorkService goodsPaperWorkService;
    @Autowired
    private ISellingPointService sellingPointService;
    @Autowired
    private IAdsService adsService;
    @Autowired
    private GoodsBiz goodsBiz;
    @Autowired
    private ThirdpartyInventoryBiz inventoryBiz;
    @Autowired
    private IMappingGoodsService mappingGoodsService;
    @Autowired
    private PlatformListingFactory platformListingFactory;
    @Autowired
    private IListingAdaptiveTempService adaptiveTempService;
    @Resource
    private IGoodsResourceService goodsResourceService;
    @Autowired
    ListingInfoServiceImpl listingInfoService;

    @Autowired
    private ICensorshipInfoService censorshipInfoService;
    @Autowired
    private PDMHttpRequestBiz PDMHttpRequestBiz;
    @Autowired
    private AmazonPlatformListingServiceImpl amazonPlatformListingService;
    @Autowired
    ISkuRefundRatePjService skuRefundRatePjService;

    @Autowired
    ICdpBaseConfigService baseConfigService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    TemuBiz temuBiz;
    @Autowired
    private IShopService shopService;
    @Autowired
    private IGoodsHeadService goodsHeadService;

    @Autowired
    ListingInfoBiz listingInfoBiz;
    @Autowired
    private AmazonProductBiz amazonProductBiz;
    @Autowired
    private EbayContentGenerationHttpRequestBiz ebayContentGenerationHttpRequestBiz;
    @Autowired
    private PDMHttpRequestBiz pdmHttpRequestBiz;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ISysConfigService configService;
    @Resource
    private IShortDescriptionApiService shortDescriptionApiService;
    @Autowired
    private ICateProductService cateProductService;
    @RequiresPermissions("publication:goods:view")
    @GetMapping()
    public String list(String goodsCode,ModelMap mmap) {
        if (ObjUtil.equals(goodsCode, "false")){
            mmap.put("goodsCode", "");
            mmap.put("searchBar", false);
            // 添加业务分类数据到页面
            mmap.put("operationClassificationList", cateProductService.getOperationClassification());
            return prefix + "/goods";
        }
        if (ObjUtil.equals(goodsCode, "radio")) {
            mmap.put("goodsCode", "");
            mmap.put("searchBar", false);
            mmap.put("isRadio", true);
            // 添加业务分类数据到页面
            mmap.put("operationClassificationList", cateProductService.getOperationClassification());
            return prefix + "/goods";
        }
        mmap.put("goodsCode", StringUtils.isBlank(goodsCode)?"":goodsCode);
        mmap.put("searchBar", true);
        // 添加业务分类数据到页面
        mmap.put("operationClassificationList", cateProductService.getOperationClassification());
        return prefix + "/goods";
    }

    @GetMapping("/draft")
    public String draft() {
        return prefix + "/draft";
    }

    /**
     * 新建亚马逊商品刊登页面
     */
    @RequiresPermissions("publication:goods:addAmazon")
    @GetMapping("/add/{id}")
    public String add(@PathVariable("id") Long id, ModelMap mmap) {
        packingAMAddData(id, mmap, false);

        return prefix + "/am_add_publish";
    }

    /**
     * 新建亚马逊商品刊登页面
     */
    @RequiresPermissions("publication:goods:addAmazon")
    @GetMapping("/v2/add/{type}/{id}")
    public String amAdd(@PathVariable("type") String type, @PathVariable("id") Long id, ModelMap mmap) {
        packingAMAddData(id, mmap, true);
        mmap.put("type", type);
        return prefix + "/v2/am_add_publish";
    }


    private void packingAMAddData(Long id, ModelMap mmap, Boolean isVc) {
        GetGoodsDetailQueryDTO goodsDetailQueryDTO = new GetGoodsDetailQueryDTO();
        goodsDetailQueryDTO.setGoodsIdList(Lists.newArrayList(id));
        List<SaleGoodsDTO> saleGoodsDTOS = PDMHttpRequestBiz.listGoodsDetail(goodsDetailQueryDTO);

        if (CollectionUtils.isEmpty(saleGoodsDTOS)) {
            throw new BusinessException("商品不存在");
        }
        SaleGoodsDTO goodsDto = saleGoodsDTOS.get(0);
        //站点
        goodsDto.setSiteCode(PlatformSiteEnum.AM_US.getName());
        goodsDto.setUuid(IdUtil.randomUUID());

        // 商品规格数据
        goodsDto.setGoodsSpecifications(goodsDto.getSpecifications());
        //库存
        List<ThirdpartyFbmDTO> stock = inventoryBiz.selectStockShareAndPartGoodsCode(Collections.singletonList(goodsDto.getGoodsCode()), "US", isVc);
        if (CollectionUtils.isNotEmpty(stock)) {
            // 默认取 us站点的库存 求qtyBases集合中的availableQty的和
            Integer availableQty = stock.stream().filter(s->Objects.equals(s.getWhCountry(),PlatformSiteEnum.AM_US.getSiteCodeType()))
                    .mapToInt(ThirdpartyFbmDTO::getSellableQty).sum();
            goodsDto.setStockOnSalesQty(availableQty);
        }else {
            goodsDto.setStockOnSalesQty(0);
        }
        //价格
        GoodsRedPriceDTO goodsPriceInfo = goodsDto.getGoodsPriceInfo();
        if (ObjUtil.isNotEmpty(goodsPriceInfo)) {
            goodsDto.setFbaPrice(Objects.isNull(goodsPriceInfo.getFbaRedLinePrice())?new BigDecimal(0): BigDecimal.valueOf(goodsPriceInfo.getFbaRedLinePrice()));
            goodsDto.setFbmPrice(Objects.isNull(goodsPriceInfo.getFbmRedLinePrice())?new BigDecimal(0):BigDecimal.valueOf(goodsPriceInfo.getFbmRedLinePrice()));
            goodsDto.setPurchasePrice(Objects.isNull(goodsPriceInfo.getEbayRedLinePrice())?new BigDecimal(0): BigDecimal.valueOf(goodsPriceInfo.getEbayRedLinePrice()));
        }else {
            goodsDto.setFbaPrice(new BigDecimal(0));
            goodsDto.setFbmPrice(new BigDecimal(0));
            goodsDto.setPurchasePrice(new BigDecimal(0));
        }
        //对应平台类目
        if ( StringUtils.isNotEmpty(goodsDto.getProductCategoryCode()) ){
            String goodsCode = goodsDto.getGoodsCode();
            String siteCode = goodsDto.getSiteCode();
            String productCategoryCode = goodsInfoBiz.selectGoodsAndCategoryMatch(PlatformTypeEnum.AM.name(),goodsDto.getProductCategoryCode(),siteCode,goodsCode);
            if (StrUtil.isNotEmpty(productCategoryCode)){
                goodsDto.setProductCategoryCode(productCategoryCode);
            }
        }

        //图片
        String brandCode = goodsDto.getBrandCode();
        List<GoodsImage> goodsImageList = goodsDto.getGoodsImageList();
        List<GoodsImage> imagesByBrandCode = goodsImageList.stream().filter(image -> ObjUtil.isEmpty(image.getBrandCode()) || ObjUtil.equals(image.getBrandCode(), brandCode) || ObjUtil.equals(image.getBrandCode(), "COMMON") ).collect(Collectors.toList());
        goodsDto.setGoodsImageList(imagesByBrandCode);

        //品牌名
        List<KeyValueEntity> brandList = cdpBaseConfig.getBrandAllKVList();
        if (CollectionUtils.isNotEmpty(brandList)) {
            brandList.forEach(brand -> {
                if (Objects.equals(brand.getKey(), goodsDto.getBrandCode())) {
                    goodsDto.setBrandName(brand.getValue());
                    goodsDto.setBrandCode(brand.getValue());
                }
            });
        }
        ExceptionUtil.sandbox(() -> {
            String autoPartPosition = getAutoPartPosition(goodsDto.getProductCode(), null);
            goodsDto.setAutoPartPosition(autoPartPosition);
        });

        mmap.put("goodsDto", goodsDto);
    }


    private String getAutoPartPosition(String productCode, Map<String, String> autoPartPositionMap) {
        if (StringUtils.isBlank(productCode)) {
            return "";
        }
        if (autoPartPositionMap != null && autoPartPositionMap.containsKey(productCode)) {
            return autoPartPositionMap.get(productCode);
        }
        String result = "";
        // 通过productCode获取auto_part_position
        List<AdsFitmentDataBi> adsFitmentDataBiList = adsService.getByProduct(productCode);
        if(CollUtil.isEmpty(adsFitmentDataBiList)) {
            return "";
        }
        adsFitmentDataBiList = adsFitmentDataBiList.stream().filter(a -> StringUtils.isNotBlank(a.getPosition())).collect(Collectors.toList());
        if (CollUtil.isEmpty(adsFitmentDataBiList)) {
            return "";
        }
        // 按vio倒叙，最大的放在最前面
        adsFitmentDataBiList = adsFitmentDataBiList.stream().sorted(Comparator.comparing(AdsFitmentDataBi::getVio).reversed()).collect(Collectors.toList());
        result = adsFitmentDataBiList.stream().map(AdsFitmentDataBi::getPosition).distinct().collect(Collectors.joining(","));
        // 替换;为,
        result = result.replace(";", ",");
        if (autoPartPositionMap != null) {
            autoPartPositionMap.put(productCode, result);
        }
        return result;
    }

    @GetMapping("/toAddInventoryExcludePage")
    public String inventoryExcludePage(String goodsCodes, ModelMap modelMap)
    {
        modelMap.put("saleGoodIds",goodsCodes);
        return prefix + "/inventory_exclude";
    }

    /**
     * 新建ebay商品刊登页面
     */
    @RequiresPermissions("publication:goods:addEbay")
    @GetMapping("/addEbay/{id}/{version}")
    public String addEbay(@PathVariable("id") Long id,@PathVariable("version") String version, ModelMap mmap) {
        Long userId = ShiroUtils.getUserId();
        GetGoodsDetailQueryDTO goodsDetailQueryDTO = new GetGoodsDetailQueryDTO();
        goodsDetailQueryDTO.setGoodsIdList(Lists.newArrayList(id));
        List<SaleGoodsDTO> saleGoodsDTOS = PDMHttpRequestBiz.listGoodsDetail(goodsDetailQueryDTO);

        if (CollectionUtils.isEmpty(saleGoodsDTOS)) {
            throw new BusinessException("商品不存在");
        }
        SaleGoodsDTO goodsDto = saleGoodsDTOS.get(0);
        String uuid = IdUtil.randomUUID();
        goodsDto.setUuid(uuid);
        goodsDto.setSiteCode(PlatformSiteEnum.US.getName());

        // 兑换品牌名
        List<KeyValueEntity> brandList = cdpBaseConfig.getBrandAllKVList();
        if (CollectionUtils.isNotEmpty(brandList)) {
            brandList.forEach(brand -> {
                if (Objects.equals(brand.getKey(), goodsDto.getBrandCode())) {
                    goodsDto.setBrandName(brand.getValue());
                }
            });
        }
        //封装页面展示数据对象
        goodsDto.setGoodsSpecifications(goodsDto.getSpecifications());

        GoodsRedPriceDTO goodsPriceInfo = goodsDto.getGoodsPriceInfo();
        goodsDto.setEbayPrice(ObjectUtils.isNotEmpty(goodsPriceInfo) && ObjectUtils.isNotEmpty(goodsPriceInfo.getEbayRedLinePrice())
                ? BigDecimal.valueOf(goodsPriceInfo.getEbayRedLinePrice())
                : BigDecimal.ZERO);
        //库存
        List<ThirdpartyFbmDTO> stock = inventoryBiz.selectStockShareAndPartGoodsCode(Collections.singletonList(goodsDto.getGoodsCode()), "US", false);
        if (CollectionUtils.isNotEmpty(stock)) {
            // 取us站点的库存  求qtyBases集合中的availableQty的和
            Integer availableQty = stock.stream().filter(q ->   Objects.equals(q.getWhCountry(),PlatformSiteEnum.US.getSiteCodeType()) &&
                            !Objects.equals(q.getWarehouseType(), "FBA") && !Objects.equals(q.getWarehouseType(), "小仓"))
                    .mapToInt(ThirdpartyFbmDTO::getSellableQty).sum();
            goodsDto.setStockOnSalesQty(availableQty);
        }else {
            goodsDto.setStockOnSalesQty(0);
        }
        ArrayList<PublicationDay> publicationDays = new ArrayList<>();
        PublicationDay publicationDay = new PublicationDay(0, "GTC", true);
        PublicationDay publicationDay1 = new PublicationDay(1, "1", false);
        PublicationDay publicationDay2 = new PublicationDay(2, "3", false);
        PublicationDay publicationDay3 = new PublicationDay(3, "5", false);
        PublicationDay publicationDay4 = new PublicationDay(4, "7", false);
        PublicationDay publicationDay5 = new PublicationDay(5, "10", false);
        publicationDays.add(publicationDay);
        publicationDays.add(publicationDay1);
        publicationDays.add(publicationDay2);
        publicationDays.add(publicationDay3);
        publicationDays.add(publicationDay4);
        publicationDays.add(publicationDay5);
        goodsDto.setPublicationDayList(publicationDays);
        goodsDto.setStartStockQty("1");
        //对应平台类目
        if ( StringUtils.isNotEmpty(goodsDto.getProductCategoryCode()) ){
            String goodsCode = goodsDto.getGoodsCode();
            String productCategoryCode = goodsInfoBiz.selectGoodsAndCategoryMatch(PlatformTypeEnum.EB.name(),goodsDto.getProductCategoryCode(),goodsDto.getSiteCode(),goodsCode);
            if (StrUtil.isNotEmpty(productCategoryCode)){
                goodsDto.setProductCategoryCode(productCategoryCode);

                //通过美国站点的platformcategoryId 获取其它站点的类目
                Map<String, String> map = goodsInfoBiz.getEbayOtherSiteCategoryByUS(productCategoryCode);
                goodsDto.setProductCategoryCodeBySite(map);
            }
        }

        // 适配数据写入临时表
        List<AdsFitmentDataEbay> adsFitmentDataEbayList = adsService.selectFitmentDataEbayByProductCode(goodsDto.getProductCode(),goodsDto.getGoodsCode());
        if (!adsFitmentDataEbayList.isEmpty()) {
            int limitSize = 500;
            List<ListingAdaptiveTemp> adaptiveTempList = new ArrayList<>();
            adsFitmentDataEbayList.stream().forEach(f -> {
                ListingAdaptiveTemp item = new ListingAdaptiveTemp();
                item.setAdaptiveEngine(f.getEngine());
                item.setAdaptiveMake(f.getMake());
                item.setAdaptiveModel(f.getModel());
                item.setAdaptiveTrim(f.getTrim());
                item.setAdaptiveYear(f.getYear());
                item.setAdaptiveSubmodel(f.getSubmodel());
                item.setNotes(f.getNotes());
                item.setPdmGoodsCode(goodsDto.getGoodsCode());
                item.setUuid(uuid);
                item.setCreateBy(String.valueOf(userId));
                item.setCreateTime(new Date());
                adaptiveTempList.add(item);
            });
            List<List<ListingAdaptiveTemp>> partitions = Lists.partition(adaptiveTempList, limitSize);
            // 分批写入
            partitions.stream().parallel().forEach(i -> adaptiveTempService.batchInsertAdaptiveTemp(i));
        }
        mmap.put("ebGoodsInfo", goodsDto);
        if (ObjUtil.equals(version, "V2")){
            return ebay_prefix + "/ebay_add_publish";
        }
        return prefix + "/ebay_add_publish";
    }


    /**
     * 生成草稿--保存商品刊登数据
     *
     * @param listingDTO 商品数据
     * @return 保存结果
     */
    @PostMapping("/save")
    @ResponseBody
    @RecordLog(operDesc = "单个新建Listing至草稿", ebayV2 = "#listingDTO.ebayV2")
    public AjaxResult listingSave(ListingDTO listingDTO) {
        String platformType = listingDTO.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行刊登");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType,listingDTO.isEbayV2());
            return listingServiceByPlatformType.listingSave(listingDTO);
        } catch (Exception e) {
            log.error(String.format("保存刊登商品信息失败,platformType:%s",platformType), e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 生成草稿--保存商品刊登数据
     *
     * @param listingDTO 商品数据
     * @return 保存结果
     */
    @PostMapping("/v2/save")
    @ResponseBody
    @RecordLog(operDesc = "单个新建Listing至草稿")
    public AjaxResult listingV2Save(ListingDTO listingDTO) {
        String platformType = listingDTO.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行刊登");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            return listingServiceByPlatformType.listingSaveV2(listingDTO);
        } catch (Exception e) {
            log.error(String.format("保存刊登商品信息失败,platformType:%s",platformType), e);
            return AjaxResult.error(e.getMessage());
        }
    }


    /**
     * 编辑和新建的送检
     *
     * @param listingDTO 商品数据
     * @return 保存结果
     */
    @Log(title = "Listing管理-单个送检", businessType = BusinessType.UPDATE)
    @PostMapping("/handleCensorship/{type}")
    @RequiresPermissions("publication:listing:batchCensorship")
    @ResponseBody
    public AjaxResult handleCensorship(ListingEditDTO listingDTO, @PathVariable("type") String type) {

       return goodsBiz.censorship(listingDTO,type,"");

    }

    /**
     * 编辑和新建的送检V2
     *
     * @param listingDTO 商品数据
     * @return 保存结果
     */
    @Log(title = "Listing管理-单个送检", businessType = BusinessType.UPDATE)
    @PostMapping("/handleCensorshipV2/{type}")
    @RequiresPermissions("publication:listing:batchCensorship")
    @ResponseBody
    public AjaxResult handleCensorshipV2(ListingEditDTO listingDTO, @PathVariable("type") String type) {

        return goodsBiz.censorshipV2(listingDTO, type, "");

    }

    /**
     * 批量送检
     *
     * @return 保存结果
     */
    @Log(title = "Listing管理-批量送检", businessType = BusinessType.UPDATE)
    @PostMapping("/handleBatchCensorshipV2")
    @RequiresPermissions("publication:listing:batchCensorship")
    @ResponseBody
    public AjaxResult handleBatchCensorshipV2(String ids) {
        ListingEditDTO listingDTO = new ListingEditDTO();
        return goodsBiz.censorshipV2(listingDTO, "batch", ids);

    }

    /**
     * 批量送检
     *
     * @return 保存结果
     */
    @Log(title = "Listing管理-批量送检", businessType = BusinessType.UPDATE)
    @PostMapping("/handleBatchCensorship")
    @RequiresPermissions("publication:listing:batchCensorship")
    @ResponseBody
    public AjaxResult handleBatchCensorship(String ids) {
        ListingEditDTO listingDTO=new ListingEditDTO();
        return goodsBiz.censorship(listingDTO,"batch",ids);

    }

    /**
     * 送检界面
     *
     * @return 保存结果
     */
    @GetMapping("/censorship/{ids}")
    public String getCensorship(@PathVariable("ids") String ids,ModelMap mmap) {
        if (StringUtils.isBlank(ids)){
            throw new BusinessException("送检失败,请联系管理员");
        }
        List<Integer> integers = CommonUtilsSmc.toIntegerList(ids);
        CensorshipInfo censorshipInfo=new CensorshipInfo();
        censorshipInfo.setIdList(integers);
        List<CensorshipInfo> censorshipInfoList = censorshipInfoService.selectCensorshipInfoList(censorshipInfo);
        mmap.put("censorshipInfoList",censorshipInfoList);
        return  prefix +"/censorship";
    }
    /**
     * 竞品采集页面
     */
    @GetMapping("/collection/goods")
    public String collectionGoods() {
        return prefix + "/collection_competitive_goods";
    }

    /**
     * 根据实际库存范围筛选 temu适用
     *
     * @param saleGoodsDTO
     * @return
     */
    public boolean getTemuGoodHeadParamByActulStockScoop(PageQueryGoodsDTO saleGoodsDTO) {
        BigDecimal actualStockOnSalesQtyStart = saleGoodsDTO.getActualStockOnSalesQtyStart();
        BigDecimal actualStockOnSalesQtyEnd = saleGoodsDTO.getActualStockOnSalesQtyEnd();
        String pdmGoodsCode = saleGoodsDTO.getGoodsCode();
        String siteCode= "US";
        if (ObjectUtils.isNotEmpty(actualStockOnSalesQtyStart) || ObjectUtils.isNotEmpty(actualStockOnSalesQtyEnd)) {
            List<String> specialSku = listingInfoBiz.getSpeciaSku();
            List<ThirdpartyFbmDTO> specialSkuThirdpartyFbmDTOList = inventoryBiz.selectStockShareAndPartGoodsCode(specialSku, siteCode, false);
            //过滤specialSkuThirdpartyFbmDTOList中sellableQty大于等于actualStockOnSalesQtyStart 小于等于 actualStockOnSalesQtyEnd的sku
            List<String> specialSkuList = specialSkuThirdpartyFbmDTOList.stream().filter(e -> {
                if (ObjectUtils.isNotEmpty(actualStockOnSalesQtyStart) && ObjectUtils.isNotEmpty(actualStockOnSalesQtyEnd)) {
                    return e.getSellableQty() >= actualStockOnSalesQtyStart.intValue() && e.getSellableQty() <= actualStockOnSalesQtyEnd.intValue();
                }
                if (ObjectUtils.isNotEmpty(actualStockOnSalesQtyStart)) {
                    return e.getSellableQty() >= actualStockOnSalesQtyStart.intValue();
                }
                if (ObjectUtils.isNotEmpty(actualStockOnSalesQtyEnd)) {
                    return e.getSellableQty() <= actualStockOnSalesQtyEnd.intValue();
                }
                return false;
            }).map(ThirdpartyFbmDTO::getSku).collect(Collectors.toList());

            //通过接口获取在库存范围的sku,此处因为库存api只是实物库存,所以筛出来的sku会不准确,所以需要再次筛选
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectStockByStockScope(
                    ObjectUtils.isNotEmpty(actualStockOnSalesQtyStart) ? actualStockOnSalesQtyStart.intValue() : 0,
                    ObjectUtils.isNotEmpty(actualStockOnSalesQtyEnd) ? actualStockOnSalesQtyEnd.intValue() : null,
                    siteCode, WarehouseExcludeEnum.getExcludeWarehouseCodeList(), false);
            if (ObjectUtils.isEmpty(thirdpartyFbmDTOList)) {
                //没有匹配到范围内的合适库存,也不需要查数据库直接返回
                return false;
            }

            //thirdpartyFbmDTOList中的sku与specialSku去差集
            List<String> skuList = thirdpartyFbmDTOList.stream().map(ThirdpartyFbmDTO::getSku).collect(Collectors.toList());
            skuList.removeAll(specialSku);
            skuList.addAll(specialSkuList);
            if (ObjectUtils.isEmpty(skuList)) {
                //没有匹配到范围内的合适库存,也不需要查数据库直接返回
                return false;
            }
            if (skuList.size() > 10000) {
                //skuList数量大于2000,不需要查数据库直接返回
                return false;
            }
            if (ObjectUtils.isEmpty(pdmGoodsCode)) {
                String skuStr = String.join(" ", skuList);
                saleGoodsDTO.setGoodsCode(skuStr);
            } else {
                //把pdmGoodsCode按照 分割成list
                List<String> pdmGoodsCodeList = new ArrayList<>(Arrays.asList(pdmGoodsCode.split(" ")));
                //pdmGoodsCodeList 与skuList 取交集
                pdmGoodsCodeList.retainAll(skuList);
                if (org.springframework.util.CollectionUtils.isEmpty(pdmGoodsCodeList)) {
                    //没有匹配到范围内的合适库存,也不需要查数据库直接返回
                    return false;
                } else {
                    String skuStr = String.join(" ", pdmGoodsCodeList);
                    saleGoodsDTO.setGoodsCode(skuStr);
                }
            }
        }
        return true;
    }

    /**
     * 查询商品数据列表
     */
    @RequiresPermissions("publication:goods:view:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(PageQueryGoodsDTO saleGoodsDTO) {

        if (!getTemuGoodHeadParamByActulStockScoop(saleGoodsDTO)) {
            return getCoustomDataTable(new ArrayList<>(), 0);
        }

        // 处理业务分类筛选
        if (CollUtil.isNotEmpty(saleGoodsDTO.getOperationClassificationList())) {
            List<String> goodsCodesByClassification = cateProductService.getGoodsCodesByOperationClassificationList(saleGoodsDTO.getOperationClassificationList());
            if (CollUtil.isEmpty(goodsCodesByClassification)) {
                return getCoustomDataTable(new ArrayList<>(), 0);
            }

            // 如果已经有goodsCode筛选条件，需要取交集
            if (StringUtils.isNotBlank(saleGoodsDTO.getGoodsCode())) {
                List<String> existingGoodsCodes = Arrays.asList(saleGoodsDTO.getGoodsCode().split("\\s+"));
                goodsCodesByClassification = goodsCodesByClassification.stream()
                        .filter(existingGoodsCodes::contains)
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(goodsCodesByClassification)) {
                    return getCoustomDataTable(new ArrayList<>(), 0);
                }
                // 设置筛选后的goodsCode（取交集结果）
                saleGoodsDTO.setGoodsCode(String.join(" ", goodsCodesByClassification));
            } else {
                // 如果没有手动输入SKU，则使用业务分类查询出的goodsCode列表
                saleGoodsDTO.setGoodsCode(String.join(" ", goodsCodesByClassification));
            }
        }

        PageInfo<SaleGoodsDTO> pageInfo = PDMHttpRequestBiz.pageQueryGoodsDetailV2(saleGoodsDTO);
        if(pageInfo == null) {
            return getCoustomDataTable(new ArrayList<>(), 0);
        }

        List<SaleGoodsDTO> listJSON = pageInfo.getList();
        if (CollUtil.isEmpty(listJSON)) {
            return getCoustomDataTable(new ArrayList<>(), pageInfo.getTotal());
        }
        List<SaleGoodsDTO> list = JSONObject.parseArray(JSONObject.toJSONString(listJSON), SaleGoodsDTO.class);

        handleSaleGoodsDTOS(list);

        return getCoustomDataTable(list, pageInfo.getTotal());
    }

    private void handleSaleGoodsDTOS(List<SaleGoodsDTO> list) {
        List<String> goodsCodes = list.stream().map(Goods::getGoodsCode).collect(Collectors.toList());
        List<String> productCodes = list.stream().map(Goods::getProductCode).collect(Collectors.toList());

        // 适配信息
        Map<String, List<Map<String, Object>>> adsVioMap = adsService.countByProductCodes(productCodes);
        Map<String, Integer> adaptCountMap = new HashMap<>(adsVioMap.size());
        adsVioMap.forEach((k, v) -> {
            if (CollUtil.isNotEmpty(v)) {
                Integer count = v.stream().mapToInt(m -> NumberUtil.parseInt(m.get("count").toString())).sum();
                adaptCountMap.put(k, count);
            }
        });

        Map<String, List<ThirdpartyFbmDTO>> thirdpartyInventoryBaseMap = new HashMap<>();
        Map<String, List<GoodsPart>> partMap = new HashMap<>();
        Map<String, GoodsSpecifications> specMap = new HashMap<>();
        Map<String, List<GoodsImage>> imageMap = new HashMap<>();
        if (CollUtil.isNotEmpty(goodsCodes)) {
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectStockShareAndPartGoodsCode(goodsCodes, "US", false);
            if (CollUtil.isNotEmpty(thirdpartyFbmDTOList)) {
                thirdpartyInventoryBaseMap = thirdpartyFbmDTOList.stream().collect(Collectors.groupingBy(ThirdpartyFbmDTO::getSku));
            }
            GetGoodsDetailQueryDTO partQuery = new GetGoodsDetailQueryDTO();
//            partQuery.setModels(Collections.singletonList("PartList"));
            partQuery.setModels(Lists.newArrayList("GoodsSpecifications","PartList","GoodsImage"));
            partQuery.setGoodsCodes(goodsCodes);
            List<SaleGoodsDTO> goodsDTOList = PDMHttpRequestBiz.listGoodsDetail(partQuery);
            if (ObjectUtils.isNotEmpty(goodsDTOList)) {
                goodsDTOList.forEach(goodsDTO -> {
                    List<GoodsPart> partList = goodsDTO.getPartList();
                    partMap.put(goodsDTO.getGoodsCode(), partList);
                    GoodsSpecifications specifications = goodsDTO.getSpecifications();
                    specMap.put(goodsDTO.getGoodsCode(), specifications);
                    List<GoodsImage> goodsImageList = goodsDTO.getGoodsImageList();
                    imageMap.put(goodsDTO.getGoodsCode(), goodsImageList);
                });
            }

        }
        //退货标签信息
        List<SkuRefundRatePj> skuRefundRatePjList = skuRefundRatePjService.selectSkuRefundRatePjByGoodsCodes(goodsCodes);
        if (CollUtil.isNotEmpty(skuRefundRatePjList)) {
            // 过滤掉getRefundRateLabel为null
            skuRefundRatePjList = skuRefundRatePjList.stream().filter(f -> StrUtil.isNotBlank(f.getRefundRateLabel())).collect(Collectors.toList());
        }
        Map<String, List<SkuRefundRatePj>> refundRateMap = skuRefundRatePjList.stream().collect(Collectors.groupingBy(SkuRefundRatePj::getSku));

        List<String> allShareSKuList = new ArrayList<>();

        for (SaleGoodsDTO saleGoods : list) {
//            saleGoods.setStockWhSkuList(stringListMap.get(saleGoods.getGoodsCode()));
            Integer count = adaptCountMap.get(saleGoods.getProductCode());
            saleGoods.setSmcAdaptFlag(count != null && count > 0 ? Constants.YesOrNo.YES : Constants.YesOrNo.NO);
            // 实际库存 -> 所有站点 总库存
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOS = thirdpartyInventoryBaseMap.get(saleGoods.getGoodsCode());
            if (CollUtil.isNotEmpty(thirdpartyFbmDTOS)) {
                Integer availableQty = thirdpartyFbmDTOS.stream().mapToInt(ThirdpartyFbmDTO::getSellableQty).sum();
                saleGoods.setStockOnSalesQty(availableQty);
            }
            //零件信息
            if (ObjectUtils.isNotEmpty(partMap)) {
                saleGoods.setPartList(partMap.get(saleGoods.getGoodsCode()));
            }
            if (ObjectUtils.isNotEmpty(specMap)){
                saleGoods.setSpecifications(specMap.get(saleGoods.getGoodsCode()));
            }
            if (ObjectUtils.isNotEmpty(imageMap)){
                saleGoods.setGoodsImageList(imageMap.get(saleGoods.getGoodsCode()));
            }

            // 退货率标签
            List<SkuRefundRatePj> skuRefundRatePjs = refundRateMap.get(saleGoods.getGoodsCode());
            if (CollUtil.isNotEmpty(skuRefundRatePjs)) {
                Map<String, List<String>> refundRateLabelMap = new HashMap<>();
                saleGoods.setRefundRateLabelMap(refundRateLabelMap);

                for (SkuRefundRatePj skuRefundRatePj : skuRefundRatePjs) {
                    String dictCode = RefundRateLabelEnum.getDictCode(skuRefundRatePj.getPlatform(), "sku", skuRefundRatePj.getRefundRateLabel());
                    refundRateLabelMap.computeIfAbsent(skuRefundRatePj.getPlatform(), k -> new ArrayList<>()).add(dictCode);
                }
            }
            //共享库存
            List<String> shareInventroySkuList = new ArrayList<>();
            if (ObjUtil.isNotEmpty(saleGoods.getMainGoodsCode())) {
                shareInventroySkuList.add(saleGoods.getGoodsCode());
            }
            if (ObjUtil.isNotEmpty(saleGoods.getSubGoodsCodes())) {
                shareInventroySkuList.addAll(saleGoods.getSubGoodsCodes());
            }
            if (CollUtil.isNotEmpty(shareInventroySkuList)) {
                //去重
                shareInventroySkuList = shareInventroySkuList.stream().distinct().collect(Collectors.toList());
                saleGoods.setShareInventroySkuList(shareInventroySkuList);

                allShareSKuList.addAll(shareInventroySkuList);
            }
        }

        if (CollUtil.isNotEmpty(allShareSKuList)) {
            List<ThirdpartyFbmDTO> stocks = inventoryBiz.getFbmStock(allShareSKuList, WarehouseExcludeEnum.getExcludeWarehouseCodeList(), false);
            Map<String, Integer> stockByWareHouseCode = stocks.stream()
                    .filter(s -> Objects.equals(s.getWhCountry(), PlatformSiteEnum.AM_US.getSiteCodeType()))
                    .collect(Collectors.groupingBy(ThirdpartyFbmDTO::getSku, Collectors.summingInt(ThirdpartyFbmDTO::getSellableQty)));
            list.forEach(saleGoods -> {
                if (CollUtil.isNotEmpty(saleGoods.getShareInventroySkuList())) {
                    Map<String, Integer> stockMap = new HashMap<>();
                    saleGoods.setShareInventroySkuMap(stockMap);
                    saleGoods.getShareInventroySkuList().forEach(sku -> {
                        Integer stock = stockByWareHouseCode.get(sku);
                        stockMap.put(sku, stock == null ? 0 : stock);
                    });
                }
            });
        }

        // 链接信息，加入权限过滤
        if(!ShiroUtils.getSysUser().isAdmin()) {
            //通过userId获取用户的店铺权限
            Long userId = ShiroUtils.getUserId();
            List<String> shopCode = shopService.getShopCodeByUserId(userId);

            for (SaleGoodsDTO saleGoodsDTO : list) {
                List<SaleGoodsDTO.OnlineListing> countOnlineListing = saleGoodsDTO.getCountOnlineListing();
                if(CollUtil.isEmpty(countOnlineListing)) {
                    saleGoodsDTO.setOnSaleLink(0);
                    continue;
                }
                if (CollUtil.isEmpty(shopCode)) {
                    saleGoodsDTO.setOnSaleLink(0);
                    saleGoodsDTO.setCountOnlineListing(null);
                    continue;
                }

                countOnlineListing = countOnlineListing.stream().filter(onlineListing -> shopCode.contains(onlineListing.getShopCode())).collect(Collectors.toList());
                saleGoodsDTO.setCountOnlineListing(countOnlineListing);

                int count = countOnlineListing.stream().filter(e -> StrUtil.isNotBlank(e.getOnlineCount())).mapToInt(e -> Integer.parseInt(e.getOnlineCount())).sum();
                saleGoodsDTO.setOnSaleLink(count);
            }
        }
     }

    /**
     * 导出商品详情
     */
    @Log(title = "商品详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(PageQueryGoodsDTO saleGoodsDTO) {
        String ids = saleGoodsDTO.getIds();
        List<Long> saleGoodIds= new ArrayList<>();
        if (ObjUtil.isNotEmpty(ids)){
            saleGoodIds = CommonUtilsSmc.toLongList(ids);
        }
        if (saleGoodIds.size()>7000){
            throw new BusinessException("导出数据不能超过7000条。如需大批量导出,请联系管理员！");
        }
        saleGoodsDTO.setGoodsIdList(saleGoodIds);
        List<SaleGoodsDTO> list = null;

        // 处理业务分类筛选
        if (CollUtil.isNotEmpty(saleGoodsDTO.getOperationClassificationList())) {
            List<String> goodsCodesByClassification = cateProductService.getGoodsCodesByOperationClassificationList(saleGoodsDTO.getOperationClassificationList());
            if (CollUtil.isEmpty(goodsCodesByClassification)) {
                // 如果业务分类筛选后没有数据，返回空的导出结果
                ExcelUtil<SaleGoodsDTO> util = new ExcelUtil<>(SaleGoodsDTO.class);
                util.hideColumn("productCode", "sellingArea", "partType");
                return util.exportExcel(new ArrayList<>(), "商品管理");
            }

            // 如果已经有goodsCode筛选条件，需要取交集
            if (StringUtils.isNotBlank(saleGoodsDTO.getGoodsCode())) {
                List<String> existingGoodsCodes = Arrays.asList(saleGoodsDTO.getGoodsCode().split("\\s+"));
                goodsCodesByClassification = goodsCodesByClassification.stream()
                        .filter(existingGoodsCodes::contains)
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(goodsCodesByClassification)) {
                    // 如果交集后没有数据，返回空的导出结果
                    ExcelUtil<SaleGoodsDTO> util = new ExcelUtil<>(SaleGoodsDTO.class);
                    util.hideColumn("productCode", "sellingArea", "partType");
                    return util.exportExcel(new ArrayList<>(), "商品管理");
                }
                // 设置筛选后的goodsCode（取交集结果）
                saleGoodsDTO.setGoodsCode(String.join(" ", goodsCodesByClassification));
            } else {
                // 如果没有手动输入SKU，则使用业务分类查询出的goodsCode列表
                saleGoodsDTO.setGoodsCode(String.join(" ", goodsCodesByClassification));
            }
        }

        // 搜索条件导出
        saleGoodsDTO.setPageNum(1);
        saleGoodsDTO.setPageSize(2000);
        PageInfo<SaleGoodsDTO> pageInfo = PDMHttpRequestBiz.pageQueryGoodsDetailV2(saleGoodsDTO);
        List<SaleGoodsDTO> saleGoodsDTOS = pageInfo.getList();
        if (CollUtil.isNotEmpty(saleGoodsDTOS)) {
            if (pageInfo.getTotal() > 7000) {
                throw new BusinessException("导出数据不能超过7000条。如需大批量导出,请联系管理员！");
            }

            List<SaleGoodsDTO> dataList = JSONObject.parseArray(JSONObject.toJSONString(saleGoodsDTOS), SaleGoodsDTO.class);
            list = new ArrayList<>((int) pageInfo.getTotal());
            list.addAll(dataList);

            int pages = pageInfo.getPages();
            for (int i = 2; i <= pages; i++) {
                saleGoodsDTO.setPageNum(i);
                PageInfo<SaleGoodsDTO> page = PDMHttpRequestBiz.pageQueryGoodsDetailV2(saleGoodsDTO);
                saleGoodsDTOS = page.getList();
                if (CollUtil.isNotEmpty(saleGoodsDTOS)) {
                    dataList = JSONObject.parseArray(JSONObject.toJSONString(saleGoodsDTOS), SaleGoodsDTO.class);
                    list.addAll(dataList);
                }
            }
        }
        if (CollUtil.isNotEmpty(list)) {
            handleExportData(list);
        }
        ExcelUtil<SaleGoodsDTO> util = new ExcelUtil<>(SaleGoodsDTO.class);
        util.hideColumn("productCode", "sellingArea", "partType");
        return util.exportExcel(list, "商品管理");
    }

    /**
     * 处理导出数据
     * @param saleGoodsDTOS
     */
    private void handleExportData(List<SaleGoodsDTO> saleGoodsDTOS) {
        List<List<SaleGoodsDTO>> partition = Lists.partition(saleGoodsDTOS, 2000);
        List<KeyValueEntity> productAllKVList = baseConfigService.getCateProductAllKVList();
        Map<String, String> productMap = productAllKVList.stream().collect(Collectors.toMap(KeyValueEntity::getKey, KeyValueEntity::getValue));

        for (List<SaleGoodsDTO> list : partition) {
            List<String> goodsCodes = list.stream().map(SaleGoodsDTO::getGoodsCode).distinct().collect(Collectors.toList());

            List<SkuRefundRatePj> refundRatePjList = skuRefundRatePjService.selectSkuRefundRatePjByGoodsCodes(goodsCodes);
            Map<String, List<SkuRefundRatePj>> refundRatePjMap = refundRatePjList.stream().collect(Collectors.groupingBy(SkuRefundRatePj::getSku));

            handleSaleGoodsDTOS(list);

            list.forEach(good->{
                //销售状态
                buildSalesStatus(good);
                //商品类别
                good.setProductCategoryCode(productMap.containsKey(good.getProductCategoryCode()) ? productMap.get(good.getProductCategoryCode()) : good.getProductCategoryCode());
                //商品类型
                buildPartType(good);
                //共享库存
                buildShareStock(good);
                //虚拟套件
                buildPartList(good);
                // 是否需适配
                buildAdapt(good);
                good.setWhiteBackImage(good.getIsExistWhiteBackImage() != null && good.getIsExistWhiteBackImage()? "是" : "否");

                //退货标签
                if (refundRatePjMap.containsKey(good.getGoodsCode())) {
                    List<SkuRefundRatePj> skuRefundRatePjs = refundRatePjMap.get(good.getGoodsCode());
                    StringBuilder sb = new StringBuilder();
                    if (CollUtil.isNotEmpty(skuRefundRatePjs)) {
                        for (SkuRefundRatePj skuRefundRatePj : skuRefundRatePjs) {
                            if (sb.length() > 0) {
                                sb.append(",");
                            }
                            sb.append(skuRefundRatePj.getPlatform() + ":" + skuRefundRatePj.getRefundRateLabel() + "退货率");
                        }
                    }
                    good.setRefundRateLabel(sb.toString());
                }
            });
        }
    }

    private static void buildShareStock(SaleGoodsDTO good) {
        if(MapUtil.isNotEmpty(good.getShareInventroySkuMap())) {
            StringBuilder stringBuilder = new StringBuilder();
            good.getShareInventroySkuMap().forEach((k, v) -> {
                stringBuilder.append(k).append(":").append(v).append("\n");
            });
            good.setExportshareInventroySkuList(stringBuilder.toString());
        }
    }

    private void buildAdapt(SaleGoodsDTO good) {
        if (Objects.equals(good.getSmcAdaptFlag(), Constants.YesOrNo.YES)) {
            good.setAdaptFlag("已适配");
        } else {
            if ("Y".equalsIgnoreCase(good.getAdaptFlag()) ){
                good.setAdaptFlag("未适配");
            }else if ("N".equalsIgnoreCase(good.getAdaptFlag())){
                good.setAdaptFlag("无需适配");
            }else {
                good.setAdaptFlag("");
            }
        }
    }

    private static void buildPartList(SaleGoodsDTO good) {
        List<GoodsPart> partList = good.getPartList();
        if (CollUtil.isNotEmpty(partList)){
            StringBuilder sb = new StringBuilder();
            partList.forEach(part -> {
                sb.append(part.getPartSku()).append("*").append(part.getQuantity()).append("\n");
            });
            good.setExportPartList(sb.toString());
        }
    }

    private static void buildPartType(SaleGoodsDTO good) {
        if (Objects.equals(good.getPartType(), "1")) {
            good.setPartType("单品");
        } else if (Objects.equals(good.getPartType(), "2")) {
            good.setPartType("实物套件");
        } else if (Objects.equals(good.getPartType(), "3")) {
            good.setPartType("虚拟套件");
        }
    }

    private static void buildSalesStatus(SaleGoodsDTO good) {
        if (Objects.equals(good.getSalesStatus(), "1")) {
            good.setSalesStatus("在售");
        } else if (Objects.equals(good.getSalesStatus(), "2")) {
            good.setSalesStatus("停售");
        } else if (Objects.equals(good.getSalesStatus(), "3")) {
            good.setSalesStatus("清库");
        } else if (Objects.equals(good.getSalesStatus(), "4")) {
            good.setSalesStatus("维稳");
        } else if (Objects.equals(good.getSalesStatus(), "5")) {
            good.setSalesStatus("开发中");
        } else if (Objects.equals(good.getSalesStatus(), "6")) {
            good.setSalesStatus("交付中");
        } else if (Objects.equals(good.getSalesStatus(), "7")) {
            good.setSalesStatus("风险");
        } else if (Objects.equals(good.getSalesStatus(), "8")) {
            good.setSalesStatus("无状态");
        } else if (Objects.equals(good.getSalesStatus(), "9")) {
            good.setSalesStatus("淘汰");
        } else {
            good.setSalesStatus("");
        }
    }

    /**
     * 查看商品
     *
     * @param id
     * @param mmap
     * @return
     */
    @GetMapping("/watch/{id}")
    public String watchGoods(@PathVariable("id") Long id, ModelMap mmap) {
        GoodsDetailDTO goods = goodsBiz.selectGoodsById(id);
        goods.setGoodsFunctionInfo(goodsFunctionInfoService.selectGoodsFunctionInfoByGoodsId(goods.getId()));
        goods.setGoodsImageList(goodsImageService.selectGoodsImageByGoodsId(goods.getId(), null,null));
//        goods.setGoodsPriceList(goodsPriceService.selectGoodsPriceByGoodsId(goods.getId(), null));
        goods.setGoodsBiPrice(goodsBiPriceService.selectGoodsBiPriceByGoodsCode(goods.getGoodsCode()));
        goods.setGoodsPaperWorkList(goodsPaperWorkService.selectGoodsPaperWorkByGoodsId(goods.getId(), null));
        goods.setStockWhSkuList(stockWhSkuService.selectStockWhSkuBySku(goods.getGoodsCode()));
        goods.setSellingPoint(sellingPointService.selectSellingPointByProductCategoryCodeAndType(goods.getProductCategoryCode(), "1"));
        goods.setEnSellingPoint(sellingPointService.selectSellingPointByProductCategoryCodeAndType(goods.getProductCategoryCode(), "2"));
        mmap.put("goods", goods);
        List<Map<String, String>> imageTypes = ImageType.getAll();
        mmap.put("imageTypes",imageTypes);
        return prefix + "/watch";
    }


    @GetMapping("/watchMappingGoodsList")
    public String selectMappingGoodsList(MappingGoods mappingGoods,ModelMap mmap) {
        mmap.put("mappingGoods", mappingGoods);
        return prefix + "/selectMappingGood";
    }


    @PostMapping("/selectMappingGoods")
    @ResponseBody
    public TableDataInfo selectMappingGoods(MappingGoods mappingGoods) {
        if (StringUtils.isEmpty(mappingGoods.getGoodsCode()) || StringUtils.isEmpty(mappingGoods.getShopCode())) {
            return getDataTable(new ArrayList<>());
        }
        Long goodsId = mappingGoods.getGoodsId();
        mappingGoods.setGoodsId(null);
        List<MappingGoods> allMappingGoodsList = mappingGoodsService.selectMappingGoodsList(mappingGoods);
        if (CollectionUtils.isEmpty(allMappingGoodsList)) {
            return getDataTable(new ArrayList<>());
        }
        // asin未使用且平台sku不为空
        List<MappingGoods> mappingGoodsList = allMappingGoodsList.stream().filter(f -> StringUtils.isEmpty(f.getAsin()) && StringUtils.isNotBlank(f.getPlatformSku())).collect(Collectors.toList());

        // goodsId不为空，是listing管理进行批量更新平台SKU，需要判断是否是跟卖VCPO
        if (goodsId != null) {
            GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(goodsId.intValue());
            String followVcPoAsin = goodsHeadService.getFollowVcPoAsin(goodsHead);
            if (StringUtils.isNotBlank(followVcPoAsin)) {
                List<MappingGoods> goodsList = allMappingGoodsList.stream().filter(f -> StringUtils.isNotBlank(f.getPlatformSku())
                        && StringUtils.isNotBlank(f.getAsin()) && f.getAsin().equals(followVcPoAsin)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    mappingGoodsList.addAll(goodsList);
                }
            }
        }

        return getDataTable(mappingGoodsList);
    }

    @GetMapping("/batchAddPublishAmazon")
    @RequiresPermissions("publication:goods:batchAddPublishAmazon")
    public String batchAddPublishAmazon(String saleGoodIds, String platformType, String data, ModelMap mmap) {
        if (Objects.isNull(saleGoodIds)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        Long[] saleGoodIdsArr = Arrays.stream(saleGoodIds.split(",")).map(Long::valueOf).toArray(Long[]::new);
        if (Objects.isNull(saleGoodIds) || StringUtils.isEmpty(platformType)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            SaleGoodsVO saleGoodsVO = listingServiceByPlatformType.getSaleGoodsDTOListByGoodsIds(Arrays.stream(saleGoodIdsArr).collect(Collectors.toList()), false);
            if (ObjUtil.isNotEmpty(data)) {
                List<SaleGoodsDTO> saleGoodsDTOList = saleGoodsVO.getSaleGoodsDTOList();
                List<SaleGoodsDTO> saleGoodsDTOListClone = new ArrayList<>();
                //对data进行解析成map
                Map<String, Integer> map = Arrays.stream(data.split(",")).collect(Collectors.toMap(s -> s.split("=")[0], s -> Integer.valueOf(s.split("=")[1])));
                map.forEach((k, v) -> {
                    //saleGoodsDTOList getGoodsCode中与k相等的对象,复制v份放回saleGoodsDTOList
                    SaleGoodsDTO target = saleGoodsDTOList.stream().filter(s -> s.getGoodsCode().equals(k)).findFirst().orElse(null);
                    if (ObjUtil.isEmpty(target)) {
                        return;
                    }
                    for (int i = 0; i < v; i++) {
                        SaleGoodsDTO dto = new SaleGoodsDTO();
                        BeanUtils.copyProperties(target, dto);
                        dto.setGoodsCode(target.getGoodsCode() + "(" + (i + 1) + ")");
                        saleGoodsDTOListClone.add(dto);
                    }
                });
                if (CollUtil.isNotEmpty(saleGoodsDTOListClone)) {
                    saleGoodsDTOListClone.sort(Comparator.comparing(SaleGoodsDTO::getGoodsCode));
                    saleGoodsVO.setSaleGoodsDTOList(saleGoodsDTOListClone);
                }

            }
            mmap.put("saleGoodsVO", saleGoodsVO);
        } catch (Exception e) {
            log.error("批量刊登获取商品信息失败,platformType:{}",platformType, e);
            throw new RuntimeException(e);
        }
        if(PlatformTypeEnum.AM.name().equals(platformType)){
            return prefix + "/am_batch_add_publish";
        }else {
            return prefix + "/ebay_batch_add_publish";
        }

    }


    @GetMapping("/v2/batchAddPublishAmazon")
    @RequiresPermissions("publication:goods:batchAddPublishAmazon")
    public String batchAddPublishAmazonV2(String saleGoodIds, String platformType, String data, String type, ModelMap mmap) {
        if (Objects.isNull(saleGoodIds)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        Long[] saleGoodIdsArr = Arrays.stream(saleGoodIds.split(",")).map(Long::valueOf).toArray(Long[]::new);
        if (Objects.isNull(saleGoodIds) || StringUtils.isEmpty(platformType)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        if(!PlatformTypeEnum.AM.name().equals(platformType)) {
            throw new RuntimeException("请选择亚马逊平台进行刊登.");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            SaleGoodsVO saleGoodsVO = listingServiceByPlatformType.getSaleGoodsDTOListByGoodsIds(Arrays.stream(saleGoodIdsArr).collect(Collectors.toList()), true);
            if (ObjUtil.isNotEmpty(data)) {
                List<SaleGoodsDTO> saleGoodsDTOList = saleGoodsVO.getSaleGoodsDTOList();
                ExceptionUtil.sandbox(()->setAutoPartPositionMap(saleGoodsDTOList));
                List<SaleGoodsDTO> saleGoodsDTOListClone = new ArrayList<>();
                //对data进行解析成map
                Map<String, Integer> map = Arrays.stream(data.split(",")).collect(Collectors.toMap(s -> s.split("=")[0], s -> Integer.valueOf(s.split("=")[1])));
                map.forEach((k, v) -> {
                    //saleGoodsDTOList getGoodsCode中与k相等的对象,复制v份放回saleGoodsDTOList
                    SaleGoodsDTO target = saleGoodsDTOList.stream().filter(s -> s.getGoodsCode().equals(k)).findFirst().orElse(null);
                    if (ObjUtil.isEmpty(target)) {
                        return;
                    }
                    for (int i = 0; i < v; i++) {
                        SaleGoodsDTO dto = new SaleGoodsDTO();
                        BeanUtils.copyProperties(target, dto);
                        dto.setGoodsCode(target.getGoodsCode() + "(" + (i + 1) + ")");
                        saleGoodsDTOListClone.add(dto);
                    }
                });
                if (CollUtil.isNotEmpty(saleGoodsDTOListClone)) {
                    saleGoodsDTOListClone.sort(Comparator.comparing(SaleGoodsDTO::getGoodsCode));
                    saleGoodsVO.setSaleGoodsDTOList(saleGoodsDTOListClone);
                }

            }
            mmap.put("saleGoodsVO", saleGoodsVO);
            mmap.put("type", type);
        } catch (Exception e) {
            log.error("批量刊登获取商品信息失败,platformType:{}",platformType, e);
            throw new RuntimeException(e);
        }
        return prefix + "/v2/am_batch_add_publish";
    }

    private void setAutoPartPositionMap(List<SaleGoodsDTO> saleGoodsDTOList) {
        Map<String, String> autoPartPositionMap = new HashMap<>();
        saleGoodsDTOList.forEach(saleGoodsDTO -> {
            if (StringUtils.isNotBlank(saleGoodsDTO.getGoodsCode())) {
                saleGoodsDTO.setAutoPartPosition(getAutoPartPosition(saleGoodsDTO.getProductCode(), autoPartPositionMap));
            }
        });
    }

    @GetMapping("/batchAddPublishAmazonByGoodCode")
    public String batchAddPublishAmazonByGoodCode(String goodsCodes,String version, ModelMap mmap) {
        if (Objects.isNull(goodsCodes)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        mmap.put("goodsCodes", goodsCodes);
        mmap.put("version", version);
        return prefix + "/am_batch_confirm_amount";
    }


    @GetMapping("/batchAddPublishEbay")
    @RequiresPermissions("publication:goods:batchAddPublishEbay")
    public String batchAddPublishEbay(String saleGoodIds,String platformType,ModelMap mmap) {
        if (Objects.isNull(saleGoodIds)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        Long[] saleGoodIdsArr = Arrays.stream(saleGoodIds.split(",")).map(Long::valueOf).toArray(Long[]::new);
        if (Objects.isNull(saleGoodIdsArr) || StringUtils.isEmpty(platformType)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            SaleGoodsVO saleGoodsVO = listingServiceByPlatformType.getSaleGoodsDTOListByGoodsIds(Arrays.stream(saleGoodIdsArr).collect(Collectors.toList()), false);
            mmap.put("saleGoodsVO", saleGoodsVO);
        } catch (Exception e) {
            log.error("批量刊登获取商品信息失败,platformType:{}",platformType, e);
            throw new RuntimeException(e);
        }
        if(PlatformTypeEnum.AM.name().equals(platformType)){
            return prefix + "/am_batch_add_publish";
        }else {
            return prefix + "/ebay_batch_add_publish";
        }

    }

    @GetMapping("/batchAddPublishEbayAI")
    @RequiresPermissions("publication:goods:batchAddPublishEbay")
    public String batchAddPublishEbayAI(String saleGoodIds,String platformType,ModelMap mmap) {
        if (Objects.isNull(saleGoodIds)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        Long[] saleGoodIdsArr = Arrays.stream(saleGoodIds.split(",")).map(Long::valueOf).toArray(Long[]::new);
        if (Objects.isNull(saleGoodIdsArr) || StringUtils.isEmpty(platformType)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            SaleGoodsVO saleGoodsVO = listingServiceByPlatformType.getSaleGoodsDTOListByGoodsIds(Arrays.stream(saleGoodIdsArr).collect(Collectors.toList()), false);
            mmap.put("saleGoodsVO", saleGoodsVO);
        } catch (Exception e) {
            log.error("批量刊登获取商品信息失败,platformType:{}",platformType, e);
            throw new RuntimeException(e);
        }
        return prefix + "/ebay_batch_add_publish_ai";
    }

    /**
     * Ebay商品标题生成API接口（异步模式）
     *
     * @param requestDTO 标题生成请求参数
     * @return 任务提交结果
     */
    @PostMapping("/generateEbayTitles")
    @ResponseBody
    public AjaxResult generateEbayTitles(@RequestBody com.suncent.smc.provider.biz.publication.dto.EbayTitleGenerationRequestDTO requestDTO) {
        try {
            log.info("用户{}调用Ebay标题生成API（异步模式）", ShiroUtils.getUserId());

            // 参数校验
            if (requestDTO == null) {
                return AjaxResult.error("请求参数不能为空");
            }

            if (requestDTO.getGoods() == null || requestDTO.getGoods().isEmpty()) {
                return AjaxResult.error("商品列表不能为空");
            }
            // 后端生成taskCorrelationId
            String taskCorrelationId = "title_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);

            // 后端构建回调地址
            String callbackUrl = buildCallbackUrl();

            // 设置到请求DTO中
            requestDTO.setTaskCorrelationId(taskCorrelationId);
            requestDTO.setCallbackUrl(callbackUrl);

            log.info("生成任务标识：{}, 回调地址：{}", taskCorrelationId, callbackUrl);

            // 标记任务为处理中
            String taskKey = "ai_title_task:" + taskCorrelationId;
            redisTemplate.opsForValue().set(taskKey, "processing", 15, TimeUnit.MINUTES);

            AjaxResult ajaxResult = ebayContentGenerationHttpRequestBiz.generateTitles(requestDTO);
            if (!ajaxResult.isSuccess()) {
                return ajaxResult;
            }else {
                Object resultData = ajaxResult.get("data");
                if (resultData instanceof EbayTitleGenerationResponseDTO) {
                    EbayTitleGenerationResponseDTO responseDTO = (EbayTitleGenerationResponseDTO) resultData;
                    if (MapUtil.isNotEmpty(responseDTO.getData())) {
                        // 存储结果到Redis，设置30分钟过期时间
                        // 提取result中的data字段，保持与原有接口返回格式一致
                        String redisKey = "ai_title_result:" + taskCorrelationId;
                        redisTemplate.opsForValue().set(redisKey, responseDTO.getData(), 30, TimeUnit.MINUTES);

                        redisTemplate.opsForValue().set(taskKey, "completed", 30, TimeUnit.MINUTES);
                    }
                }
            }

            // 返回taskCorrelationId给前端
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("taskCorrelationId", taskCorrelationId);
            responseData.put("message", "任务已提交，正在处理中");
            return AjaxResult.success(responseData);
        } catch (Exception e) {
            log.error("提交标题生成任务异常", e);
            return AjaxResult.error("提交任务失败: " + e.getMessage());
        }
    }

    /**
     * 构建回调地址
     */
    private String buildCallbackUrl() {
        // 从配置中获取当前服务的域名和端口
        String serverUrl = getServerUrl();
        return serverUrl + "/smc/api/titleGenerationCallback";
    }

    /**
     * 获取服务器URL（从配置中读取）
     */
    private String getServerUrl() {
        return configService.selectConfigByKey("ai.callback.base-url");
    }
    /**
     * 获取AI标题生成结果
     *
     * @param taskCorrelationId 任务关联标识符
     * @return 生成结果
     */
    @GetMapping("/getTitleGenerationResult")
    @ResponseBody
    public AjaxResult getTitleGenerationResult(String taskCorrelationId) {
        try {
            if (StringUtils.isEmpty(taskCorrelationId)) {
                return AjaxResult.success().put("msg", "taskCorrelationId不能为空");
            }

            // 检查任务状态
            String taskKey = "ai_title_task:" + taskCorrelationId;
            String status = (String) redisTemplate.opsForValue().get(taskKey);

            if ("completed".equals(status)) {
                // 任务完成，返回结果
                String resultKey = "ai_title_result:" + taskCorrelationId;
                Object result = redisTemplate.opsForValue().get(resultKey);

                if (result != null) {
                    // 获取结果后清理数据
                    redisTemplate.delete(taskKey);
                    redisTemplate.delete(resultKey);
                    return AjaxResult.success(result).put("clearStorage", true).put("status", "completed");
                } else {
                    return AjaxResult.error( "任务已完成但结果丢失，请重新生成");
                }

            } else if ("failed".equals(status)) {
                // 任务失败，返回详细错误信息
                String errorKey = "ai_title_error:" + taskCorrelationId;
                String errorMsg = (String) redisTemplate.opsForValue().get(errorKey);

                // 清理失败任务数据
                redisTemplate.delete(taskKey);
                redisTemplate.delete(errorKey);

                return AjaxResult.error("处理失败：" + (errorMsg != null ? errorMsg : "未知错误，请重新生成"));

            } else if ("processing".equals(status)) {
                return AjaxResult.success().put("status", "processing");
            } else {
                // 任务不存在或状态异常
                return AjaxResult.error("任务不存在或已过期，请重新生成");
            }

        } catch (Exception e) {
            log.error("获取任务结果失败", e);
            return AjaxResult.error("获取结果失败: " + e.getMessage());
        }
    }

    /**
     * Ebay商品描述生成API接口（异步模式）
     *
     * @param requestDTO 描述生成请求参数
     * @return 任务提交结果
     */
    @PostMapping("/generateEbayDescriptions")
    @ResponseBody
    public AjaxResult generateEbayDescriptions(@RequestBody com.suncent.smc.provider.biz.publication.dto.EbayDescriptionGenerationRequestDTO requestDTO) {
        try {
            log.info("用户{}调用Ebay描述生成API（异步模式）", ShiroUtils.getUserId());

            // 参数校验
            if (requestDTO == null) {
                return AjaxResult.error("请求参数不能为空");
            }

            if (requestDTO.getGoods() == null || requestDTO.getGoods().isEmpty()) {
                return AjaxResult.error("商品列表不能为空");
            }

            // 店铺是否配置了短描述
            ConfigStoreShortDescription configStoreShortDescription = shortDescriptionApiService.getByShopCode(requestDTO.getShop());
            if (configStoreShortDescription == null) {
                return AjaxResult.error("店铺" + requestDTO.getShop() + "未配置短描述，请先配置短描述");
            }

            // 后端生成taskCorrelationId
            String taskCorrelationId = "desc_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);

            // 后端构建回调地址
            String callbackUrl = buildCallbackUrl().replace("titleGenerationCallback", "descriptionGenerationCallback");

            // 设置到请求DTO中
            requestDTO.setTaskCorrelationId(taskCorrelationId);
            requestDTO.setCallbackUrl(callbackUrl);

            log.info("生成描述任务标识：{}, 回调地址：{}", taskCorrelationId, callbackUrl);

            // 标记任务为处理中
            String taskKey = "ai_desc_task:" + taskCorrelationId;
            redisTemplate.opsForValue().set(taskKey, "processing", 15, TimeUnit.MINUTES);

            AjaxResult ajaxResult = ebayContentGenerationHttpRequestBiz.generateDescriptions(requestDTO);
            if (!ajaxResult.isSuccess()) {
                return ajaxResult;
            } else {
                String redisKey = "ai_desc_result:" + taskCorrelationId;
                Object resultData = ajaxResult.get("data");
                if (resultData instanceof EbayDescriptionGenerationResponseDTO) {
                    EbayDescriptionGenerationResponseDTO responseDTO = (EbayDescriptionGenerationResponseDTO) resultData;
                    if (MapUtil.isNotEmpty(responseDTO.getData())) {
                        redisTemplate.opsForValue().set(redisKey, responseDTO.getData(), 30, TimeUnit.MINUTES);

                        redisTemplate.opsForValue().set(taskKey, "completed", 30, TimeUnit.MINUTES);
                    }
                }
            }


            // 返回taskCorrelationId给前端
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("taskCorrelationId", taskCorrelationId);
            responseData.put("message", "描述生成任务已提交，正在处理中");
            return AjaxResult.success(responseData);

        } catch (Exception e) {
            log.error("提交描述生成任务异常", e);
            return AjaxResult.error("提交任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI描述生成结果
     *
     * @param taskCorrelationId 任务关联标识符
     * @return 生成结果
     */
    @GetMapping("/getDescriptionGenerationResult")
    @ResponseBody
    public AjaxResult getDescriptionGenerationResult(String taskCorrelationId) {
        try {
            if (StringUtils.isEmpty(taskCorrelationId)) {
                return AjaxResult.success().put("msg", "taskCorrelationId不能为空");
            }

            // 检查任务状态
            String taskKey = "ai_desc_task:" + taskCorrelationId;
            String status = (String) redisTemplate.opsForValue().get(taskKey);

            if ("completed".equals(status)) {
                // 任务完成，返回结果
                String resultKey = "ai_desc_result:" + taskCorrelationId;
                Object result = redisTemplate.opsForValue().get(resultKey);

                if (result != null) {
                    // 获取结果后清理数据
                    redisTemplate.delete(taskKey);
                    redisTemplate.delete(resultKey);
                    return AjaxResult.success(result).put("clearStorage", true).put("status", "completed");
                } else {
                    return AjaxResult.error("任务已完成但结果丢失，请重新生成");
                }

            } else if ("failed".equals(status)) {
                // 任务失败，返回详细错误信息
                String errorKey = "ai_desc_error:" + taskCorrelationId;
                String errorMsg = (String) redisTemplate.opsForValue().get(errorKey);

                // 清理失败任务数据
                redisTemplate.delete(taskKey);
                redisTemplate.delete(errorKey);

                return AjaxResult.error("处理失败：" + (errorMsg != null ? errorMsg : "未知错误，请重新生成"));

            } else if ("processing".equals(status)) {
                return AjaxResult.success().put("msg", "任务仍在处理中，请稍候...").put("status", "processing");
            } else {
                // 任务不存在或状态异常
                return AjaxResult.error("任务不存在或已过期，请重新生成");
            }

        } catch (Exception e) {
            log.error("获取描述生成任务结果失败", e);
            return AjaxResult.error("获取结果失败: " + e.getMessage());
        }
    }

    @PostMapping("/batchAddPublish")
    @ResponseBody
    @RecordLog(operDesc = "批量新建Listing至草稿")
    public AjaxResult batchAddPublish(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行刊登");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            List<String> businessIds = listingServiceByPlatformType.saveBatchListingDTO(dto);
            return AjaxResult.success(businessIds);
        } catch (Exception e) {
            log.error("批量保存刊登商品信息失败,platformType:{}",platformType, e);
            return AjaxResult.error(e.getMessage());
        }
    }


    @PostMapping("/v2/batchAddPublish")
    @ResponseBody
    @RecordLog(operDesc = "批量新建Listing至草稿")
    public AjaxResult batchAddPublishV2(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行刊登");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            List<String> businessIds = listingServiceByPlatformType.saveBatchListingDTOV2(dto);
            return AjaxResult.success(businessIds);
        } catch (Exception e) {
            log.error("批量保存刊登商品信息失败,platformType:{}",platformType, e);
            return AjaxResult.error(e.getMessage());
        }
    }
    /**
     * 批量操作违禁词校验
     * @param
     * @Date 2023/9/1 11:24
     * @return
     * <AUTHOR>
     */
    @PostMapping("/checkViolateWord")
    @ResponseBody
    public AjaxResult checkViolateWord(BatchListingDTO dto) {
          return   goodsBiz.verificationWord(dto);

    }

    @PostMapping("/batchTempSaveListing")
    @ResponseBody
    public AjaxResult batchTempSaveListing(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行临时保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.saveBatchTempListingDTO(dto);
        } catch (Exception e) {
            log.error("批量保存刊登商品信息失败,platformType:{}",platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }



    @GetMapping("/autoCreateListing")
    @RequiresPermissions(value = { "publication:goods:autoCreateListingAmazon", "publication:goods:autoCreateListingEbay" }, logical = Logical.OR)
    public String autoCreateListing(String saleGoodIds,String platformType,ModelMap mmap) {
        if (Objects.isNull(saleGoodIds)) {
            throw new RuntimeException("请重新选择产品，进行批量刊登.");
        }
        Long[] saleGoodIdsArr = Arrays.stream(saleGoodIds.split(",")).map(Long::valueOf).toArray(Long[]::new);
        if (Objects.isNull(saleGoodIdsArr) || StringUtils.isEmpty(platformType)) {
            throw new RuntimeException("请重新选择产品，进行自动生成Listing.");
        }
        mmap.put("saleGoodIds", saleGoodIdsArr);
        mmap.put("platformType", platformType);
        mmap.put("siteCode", "US");
        if (PlatformTypeEnum.AM.name().equals(platformType)) {
            return prefix + "/bulkoperation/am_auto_create_listing";
        } else if (PlatformTypeEnum.EB.name().equals(platformType)) {
            return prefix + "/bulkoperation/eb_auto_create_listing";
        }
        return "error/404";
    }

    @PostMapping("/autoCreateListing")
    @ResponseBody
    public AjaxResult autoCreateListing(AutoCreateListingVO autoCreateListingVO) {
        if (Objects.isNull(autoCreateListingVO.getSaleGoodIds())) {
            throw new RuntimeException("请选择产品，进行自动生成Listing.");
        }
        if (Objects.isNull(autoCreateListingVO.getPlatformType())) {
            return AjaxResult.error("请选择平台后进行自动生成Listing");
        }
        if (Objects.isNull(autoCreateListingVO.getWordPartsModuleId())) {
            return AjaxResult.error("请选择文案组合配置进行自动生成Listing");
        }
        autoCreateListingVO.setUser(ShiroUtils.getSysUser());
        listingInfoService.autoCreateListing(autoCreateListingVO);
        return AjaxResult.success();
    }


    @GetMapping("/addEbayDistribution")
    @RequiresPermissions("publication:goods:addEbayDistribution")
    public String addEbayDistribution(ModelMap mmap) {
        SaleGoodsDTO goodsDto = new SaleGoodsDTO();
        ArrayList<PublicationDay> publicationDays = new ArrayList<>();
        PublicationDay publicationDay = new PublicationDay(0, "GTC", true);
        PublicationDay publicationDay1 = new PublicationDay(1, "1", false);
        PublicationDay publicationDay2 = new PublicationDay(2, "3", false);
        PublicationDay publicationDay3 = new PublicationDay(3, "5", false);
        PublicationDay publicationDay4 = new PublicationDay(4, "7", false);
        PublicationDay publicationDay5 = new PublicationDay(5, "10", false);
        publicationDays.add(publicationDay);
        publicationDays.add(publicationDay1);
        publicationDays.add(publicationDay2);
        publicationDays.add(publicationDay3);
        publicationDays.add(publicationDay4);
        publicationDays.add(publicationDay5);
        goodsDto.setPublicationDayList(publicationDays);

        mmap.put("ebGoodsInfo", goodsDto);
        return prefix + "/ebay_distribution_add_publish";
    }


    @GetMapping("/getALlImagesByGoodsId/{goodsId}/{brandCode}")
    public String getALlImagesByGoodsId(@PathVariable Long goodsId,@PathVariable String brandCode, ModelMap mmap) {
        SysUser sysUser = ShiroUtils.getSysUser();
        SaleGoodsDTO saleGoodsDTO = new SaleGoodsDTO();
        saleGoodsDTO.setId(goodsId);
        SaleGoodsDTO goodsDto = goodsService.getGoodsDtoByStock(saleGoodsDTO);

        List<GoodsImage> goodsImages = goodsImageService.selectGoodsImageByGoodsId(goodsId, null,brandCode);
        for (GoodsImage goodsImage : goodsImages) {
            goodsImage.setType(ImageType.getTypeNameByType(Integer.valueOf(goodsImage.getType())));
        }

        List<GoodsResource> resourceList = goodsResourceService.getOnlineAllResource(goodsDto.getGoodsCode(), PlatformTypeEnum.AM.name(),sysUser);
        if (ObjUtil.isNotEmpty(resourceList)){
            resourceList.stream().forEach(resource -> {
                GoodsImage goodsImage = new GoodsImage();
                goodsImage.setImageUrl(resource.getResourceUrl());
                goodsImage.setImageName(resource.getResourceName());
                goodsImage.setType(resource.getResourceType());
                goodsImages.add(goodsImage);
            });
        }
        List<String> types = goodsImages.stream().map(GoodsImage::getType).distinct().collect(Collectors.toList());
        mmap.put("images", goodsImages);
        mmap.put("imagesTypes", types);
        return prefix + "/property/image_list";
    }


    @PostMapping("/batchUpdateListing")
    @ResponseBody
    @RecordLog(isBackup = true,businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_EDIT,operDesc = "批量更新Lisitng信息:", taskRecord = true)
    public AjaxResult batchUpdateListingDTO(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.updateBatchListingDTO(dto);
        } catch (Exception e) {
            logger.error("批量更新刊登商品信息失败,platformType:{}",platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量更新属性
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdateAttribute")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_EDIT, operDesc = "批量更新Lisitng属性信息:", taskRecord = true)
    public AjaxResult batchUpdateAttribute(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdateAttribute(dto);
        } catch (Exception e) {
            logger.error("批量更新刊登商品信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量更新图片
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdatePictures")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_EDIT, operDesc = "批量更新图片信息:", taskRecord = true)
    public AjaxResult batchUpdatePictures(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdatePictures(dto);
        } catch (Exception e) {
            logger.error("批量更新刊登商品信息失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量更新五点描述
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdateFiveDescription")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_EDIT, operDesc = "批量更新五点描述信息:", taskRecord = true)
    public AjaxResult batchUpdateFiveDescription(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        if (!Objects.equals(platformType,PlatformTypeEnum.AM.name())){
            return AjaxResult.error("五点修改仅支持amazon");
        }
        try {
            amazonPlatformListingService.batchUpdateFiveDescription(dto);
        } catch (Exception e) {
            logger.error("批量更新Listing描述失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 批量更新描述
     *
     * @return 保存结果
     */
    @PostMapping("/batchUpdateDescription")
    @ResponseBody
    @RecordLog(isBackup = true, businessId = "#dto.listings.![id]", operType = OperTypeEnum.BATCH_EDIT, operDesc = "批量更新描述信息:", taskRecord = true)
    public AjaxResult batchUpdateDescription(BatchListingDTO dto) {
        String platformType = dto.getPlatform();
        if (Objects.isNull(platformType)) {
            return AjaxResult.error("请选择平台后进行保存");
        }
        try {
            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            listingServiceByPlatformType.batchUpdateDescription(dto);
        } catch (Exception e) {
            logger.error("批量更新Listing描述失败,platformType:{}", platformType, e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 导出商品适配保有量数据列表
     */
    @Log(title = "适配保有量数据列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAds")
    @ResponseBody
    public AjaxResult exportAds(PageQueryGoodsDTO queryGoodsDTO) {
        String selectColumn = queryGoodsDTO.getSelectColumn();
//        if (ObjUtil.isEmpty(selectColumn)) {
//            return AjaxResult.error("请选择组合分组规则");
//        }

        String rules = queryGoodsDTO.getRules();
        if (StrUtil.isBlank(rules)) {
            return AjaxResult.error("请选择适配导出字段");
        }

        List<Map<String, Object>> allColumns = new ArrayList<>();
        JSONArray rulesArray = JSON.parseArray(rules);
        for (int i = 0; i < rulesArray.size(); i++) {
            JSONArray subArray = rulesArray.getJSONArray(i);
            Map<String, Object> columnMap = new HashMap<>();
            allColumns.add(columnMap);

            List<String> groupParamList = getColumns(subArray, false);
            columnMap.put("columns", groupParamList);

            columnMap.put("originColumns", getColumns(subArray, true));

            // 分隔样式 adaptStyle
            String splitStr = subArray.getJSONObject(0).getString("adaptStyle");
            columnMap.put("originSplitStr", splitStr);

            splitStr = parseSplitStr(splitStr);
            columnMap.put("splitStr", splitStr);
        }

        if (!getTemuGoodHeadParamByActulStockScoop(queryGoodsDTO)) {
            return AjaxResult.error("没有适配保有量数据");
        }

        String goodIds = queryGoodsDTO.getIds();
        List<SaleGoodsDTO> saleGoodsDTOList = null;
        if (StringUtils.isNotEmpty(goodIds)) {
            String[] idList = goodIds.split(",");
            saleGoodsDTOList = goodsService.getGoodsDtoByIds(Arrays.stream(idList).map(Long::valueOf).collect(Collectors.toList()));
        } else {
            // 处理业务分类筛选
            if (CollUtil.isNotEmpty(queryGoodsDTO.getOperationClassificationList())) {
                List<String> goodsCodesByClassification = cateProductService.getGoodsCodesByOperationClassificationList(queryGoodsDTO.getOperationClassificationList());
                if (CollUtil.isEmpty(goodsCodesByClassification)) {
                    return AjaxResult.error("没有适配保有量数据");
                }

                // 如果已经有goodsCode筛选条件，需要取交集
                if (StringUtils.isNotBlank(queryGoodsDTO.getGoodsCode())) {
                    List<String> existingGoodsCodes = Arrays.asList(queryGoodsDTO.getGoodsCode().split("\\s+"));
                    goodsCodesByClassification = goodsCodesByClassification.stream()
                            .filter(existingGoodsCodes::contains)
                            .collect(Collectors.toList());
                    if (CollUtil.isEmpty(goodsCodesByClassification)) {
                        return AjaxResult.error("没有适配保有量数据");
                    }
                    // 设置筛选后的goodsCode（取交集结果）
                    queryGoodsDTO.setGoodsCode(String.join(" ", goodsCodesByClassification));
                } else {
                    // 如果没有手动输入SKU，则使用业务分类查询出的goodsCode列表
                    queryGoodsDTO.setGoodsCode(String.join(" ", goodsCodesByClassification));
                }
            }

            queryGoodsDTO.setPageNum(1);
            queryGoodsDTO.setPageSize(500);
            PageInfo<SaleGoodsDTO> pageInfo = PDMHttpRequestBiz.pageQueryGoodsDetailV2(queryGoodsDTO);
//            PageInfo<SaleGoodsDTO> pageInfo = PDMHttpRequestBiz.pageQueryGoodsDetail(queryGoodsDTO);
            List<SaleGoodsDTO> saleGoodsDTOS = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(saleGoodsDTOS)) {
                if (pageInfo.getTotal() > 7000) {
                    throw new BusinessException("导出数据不能超过7000条。如需大批量导出,请联系管理员！");
                }
                List<SaleGoodsDTO> dataList = JSONObject.parseArray(JSONObject.toJSONString(saleGoodsDTOS), SaleGoodsDTO.class);
                saleGoodsDTOList = new ArrayList<>((int) pageInfo.getTotal());
                saleGoodsDTOList.addAll(dataList);
                int pages = pageInfo.getPages();
                for (int i = 2; i <= pages; i++) {
                    queryGoodsDTO.setPageNum(i);
//                    PageInfo<SaleGoodsDTO> page = PDMHttpRequestBiz.pageQueryGoodsDetail(queryGoodsDTO);
                    PageInfo<SaleGoodsDTO> page = PDMHttpRequestBiz.pageQueryGoodsDetailV2(queryGoodsDTO);
                    saleGoodsDTOS = page.getList();
                    if (CollUtil.isNotEmpty(saleGoodsDTOS)) {
                        dataList = JSONObject.parseArray(JSONObject.toJSONString(saleGoodsDTOS), SaleGoodsDTO.class);
                        saleGoodsDTOList.addAll(dataList);
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(saleGoodsDTOList)) {
            return AjaxResult.error("没有适配保有量数据");
        }

        List<String> skus = saleGoodsDTOList.stream().map(SaleGoodsDTO::getProductCode).collect(Collectors.toList());
        List<List<String>> partitions = Lists.partition(skus, 20);
        Map<String, String> goodMap = saleGoodsDTOList.stream().collect(Collectors.toMap(SaleGoodsDTO::getGoodsCode, SaleGoodsDTO::getProductCode));
        List<AdsFitmentDataVIO> fitmentDataList = new ArrayList<>();
        List<AdsFitmentGPTData> gptDataList = new ArrayList<>();
        // 查询OE数据，用于适配图数据sheet
        List<AdsFitmentDataOe> oeDataList = new ArrayList<>();

        for (List<String> partition : partitions) {
            fitmentDataList.addAll(adsService.selectFitmentDataVIO(partition));
            gptDataList.addAll(adsService.selectFitmentGPTData(partition));
            // 获取OE数据
            oeDataList.addAll(adsService.selectFitmentDataOeList(partition));
        }

        if (CollectionUtils.isEmpty(fitmentDataList) && CollectionUtils.isEmpty(gptDataList)) {
            return AjaxResult.error("没有适配保有量数据");
        }

        Map<String, List<AdsFitmentDataVIO>> groupedFitmentData = fitmentDataList.stream().collect(Collectors.groupingBy(AdsFitmentDataVIO::getProductCode));

        // 按产品编码分组OE数据
        Map<String, List<AdsFitmentDataOe>> groupedOeData = oeDataList.stream().collect(Collectors.groupingBy(AdsFitmentDataOe::getProductCode));
        
        List<AdsFitmentCombinationDTO> referenceDataList = new ArrayList<>();
        List<List<Object>> adaptDataList = new ArrayList<>();
        List<List<String>> heads = new ArrayList<>();

        List<String> head0 = new ArrayList<>();
        head0.add("商品编码");
        heads.add(head0);
        List<String> head1 = new ArrayList<>();
        head1.add("产品编码");
        heads.add(head1);
        allColumns.forEach(columnMap -> {
            List<String> originColumns = (List<String>) columnMap.get("originColumns");
            String originSplitStr = (String) columnMap.get("originSplitStr");

            List<String> head = new ArrayList<>();
            head.add(String.join("+", originColumns) + (StrUtil.isNotBlank(originSplitStr) ? getSplitDesc(originSplitStr) : ""));
            heads.add(head);
        });

        for (SaleGoodsDTO goodsDTO : saleGoodsDTOList) {
            String productCode = goodMap.get(goodsDTO.getGoodsCode());
            AdsFitmentCombinationDTO referenceData = new AdsFitmentCombinationDTO();
            referenceData.setPdmGoodsCode(goodsDTO.getGoodsCode());
            referenceData.setProductCode(productCode);


            List<Object> adaptData = new ArrayList<>();
            adaptDataList.add(adaptData);
            adaptData.add(goodsDTO.getGoodsCode());
            adaptData.add(productCode);

            AdsFitmentGPTData gptData = gptDataList.stream().filter(g -> ObjUtil.equals(g.getProductCode(), productCode)).findFirst().orElse(null);
            if (ObjUtil.isNotEmpty(gptData)) {
                BeanUtil.copyProperties(gptData, referenceData, "pdmGoodsCode");
            }

            List<AdsFitmentDataVIO> adsList = groupedFitmentData.get(productCode);
            if (CollUtil.isNotEmpty(adsList)) {
                allColumns.forEach(columnMap -> {
                    List<String> columns = (List<String>) columnMap.get("columns");
                    String splitStr = (String) columnMap.get("splitStr");
                    List<Object> subDatas = adsService.handleAdsDataForExportV2(adsList, columns, splitStr);
                    if (CollUtil.isNotEmpty(subDatas)) {
                        adaptData.addAll(subDatas);
                    }
                });
            }
            referenceDataList.add(referenceData);
        }

        String fileName = System.currentTimeMillis()+".xlsx";
        String path = RuoYiConfig.getDownloadPath() + fileName;

        // 对referenceDataList的original字段，不能超过单元格最大长度
        referenceDataList.forEach(e -> {
            if (StrUtil.isNotBlank(e.getOriginal()) && e.getOriginal().length() > 32767) {
                e.setOriginal(e.getOriginal().substring(0, 32767));
            }
        });

        //写出错误信息
        try(ExcelWriter excelWriter = EasyExcel.write(path).build()){
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "数据组参考数据").head(AdsFitmentCombinationDTO.class).build();
            excelWriter.write(referenceDataList, writeSheet);

            Set<String> excludeColumnFiledNames = new HashSet<>();
            excludeColumnFiledNames.add("id");
            WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "适配保有量数据").head(heads).excludeColumnFieldNames(excludeColumnFiledNames).build();
            excelWriter.write(adaptDataList, writeSheet1);


            // Sheet 2：适配图数据 + 合并策略
            List<AdsFitmentImageData> imageDataList = generateImageData(groupedFitmentData, groupedOeData);

            // 使用新的全局列合并策略，每列根据不同字段进行合并
            Map<Integer, String> columnMergeFields = new HashMap<>();
            columnMergeFields.put(0, "productCode");
            columnMergeFields.put(1, "oeNumber");
            columnMergeFields.put(2, "makeName");
            columnMergeFields.put(3, "modelName");
            // 添加bodyTypeName列的合并配置
            columnMergeFields.put(6, "bodyTypeName");

            GlobalColumnMergeStrategy<AdsFitmentImageData> mergeStrategy =
                    new GlobalColumnMergeStrategy<>(imageDataList, columnMergeFields, 1);

            WriteSheet writeSheet2 = EasyExcel.writerSheet(2, "适配图数据")
                    .head(AdsFitmentImageData.class)
                    .registerWriteHandler(mergeStrategy)
                    .build();
            excelWriter.write(imageDataList, writeSheet2);
        }
        return AjaxResult.success(fileName);
    }

    private String getSplitDesc(String originSplitStr) {
        switch (originSplitStr) {
            case "1":
                return "英文逗号 + 空格";
            case "2":
                return "换行";
            case "3":
                return "空格";
            case "4":
                return "英文逗号";
            default:
                return "空格";
        }
    }

    private String parseSplitStr(String splitStr) {
        if (StrUtil.isBlank(splitStr)) {
            return " ";
        }
        // 英文逗号 + 空格
        switch (splitStr) {
            case "1":
                return ", ";

            // 换行
            case "2":
                return "\n";

            // 空格
            case "3":
                return " ";

            // 英文逗号
            case "4":
                return ",";
            default:
                return " ";
        }
    }

    private List<String> getColumns(JSONArray subArray, boolean originName) {
        // [[{"class":"adaptation","type":"adapt_param","param":""},{"class":"adaptation","type":"adapt_param","param":"make"},{"class":"adaptation","type":"adapt_param","param":"cylinders"}],[{"class":"adaptation","type":"adapt_param","param":""},{"class":"adaptation","type":"adapt_param","param":"fuelType"}]]'
        List<String> columns = new ArrayList<>();
        // 去重
        List<String> distinctColumns = new ArrayList<>();
        for (int i = 0; i < subArray.size(); i++) {
            JSONObject jsonObject = subArray.getJSONObject(i);
            String param = jsonObject.getString("param");
            if (StrUtil.isNotBlank(param) && !distinctColumns.contains(param) && !"adaptStyle".equals(param)) {
                columns.add(param);
                distinctColumns.add(param);
            }
        }
        if (originName) {
            return columns;
        }
        // 页面上的值是Excel注解的name，现在要转换成AdsFitmentDataFieldVIO的field属性名
        return columns.stream().map(e -> {
            Field[] fields = ReflectUtil.getFields(AdsFitmentDataFieldVIO.class);
            for (Field field : fields) {
                field.setAccessible(true);

                Excel excel = field.getAnnotation(Excel.class);
                if (excel != null && excel.name().equals(e)) {
                    return field.getName();
                }
            }
            return e;
        }).collect(Collectors.toList());
    }
    /**
     * 获取分组参数
     * @param selectColumn
     * @return
     */
    private List<String> getGroupParamList(String selectColumn) {
        List<String> groupParamList = Lists.newArrayList();
        if (ObjUtil.isNotEmpty(selectColumn)&&selectColumn.contains("goodAdaptive_all")){
            groupParamList.add("make");
            groupParamList.add("model");
//            groupParamList.add("year");
            groupParamList.add("liter");
            return groupParamList;
        }

        if (ObjUtil.isNotEmpty(selectColumn)&& !selectColumn.contains("goodAdaptive_all")){
            String[] split = selectColumn.split(",");
            for (String s : split) {
                String word = s.substring(s.indexOf("_") + 1);
                groupParamList.add(word);
            }
        }
        return groupParamList;
    }

    /**
     * 适配保有量导出
     * @param mmap
     * @return
     */
    @GetMapping("/exportColumns")
    public String exportColumns(ModelMap mmap) {
        Field[] fields = ReflectUtil.getFields(AdsFitmentDataFieldVIO.class);
        List<String> columns = Arrays.stream(fields).filter(e -> e.getAnnotation(Excel.class) != null).map(e -> {
            Excel excel = e.getAnnotation(Excel.class);
            return excel.name();
        }).collect(Collectors.toList());

        List<List<Map<String, String>>> defaultRules = defaultRules();
        mmap.put("defaultRules", defaultRules);
        mmap.put("columns", columns);
        return prefix + "/export_column";
    }


    private List<List<Map<String, String>>> defaultRules() {
        List<List<Map<String, String>>> defaultRules = new ArrayList<>();
        // model,适配样式为英文逗号 + 空格
        List<Map<String, String>> defaultRule0 = getDefaultRule(Collections.singletonList("model"), "1");
        defaultRules.add(defaultRule0);
        // year + model + liter,适配样式为英文逗号 + 空格
        List<Map<String, String>> defaultRule1 = getDefaultRule(Arrays.asList("year", "model", "liter"), "1");
        defaultRules.add(defaultRule1);
        // year + model + liter,适配样式为换行
        List<Map<String, String>> defaultRule2 = getDefaultRule(Arrays.asList("year", "model", "liter"), "2");
        defaultRules.add(defaultRule2);
        // model + year + liter,适配样式为英文逗号+空格
        List<Map<String, String>> defaultRule3 = getDefaultRule(Arrays.asList("model", "year", "liter"), "1");
        defaultRules.add(defaultRule3);
        // model + year + liter,适配样式为换行
        List<Map<String, String>> defaultRule4 = getDefaultRule(Arrays.asList("model", "year", "liter"), "2");
        defaultRules.add(defaultRule4);
        return defaultRules;
    }

    private List<Map<String, String>> getDefaultRule(List<String> list, String adaptStyle) {
        List<Map<String, String>> defaultRule = new ArrayList<>();
        for (String s : list) {
            Map<String, String> map = new HashMap<>();
            map.put("class", "adaptation");
            map.put("type", "adapt_param");
            map.put("param", s);
            map.put("adaptStyle", adaptStyle);
            defaultRule.add(map);
        }
        return defaultRule;
    }

    @PostMapping("/getStock")
    @ResponseBody
    public AjaxResult getStock(@RequestParam("goodsCode") String goodsCode,@RequestParam("siteCode") String siteCode){
        List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectStockShareAndPartGoodsCode(Collections.singletonList(goodsCode), siteCode, false);
        if (ObjectUtils.isEmpty(thirdpartyFbmDTOList)){
            return AjaxResult.success("获取库存成功",0);
        }
        Integer availableQty = thirdpartyFbmDTOList.stream().mapToInt(ThirdpartyFbmDTO::getSellableQty).sum();
        return AjaxResult.success("获取库存成功",availableQty);
    }


    /**
     * 同步temu运费模板
     */
    @PostMapping("syncTemuTemplate")
    @ResponseBody
    public AjaxResult syncTemuTemplate(@RequestParam("shopCode") String shopCode){
        if (ObjUtil.isEmpty(shopCode)) {
            throw new BusinessException("请选择店铺后再进行同步Temu运费模板");
        }
        try {
            temuBiz.syncTemplates(shopCode);
        } catch (Exception e) {
            AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }


    @PostMapping("refreshAttrDefaultValue")
    @ResponseBody
    public AjaxResult refreshAttrDefaultValue(@RequestBody ListAttrDefaultValueDTO defaultValueDTO){
        List<ListAttrDefaultValueVO> defaultValueVOS =  new ArrayList<>();
        if (CollUtil.isEmpty(defaultValueDTO.getGoodsCode())){
            return AjaxResult.error("商品编码不能为空");
        }
        if (CollUtil.isEmpty(defaultValueDTO.getGoodsIds())){
            return AjaxResult.error("商品主键不能为空");
        }
        if (CollUtil.isEmpty(defaultValueDTO.getRequiredFieldList())) {
            return defaultResult(defaultValueDTO, defaultValueVOS);
        }

        IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(defaultValueDTO.getPlatformCode());
        SaleGoodsVO saleGoodsVO = listingServiceByPlatformType.getSaleGoodsDTOListByGoodsIds(defaultValueDTO.getGoodsIds(), false);
        if (ObjUtil.isEmpty(saleGoodsVO)){
            return defaultResult(defaultValueDTO, defaultValueVOS);
        }
        if (CollUtil.isEmpty(saleGoodsVO.getSaleGoodsDTOList())){
            return defaultResult(defaultValueDTO, defaultValueVOS);
        }
        List<String> goodsCodes = saleGoodsVO.getSaleGoodsDTOList().stream().map(SaleGoodsDTO::getGoodsCode).distinct().collect(Collectors.toList());
        List<String> productCodes = saleGoodsVO.getSaleGoodsDTOList().stream().map(SaleGoodsDTO::getProductCode).filter(e -> StrUtil.isNotBlank(e)).distinct().collect(Collectors.toList());
        Map<String, List<AdsFitmentDataOe>> productCodeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(productCodes)) {
            List<AdsFitmentDataOe> adsFitmentDataOeList = adsService.selectFitmentDataOeList(productCodes);
            productCodeMap  = adsFitmentDataOeList.stream().collect(Collectors.groupingBy(AdsFitmentDataOe::getProductCode));
        }

        // 通过品类配置的映射从PDM取值
        List<GoodsNewAttributeVO> goodsAttributeDTOS = pdmHttpRequestBiz.queryGoodsAttribute(goodsCodes);

        Map<String, SaleGoodsDTO> goodsMap = saleGoodsVO.getSaleGoodsDTOList().stream().collect(Collectors.toMap(SaleGoodsDTO::getGoodsCode, Function.identity()));
        Map<String, List<GoodsNewAttributeVO>> goodsAttributeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(goodsAttributeDTOS)) {
            goodsAttributeMap = goodsAttributeDTOS.stream().collect(Collectors.groupingBy(GoodsNewAttributeVO::getSku));
        }
        
        for (String goodsCode : defaultValueDTO.getGoodsCode()) {
            SaleGoodsDTO saleGoodsDTO = goodsMap.get(goodsCode);
            if (ObjUtil.isEmpty(saleGoodsDTO)){
                continue;
            }

            ListAttrDefaultValueVO defaultValueVO = new ListAttrDefaultValueVO();
            defaultValueVO.setGoodsCode(goodsCode);
            List<ConfigRequiredField> requiredFieldList = new ArrayList<>();
            defaultValueVO.setRequiredFieldList(requiredFieldList);
            defaultValueVOS.add(defaultValueVO);

            if ("AM".equalsIgnoreCase(defaultValueDTO.getPlatformCode())) {
                amazonHandle(saleGoodsDTO,defaultValueDTO.getRequiredFieldList(), requiredFieldList, defaultValueDTO.getPublishType(), defaultValueDTO.getBrandCode());
            }else if ("EB".equalsIgnoreCase(defaultValueDTO.getPlatformCode())) {
                ebayHandle(saleGoodsDTO,defaultValueDTO.getRequiredFieldList(), requiredFieldList,goodsAttributeMap.get(saleGoodsDTO.getGoodsCode()), productCodeMap.get(saleGoodsDTO.getProductCode()));
            }
        }
        return AjaxResult.success(defaultValueVOS);
    }

    private void amazonHandle(SaleGoodsDTO saleGoodsDTO, List<ConfigRequiredField> originFieldList, List<ConfigRequiredField> requiredFieldList, String publishType, String brandCode) {
        /**
         * manufacturer 取值品牌字段
         * list_price 取值售价字段
         */
        originFieldList.forEach(requiredField -> {
            if (StrUtil.isNotBlank(requiredField.getAttributeValue())){
                return;
            }
            if (Stream.of("manufacturer", "list_price").noneMatch(s -> s.equalsIgnoreCase(requiredField.getAttributeCode()))){
                return;
            }

            ConfigRequiredField field = BeanUtil.copyProperties(requiredField, ConfigRequiredField.class);
            requiredFieldList.add(field);

            if ("manufacturer".equalsIgnoreCase(requiredField.getAttributeCode())) {
                field.setAttributeValue(brandCode);
            }
            if ("list_price".equalsIgnoreCase(requiredField.getAttributeCode())) {
                if ("FBM".equalsIgnoreCase(publishType)) {
                    field.setAttributeValue(saleGoodsDTO.getFbmPrice() +"");
                }else {
                    field.setAttributeValue(saleGoodsDTO.getFbaPrice() +"");
                }
            }
        });
    }

    private void ebayHandle(SaleGoodsDTO saleGoodsDTO, List<ConfigRequiredField> originFieldList, List<ConfigRequiredField> requiredFieldList, List<GoodsNewAttributeVO> goodsAttributeList, List<AdsFitmentDataOe> adsFitmentDataOeList) {
        if (CollUtil.isNotEmpty(goodsAttributeList)){
            // 以attributeCode为分组，将值合并，以,分隔
            Map<String, String> goodsAttributeMap = goodsAttributeList.stream().filter(e -> StrUtil.isNotBlank(e.getEbayAttributeValue())).collect(Collectors.groupingBy(GoodsNewAttributeVO::getAttributeCode, Collectors.mapping(GoodsNewAttributeVO::getEbayAttributeValue, Collectors.joining(","))));
            originFieldList.forEach(requiredField -> {
                if (StrUtil.isNotBlank(requiredField.getPdmAttributeName())) {
                    String attributeValue = goodsAttributeMap.get(requiredField.getPdmAttributeName());
                    if (StrUtil.isNotBlank(attributeValue))  {
                        ConfigRequiredField field = BeanUtil.copyProperties(requiredField, ConfigRequiredField.class);
                        requiredFieldList.add(field);

                        field.setAttributeValue(attributeValue);
                    }
                }
            });
        }
        // 如果出现key为OE Part Number、OEM Part Number，OE/OEM Part Number,取ads_fitmentdata_oe的oe_brand为OE的数据，数据需要对oe_number进行去重，然后以,分隔
        if (CollUtil.isNotEmpty(originFieldList) && CollUtil.isNotEmpty(adsFitmentDataOeList)) {
            // adsFitmentDataOeList按importance倒序,如果importance为null，则按nulls last排序
            adsFitmentDataOeList.sort(Comparator.comparing(AdsFitmentDataOe::getImportance, Comparator.nullsLast(Comparator.naturalOrder())).reversed());
            originFieldList.forEach(requiredField -> {
                if (StrUtil.isNotBlank(requiredField.getAttributeCode()) && ArrayUtil.containsAny(new String[]{"OE Part Number", "OEM Part Number", "OE/OEM Part Number"}, requiredField.getAttributeCode())) {
                    // 取ads_fitmentdata_oe的oe_brand为OE的数据，数据需要对oe_number进行去重，然后以,分隔
                    List<AdsFitmentDataOe> oeList = adsFitmentDataOeList.stream().filter(e -> "OE".equalsIgnoreCase(e.getOeBrand())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(oeList)){
                        // 去重后最多10个
                        String oeNumber = oeList.stream().map(AdsFitmentDataOe::getOeNumber).distinct().limit(10).collect(Collectors.joining(","));
                
                        ConfigRequiredField field = BeanUtil.copyProperties(requiredField, ConfigRequiredField.class);
                        requiredFieldList.add(field);
                        field.setAttributeValue(oeNumber);
                    }
                }
                // 如果出现Interchange Part Number和	Superseded Part Number， 取ads_fitmentdata_oe的oe_brand不为OE的数据，然后对oe_number进行去重，然后以,分隔，需要按Importance倒序
                if (StrUtil.isNotBlank(requiredField.getAttributeCode()) && ArrayUtil.containsAny(new String[]{"Interchange Part Number", "Superseded Part Number"}, requiredField.getAttributeCode())) {
                    // 取ads_fitmentdata_oe的oe_brand不为OE的数据，然后对oe_number进行去重，然后以,分隔，需要按Importance倒序
                    List<AdsFitmentDataOe> nonOeList = adsFitmentDataOeList.stream().filter(e -> !"OE".equalsIgnoreCase(e.getOeBrand())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(nonOeList)){
                        // 去重后最多10个
                        String nonOeNumber = nonOeList.stream().map(AdsFitmentDataOe::getOeNumber).distinct().limit(10).collect(Collectors.joining(","));
                        ConfigRequiredField field = BeanUtil.copyProperties(requiredField, ConfigRequiredField.class);
                        requiredFieldList.add(field);
                        field.setAttributeValue(nonOeNumber);
                    }
                }
            });
        }
        /**
         * PDM对应灵活取值
         * eBay
         * Length——PDM
         * width——PDM
         * height——PDM
         */
        GoodsSpecifications goodsSpecifications = saleGoodsDTO.getGoodsSpecifications();
        if (goodsSpecifications == null) {
            return;
        }
        originFieldList.forEach(requiredField -> {
            if (StrUtil.isNotBlank(requiredField.getAttributeValue())){
                return;
            }
            if (Stream.of("Length", "width", "height").noneMatch(s -> s.equalsIgnoreCase(requiredField.getAttributeCode()))){
                return;
            }

            ConfigRequiredField field = BeanUtil.copyProperties(requiredField, ConfigRequiredField.class);
            requiredFieldList.add(field);

            if ("Length".equalsIgnoreCase(requiredField.getAttributeCode()) && goodsSpecifications.getLength() != null) {
                field.setAttributeValue(saleGoodsDTO.getSpecifications().getLength().setScale(2, RoundingMode.HALF_UP).toString());
                return;
            }
            if ("width".equalsIgnoreCase(requiredField.getAttributeCode()) && goodsSpecifications.getWidth() != null) {
                field.setAttributeValue(saleGoodsDTO.getSpecifications().getWidth().setScale(2, RoundingMode.HALF_UP).toString());
                return;
            }
            if ("height".equalsIgnoreCase(requiredField.getAttributeCode()) && goodsSpecifications.getHeight() != null) {
                field.setAttributeValue(saleGoodsDTO.getSpecifications().getHeight().setScale(2, RoundingMode.HALF_UP).toString());
                return;
            }
        });
    }

    private static AjaxResult defaultResult(ListAttrDefaultValueDTO defaultValueDTO, List<ListAttrDefaultValueVO> defaultValueVOS) {
        for (String goodsCode : defaultValueDTO.getGoodsCode()) {
            ListAttrDefaultValueVO defaultValueVO = new ListAttrDefaultValueVO();
            defaultValueVO.setGoodsCode(goodsCode);
            defaultValueVO.setRequiredFieldList(defaultValueDTO.getRequiredFieldList());
            defaultValueVOS.add(defaultValueVO);
        }
        return AjaxResult.success(defaultValueVOS);
    }


    /**
     * 同步temu运费模板
     */
    @GetMapping("getPDMGoodsInfo")
    @ResponseBody
    public AjaxResult getPDMGoodsInfo(@RequestParam("goodsCode") String goodsCode){
        if (ObjUtil.isEmpty(goodsCode)) {
            return AjaxResult.error("商品编码不能为空");
        }
        try {
            List<String> goodsCodes =  Collections.singletonList(goodsCode);
            List<GoodsInfoDTO> goodsInfoDTOS = PDMHttpRequestBiz.queryGoodsListInfo(goodsCode);
            if (CollUtil.isEmpty(goodsInfoDTOS)) {
                return AjaxResult.error("商品信息不存在");
            }

            GoodsInfoDTO goodsInfoDTO = goodsInfoDTOS.get(0);
            Map<String, List<GoodsPart>> partMap = new HashMap<>();
            Map<String, Integer> fbmStockMap = null;
            List<SaleGoodsDTO> goodsDTOList = null;
            List<GoodsInfoDTO.GoodsStockDetailDTO> stockDetailDTOS = new ArrayList<>();
            goodsInfoDTO.setGoodsStockDetailList(stockDetailDTOS);

            if (goodsInfoDTO.getBasicsInfo() != null) {
                String developers = goodsInfoDTO.getBasicsInfo().getDevelopers();
                if (StringUtils.isNotBlank(developers) && NumberUtil.isNumber(developers)) {
                    SysUser sysUser = sysUserService.selectUserById(Long.valueOf(developers));
                    goodsInfoDTO.getBasicsInfo().setDevelopers(sysUser.getUserName());
                }
            }

            GetGoodsDetailQueryDTO partQuery = new GetGoodsDetailQueryDTO();
            partQuery.setModels(Arrays.asList("GoodsMainList", "PartList"));
            partQuery.setGoodsCodes(goodsCodes);
            goodsDTOList = PDMHttpRequestBiz.listGoodsDetail(partQuery);
            List<String> shareInventroySkuList = null;
            if(ObjectUtils.isNotEmpty(goodsDTOList)){
                SaleGoodsDTO saleGoodsDTO = goodsDTOList.get(0);

                List<GoodsPart> partList = saleGoodsDTO.getPartList();
                partMap.put(saleGoodsDTO.getGoodsCode(), partList);
                if (StrUtil.isNotBlank(saleGoodsDTO.getMainGoodsCode()) && StrUtil.isBlank(goodsInfoDTO.getMainGoodsCode())) {
                    goodsInfoDTO.setMainGoodsCode(saleGoodsDTO.getMainGoodsCode());
                }

                if (ObjUtil.isNotEmpty(saleGoodsDTO.getShareInventroySkuList())){
                    shareInventroySkuList = saleGoodsDTO.getShareInventroySkuList();
                }
                goodsInfoDTO.setGoodsId(saleGoodsDTO.getId());

            }
            if (CollUtil.isNotEmpty(shareInventroySkuList)){
                //去重
                shareInventroySkuList = shareInventroySkuList.stream().distinct().collect(Collectors.toList());
                List<ThirdpartyFbmDTO> stocks = inventoryBiz.getNowStock(shareInventroySkuList, "US", WarehouseExcludeEnum.getExcludeWarehouseCodeList(), false);
                if (CollUtil.isNotEmpty(stocks)) {
                    Map<String, Integer> stockByPdmGoodsCode = stocks.stream()
                            .filter(s-> Objects.equals(s.getWhCountry(), PlatformSiteEnum.AM_US.getSiteCodeType()))
                            .collect(Collectors.groupingBy(ThirdpartyFbmDTO::getSku, Collectors.summingInt(ThirdpartyFbmDTO::getSellableQty)));
                    for (String sku : shareInventroySkuList) {
                        Integer stock = stockByPdmGoodsCode.get(sku);
                        GoodsInfoDTO.GoodsStockDetailDTO stockDetailDTO = new GoodsInfoDTO.GoodsStockDetailDTO();
                        stockDetailDTO.setGoodsCode(sku);
                        stockDetailDTO.setStockQty(stock);
                        stockDetailDTOS.add(stockDetailDTO);
                    }
                }
            }
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectStockShareAndPartGoodsCode(goodsCodes, "US", false);
            fbmStockMap = thirdpartyFbmDTOList.stream().filter(dto -> dto.getSku() != null)
                    .collect(Collectors.groupingBy(ThirdpartyFbmDTO::getSku, Collectors.summingInt(ThirdpartyFbmDTO::getSellableQty)));

            goodsInfoDTO.setStockQty(fbmStockMap.get(goodsCode));

            // 适配信息
            String productCode = goodsInfoDTO.getBasicsInfo().getProductCode();
            if (StrUtil.isNotBlank(productCode)) {
                List<AdsFitmentGPTData> list = adsService.selectFitmentGPTData(Collections.singletonList(productCode));
                List<GoodsInfoDTO.GoodsAdaptDTO> goodsAdaptDTOS = new ArrayList<>();
                goodsInfoDTO.setGoodsAdaptDTOS(goodsAdaptDTOS);
                if (CollUtil.isNotEmpty(list)) {
                    for (AdsFitmentGPTData adsFitmentGPTData : list) {
                        if (StrUtil.isNotBlank(adsFitmentGPTData.getTitle())) {
                            GoodsInfoDTO.GoodsAdaptDTO goodsAdaptDTO = new GoodsInfoDTO.GoodsAdaptDTO();
                            goodsAdaptDTO.setType("title");
                            goodsAdaptDTO.setContent(adsFitmentGPTData.getTitle());
                            goodsAdaptDTOS.add(goodsAdaptDTO);
                        }
                        if (StrUtil.isNotBlank(adsFitmentGPTData.getDescription())) {
                            GoodsInfoDTO.GoodsAdaptDTO goodsAdaptDTO = new GoodsInfoDTO.GoodsAdaptDTO();
                            goodsAdaptDTO.setType("description");
                            goodsAdaptDTO.setContent(adsFitmentGPTData.getDescription());
                            goodsAdaptDTOS.add(goodsAdaptDTO);
                        }
                        if (StrUtil.isNotBlank(adsFitmentGPTData.getDetail())) {
                            GoodsInfoDTO.GoodsAdaptDTO goodsAdaptDTO = new GoodsInfoDTO.GoodsAdaptDTO();
                            goodsAdaptDTO.setType("detail");
                            goodsAdaptDTO.setContent(adsFitmentGPTData.getDetail());
                            goodsAdaptDTOS.add(goodsAdaptDTO);
                        }
                        if (StrUtil.isNotBlank(adsFitmentGPTData.getOriginal())) {
                            GoodsInfoDTO.GoodsAdaptDTO goodsAdaptDTO = new GoodsInfoDTO.GoodsAdaptDTO();
                            goodsAdaptDTO.setType("original");
                            goodsAdaptDTO.setContent(adsFitmentGPTData.getOriginal());
                            goodsAdaptDTOS.add(goodsAdaptDTO);
                        }
                        if (StrUtil.isNotBlank(adsFitmentGPTData.getTitleIndex())) {
                            GoodsInfoDTO.GoodsAdaptDTO goodsAdaptDTO = new GoodsInfoDTO.GoodsAdaptDTO();
                            goodsAdaptDTO.setType("titleIndex");
                            goodsAdaptDTO.setContent(adsFitmentGPTData.getTitleIndex());
                            goodsAdaptDTOS.add(goodsAdaptDTO);
                        }
                        if (StrUtil.isNotBlank(adsFitmentGPTData.getDescriptionIndex())) {
                            GoodsInfoDTO.GoodsAdaptDTO goodsAdaptDTO = new GoodsInfoDTO.GoodsAdaptDTO();
                            goodsAdaptDTO.setType("descriptionIndex");
                            goodsAdaptDTO.setContent(adsFitmentGPTData.getDescriptionIndex());
                            goodsAdaptDTOS.add(goodsAdaptDTO);
                        }
                        if (StrUtil.isNotBlank(adsFitmentGPTData.getDetailIndex())) {
                            GoodsInfoDTO.GoodsAdaptDTO goodsAdaptDTO = new GoodsInfoDTO.GoodsAdaptDTO();
                            goodsAdaptDTO.setType("detailIndex");
                            goodsAdaptDTO.setContent(adsFitmentGPTData.getDetailIndex());
                            goodsAdaptDTOS.add(goodsAdaptDTO);
                        }
                        if (StrUtil.isNotBlank(adsFitmentGPTData.getOriginalIndex())) {
                            GoodsInfoDTO.GoodsAdaptDTO goodsAdaptDTO = new GoodsInfoDTO.GoodsAdaptDTO();
                            goodsAdaptDTO.setType("originalIndex");
                            goodsAdaptDTO.setContent(adsFitmentGPTData.getOriginalIndex());
                            goodsAdaptDTOS.add(goodsAdaptDTO);
                        }

                    }
                }
            }
            return AjaxResult.success(goodsInfoDTO);
        } catch (Exception e) {
            log.error("获取商品信息失败,goodsCode:{}",goodsCode, e);
            AjaxResult.error(e.getMessage());
        }
        return AjaxResult.error();
    }

    @PostMapping("watchListing")
    public String watchListing(String json, ModelMap mmap) {
        mmap.put("listing", json);
        return prefix + "/watch_listing";
    }

    @PostMapping("batchAddMonitor")
    @ResponseBody
    public AjaxResult batchAddMonitor(@RequestParam(value = "goodsCodes", required = false) String goodsCodes, @RequestParam(value = "listingIds", required = false) String listingIds) {
        if (StrUtil.isBlank(goodsCodes) && StrUtil.isBlank(listingIds)) {
            return AjaxResult.error("参数不能为空");
        }
        amazonProductBiz.batchAddMonitor(goodsCodes, listingIds);
        return AjaxResult.success();
    }

    @PostMapping("batchRemoveMonitor")
    @ResponseBody
    public AjaxResult batchRemoveMonitor(@RequestParam(value = "goodsCodes", required = false) String goodsCodes, @RequestParam(value = "listingIds", required = false) String listingIds) {
        if (StrUtil.isBlank(goodsCodes) && StrUtil.isBlank(listingIds)) {
            return AjaxResult.error("参数不能为空");
        }
        amazonProductBiz.batchRemoveMonitor(goodsCodes, listingIds);
        return AjaxResult.success();
    }

    /**
     * 生成适配图数据（合并ads_fitmentdata_bi和ads_fitmentdata_oe的数据）
     *
     * @param groupedFitmentData 按产品编码分组的bi数据
     * @param groupedOeData      按产品编码分组的oe数据
     * @return 适配图数据列表
     */
    private List<AdsFitmentImageData> generateImageData(Map<String, List<AdsFitmentDataVIO>> groupedFitmentData,
                                                        Map<String, List<AdsFitmentDataOe>> groupedOeData) {
        List<AdsFitmentImageData> result = new ArrayList<>();

        for (Map.Entry<String, List<AdsFitmentDataVIO>> entry : groupedFitmentData.entrySet()) {
            String productCode = entry.getKey();
            List<AdsFitmentDataVIO> biDataList = entry.getValue();

            if (CollUtil.isEmpty(biDataList)) {
                continue;
            }

            // 获取OE数据
            List<AdsFitmentDataOe> oeDataList = groupedOeData.getOrDefault(productCode, new ArrayList<>());
            String oeNumberStr = oeDataList.stream()
                    .map(AdsFitmentDataOe::getOeNumber)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.joining(", "));

            // 为每条 biData 创建一条 imageData
            for (AdsFitmentDataVIO biData : biDataList) {
                if (ObjectUtil.isEmpty(biData)) {
                    continue;
                }
                AdsFitmentImageData imageData = new AdsFitmentImageData();
                imageData.setProductCode(productCode);
                imageData.setOeNumber(oeNumberStr);
                imageData.setMakeName(biData.getMakeName());
                imageData.setModelName(biData.getModelName());
                imageData.setYearName(biData.getYearName());
                imageData.setBodyTypeName(biData.getBodyTypeName());
                imageData.setSubModelName(biData.getSubModelName());
                imageData.setAspirationName(biData.getAspirationName());

                imageData.setVio(ObjectUtil.isEmpty(biData.getVio()) ? 0 : biData.getVio().intValue());

                // 设置 BlockType_Cylinders
                String blockTypeCylinders = "";
                if (StrUtil.isNotBlank(biData.getBlockType()) && StrUtil.isNotBlank(biData.getCylinders())) {
                    blockTypeCylinders = biData.getBlockType() + biData.getCylinders();
                } else if (StrUtil.isNotBlank(biData.getBlockType())) {
                    blockTypeCylinders = biData.getBlockType();
                } else if (StrUtil.isNotBlank(biData.getCylinders())) {
                    blockTypeCylinders = biData.getCylinders();
                }
                imageData.setBlockTypeCylinders(blockTypeCylinders);

                // 设置 Liter_aspiration
                String literAspiration = "";
                if (StrUtil.isNotBlank(biData.getLiter()) && StrUtil.isNotBlank(biData.getAspirationName())) {
                    literAspiration = biData.getLiter() + "_" + biData.getAspirationName();
                } else if (StrUtil.isNotBlank(biData.getLiter())) {
                    literAspiration = biData.getLiter();
                } else if (StrUtil.isNotBlank(biData.getAspirationName())) {
                    literAspiration = biData.getAspirationName();
                }
                imageData.setLiter(literAspiration);
                // 设置单条数据方便后续处理
                imageData.setBiDataList(Collections.singletonList(biData));
                imageData.setOeDataList(oeDataList);
                result.add(imageData);
            }
        }

        // 第一步：按照productCode+oeNumber+makeName+modelName+blockTypeCylinders+bodyTypeName+liter进行分组
        Map<String, List<AdsFitmentImageData>> groupedData = new HashMap<>();
        for (AdsFitmentImageData data : result) {
            // 创建组合键，包含所有需要相同的字段
            String groupKey = data.getProductCode() + "|" +
                    (data.getOeNumber() != null ? data.getOeNumber() : "") + "|" +
                    (data.getMakeName() != null ? data.getMakeName() : "") + "|" +
                    (data.getModelName() != null ? data.getModelName() : "") + "|" +
                    (data.getBlockTypeCylinders() != null ? data.getBlockTypeCylinders() : "") + "|" +
                    (data.getBodyTypeName() != null ? data.getBodyTypeName() : "") + "|" +
                    (data.getLiter() != null ? data.getLiter() : "");

            if (!groupedData.containsKey(groupKey)) {
                groupedData.put(groupKey, new ArrayList<>());
            }
            groupedData.get(groupKey).add(data);
        }

        // 第二步：合并每个分组的数据
        List<AdsFitmentImageData> mergedResult = new ArrayList<>();
        for (List<AdsFitmentImageData> group : groupedData.values()) {
            if (group.isEmpty()) {
                continue;
            }

            // 使用第一条记录作为基础
            AdsFitmentImageData baseData = group.get(0);
            AdsFitmentImageData mergedData = new AdsFitmentImageData();

            // 复制共同字段
            mergedData.setProductCode(baseData.getProductCode());
            mergedData.setOeNumber(baseData.getOeNumber());
            mergedData.setMakeName(baseData.getMakeName());
            mergedData.setModelName(baseData.getModelName());
            mergedData.setBodyTypeName(baseData.getBodyTypeName());
            mergedData.setBlockTypeCylinders(baseData.getBlockTypeCylinders());
            mergedData.setLiter(baseData.getLiter());
            mergedData.setAspirationName(baseData.getAspirationName());
            mergedData.setSubModelName(baseData.getSubModelName());

            // 合并数值型字段（如vio取最大值）
            mergedData.setVio(group.stream()
                    .mapToInt(AdsFitmentImageData::getVio)
                    .max()
                    .orElse(0));

            // 合并yearName字段（解析年份并处理为范围格式）
            List<Integer> yearNumbers = new ArrayList<>();
            for (AdsFitmentImageData data : group) {
                if (StrUtil.isNotBlank(data.getYearName())) {
                    try {
                        // 尝试将yearName转换为整数
                        int yearNum = Integer.parseInt(data.getYearName());
                        yearNumbers.add(yearNum);
                    } catch (NumberFormatException e) {
                        // 如果无法解析为整数，忽略
                    }
                }
            }

            // 对年份进行排序
            Collections.sort(yearNumbers);

            // 去重
            yearNumbers = yearNumbers.stream().distinct().collect(Collectors.toList());

            // 格式化年份
            if (yearNumbers.isEmpty()) {
                // 如果没有有效年份，保留原始年份
                mergedData.setYearName(baseData.getYearName());
            } else {
                StringBuilder yearBuilder = new StringBuilder();
                int i = 0;
                while (i < yearNumbers.size()) {
                    int startYear = yearNumbers.get(i);
                    int endYear = startYear;

                    // 寻找连续的年份
                    while (i + 1 < yearNumbers.size() && yearNumbers.get(i + 1) == endYear + 1) {
                        endYear = yearNumbers.get(i + 1);
                        i++;
                    }

                    // 添加年份范围或单个年份
                    if (startYear == endYear) {
                        yearBuilder.append(startYear);
                    } else {
                        yearBuilder.append(startYear).append("-").append(endYear);
                    }

                    i++;
                    // 如果还有更多年份，添加分隔符
                    if (i < yearNumbers.size()) {
                        yearBuilder.append(", ");
                    }
                }

                mergedData.setYearName(yearBuilder.toString());
            }

            // 合并SubModelName字段（不同的子模型用分号分隔）
            Set<String> subModels = new LinkedHashSet<>(); // 使用LinkedHashSet保持添加顺序
            for (AdsFitmentImageData data : group) {
                if (StrUtil.isNotBlank(data.getSubModelName())) {
                    subModels.add(data.getSubModelName());
                }
            }
            mergedData.setSubModelName(String.join(";", subModels));

            // 合并biDataList和oeDataList
            List<AdsFitmentDataVIO> allBiData = new ArrayList<>();
            for (AdsFitmentImageData data : group) {
                if (data.getBiDataList() != null) {
                    allBiData.addAll(data.getBiDataList());
                }
            }
            mergedData.setBiDataList(allBiData);

            // OE数据应该是相同的，直接使用基础数据的OE列表
            mergedData.setOeDataList(baseData.getOeDataList());

            mergedResult.add(mergedData);
        }

        // 使用数据重组策略对数据进行重组
        List<String> groupByFields = new ArrayList<>();
        groupByFields.add("productCode"); // 首先按productCode分组
        groupByFields.add("makeName");
        groupByFields.add("modelName");

        List<String> orderByFields = new ArrayList<>();
        orderByFields.add("yearName");
        orderByFields.add("blockTypeCylinders");
        orderByFields.add("bodyTypeName");
        orderByFields.add("liter");

        DataReorganizeStrategy<AdsFitmentImageData> reorganizeStrategy =
                new DataReorganizeStrategy<>(mergedResult, groupByFields, orderByFields);

        // 返回重组后的数据
        return reorganizeStrategy.reorganize();
    }

}